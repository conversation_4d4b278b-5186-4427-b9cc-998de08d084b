#!/usr/bin/env python3
"""
启动真实语音流水线测试环境
包含TTS API服务器和Web界面
"""

import subprocess
import sys
import time
import webbrowser
import threading
import os
import signal
from pathlib import Path
import http.server
import socketserver

class RealVoiceTestLauncher:
    def __init__(self):
        self.web_server_process = None
        self.tts_api_process = None
        self.backend_server_process = None
        self.running = True
        
    def start_tts_api_server(self):
        """启动TTS API服务器"""
        try:
            print("🚀 启动TTS API服务器...")
            self.tts_api_process = subprocess.Popen([
                sys.executable, "tts_api_server.py"
            ])
            print("✅ TTS API服务器已启动: http://localhost:8001")
            return True
        except Exception as e:
            print(f"❌ TTS API服务器启动失败: {e}")
            return False
    
    def start_backend_server(self):
        """启动后端服务器"""
        try:
            print("🚀 启动后端服务器...")
            backend_dir = Path("backend")
            if backend_dir.exists():
                self.backend_server_process = subprocess.Popen([
                    sys.executable, "-m", "uvicorn", "main:app", 
                    "--host", "0.0.0.0", "--port", "8000", "--reload"
                ], cwd=backend_dir)
                print("✅ 后端服务器已启动: http://localhost:8000")
                return True
            else:
                print("⚠️ backend目录不存在，跳过后端服务器启动")
                return False
        except Exception as e:
            print(f"❌ 后端服务器启动失败: {e}")
            return False
    
    def start_web_server(self):
        """启动Web服务器"""
        try:
            PORT = 8002
            Handler = http.server.SimpleHTTPRequestHandler
            
            # 切换到当前目录
            os.chdir(Path(__file__).parent)
            
            with socketserver.TCPServer(("", PORT), Handler) as httpd:
                print(f"✅ Web服务器已启动: http://localhost:{PORT}")
                print(f"📄 测试页面: http://localhost:{PORT}/real_voice_pipeline_test.html")
                httpd.serve_forever()
        except Exception as e:
            print(f"❌ Web服务器启动失败: {e}")
    
    def signal_handler(self, signum, frame):
        """处理中断信号"""
        print("\n🛑 正在停止所有服务...")
        self.running = False
        
        if self.tts_api_process:
            self.tts_api_process.terminate()
            try:
                self.tts_api_process.wait(timeout=5)
                print("✅ TTS API服务器已停止")
            except subprocess.TimeoutExpired:
                self.tts_api_process.kill()
                print("⚠️ 强制终止TTS API服务器")
        
        if self.backend_server_process:
            self.backend_server_process.terminate()
            try:
                self.backend_server_process.wait(timeout=5)
                print("✅ 后端服务器已停止")
            except subprocess.TimeoutExpired:
                self.backend_server_process.kill()
                print("⚠️ 强制终止后端服务器")
        
        print("✅ 所有服务已停止")
        sys.exit(0)
    
    def check_environment(self):
        """检查环境配置"""
        print("🔍 检查环境配置...")
        
        # 检查必要文件
        required_files = [
            "real_voice_pipeline_test.html",
            "tts_api_server.py",
            "backend/.env"
        ]
        
        missing_files = []
        for file in required_files:
            if not Path(file).exists():
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
            return False
        
        print("✅ 环境检查完成")
        return True
    
    def run(self):
        """主运行函数"""
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("🎤 启动真实语音流水线测试环境")
        print("=" * 60)
        
        # 检查环境
        if not self.check_environment():
            return
        
        # 启动TTS API服务器
        if not self.start_tts_api_server():
            return
        
        time.sleep(2)
        
        # 启动后端服务器（可选）
        backend_started = self.start_backend_server()
        time.sleep(3)
        
        # 在后台启动Web服务器
        print("🚀 启动Web服务器...")
        server_thread = threading.Thread(target=self.start_web_server, daemon=True)
        server_thread.start()
        time.sleep(2)
        
        # 自动打开浏览器
        print("🌍 正在打开浏览器...")
        webbrowser.open("http://localhost:8002/real_voice_pipeline_test.html")
        
        print("\n" + "="*70)
        print("🎤 真实语音流水线测试环境已启动:")
        print("- TTS API服务器: http://localhost:8001")
        print("- TTS API文档: http://localhost:8001/docs")
        if backend_started:
            print("- 后端服务器: http://localhost:8000")
        print("- Web测试界面: http://localhost:8002/real_voice_pipeline_test.html")
        print("\n📋 使用说明:")
        print("1. 点击'测试后端连接'检查服务状态")
        print("2. 点击'测试真实服务'体验LLM+TTS")
        print("3. 点击'开始录音'进行完整流水线测试")
        print("4. 现在播放的是真实的TTS音频，不再是模拟音频")
        print("5. 按 Ctrl+C 停止所有服务")
        print("="*70)
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)

def main():
    """主函数"""
    launcher = RealVoiceTestLauncher()
    launcher.run()

if __name__ == "__main__":
    main()