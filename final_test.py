#!/usr/bin/env python3
"""
最终测试脚本 - 启动服务器并测试完整流程
"""
import subprocess
import time
import requests
import json
import sys
import os
import signal

def start_server():
    """启动后端服务器"""
    print("🚀 启动后端服务器...")
    
    # 切换到 backend 目录并启动服务
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    
    try:
        # 使用 subprocess 启动服务器
        process = subprocess.Popen([
            sys.executable, '-c', '''
import sys
import os
sys.path.insert(0, ".")
os.chdir("backend")

# 修改 VAD 服务以支持模拟模式
import logging
logging.basicConfig(level=logging.INFO)

# 启动服务器
import uvicorn
from main import app

print("Starting server on http://localhost:8000")
uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
'''
        ], cwd=os.path.dirname(__file__))
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        for i in range(30):  # 最多等待30秒
            try:
                response = requests.get("http://localhost:8000/", timeout=2)
                if response.status_code == 200:
                    print("✅ 服务器启动成功！")
                    return process
            except:
                pass
            time.sleep(1)
            print(f"   等待中... ({i+1}/30)")
        
        print("❌ 服务器启动超时")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None

def test_endpoints():
    """测试所有端点"""
    print("\n🧪 测试后端端点...")
    
    base_url = "http://localhost:8000"
    results = {}
    
    # 1. 根端点
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        results['root'] = {
            'status': response.status_code,
            'success': response.status_code == 200
        }
        print(f"✅ 根端点: {response.status_code}")
    except Exception as e:
        results['root'] = {'status': 'error', 'error': str(e), 'success': False}
        print(f"❌ 根端点失败: {e}")
    
    # 2. 健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        results['health'] = {
            'status': response.status_code,
            'success': response.status_code == 200,
            'data': response.json() if response.status_code == 200 else None
        }
        print(f"✅ 健康检查: {response.status_code}")
        if response.status_code == 200:
            health_data = response.json()
            print(f"   状态: {health_data.get('status')}")
            print(f"   服务: {health_data.get('services', {})}")
    except Exception as e:
        results['health'] = {'status': 'error', 'error': str(e), 'success': False}
        print(f"❌ 健康检查失败: {e}")
    
    # 3. NPCs 端点
    try:
        response = requests.get(f"{base_url}/npcs", timeout=10)
        results['npcs'] = {
            'status': response.status_code,
            'success': response.status_code == 200,
            'data': response.json() if response.status_code == 200 else None
        }
        print(f"✅ NPCs 端点: {response.status_code}")
        if response.status_code == 200:
            npcs_data = response.json()
            print(f"   数据源: {npcs_data.get('source')}")
            print(f"   NPC 数量: {len(npcs_data.get('npcs', []))}")
    except Exception as e:
        results['npcs'] = {'status': 'error', 'error': str(e), 'success': False}
        print(f"❌ NPCs 端点失败: {e}")
    
    # 4. 测试端点 (最重要的)
    try:
        response = requests.get(f"{base_url}/test", timeout=30)
        results['test'] = {
            'status': response.status_code,
            'success': response.status_code == 200,
            'data': response.json() if response.status_code == 200 else None
        }
        print(f"✅ 测试端点: {response.status_code}")
        if response.status_code == 200:
            test_data = response.json()
            print(f"   状态: {test_data.get('status')}")
            print(f"   问题: {test_data.get('question')}")
            print(f"   回答: {test_data.get('response')}")
            print(f"   数据库: {test_data.get('database_status')}")
            
            # 检查是否成功
            if test_data.get('status') == 'success':
                results['test']['success'] = True
                print("   🎉 测试端点完全成功！")
            else:
                print(f"   ⚠️ 测试端点返回错误: {test_data}")
        else:
            print(f"   ❌ 测试端点响应: {response.text}")
    except Exception as e:
        results['test'] = {'status': 'error', 'error': str(e), 'success': False}
        print(f"❌ 测试端点失败: {e}")
    
    return results

def main():
    """主函数"""
    print("🎯 最终测试 - 完整后端验证")
    print("=" * 50)
    
    server_process = None
    
    try:
        # 启动服务器
        server_process = start_server()
        if not server_process:
            print("❌ 无法启动服务器，测试终止")
            return 1
        
        # 测试端点
        results = test_endpoints()
        
        # 汇总结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        
        success_count = 0
        total_count = 0
        
        for endpoint, result in results.items():
            total_count += 1
            if result.get('success'):
                success_count += 1
                print(f"✅ {endpoint}: 成功")
            else:
                print(f"❌ {endpoint}: 失败 - {result.get('error', result.get('status'))}")
        
        print(f"\n📈 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        # 检查关键测试是否通过
        if results.get('test', {}).get('success'):
            print("\n🎉 关键测试通过！'1+1等于几' 问答功能正常工作！")
            print("✨ 后端服务已准备就绪，可以处理语音聊天请求。")
            return 0
        else:
            print("\n⚠️ 关键测试未完全通过，但基础功能可用。")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return 1
    finally:
        # 清理服务器进程
        if server_process:
            print("\n🧹 清理服务器进程...")
            try:
                server_process.terminate()
                server_process.wait(timeout=5)
            except:
                server_process.kill()
            print("✅ 服务器已停止")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
