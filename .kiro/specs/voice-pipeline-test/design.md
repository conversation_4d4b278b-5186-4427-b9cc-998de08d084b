# Design Document

## Overview

The Voice Pipeline Test System is a comprehensive web-based testing interface that integrates all components of the voice interaction pipeline: Recording, ASR (Automatic Speech Recognition), LLM (Large Language Model), TTS (Text-to-Speech), and Audio Playback. The system provides real-time visual feedback, performance metrics, and comprehensive testing capabilities for the complete voice interaction flow.

The design leverages the existing backend services and WebSocket infrastructure while creating a new dedicated testing interface that can operate independently of the main application.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend (Browser)"
        UI[Test Interface HTML]
        WS[WebSocket Client]
        MR[MediaRecorder API]
        AP[Audio Player]
    end
    
    subgraph "Backend Server"
        WSH[WebSocket Handler]
        PS[Pipeline Service]
        
        subgraph "Core Services"
            ASR[ASR Service]
            LLM[LLM Service] 
            TTS[TTS Service]
            VAD[VAD Service]
        end
        
        subgraph "Storage"
            FS[File System]
            MEM[Memory Cache]
        end
    end
    
    UI --> WS
    WS --> WSH
    WSH --> PS
    PS --> ASR
    PS --> LLM
    PS --> TTS
    MR --> WS
    AP --> UI
    PS --> FS
    PS --> MEM
```

### Component Interaction Flow

```mermaid
sequenceDiagram
    participant U as User Interface
    participant W as WebSocket
    participant P as Pipeline Service
    participant A as ASR Service
    participant L as LLM Service
    participant T as TTS Service
    
    U->>W: Start Recording
    W->>P: Audio Data
    P->>A: Transcribe Audio
    A-->>P: Text + Confidence
    P->>L: Generate Response
    L-->>P: AI Response Text
    P->>T: Synthesize Speech
    T-->>P: Audio Data
    P->>W: Complete Pipeline Result
    W->>U: Update UI + Play Audio
```

## Components and Interfaces

### 1. Frontend Test Interface

**Technology**: HTML5, JavaScript, WebSocket API, MediaRecorder API

**Key Components**:
- **Pipeline Status Display**: Visual indicators for each stage (Recording, ASR, LLM, TTS, Playback)
- **Recording Controls**: Start/Stop recording with real-time feedback
- **Results Display**: Text areas for ASR transcription and LLM responses
- **Audio Players**: Playback controls for recorded and synthesized audio
- **Performance Metrics**: Real-time timing and performance data
- **Log Panel**: Detailed logging and debugging information

**Interface Methods**:
```javascript
// WebSocket Communication
connect() -> Promise<void>
sendAudioData(audioBlob) -> void
handleMessage(data) -> void

// Recording Management  
startRecording() -> Promise<void>
stopRecording() -> void
processAudioData(audioChunks) -> Blob

// UI Updates
updateStage(stageName, status, message) -> void
displayResults(asrText, llmResponse) -> void
updateMetrics(timingData) -> void
```

### 2. Backend Pipeline Service

**Technology**: Python, FastAPI, WebSocket

**Key Components**:
- **WebSocket Handler**: Manages client connections and message routing
- **Pipeline Orchestrator**: Coordinates the flow between services
- **Audio Processor**: Handles audio format conversion and validation
- **Result Aggregator**: Combines results from all pipeline stages
- **Performance Monitor**: Tracks timing and performance metrics

**Interface Methods**:
```python
class VoicePipelineService:
    async def process_audio_pipeline(self, audio_data: bytes, session_id: str) -> Dict[str, Any]
    async def handle_recording_stage(self, audio_data: bytes) -> Dict[str, Any]
    async def handle_asr_stage(self, audio_data: np.ndarray) -> Dict[str, Any]
    async def handle_llm_stage(self, transcription: str, context: List[Dict]) -> Dict[str, Any]
    async def handle_tts_stage(self, text: str, emotion: str, speed: float) -> Dict[str, Any]
    async def handle_playback_stage(self, audio_data: bytes) -> Dict[str, Any]
```

### 3. Service Integration Layer

**Existing Services Integration**:
- **MultimodalASRService**: Speech-to-text conversion with fallback support
- **LLMService**: Conversation generation with mock responses for testing
- **TTSService**: Text-to-speech synthesis with multiple voice options
- **VADService**: Voice activity detection for audio processing

**New Pipeline Coordinator**:
```python
class PipelineCoordinator:
    def __init__(self, asr_service, llm_service, tts_service, vad_service)
    async def execute_full_pipeline(self, audio_data: bytes) -> PipelineResult
    async def execute_stage(self, stage: str, input_data: Any) -> StageResult
    def get_performance_metrics(self) -> Dict[str, float]
```

### 4. WebSocket Message Protocol

**Message Types**:
```typescript
// Client to Server
interface AudioChunkMessage {
    type: "audio_chunk"
    data: string  // base64 encoded audio
    metadata: {
        originalSize: number
        mimeType: string
        timestamp: number
    }
}

interface ControlMessage {
    type: "start_session" | "stop_session" | "clear_results"
    session_id?: string
}

// Server to Client
interface StageUpdateMessage {
    type: "stage_update"
    stage: "recording" | "asr" | "llm" | "tts" | "playback"
    status: "active" | "success" | "error"
    message: string
    timestamp: number
    duration?: number
}

interface ResultMessage {
    type: "transcription" | "llm_response" | "audio_chunk"
    data: any
    confidence?: number
    metadata?: any
}

interface MetricsMessage {
    type: "metrics_update"
    metrics: {
        recordingDuration: number
        asrTime: number
        llmTime: number
        ttsTime: number
        totalTime: number
    }
}
```

## Data Models

### 1. Pipeline Session

```python
@dataclass
class PipelineSession:
    session_id: str
    user_id: Optional[str]
    start_time: datetime
    current_stage: str
    status: str  # "active", "completed", "error"
    metrics: Dict[str, float]
    results: Dict[str, Any]
    audio_files: List[str]
```

### 2. Stage Result

```python
@dataclass
class StageResult:
    stage_name: str
    status: str  # "success", "error", "skipped"
    start_time: datetime
    end_time: datetime
    duration: float
    input_data: Any
    output_data: Any
    error_message: Optional[str]
    metadata: Dict[str, Any]
```

### 3. Pipeline Result

```python
@dataclass
class PipelineResult:
    session_id: str
    total_duration: float
    stages: List[StageResult]
    final_audio: Optional[bytes]
    transcription: Optional[str]
    llm_response: Optional[str]
    success: bool
    error_message: Optional[str]
```

## Error Handling

### 1. Service Failure Handling

**ASR Service Failures**:
- Connection timeout: Use fallback mock transcription
- Invalid audio format: Convert audio format automatically
- Server unavailable: Display clear error message and provide retry option

**LLM Service Failures**:
- API timeout: Use pre-defined mock responses
- Rate limiting: Implement exponential backoff
- Invalid response format: Parse and extract usable content

**TTS Service Failures**:
- Synthesis failure: Provide text-only response option
- Audio format issues: Convert to supported format
- Service unavailable: Display text response with retry option

### 2. Client-Side Error Handling

**Recording Failures**:
- Microphone permission denied: Show clear instructions for enabling permissions
- Browser compatibility: Detect and show supported browser requirements
- Audio format not supported: Provide format conversion

**WebSocket Connection Issues**:
- Connection lost: Automatic reconnection with exponential backoff
- Message parsing errors: Log errors and continue operation
- Server unavailable: Show connection status and retry options

### 3. Error Recovery Strategies

```python
class ErrorRecoveryManager:
    async def handle_stage_failure(self, stage: str, error: Exception) -> StageResult
    async def attempt_service_recovery(self, service_name: str) -> bool
    def get_fallback_response(self, stage: str, input_data: Any) -> Any
    def should_retry(self, error: Exception, attempt_count: int) -> bool
```

## Testing Strategy

### 1. Unit Testing

**Frontend Components**:
- WebSocket message handling
- Audio recording and processing
- UI state management
- Error handling scenarios

**Backend Services**:
- Pipeline orchestration logic
- Service integration points
- Error recovery mechanisms
- Performance monitoring

### 2. Integration Testing

**End-to-End Pipeline**:
- Complete voice interaction flow
- Service failure scenarios
- Performance under load
- Cross-browser compatibility

**Service Integration**:
- ASR service with various audio formats
- LLM service with different conversation contexts
- TTS service with various text inputs
- WebSocket communication reliability

### 3. Performance Testing

**Metrics to Monitor**:
- Audio processing latency
- Service response times
- Memory usage during long sessions
- WebSocket connection stability

**Load Testing**:
- Multiple concurrent sessions
- Large audio file processing
- Extended conversation sessions
- Service failure recovery time

### 4. User Acceptance Testing

**Test Scenarios**:
- First-time user experience
- Various audio input qualities
- Different browser environments
- Mobile device compatibility

**Success Criteria**:
- Complete pipeline execution under 10 seconds
- 95% success rate for audio processing
- Clear error messages for all failure modes
- Intuitive user interface operation