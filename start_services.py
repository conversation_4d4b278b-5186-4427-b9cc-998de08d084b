#!/usr/bin/env python3
"""
启动所有必需的服务
"""

import subprocess
import time
import sys
import os
import requests
from threading import Thread

def check_port(port):
    """检查端口是否可用"""
    try:
        response = requests.get(f'http://localhost:{port}/health', timeout=2)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """启动后端服务"""
    print("🔧 启动后端ASR服务 (端口8000)...")
    os.chdir('backend')
    subprocess.run([sys.executable, 'main.py'])

def start_tts():
    """启动TTS服务"""
    print("🔊 启动TTS API服务 (端口8001)...")
    subprocess.run([sys.executable, 'start_real_voice_test.py'])

def start_web():
    """启动Web服务器"""
    print("🌐 启动Web服务器 (端口8002)...")
    subprocess.run([sys.executable, '-m', 'http.server', '8002'])

def main():
    print("🚀 语音对话系统启动器")
    print("=" * 50)
    
    # 检查当前服务状态
    print("🔍 检查服务状态...")
    backend_running = check_port(8000)
    tts_running = check_port(8001)
    web_running = check_port(8002)
    
    print(f"后端ASR服务 (8000): {'✅ 运行中' if backend_running else '❌ 未运行'}")
    print(f"TTS API服务 (8001): {'✅ 运行中' if tts_running else '❌ 未运行'}")
    print(f"Web服务器 (8002): {'✅ 运行中' if web_running else '❌ 未运行'}")
    
    if backend_running and tts_running and web_running:
        print("\n🎉 所有服务都在运行！")
        print("🌐 打开浏览器访问: http://localhost:8002/real_voice_pipeline_test.html")
        return
    
    print("\n📋 需要启动的服务:")
    if not backend_running:
        print("1. 后端ASR服务 - Enhanced ASR + LLM")
    if not tts_running:
        print("2. TTS API服务 - 语音合成")  
    if not web_running:
        print("3. Web服务器 - 前端界面")
    
    print("\n🎯 请选择启动方式:")
    print("1. 手动启动 (推荐) - 在不同终端窗口启动")
    print("2. 自动启动 - 在后台启动所有服务")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == "1":
        print("\n📋 手动启动命令:")
        print("\n终端1 - 后端ASR服务:")
        print("cd backend && python main.py")
        print("\n终端2 - TTS API服务:")
        print("python start_real_voice_test.py")
        print("\n终端3 - Web服务器:")
        print("python -m http.server 8002")
        print("\n🌐 然后打开: http://localhost:8002/real_voice_pipeline_test.html")
        
    elif choice == "2":
        print("\n🚀 自动启动所有服务...")
        
        # 在后台启动服务
        if not backend_running:
            Thread(target=start_backend, daemon=True).start()
            time.sleep(2)
        
        if not tts_running:
            Thread(target=start_tts, daemon=True).start()
            time.sleep(2)
            
        if not web_running:
            Thread(target=start_web, daemon=True).start()
            time.sleep(1)
        
        print("⏳ 等待服务启动...")
        time.sleep(5)
        
        # 检查服务状态
        backend_ok = check_port(8000)
        tts_ok = check_port(8001)
        web_ok = check_port(8002)
        
        print(f"\n📊 服务状态:")
        print(f"后端ASR: {'✅' if backend_ok else '❌'}")
        print(f"TTS API: {'✅' if tts_ok else '❌'}")
        print(f"Web服务器: {'✅' if web_ok else '❌'}")
        
        if backend_ok and tts_ok and web_ok:
            print("\n🎉 所有服务启动成功！")
            print("🌐 打开浏览器访问: http://localhost:8002/real_voice_pipeline_test.html")
        else:
            print("\n⚠️ 部分服务启动失败，请手动启动")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()