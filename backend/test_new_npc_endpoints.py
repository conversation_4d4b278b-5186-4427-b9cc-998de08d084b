#!/usr/bin/env python3
"""
测试新的NPC API端点
验证集成到main.py中的优化功能
"""

import asyncio
import requests
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NPCEndpointTester:
    """NPC端点测试器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        
    def test_get_npcs(self):
        """测试获取NPCs列表"""
        logger.info("🎭 测试获取NPCs列表...")
        
        try:
            response = requests.get(f"{self.base_url}/npcs")
            
            if response.status_code == 200:
                data = response.json()
                npcs = data.get("npcs", [])
                source = data.get("source", "unknown")
                count = data.get("count", 0)
                
                logger.info(f"✅ 获取NPCs成功: {count}个NPC，数据源: {source}")
                
                # 显示NPC信息
                for npc in npcs[:3]:  # 只显示前3个
                    logger.info(f"  - {npc.get('name')}: {npc.get('description', 'No description')}")
                
                self.test_results.append({
                    "test": "get_npcs",
                    "status": "success",
                    "details": {
                        "npc_count": count,
                        "data_source": source,
                        "first_npc": npcs[0] if npcs else None
                    }
                })
                return npcs[0] if npcs else None
            else:
                logger.error(f"❌ 获取NPCs失败: {response.status_code}")
                self.test_results.append({
                    "test": "get_npcs",
                    "status": "failed",
                    "error": f"HTTP {response.status_code}"
                })
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取NPCs异常: {e}")
            self.test_results.append({
                "test": "get_npcs",
                "status": "failed",
                "error": str(e)
            })
            return None
    
    def test_get_npc_detail(self, npc_id):
        """测试获取NPC详情"""
        logger.info(f"🔍 测试获取NPC详情: {npc_id}")
        
        try:
            response = requests.get(f"{self.base_url}/npcs/{npc_id}")
            
            if response.status_code == 200:
                data = response.json()
                npc = data.get("npc", {})
                
                logger.info(f"✅ 获取NPC详情成功: {npc.get('name')}")
                logger.info(f"  描述: {npc.get('description', 'No description')}")
                logger.info(f"  系统提示词长度: {len(npc.get('system_prompt', ''))} 字符")
                logger.info(f"  有persona数据: {bool(npc.get('persona_data'))}")
                
                self.test_results.append({
                    "test": "get_npc_detail",
                    "status": "success",
                    "details": {
                        "npc_id": npc_id,
                        "npc_name": npc.get("name"),
                        "has_description": bool(npc.get("description")),
                        "has_system_prompt": bool(npc.get("system_prompt")),
                        "has_persona_data": bool(npc.get("persona_data"))
                    }
                })
                return True
            else:
                logger.error(f"❌ 获取NPC详情失败: {response.status_code}")
                self.test_results.append({
                    "test": "get_npc_detail",
                    "status": "failed",
                    "error": f"HTTP {response.status_code}"
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ 获取NPC详情异常: {e}")
            self.test_results.append({
                "test": "get_npc_detail",
                "status": "failed",
                "error": str(e)
            })
            return False
    
    def test_get_npc_persona(self, npc_id):
        """测试获取NPC persona数据"""
        logger.info(f"👤 测试获取NPC persona: {npc_id}")
        
        try:
            response = requests.get(f"{self.base_url}/npcs/{npc_id}/persona")
            
            if response.status_code == 200:
                data = response.json()
                persona = data.get("persona", {})
                
                logger.info(f"✅ 获取NPC persona成功: {data.get('npc_name')}")
                logger.info(f"  年龄: {persona.get('age')}")
                logger.info(f"  性格: {persona.get('personality')}")
                logger.info(f"  兴趣: {', '.join(persona.get('hobbies', [])[:3])}")
                logger.info(f"  生成描述: {data.get('generated_description', 'No description')}")
                
                self.test_results.append({
                    "test": "get_npc_persona",
                    "status": "success",
                    "details": {
                        "npc_id": npc_id,
                        "npc_name": data.get("npc_name"),
                        "persona_fields": list(persona.keys()),
                        "system_prompt_length": data.get("system_prompt_length", 0)
                    }
                })
                return True
            elif response.status_code == 404:
                logger.warning(f"⚠️ NPC persona不存在: {npc_id}")
                self.test_results.append({
                    "test": "get_npc_persona",
                    "status": "warning",
                    "message": "NPC persona not found"
                })
                return False
            else:
                logger.error(f"❌ 获取NPC persona失败: {response.status_code}")
                self.test_results.append({
                    "test": "get_npc_persona",
                    "status": "failed",
                    "error": f"HTTP {response.status_code}"
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ 获取NPC persona异常: {e}")
            self.test_results.append({
                "test": "get_npc_persona",
                "status": "failed",
                "error": str(e)
            })
            return False
    
    def test_create_chat_session(self, npc_id, user_id=14):
        """测试创建聊天会话"""
        logger.info(f"💬 测试创建聊天会话: NPC={npc_id}, User={user_id}")
        
        try:
            response = requests.post(f"{self.base_url}/npcs/{npc_id}/chat", params={"user_id": user_id})
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get("session_id")
                
                logger.info(f"✅ 创建聊天会话成功: {session_id}")
                logger.info(f"  NPC: {data.get('npc_name')}")
                logger.info(f"  用户ID: {data.get('user_id')}")
                
                self.test_results.append({
                    "test": "create_chat_session",
                    "status": "success",
                    "details": {
                        "session_id": session_id,
                        "npc_id": npc_id,
                        "user_id": user_id,
                        "npc_name": data.get("npc_name")
                    }
                })
                return session_id
            else:
                logger.error(f"❌ 创建聊天会话失败: {response.status_code}")
                logger.error(f"   响应: {response.text}")
                self.test_results.append({
                    "test": "create_chat_session",
                    "status": "failed",
                    "error": f"HTTP {response.status_code}: {response.text}"
                })
                return None
                
        except Exception as e:
            logger.error(f"❌ 创建聊天会话异常: {e}")
            self.test_results.append({
                "test": "create_chat_session",
                "status": "failed",
                "error": str(e)
            })
            return None
    
    def test_add_message(self, session_id, role="user", content="你好，请介绍一下自己"):
        """测试添加消息"""
        logger.info(f"📝 测试添加消息到会话: {session_id}")
        
        try:
            params = {
                "role": role,
                "content": content,
                "emotion": "neutral",
                "speed": 1.0
            }
            
            response = requests.post(f"{self.base_url}/sessions/{session_id}/messages", params=params)
            
            if response.status_code == 200:
                data = response.json()
                
                logger.info(f"✅ 添加消息成功")
                logger.info(f"  角色: {data.get('role')}")
                logger.info(f"  内容: {data.get('content')}")
                
                self.test_results.append({
                    "test": "add_message",
                    "status": "success",
                    "details": {
                        "session_id": session_id,
                        "role": role,
                        "content": content
                    }
                })
                return True
            else:
                logger.error(f"❌ 添加消息失败: {response.status_code}")
                logger.error(f"   响应: {response.text}")
                self.test_results.append({
                    "test": "add_message",
                    "status": "failed",
                    "error": f"HTTP {response.status_code}: {response.text}"
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ 添加消息异常: {e}")
            self.test_results.append({
                "test": "add_message",
                "status": "failed",
                "error": str(e)
            })
            return False
    
    def test_get_session_context(self, session_id):
        """测试获取会话上下文"""
        logger.info(f"🔍 测试获取会话上下文: {session_id}")
        
        try:
            response = requests.get(f"{self.base_url}/sessions/{session_id}/context")
            
            if response.status_code == 200:
                data = response.json()
                messages = data.get("messages", [])
                npc = data.get("npc", {})
                
                logger.info(f"✅ 获取会话上下文成功")
                logger.info(f"  NPC: {npc.get('name', 'Unknown')}")
                logger.info(f"  消息数: {data.get('message_count', 0)}")
                
                # 显示最近的消息
                for msg in messages[-3:]:  # 显示最后3条消息
                    role = msg.get('role', 'unknown')
                    content = msg.get('content', '')
                    content_preview = content[:50] + "..." if len(content) > 50 else content
                    logger.info(f"    - {role}: {content_preview}")
                
                self.test_results.append({
                    "test": "get_session_context",
                    "status": "success",
                    "details": {
                        "session_id": session_id,
                        "message_count": data.get("message_count", 0),
                        "npc_name": npc.get("name"),
                        "has_npc_info": data.get("has_npc_info", False)
                    }
                })
                return True
            else:
                logger.error(f"❌ 获取会话上下文失败: {response.status_code}")
                self.test_results.append({
                    "test": "get_session_context",
                    "status": "failed",
                    "error": f"HTTP {response.status_code}"
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ 获取会话上下文异常: {e}")
            self.test_results.append({
                "test": "get_session_context",
                "status": "failed",
                "error": str(e)
            })
            return False
    
    def test_cache_status(self):
        """测试缓存状态"""
        logger.info("🗄️ 测试NPC缓存状态...")
        
        try:
            response = requests.get(f"{self.base_url}/npcs/cache/status")
            
            if response.status_code == 200:
                data = response.json()
                cache_size = data.get("cache_size", 0)
                cached_npcs = data.get("cached_npcs", {})
                
                logger.info(f"✅ 获取缓存状态成功")
                logger.info(f"  缓存大小: {cache_size}")
                logger.info(f"  TTL: {data.get('cache_ttl_seconds', 0)} 秒")
                
                for npc_id, cache_info in list(cached_npcs.items())[:3]:  # 显示前3个
                    logger.info(f"    - {cache_info.get('npc_name')}: 缓存{cache_info.get('cache_age_seconds', 0):.1f}秒")
                
                self.test_results.append({
                    "test": "cache_status",
                    "status": "success",
                    "details": {
                        "cache_size": cache_size,
                        "cache_ttl": data.get("cache_ttl_seconds", 0)
                    }
                })
                return True
            else:
                logger.error(f"❌ 获取缓存状态失败: {response.status_code}")
                self.test_results.append({
                    "test": "cache_status",
                    "status": "failed",
                    "error": f"HTTP {response.status_code}"
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ 获取缓存状态异常: {e}")
            self.test_results.append({
                "test": "cache_status",
                "status": "failed",
                "error": str(e)
            })
            return False
    
    def run_all_tests(self):
        """运行所有端点测试"""
        logger.info("🚀 开始NPC端点测试...")
        
        # 1. 获取NPCs列表
        first_npc = self.test_get_npcs()
        if not first_npc:
            logger.error("❌ 无法获取NPC列表，停止测试")
            return False
        
        npc_id = first_npc.get("id")
        logger.info(f"📋 使用NPC进行测试: {npc_id}")
        
        # 2. 获取NPC详情
        self.test_get_npc_detail(npc_id)
        
        # 3. 获取NPC persona
        self.test_get_npc_persona(npc_id)
        
        # 4. 创建聊天会话
        session_id = self.test_create_chat_session(npc_id)
        
        if session_id:
            # 5. 添加消息
            self.test_add_message(session_id)
            
            # 6. 获取会话上下文
            self.test_get_session_context(session_id)
        
        # 7. 检查缓存状态
        self.test_cache_status()
        
        # 生成测试报告
        passed = len([r for r in self.test_results if r.get("status") == "success"])
        failed = len([r for r in self.test_results if r.get("status") == "failed"])
        warnings = len([r for r in self.test_results if r.get("status") == "warning"])
        
        logger.info(f"\n{'='*60}")
        logger.info("📊 端点测试总结")
        logger.info(f"{'='*60}")
        logger.info(f"总测试数: {len(self.test_results)}")
        logger.info(f"通过: {passed}")
        logger.info(f"警告: {warnings}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {(passed/len(self.test_results)*100):.1f}%")
        
        # 保存测试结果
        report = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "endpoint_testing",
            "base_url": self.base_url,
            "summary": {
                "total_tests": len(self.test_results),
                "passed": passed,
                "warnings": warnings,
                "failed": failed,
                "success_rate": f"{(passed/len(self.test_results)*100):.1f}%"
            },
            "test_results": self.test_results
        }
        
        with open("npc_endpoint_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("📄 端点测试报告已保存到 npc_endpoint_test_report.json")
        
        return failed == 0

def main():
    """主函数"""
    tester = NPCEndpointTester()
    
    logger.info("⚠️ 请确保后端服务正在运行 (python main.py)")
    logger.info("🔗 测试URL: http://localhost:8000")
    
    success = tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有端点测试通过！")
        return 0
    else:
        logger.error("💥 部分端点测试失败，请检查报告")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)