#!/usr/bin/env python3
"""
数据库表初始化脚本 - 创建0802前缀的表
"""
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def init_tables():
    """初始化数据库表"""
    print("🔧 初始化数据库表...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    # 创建以0802为前缀的表
    try:
        print("\n📝 创建0802前缀的表...")
        
        # 创建0802_users表
        supabase.rpc("execute_sql", {
            "sql": """
            CREATE TABLE IF NOT EXISTS "0802_users" (
                id SERIAL PRIMARY KEY,
                uuid UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
                username VARCHAR(50) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE,
                nickname VARCHAR(100),
                avatar_url TEXT,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            """
        }).execute()
        print("✅ 创建 0802_users 表")
        
        # 创建0802_npcs表
        supabase.rpc("execute_sql", {
            "sql": """
            CREATE TABLE IF NOT EXISTS "0802_npcs" (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                system_prompt TEXT NOT NULL,
                avatar_url TEXT,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            """
        }).execute()
        print("✅ 创建 0802_npcs 表")
        
        # 创建0802_conversation_sessions表
        supabase.rpc("execute_sql", {
            "sql": """
            CREATE TABLE IF NOT EXISTS "0802_conversation_sessions" (
                id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                user_id INTEGER REFERENCES "0802_users"(id) ON DELETE CASCADE,
                npc_id INTEGER REFERENCES "0802_npcs"(id) ON DELETE CASCADE,
                started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                ended_at TIMESTAMP WITH TIME ZONE,
                is_active BOOLEAN DEFAULT true
            );
            """
        }).execute()
        print("✅ 创建 0802_conversation_sessions 表")
        
        # 创建0802_conversation_messages表
        supabase.rpc("execute_sql", {
            "sql": """
            CREATE TABLE IF NOT EXISTS "0802_conversation_messages" (
                id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                session_id UUID REFERENCES "0802_conversation_sessions"(id) ON DELETE CASCADE,
                role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'developer', 'tool')),
                content TEXT NOT NULL,
                audio_url TEXT,
                emotion VARCHAR(50),
                speed DECIMAL(3,1),
                tool_calls JSONB,
                tool_call_id VARCHAR(255),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            """
        }).execute()
        print("✅ 创建 0802_conversation_messages 表")
        
        # 创建索引
        supabase.rpc("execute_sql", {
            "sql": """
            CREATE INDEX IF NOT EXISTS idx_0802_users_username ON "0802_users"(username);
            CREATE INDEX IF NOT EXISTS idx_0802_users_email ON "0802_users"(email);
            CREATE INDEX IF NOT EXISTS idx_0802_conversation_sessions_user_id ON "0802_conversation_sessions"(user_id);
            CREATE INDEX IF NOT EXISTS idx_0802_conversation_messages_session_id ON "0802_conversation_messages"(session_id);
            CREATE INDEX IF NOT EXISTS idx_0802_conversation_messages_created_at ON "0802_conversation_messages"(created_at);
            CREATE INDEX IF NOT EXISTS idx_0802_conversation_messages_role ON "0802_conversation_messages"(role);
            """
        }).execute()
        print("✅ 创建索引")
        
        # 插入默认NPC数据
        try:
            result = supabase.table("0802_npcs").select("*").execute()
            if not result.data:
                default_npcs = [
                    {
                        "name": "默认助手",
                        "description": "通用AI助手",
                        "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                        "is_active": True
                    },
                    {
                        "name": "朋友",
                        "description": "亲密朋友角色",
                        "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                        "is_active": True
                    }
                ]
                
                for npc in default_npcs:
                    supabase.table("0802_npcs").insert(npc).execute()
                    print(f"✅ 插入默认NPC: {npc['name']}")
            else:
                print("✅ 默认NPC数据已存在")
        except Exception as e:
            print(f"⚠️ 插入默认NPC数据时出错: {e}")
        
        print("\n🎉 数据库表初始化完成！")
        return True
        
    except Exception as e:
        print(f"❌ 创建表时出错: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始数据库表初始化...")
    
    if init_tables():
        print("\n✅ 所有表创建成功！")
        sys.exit(0)
    else:
        print("\n❌ 表初始化失败")
        sys.exit(1)
