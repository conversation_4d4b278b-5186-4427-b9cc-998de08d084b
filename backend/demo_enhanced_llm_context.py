#!/usr/bin/env python3
"""
增强LLM服务上下文功能演示脚本
展示LLM的记忆和上下文感知能力
"""

import os
import sys
import logging
import asyncio
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.enhanced_llm_service import EnhancedLLMService
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedLLMContextDemo:
    """增强LLM上下文功能演示"""
    
    def __init__(self):
        """初始化演示"""
        logger.info("🎯 初始化增强LLM上下文功能演示...")
        
        # 初始化增强LLM服务
        self.llm_service = EnhancedLLMService(
            api_key=os.getenv("VOLCANO_API_KEY", "demo_key"),
            endpoint=os.getenv("VOLCANO_ENDPOINT", "demo_endpoint"),
            enable_context=True
        )
        
        # 演示场景
        self.demo_scenarios = [
            {
                "name": "个人信息记忆场景",
                "description": "测试LLM对用户个人信息的记忆能力",
                "system_prompt": "你是一个智能个人助手，请记住用户的个人信息并在后续对话中使用。",
                "conversations": [
                    "我叫王小明，今年25岁，是一名软件工程师",
                    "我住在北京，喜欢看电影和读书",
                    "你还记得我的名字吗？",
                    "我的职业是什么？",
                    "根据我的兴趣，推荐一些活动"
                ]
            },
            {
                "name": "学习辅导场景",
                "description": "测试LLM在教育辅导中的上下文连贯性",
                "system_prompt": "你是一个编程学习导师，请根据学生的学习进度提供个性化指导。",
                "conversations": [
                    "我想学习Python编程",
                    "我是完全的初学者",
                    "请从基础语法开始教我",
                    "变量和数据类型我已经理解了",
                    "接下来应该学什么？"
                ]
            },
            {
                "name": "项目规划场景",
                "description": "测试LLM在复杂项目讨论中的记忆和推理能力",
                "system_prompt": "你是一个项目管理专家，帮助用户规划和管理项目。",
                "conversations": [
                    "我要开发一个在线购物网站",
                    "预算大概10万元，时间3个月",
                    "团队有5个人：2个前端，2个后端，1个设计师",
                    "主要功能包括商品展示、购物车、支付",
                    "基于之前的信息，制定详细的开发计划"
                ]
            }
        ]
    
    async def demonstrate_scenario(self, scenario: dict) -> dict:
        """演示单个场景"""
        logger.info(f"\n{'='*60}")
        logger.info(f"🎬 演示场景: {scenario['name']}")
        logger.info(f"📝 场景描述: {scenario['description']}")
        logger.info(f"{'='*60}")
        
        session_id = f"demo_{scenario['name'].replace(' ', '_').lower()}"
        system_prompt = scenario['system_prompt']
        results = []
        
        for i, user_input in enumerate(scenario['conversations']):
            logger.info(f"\n--- 第 {i+1} 轮对话 ---")
            logger.info(f"👤 用户: {user_input}")
            
            # 调用增强LLM服务
            start_time = time.time()
            try:
                response = await self.llm_service.generate_response_with_tools(
                    user_input=user_input,
                    conversation_history=[],  # 使用内部记忆而不是外部历史
                    system_prompt=system_prompt,
                    session_id=session_id
                )
                llm_time = time.time() - start_time
                
                # 记录响应结果
                if response.get('success'):
                    assistant_text = response['speak_content']['text']
                    context_used = response.get('context_used', False)
                    tools_used = response.get('tools_used', [])
                    
                    logger.info(f"🤖 助手回复: {assistant_text}")
                    logger.info(f"🧠 使用上下文: {'是' if context_used else '否'}")
                    logger.info(f"🔧 使用工具: {', '.join(tools_used) if tools_used else '无'}")
                    logger.info(f"⏱️ 响应耗时: {llm_time:.2f}秒")
                    
                    # 记录结果
                    turn_result = {
                        "turn": i + 1,
                        "user_input": user_input,
                        "assistant_response": assistant_text,
                        "context_used": context_used,
                        "tools_used": tools_used,
                        "response_time": llm_time,
                        "success": True
                    }
                else:
                    logger.warning(f"⚠️ LLM响应失败: {response.get('error', 'Unknown error')}")
                    turn_result = {
                        "turn": i + 1,
                        "user_input": user_input,
                        "assistant_response": "响应失败",
                        "context_used": False,
                        "tools_used": [],
                        "response_time": llm_time,
                        "success": False
                    }
                
            except Exception as e:
                logger.error(f"❌ 调用LLM服务失败: {e}")
                turn_result = {
                    "turn": i + 1,
                    "user_input": user_input,
                    "assistant_response": f"服务异常: {str(e)}",
                    "context_used": False,
                    "tools_used": [],
                    "response_time": 0,
                    "success": False
                }
            
            results.append(turn_result)
            
            # 短暂延迟模拟真实对话节奏
            await asyncio.sleep(0.5)
        
        # 获取最终记忆统计
        memory_stats = self.llm_service.get_memory_stats()
        session_stats = memory_stats['sessions'].get(session_id, {})
        
        scenario_summary = {
            "scenario_name": scenario['name'],
            "total_turns": len(results),
            "successful_turns": sum(1 for r in results if r['success']),
            "context_usage_rate": sum(1 for r in results[1:] if r['context_used']) / max(1, len(results) - 1),
            "tools_usage_rate": sum(1 for r in results if r['tools_used']) / len(results),
            "average_response_time": sum(r['response_time'] for r in results) / len(results),
            "final_memory_turns": session_stats.get('turns_count', 0),
            "results": results
        }
        
        logger.info(f"\n📊 场景总结:")
        logger.info(f"总轮次: {scenario_summary['total_turns']}")
        logger.info(f"成功轮次: {scenario_summary['successful_turns']}")
        logger.info(f"上下文使用率: {scenario_summary['context_usage_rate']:.1%}")
        logger.info(f"工具使用率: {scenario_summary['tools_usage_rate']:.1%}")
        logger.info(f"平均响应时间: {scenario_summary['average_response_time']:.2f}秒")
        logger.info(f"最终记忆轮次: {scenario_summary['final_memory_turns']}")
        
        return scenario_summary
    
    async def demonstrate_memory_persistence(self):
        """演示记忆持久性"""
        logger.info(f"\n{'='*60}")
        logger.info("🧠 演示记忆持久性")
        logger.info(f"{'='*60}")
        
        session_id = "memory_persistence_demo"
        system_prompt = "你是一个有记忆的智能助手。"
        
        # 第一阶段：建立记忆
        logger.info("\n--- 第一阶段：建立记忆 ---")
        phase1_inputs = [
            "我的名字是李华，我是一名医生",
            "我在北京协和医院工作",
            "我专门研究心脏病"
        ]
        
        for i, user_input in enumerate(phase1_inputs, 1):
            logger.info(f"👤 用户 (第{i}轮): {user_input}")
            response = await self.llm_service.generate_response_with_tools(
                user_input, [], system_prompt, session_id
            )
            if response.get('success'):
                logger.info(f"🤖 助手: {response['speak_content']['text']}")
        
        # 等待一段时间模拟会话间隔
        logger.info("\n⏳ 模拟会话间隔...")
        await asyncio.sleep(2)
        
        # 第二阶段：测试记忆
        logger.info("\n--- 第二阶段：测试记忆 ---")
        phase2_inputs = [
            "你还记得我的职业吗？",
            "我在哪家医院工作？",
            "我的专业领域是什么？"
        ]
        
        memory_test_results = []
        for i, user_input in enumerate(phase2_inputs, 1):
            logger.info(f"👤 用户 (测试{i}): {user_input}")
            response = await self.llm_service.generate_response_with_tools(
                user_input, [], system_prompt, session_id
            )
            if response.get('success'):
                assistant_text = response['speak_content']['text']
                context_used = response.get('context_used', False)
                logger.info(f"🤖 助手: {assistant_text}")
                logger.info(f"🧠 使用上下文: {'是' if context_used else '否'}")
                
                memory_test_results.append({
                    "question": user_input,
                    "answer": assistant_text,
                    "context_used": context_used
                })
        
        # 分析记忆测试结果
        context_usage = sum(1 for r in memory_test_results if r['context_used'])
        logger.info(f"\n📊 记忆测试结果:")
        logger.info(f"测试问题数: {len(memory_test_results)}")
        logger.info(f"使用上下文次数: {context_usage}")
        logger.info(f"记忆准确率: {context_usage / len(memory_test_results):.1%}")
        
        return memory_test_results
    
    async def run_demo(self):
        """运行完整演示"""
        logger.info("🚀 开始增强LLM上下文功能演示...")
        
        # 检查环境变量
        if not os.getenv("VOLCANO_API_KEY"):
            logger.warning("⚠️ VOLCANO_API_KEY未设置，将使用模拟模式")
        
        # 显示初始状态
        initial_stats = self.llm_service.get_memory_stats()
        logger.info(f"📊 初始状态: {json.dumps(initial_stats, ensure_ascii=False, indent=2)}")
        
        # 运行所有演示场景
        all_results = []
        for scenario in self.demo_scenarios:
            try:
                result = await self.demonstrate_scenario(scenario)
                all_results.append(result)
            except Exception as e:
                logger.error(f"❌ 场景 '{scenario['name']}' 演示失败: {e}")
        
        # 演示记忆持久性
        try:
            memory_results = await self.demonstrate_memory_persistence()
        except Exception as e:
            logger.error(f"❌ 记忆持久性演示失败: {e}")
            memory_results = []
        
        # 显示最终统计
        final_stats = self.llm_service.get_memory_stats()
        logger.info(f"\n{'='*60}")
        logger.info(f"🎯 演示完成 - 最终统计")
        logger.info(f"{'='*60}")
        logger.info(f"📊 最终状态: {json.dumps(final_stats, ensure_ascii=False, indent=2)}")
        
        # 生成演示报告
        demo_report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_scenarios": len(self.demo_scenarios),
            "successful_scenarios": len(all_results),
            "initial_stats": initial_stats,
            "final_stats": final_stats,
            "scenario_results": all_results,
            "memory_test_results": memory_results
        }
        
        # 保存演示报告
        report_file = f"enhanced_llm_context_demo_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(demo_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 演示报告已保存: {report_file}")
        
        # 显示总结
        if all_results:
            avg_context_usage = sum(r['context_usage_rate'] for r in all_results) / len(all_results)
            avg_success_rate = sum(r['successful_turns'] / r['total_turns'] for r in all_results) / len(all_results)
            total_turns = sum(r['total_turns'] for r in all_results)
            
            logger.info(f"\n🎉 演示总结:")
            logger.info(f"✅ 成功场景: {len(all_results)}/{len(self.demo_scenarios)}")
            logger.info(f"📊 总对话轮次: {total_turns}")
            logger.info(f"🧠 平均上下文使用率: {avg_context_usage:.1%}")
            logger.info(f"✅ 平均成功率: {avg_success_rate:.1%}")
            logger.info(f"💾 最终会话数: {final_stats['total_sessions']}")
        
        return demo_report


async def main():
    """主函数"""
    logger.info("🎯 增强LLM服务上下文功能演示程序")
    
    try:
        demo = EnhancedLLMContextDemo()
        report = await demo.run_demo()
        
        logger.info("🎉 演示完成！")
        return 0
        
    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断演示")
        return 1
    except Exception as e:
        logger.error(f"❌ 演示失败: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
