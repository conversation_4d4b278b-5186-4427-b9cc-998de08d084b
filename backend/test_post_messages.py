#!/usr/bin/env python3
"""
专门测试 POST /sessions/{id}/messages 端点
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def test_post_messages_endpoint():
    """专门测试POST消息端点"""
    
    logger.info("🎯 专门测试 POST /sessions/{id}/messages 端点")
    logger.info("="*60)
    
    # 1. 准备测试环境
    logger.info("1. 准备测试环境...")
    
    # 获取NPC
    response = requests.get(f"{BASE_URL}/npcs")
    if response.status_code != 200:
        logger.error(f"获取NPCs失败: {response.status_code}")
        return False
    
    npcs = response.json().get("npcs", [])
    if not npcs:
        logger.error("没有可用的NPCs")
        return False
    
    npc_id = npcs[0]["id"]
    npc_name = npcs[0]["name"]
    
    # 创建会话
    response = requests.post(f"{BASE_URL}/npcs/{npc_id}/chat", params={"user_id": 14})
    if response.status_code != 200:
        logger.error(f"创建会话失败: {response.status_code}")
        return False
    
    session_id = response.json()["session_id"]
    logger.info(f"✅ 测试环境准备完成 - 会话ID: {session_id}")
    
    # 2. 测试不同的消息类型
    logger.info("\n2. 测试不同类型的消息...")
    
    test_cases = [
        {
            "name": "基本用户消息",
            "params": {
                "role": "user",
                "content": "你好，我是新用户",
                "emotion": "neutral",
                "speed": 1.0
            }
        },
        {
            "name": "助手回复消息",
            "params": {
                "role": "assistant", 
                "content": "你好！欢迎使用我们的服务！",
                "emotion": "friendly",
                "speed": 1.1
            }
        },
        {
            "name": "带特殊字符的消息",
            "params": {
                "role": "user",
                "content": "这是一条包含特殊字符的消息：@#$%^&*()，还有中文！",
                "emotion": "excited",
                "speed": 0.9
            }
        },
        {
            "name": "长消息测试",
            "params": {
                "role": "assistant",
                "content": "这是一条很长的消息，用来测试系统对长文本的处理能力。" * 10,
                "emotion": "calm",
                "speed": 1.2
            }
        },
        {
            "name": "空内容测试",
            "params": {
                "role": "user",
                "content": "",
                "emotion": "neutral",
                "speed": 1.0
            }
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n2.{i} 测试: {test_case['name']}")
        logger.info(f"     参数: {test_case['params']}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/sessions/{session_id}/messages",
                params=test_case['params']
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"     ✅ 成功")
                logger.info(f"        返回: {result}")
                success_count += 1
            else:
                logger.error(f"     ❌ 失败: {response.status_code}")
                logger.error(f"        错误: {response.text}")
                
        except Exception as e:
            logger.error(f"     ❌ 异常: {e}")
    
    # 3. 测试参数验证
    logger.info(f"\n3. 测试参数验证...")
    
    validation_tests = [
        {
            "name": "缺少role参数",
            "params": {
                "content": "测试消息",
                "emotion": "neutral",
                "speed": 1.0
            },
            "expect_error": True
        },
        {
            "name": "缺少content参数", 
            "params": {
                "role": "user",
                "emotion": "neutral",
                "speed": 1.0
            },
            "expect_error": True
        },
        {
            "name": "无效的speed值",
            "params": {
                "role": "user",
                "content": "测试消息",
                "emotion": "neutral",
                "speed": "invalid"
            },
            "expect_error": True
        },
        {
            "name": "只有必需参数",
            "params": {
                "role": "user",
                "content": "最简消息"
            },
            "expect_error": False
        }
    ]
    
    for i, test_case in enumerate(validation_tests, 1):
        logger.info(f"\n3.{i} 验证测试: {test_case['name']}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/sessions/{session_id}/messages",
                params=test_case['params']
            )
            
            if test_case['expect_error']:
                if response.status_code != 200:
                    logger.info(f"     ✅ 正确拒绝无效请求: {response.status_code}")
                else:
                    logger.warning(f"     ⚠️ 应该拒绝但接受了: {response.status_code}")
            else:
                if response.status_code == 200:
                    logger.info(f"     ✅ 正确接受有效请求")
                    success_count += 1
                else:
                    logger.error(f"     ❌ 应该接受但拒绝了: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"     ❌ 异常: {e}")
    
    # 4. 测试不存在的会话
    logger.info(f"\n4. 测试不存在的会话...")
    
    fake_session_id = "999999"
    response = requests.post(
        f"{BASE_URL}/sessions/{fake_session_id}/messages",
        params={
            "role": "user",
            "content": "测试消息",
            "emotion": "neutral", 
            "speed": 1.0
        }
    )
    
    if response.status_code == 404:
        logger.info("     ✅ 正确处理不存在的会话 (404)")
    else:
        logger.warning(f"     ⚠️ 错误处理可能有问题: {response.status_code}")
    
    # 5. 验证消息是否正确保存
    logger.info(f"\n5. 验证消息保存...")
    
    response = requests.get(f"{BASE_URL}/sessions/{session_id}/context")
    if response.status_code == 200:
        context = response.json()
        message_count = context['message_count']
        logger.info(f"     ✅ 会话中共有 {message_count} 条消息")
        
        # 显示最后几条消息
        messages = context['messages'][-3:]  # 显示最后3条
        for i, msg in enumerate(messages, 1):
            logger.info(f"        {i}. [{msg.get('role')}] {msg.get('content', '')[:30]}...")
    else:
        logger.error(f"     ❌ 无法验证消息保存: {response.status_code}")
    
    # 6. 性能测试
    logger.info(f"\n6. 简单性能测试...")
    
    import time
    start_time = time.time()
    
    for i in range(5):
        response = requests.post(
            f"{BASE_URL}/sessions/{session_id}/messages",
            params={
                "role": "user",
                "content": f"性能测试消息 {i+1}",
                "emotion": "neutral",
                "speed": 1.0
            }
        )
        if response.status_code == 200:
            success_count += 1
    
    end_time = time.time()
    avg_time = (end_time - start_time) / 5
    
    logger.info(f"     ✅ 5条消息平均响应时间: {avg_time:.3f}秒")
    
    # 总结
    logger.info(f"\n" + "="*60)
    logger.info("📊 测试总结")
    logger.info(f"="*60)
    logger.info(f"成功的请求数: {success_count}")
    logger.info(f"平均响应时间: {avg_time:.3f}秒")
    
    if success_count >= 8:  # 至少8个成功请求
        logger.info("🎉 POST /sessions/{id}/messages 端点测试通过！")
        logger.info("✅ 功能正常")
        logger.info("✅ 参数验证正确")
        logger.info("✅ 错误处理正确")
        logger.info("✅ 数据保存正确")
        logger.info("✅ 性能表现良好")
        return True
    else:
        logger.error("💥 测试未完全通过，请检查问题")
        return False

if __name__ == "__main__":
    try:
        success = test_post_messages_endpoint()
        if success:
            logger.info("\n🎊 POST消息端点完全正常！")
        else:
            logger.error("\n💥 发现问题需要修复")
    except Exception as e:
        logger.error(f"\n❌ 测试过程异常: {e}")