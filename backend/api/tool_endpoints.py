"""
工具相关的API端点
"""
from fastapi import APIRouter, HTTPException, Query, Body
from typing import Dict, Any, List, Optional
import logging
from pydantic import BaseModel

from services.tool_manager_service import tool_manager_service
from services.enhanced_llm_service import get_enhanced_llm_service
from utils.reranker_service import reranker_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/tools", tags=["tools"])

# Pydantic模型
class ToolQuery(BaseModel):
    query: str
    top_k: int = 5

class ToolExecutionRequest(BaseModel):
    tool_name: str
    parameters: Dict[str, Any]
    server_name: Optional[str] = None

class EnhancedChatRequest(BaseModel):
    user_input: str
    conversation_history: List[Dict[str, str]] = []
    system_prompt: str = "你是一个友善的AI助手。"
    use_tools: bool = True

@router.get("/enumerate")
async def enumerate_tools(
    use_cache: bool = Query(True, description="是否使用缓存"),
    category: Optional[str] = Query(None, description="按类别过滤")
):
    """
    枚举所有可用工具

    Args:
        use_cache: 是否使用缓存
        category: 可选的类别过滤

    Returns:
        工具列表
    """
    try:
        logger.info(f"枚举工具请求 - use_cache: {use_cache}, category: {category}")

        if category:
            # 对于类别过滤，先获取所有工具再过滤
            all_tools = await tool_manager_service.enumerate_all_tools_async(use_cache=use_cache)
            tools = [
                tool for tool in all_tools
                if tool.get('category', '').lower() == category.lower()
            ]
        else:
            tools = await tool_manager_service.enumerate_all_tools_async(use_cache=use_cache)

        return {
            "success": True,
            "tools": tools,
            "total": len(tools),
            "filtered_by_category": category is not None
        }

    except Exception as e:
        logger.error(f"枚举工具时出错: {e}")
        raise HTTPException(status_code=500, detail=f"枚举工具失败: {str(e)}")

@router.get("/tool/{tool_name}")
async def get_tool_detail(tool_name: str):
    """
    获取特定工具的详细信息
    
    Args:
        tool_name: 工具名称
        
    Returns:
        工具详细信息
    """
    try:
        logger.info(f"获取工具详情: {tool_name}")
        
        tool = tool_manager_service.get_tool_by_name(tool_name)
        
        if not tool:
            raise HTTPException(status_code=404, detail=f"工具 '{tool_name}' 不存在")
        
        return {
            "success": True,
            "tool": tool
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工具详情时出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取工具详情失败: {str(e)}")

@router.post("/rank")
async def rank_tools_by_relevance(request: ToolQuery):
    """
    根据用户查询对工具进行相关性排序
    
    Args:
        request: 包含查询和top_k的请求
        
    Returns:
        排序后的工具列表
    """
    try:
        logger.info(f"工具相关性排序请求: {request.query}, top_k: {request.top_k}")
        
        ranked_tools = await tool_manager_service.rank_tools_by_relevance_async(
            request.query,
            top_k=request.top_k
        )
        
        return {
            "success": True,
            "query": request.query,
            "ranked_tools": ranked_tools,
            "total": len(ranked_tools)
        }
        
    except Exception as e:
        logger.error(f"工具排序时出错: {e}")
        raise HTTPException(status_code=500, detail=f"工具排序失败: {str(e)}")

@router.get("/top-relevant")
async def get_top_relevant_tool(query: str = Query(..., description="用户查询")):
    """
    获取与查询最相关的单个工具
    
    Args:
        query: 用户查询
        
    Returns:
        最相关的工具
    """
    try:
        logger.info(f"获取最相关工具: {query}")
        
        top_tool = tool_manager_service.get_top_relevant_tool(query)
        
        if not top_tool:
            return {
                "success": True,
                "query": query,
                "top_tool": None,
                "message": "没有找到相关工具"
            }
        
        return {
            "success": True,
            "query": query,
            "top_tool": top_tool
        }
        
    except Exception as e:
        logger.error(f"获取最相关工具时出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取最相关工具失败: {str(e)}")

@router.post("/execute")
async def execute_tool(request: ToolExecutionRequest):
    """
    执行指定的工具
    
    Args:
        request: 工具执行请求
        
    Returns:
        工具执行结果
    """
    try:
        logger.info(f"执行工具: {request.tool_name}, 参数: {request.parameters}")
        
        # 验证工具是否存在
        tool_info = await tool_manager_service.get_tool_by_name_async(request.tool_name)
        if not tool_info:
            raise HTTPException(status_code=404, detail=f"工具 '{request.tool_name}' 不存在")
        
        # 执行工具
        from services.mcp_service import mcp_service

        server_name = request.server_name or tool_info.get('server', 'builtin')
        result = await mcp_service.execute_tool_async(
            request.tool_name,
            server_name,
            **request.parameters
        )
        
        return {
            "success": True,
            "tool_name": request.tool_name,
            "server_name": server_name,
            "parameters": request.parameters,
            "result": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行工具时出错: {e}")
        raise HTTPException(status_code=500, detail=f"工具执行失败: {str(e)}")

@router.post("/vector-search")
async def vector_search_tools(request: ToolQuery):
    """
    使用向量检索搜索相关工具（未来功能）
    
    Args:
        request: 搜索请求
        
    Returns:
        搜索结果
    """
    try:
        logger.info(f"向量搜索工具: {request.query}, top_k: {request.top_k}")
        
        # 目前使用重排序作为替代
        results = await tool_manager_service.vector_search_tools(
            request.query,
            top_k=request.top_k
        )
        
        return {
            "success": True,
            "query": request.query,
            "results": results,
            "total": len(results),
            "method": "reranker_fallback"  # 标识使用的是重排序替代方案
        }
        
    except Exception as e:
        logger.error(f"向量搜索时出错: {e}")
        raise HTTPException(status_code=500, detail=f"向量搜索失败: {str(e)}")

@router.get("/statistics")
async def get_tool_statistics():
    """
    获取工具统计信息
    
    Returns:
        工具统计数据
    """
    try:
        logger.info("获取工具统计信息")
        
        stats = tool_manager_service.get_tool_statistics()
        
        return {
            "success": True,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"获取工具统计时出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取工具统计失败: {str(e)}")

@router.post("/refresh-cache")
async def refresh_tool_cache():
    """
    刷新工具缓存
    
    Returns:
        刷新结果
    """
    try:
        logger.info("刷新工具缓存")
        
        success = tool_manager_service.refresh_tool_cache()
        
        return {
            "success": success,
            "message": "工具缓存刷新成功" if success else "工具缓存刷新失败"
        }
        
    except Exception as e:
        logger.error(f"刷新工具缓存时出错: {e}")
        raise HTTPException(status_code=500, detail=f"刷新工具缓存失败: {str(e)}")

@router.get("/categories")
async def get_tool_categories():
    """
    获取所有工具类别
    
    Returns:
        工具类别列表
    """
    try:
        logger.info("获取工具类别")
        
        stats = tool_manager_service.get_tool_statistics()
        categories = list(stats.get('categories', {}).keys())
        
        return {
            "success": True,
            "categories": categories,
            "category_counts": stats.get('categories', {})
        }
        
    except Exception as e:
        logger.error(f"获取工具类别时出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取工具类别失败: {str(e)}")

@router.post("/enhanced-chat")
async def enhanced_chat_with_tools(request: EnhancedChatRequest):
    """
    使用增强LLM服务进行带工具调用的对话
    
    Args:
        request: 增强对话请求
        
    Returns:
        对话响应
    """
    try:
        logger.info(f"增强对话请求: {request.user_input}, use_tools: {request.use_tools}")
        
        # 获取增强LLM服务实例
        enhanced_llm = get_enhanced_llm_service()
        
        if request.use_tools:
            # 使用工具增强的响应生成
            response = await enhanced_llm.generate_response_with_tools(
                request.user_input,
                request.conversation_history,
                request.system_prompt
            )
        else:
            # 不使用工具的普通响应生成
            response = await enhanced_llm.generate_response(
                request.user_input,
                request.conversation_history,
                request.system_prompt,
                use_tools=False
            )
        
        return {
            "success": True,
            "user_input": request.user_input,
            "response": response,
            "tools_enabled": request.use_tools
        }
        
    except Exception as e:
        logger.error(f"增强对话时出错: {e}")
        raise HTTPException(status_code=500, detail=f"增强对话失败: {str(e)}")

@router.get("/health")
async def tool_service_health():
    """
    工具服务健康检查
    
    Returns:
        健康状态
    """
    try:
        # 检查各个服务的状态
        health_status = {
            "tool_manager": "healthy",
            "mcp_service": "healthy",
            "reranker_service": "healthy",
            "enhanced_llm": "healthy"
        }
        
        # 尝试获取工具统计来验证服务状态
        try:
            stats = tool_manager_service.get_tool_statistics()
            health_status["tool_count"] = stats.get("total_tools", 0)
        except Exception as e:
            health_status["tool_manager"] = f"error: {str(e)}"
        
        # 检查reranker服务
        try:
            # 简单的测试调用
            test_tools = [{"name": "test", "description": "test tool"}]
            reranker_service.rank_tools_by_relevance("test", test_tools)
        except Exception as e:
            health_status["reranker_service"] = f"error: {str(e)}"
        
        overall_healthy = all(
            status == "healthy" for status in health_status.values() 
            if isinstance(status, str) and not status.startswith("error")
        )
        
        return {
            "success": True,
            "overall_status": "healthy" if overall_healthy else "degraded",
            "services": health_status
        }
        
    except Exception as e:
        logger.error(f"健康检查时出错: {e}")
        return {
            "success": False,
            "overall_status": "unhealthy",
            "error": str(e)
        }