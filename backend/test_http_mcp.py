#!/usr/bin/env python3
"""
测试HTTP类型的MCP服务
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.mcp_client import SimpleMCPClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_http_mcp():
    """测试HTTP MCP服务"""
    print("🚀 开始测试HTTP MCP服务...")
    
    # 初始化MCP客户端
    mcp_client = SimpleMCPClient()
    
    print(f"📋 已加载的服务器配置: {list(mcp_client.server_configs.keys())}")
    
    # 测试模拟HTTP服务
    server_name = "mock-http-mcp"
    if server_name in mcp_client.server_configs:
        print(f"\n🔧 测试 {server_name} 服务...")
        
        # 显示配置信息
        config = mcp_client.server_configs[server_name]
        print(f"配置信息: {config}")
        
        # 启动服务器
        print("🔌 连接服务器...")
        success = await mcp_client.start_server(server_name)
        print(f"连接结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            # 获取工具列表
            print("📋 获取工具列表...")
            tools = await mcp_client.get_server_tools(server_name)
            print(f"获取到 {len(tools)} 个工具:")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
                print(f"    输入参数: {tool.get('inputSchema', {})}")
            
            # 测试工具调用
            if tools:
                # 测试第一个工具
                tool = tools[0]
                tool_name = tool['name']
                print(f"\n🔧 测试工具调用: {tool_name}")
                
                if tool_name == "get_weather":
                    result = await mcp_client.execute_tool(
                        tool_name, 
                        server_name, 
                        city="上海"
                    )
                elif tool_name == "search_location":
                    result = await mcp_client.execute_tool(
                        tool_name, 
                        server_name, 
                        query="北京"
                    )
                else:
                    result = await mcp_client.execute_tool(tool_name, server_name)
                
                print(f"工具调用结果:")
                print(f"  成功: {result.get('success')}")
                print(f"  内容: {result.get('result', {}).get('content', 'N/A')}")
                if not result.get('success'):
                    print(f"  错误: {result.get('error')}")
                
                # 测试第二个工具（如果存在）
                if len(tools) > 1:
                    tool = tools[1]
                    tool_name = tool['name']
                    print(f"\n🔧 测试工具调用: {tool_name}")
                    
                    if tool_name == "search_location":
                        result = await mcp_client.execute_tool(
                            tool_name, 
                            server_name, 
                            query="深圳"
                        )
                    else:
                        result = await mcp_client.execute_tool(tool_name, server_name)
                    
                    print(f"工具调用结果:")
                    print(f"  成功: {result.get('success')}")
                    print(f"  内容: {result.get('result', {}).get('content', 'N/A')}")
                    if not result.get('success'):
                        print(f"  错误: {result.get('error')}")
        
        # 停止服务器
        print("🔌 断开连接...")
        await mcp_client.stop_server(server_name)
    else:
        print(f"❌ 未找到 {server_name} 服务配置")
    
    # 清理
    await mcp_client.cleanup()
    print("\n✅ 测试完成")

async def main():
    """主函数"""
    try:
        await test_http_mcp()
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
