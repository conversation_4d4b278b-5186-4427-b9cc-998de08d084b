#!/usr/bin/env python3
"""
验证会话端点的最终测试
专门测试 GET /sessions/{id}/context 和 POST /sessions/{id}/messages
"""

import requests
import json
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def verify_session_endpoints():
    """验证会话端点功能"""
    
    logger.info("🎯 专门验证会话端点功能")
    logger.info("="*50)
    
    # 1. 获取NPC并创建会话
    logger.info("1. 准备测试环境...")
    
    # 获取NPCs
    response = requests.get(f"{BASE_URL}/npcs")
    npcs = response.json().get("npcs", [])
    npc_id = npcs[0]["id"]
    npc_name = npcs[0]["name"]
    
    # 创建会话
    response = requests.post(f"{BASE_URL}/npcs/{npc_id}/chat", params={"user_id": 14})
    session_id = response.json()["session_id"]
    
    logger.info(f"✅ 测试环境准备完成")
    logger.info(f"   NPC: {npc_name} ({npc_id})")
    logger.info(f"   会话ID: {session_id}")
    
    # 2. 测试 POST /sessions/{id}/messages
    logger.info("\n2. 测试 POST /sessions/{id}/messages")
    logger.info("-" * 30)
    
    test_messages = [
        {"role": "user", "content": "你好，我是新用户", "emotion": "neutral", "speed": 1.0},
        {"role": "assistant", "content": "你好！欢迎！我是周可心", "emotion": "friendly", "speed": 1.1},
        {"role": "user", "content": "能介绍一下你的兴趣爱好吗？", "emotion": "curious", "speed": 0.9},
        {"role": "assistant", "content": "当然！我喜欢追番、看小说、看演唱会，还喜欢拍vlog和朋友夜游校园", "emotion": "excited", "speed": 1.2}
    ]
    
    for i, msg in enumerate(test_messages, 1):
        logger.info(f"2.{i} 添加消息: {msg['role']} - {msg['content'][:20]}...")
        
        response = requests.post(
            f"{BASE_URL}/sessions/{session_id}/messages",
            params=msg
        )
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"     ✅ 成功 - 情感: {result['emotion']}, 语速: {result['speed']}")
        else:
            logger.error(f"     ❌ 失败: {response.status_code} - {response.text}")
            return False
    
    # 3. 测试 GET /sessions/{id}/context
    logger.info("\n3. 测试 GET /sessions/{id}/context")
    logger.info("-" * 30)
    
    response = requests.get(f"{BASE_URL}/sessions/{session_id}/context")
    
    if response.status_code == 200:
        context = response.json()
        
        logger.info("✅ 会话上下文获取成功:")
        logger.info(f"   会话ID: {context['session_id']}")
        logger.info(f"   NPC: {context['npc']['name']}")
        logger.info(f"   消息总数: {context['message_count']}")
        logger.info(f"   有NPC信息: {context['has_npc_info']}")
        
        logger.info("\n📨 消息历史:")
        for i, msg in enumerate(context['messages'], 1):
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            emotion = msg.get('emotion', 'N/A')
            speed = msg.get('speed', 'N/A')
            created_at = msg.get('created_at', 'N/A')
            
            logger.info(f"   {i}. [{role}] {content}")
            logger.info(f"      情感: {emotion}, 语速: {speed}")
            logger.info(f"      时间: {created_at}")
        
        # 验证消息数量
        expected_count = len(test_messages)
        actual_count = context['message_count']
        
        if actual_count == expected_count:
            logger.info(f"\n✅ 消息数量验证通过: {actual_count}/{expected_count}")
        else:
            logger.error(f"\n❌ 消息数量不匹配: 期望{expected_count}, 实际{actual_count}")
            return False
        
        # 验证消息内容
        messages = context['messages']
        for i, (expected, actual) in enumerate(zip(test_messages, messages)):
            if (expected['role'] == actual.get('role') and 
                expected['content'] == actual.get('content')):
                logger.info(f"✅ 消息{i+1}内容验证通过")
            else:
                logger.error(f"❌ 消息{i+1}内容不匹配")
                return False
        
    else:
        logger.error(f"❌ 获取会话上下文失败: {response.status_code} - {response.text}")
        return False
    
    # 4. 测试限制参数
    logger.info("\n4. 测试限制参数 (limit=2)")
    logger.info("-" * 30)
    
    response = requests.get(f"{BASE_URL}/sessions/{session_id}/context?limit=2")
    
    if response.status_code == 200:
        context = response.json()
        limited_count = len(context['messages'])
        
        if limited_count == 2:
            logger.info(f"✅ 限制参数测试通过: 返回{limited_count}条消息")
        else:
            logger.warning(f"⚠️ 限制参数可能无效: 期望2条, 实际{limited_count}条")
    
    # 5. 测试不存在的会话
    logger.info("\n5. 测试错误处理 (不存在的会话)")
    logger.info("-" * 30)
    
    fake_session_id = "999999"
    
    # 测试获取不存在会话的上下文
    response = requests.get(f"{BASE_URL}/sessions/{fake_session_id}/context")
    if response.status_code == 404:
        logger.info("✅ 不存在会话的错误处理正确 (GET)")
    else:
        logger.warning(f"⚠️ 错误处理可能有问题: {response.status_code}")
    
    # 测试向不存在会话添加消息
    response = requests.post(
        f"{BASE_URL}/sessions/{fake_session_id}/messages",
        params={"role": "user", "content": "test", "emotion": "neutral", "speed": 1.0}
    )
    if response.status_code == 404:
        logger.info("✅ 不存在会话的错误处理正确 (POST)")
    else:
        logger.warning(f"⚠️ 错误处理可能有问题: {response.status_code}")
    
    logger.info("\n" + "="*50)
    logger.info("🎉 会话端点验证完成！")
    logger.info("✅ GET /sessions/{id}/context - 正常工作")
    logger.info("✅ POST /sessions/{id}/messages - 正常工作")
    logger.info("✅ 错误处理 - 正常工作")
    logger.info("✅ 数据持久化 - 正常工作")
    
    return True

if __name__ == "__main__":
    try:
        success = verify_session_endpoints()
        if success:
            logger.info("\n🎊 所有验证通过！会话端点功能完全正常！")
        else:
            logger.error("\n💥 验证过程中发现问题")
    except Exception as e:
        logger.error(f"\n❌ 验证过程异常: {e}")