#!/usr/bin/env python3
"""
NPC集成测试 - 模拟main.py中的实际使用场景
测试完整的NPC信息调用和上下文存取流程
"""

import asyncio
import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加backend目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NPCIntegrationTester:
    """NPC集成测试器"""
    
    def __init__(self):
        self.supabase = None
        self.test_results = []
        
    async def initialize_supabase(self):
        """初始化Supabase连接"""
        logger.info("🔑 初始化Supabase连接...")
        
        try:
            supabase_url = os.getenv("SUPABASE_URL")
            # 使用Service Key
            service_key = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiYW5vbiIsInJlZiI6InNicC13bGp2ajh6bWkxcXFvdmhoIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NTMxOTAzNjQsImV4cCI6MjA2ODc2NjM2NH0.A3O1gtaZRdTnpe1T7wHJzYSlw9UCqACVFFM35ju1J5Y"
            
            if not supabase_url:
                logger.error("❌ SUPABASE_URL未设置")
                return False
                
            self.supabase = create_client(supabase_url, service_key)
            logger.info("✅ Supabase客户端初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ Supabase初始化失败: {e}")
            return False
    
    def generate_description_from_persona(self, persona_data):
        """从persona_data生成描述（模拟main.py中的逻辑）"""
        try:
            if isinstance(persona_data, str):
                persona = json.loads(persona_data)
            else:
                persona = persona_data
            
            name = persona.get("name", "未知")
            age = persona.get("age", "未知")
            occupation = persona.get("occupation", "未知")
            personality = persona.get("personality", "友善")
            background = persona.get("background", "")
            
            description = f"{name}，{age}岁，{occupation}。{personality}。{background}"
            return description.strip()
            
        except Exception as e:
            logger.warning(f"⚠️ 解析persona_data失败: {e}")
            return "AI助手角色"
    
    def generate_system_prompt_from_persona(self, persona_data):
        """从persona_data生成系统提示词（模拟main.py中的逻辑）"""
        try:
            if isinstance(persona_data, str):
                persona = json.loads(persona_data)
            else:
                persona = persona_data
            
            name = persona.get("name", "助手")
            personality = persona.get("personality", "友善")
            background = persona.get("background", "")
            hobbies = persona.get("hobbies", [])
            education = persona.get("education", "")
            
            # 构建系统提示词
            prompt_parts = [
                f"你是{name}，{personality}。",
                f"背景：{background}" if background else "",
                f"教育背景：{education}" if education else "",
                f"兴趣爱好：{', '.join(hobbies)}" if hobbies else "",
                "请用自然的语调回复用户，保持角色一致性。",
                "回复格式必须为：<turn>\\n<THINK>\\n## 1. 意图分析: [分析用户意图]\\n## 2. 行动规划: [规划回应策略]\\n## 3. 工具选择与参数构建: [如需要]\\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\\n## 5. 最终输出序列: <SPEAK>\\n</THINK>\\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\\n<JUDGE>\\n本轮表现：[评分]/10分\\n优点：[优点分析]\\n缺点：[缺点分析]\\n重大失误：[如有]\\n</JUDGE>\\n</turn>"
            ]
            
            return " ".join([part for part in prompt_parts if part])
            
        except Exception as e:
            logger.warning(f"⚠️ 生成系统提示词失败: {e}")
            return "你是一个友善的AI助手，请用自然的语调回复用户。"
    
    async def get_npc_by_id(self, npc_id):
        """模拟main.py中的get_npc_by_id函数"""
        try:
            result = self.supabase.table("npcs").select("*").eq("id", npc_id).execute()
            
            if result.data:
                npc = result.data[0]
                
                # 处理persona数据，生成描述和系统提示词
                persona_data = npc.get('persona_data')
                if persona_data:
                    description = self.generate_description_from_persona(persona_data)
                    system_prompt = self.generate_system_prompt_from_persona(persona_data)
                else:
                    description = "AI助手角色"
                    system_prompt = "你是一个友善的AI助手，请用自然的语调回复用户。"
                
                # 返回增强的NPC对象
                enhanced_npc = {
                    "id": npc.get("id"),
                    "name": npc.get("name"),
                    "description": description,
                    "system_prompt": system_prompt,
                    "is_active": npc.get("is_active"),
                    "persona_data": persona_data,
                    "avatar_url": None  # 当前表中没有这个字段
                }
                
                return enhanced_npc
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error getting NPC: {e}")
            return None
    
    async def create_conversation_session(self, user_id, npc_id):
        """模拟main.py中的create_conversation_session函数"""
        try:
            # 首先尝试数据库操作
            session_data = {
                "user_id": user_id,
                "npc_id": npc_id,
                "is_active": True
            }
            
            result = self.supabase.table("conversation_sessions").insert(session_data).execute()
            
            if result.data:
                session_id = result.data[0]["id"]
                logger.info(f"Database session created: {session_id}")
                return str(session_id)
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            # 在实际的main.py中，这里会回退到内存存储
            # 这里我们返回None表示失败
            return None
    
    async def save_message(self, session_id, role, content, audio_url=None, emotion=None, speed=None):
        """模拟main.py中的save_message函数"""
        try:
            message_data = {
                "session_id": session_id,
                "role": role,
                "content": content,
                "audio_url": audio_url,
                "emotion": emotion,
                "speed": speed
            }
            
            result = self.supabase.table("conversation_messages").insert(message_data).execute()
            
            if result.data:
                logger.info(f"Message saved to database for session {session_id}")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error saving message to database: {e}")
            # 在实际的main.py中，这里会回退到内存存储
            return False
    
    async def test_complete_npc_workflow(self):
        """测试完整的NPC工作流程"""
        logger.info("🔄 测试完整的NPC工作流程...")
        
        try:
            # 1. 获取活跃的NPC
            logger.info("📋 步骤1: 获取活跃的NPC")
            npcs_result = self.supabase.table("npcs").select("*").eq("is_active", True).execute()
            
            if not npcs_result.data:
                logger.error("❌ 没有找到活跃的NPC")
                return False
            
            npc_id = npcs_result.data[0]["id"]
            logger.info(f"✅ 找到活跃NPC: {npc_id}")
            
            # 2. 使用get_npc_by_id获取增强的NPC信息
            logger.info("📋 步骤2: 获取增强的NPC信息")
            npc = await self.get_npc_by_id(npc_id)
            
            if not npc:
                logger.error("❌ 无法获取NPC信息")
                return False
            
            logger.info(f"✅ 获取NPC信息成功: {npc['name']}")
            logger.info(f"   描述: {npc['description']}")
            logger.info(f"   系统提示词长度: {len(npc['system_prompt'])} 字符")
            
            # 3. 获取用户信息
            logger.info("📋 步骤3: 获取用户信息")
            users_result = self.supabase.table("users").select("*").limit(1).execute()
            
            if not users_result.data:
                logger.error("❌ 没有找到用户")
                return False
            
            user_id = users_result.data[0]["id"]
            logger.info(f"✅ 找到用户: {user_id}")
            
            # 4. 创建对话会话
            logger.info("📋 步骤4: 创建对话会话")
            session_id = await self.create_conversation_session(user_id, npc_id)
            
            if not session_id:
                logger.error("❌ 无法创建会话")
                return False
            
            logger.info(f"✅ 创建会话成功: {session_id}")
            
            # 5. 保存用户消息
            logger.info("📋 步骤5: 保存用户消息")
            user_message = "你好，请介绍一下自己"
            
            if await self.save_message(session_id, "user", user_message):
                logger.info("✅ 用户消息保存成功")
            else:
                logger.error("❌ 用户消息保存失败")
                return False
            
            # 6. 模拟NPC回复（基于persona数据）
            logger.info("📋 步骤6: 生成NPC回复")
            persona_data = npc.get('persona_data')
            if persona_data:
                try:
                    if isinstance(persona_data, str):
                        persona = json.loads(persona_data)
                    else:
                        persona = persona_data
                    
                    # 生成基于persona的回复
                    name = persona.get("name", "我")
                    age = persona.get("age", "")
                    occupation = persona.get("occupation", "")
                    personality = persona.get("personality", "")
                    hobbies = persona.get("hobbies", [])
                    
                    reply_content = f"你好！我是{name}"
                    if age:
                        reply_content += f"，{age}岁"
                    if occupation:
                        reply_content += f"，目前是{occupation}"
                    if personality:
                        reply_content += f"。我的性格{personality}"
                    if hobbies:
                        reply_content += f"，平时喜欢{', '.join(hobbies[:3])}"
                    reply_content += "。很高兴认识你！"
                    
                    logger.info(f"🤖 生成的NPC回复: {reply_content}")
                    
                    # 保存NPC回复
                    if await self.save_message(session_id, "assistant", reply_content, emotion="friendly", speed=1.0):
                        logger.info("✅ NPC回复保存成功")
                    else:
                        logger.warning("⚠️ NPC回复保存失败")
                
                except Exception as e:
                    logger.warning(f"⚠️ 生成NPC回复失败: {e}")
            
            # 7. 检索对话历史
            logger.info("📋 步骤7: 检索对话历史")
            messages_result = self.supabase.table("conversation_messages").select("*").eq("session_id", session_id).order("created_at").execute()
            
            if messages_result.data:
                logger.info(f"✅ 检索到{len(messages_result.data)}条消息")
                for msg in messages_result.data:
                    role = msg.get('role', 'unknown')
                    content = msg.get('content', '')
                    content_preview = content[:50] + "..." if len(content) > 50 else content
                    logger.info(f"   - {role}: {content_preview}")
            else:
                logger.warning("⚠️ 没有找到消息历史")
            
            # 8. 清理测试数据
            logger.info("📋 步骤8: 清理测试数据")
            self.supabase.table("conversation_messages").delete().eq("session_id", session_id).execute()
            self.supabase.table("conversation_sessions").delete().eq("id", session_id).execute()
            logger.info("🗑️ 测试数据清理完成")
            
            self.test_results.append({
                "test": "complete_npc_workflow",
                "status": "success",
                "message": "完整的NPC工作流程测试成功",
                "details": {
                    "npc_id": npc_id,
                    "npc_name": npc['name'],
                    "user_id": user_id,
                    "session_id": session_id,
                    "messages_count": len(messages_result.data) if messages_result.data else 0,
                    "has_persona_data": bool(npc.get('persona_data'))
                }
            })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 完整工作流程测试失败: {e}")
            self.test_results.append({
                "test": "complete_npc_workflow",
                "status": "failed",
                "message": f"工作流程测试失败: {str(e)}"
            })
            return False
    
    async def test_npc_context_persistence(self):
        """测试NPC上下文持久化"""
        logger.info("💾 测试NPC上下文持久化...")
        
        try:
            # 获取现有的会话和消息
            sessions_result = self.supabase.table("conversation_sessions").select("*").limit(5).execute()
            
            if not sessions_result.data:
                logger.info("ℹ️ 没有现有会话，跳过上下文持久化测试")
                self.test_results.append({
                    "test": "npc_context_persistence",
                    "status": "info",
                    "message": "没有现有会话数据"
                })
                return True
            
            logger.info(f"📊 找到{len(sessions_result.data)}个会话")
            
            # 分析每个会话的上下文
            context_analysis = []
            
            for session in sessions_result.data:
                session_id = session["id"]
                npc_id = session.get("npc_id")
                user_id = session.get("user_id")
                
                # 获取会话消息
                messages_result = self.supabase.table("conversation_messages").select("*").eq("session_id", session_id).order("created_at").execute()
                
                # 获取NPC信息
                npc = await self.get_npc_by_id(npc_id) if npc_id else None
                
                context_info = {
                    "session_id": session_id,
                    "npc_id": npc_id,
                    "npc_name": npc.get("name") if npc else "未知",
                    "user_id": user_id,
                    "message_count": len(messages_result.data) if messages_result.data else 0,
                    "has_npc_info": bool(npc),
                    "has_persona_data": bool(npc.get("persona_data")) if npc else False
                }
                
                context_analysis.append(context_info)
                
                logger.info(f"  会话 {session_id}: NPC={context_info['npc_name']}, 消息数={context_info['message_count']}")
            
            # 统计分析
            total_sessions = len(context_analysis)
            sessions_with_messages = len([ctx for ctx in context_analysis if ctx["message_count"] > 0])
            sessions_with_npc_info = len([ctx for ctx in context_analysis if ctx["has_npc_info"]])
            sessions_with_persona = len([ctx for ctx in context_analysis if ctx["has_persona_data"]])
            
            logger.info(f"📊 上下文持久化分析:")
            logger.info(f"   总会话数: {total_sessions}")
            logger.info(f"   有消息的会话: {sessions_with_messages}")
            logger.info(f"   有NPC信息的会话: {sessions_with_npc_info}")
            logger.info(f"   有persona数据的会话: {sessions_with_persona}")
            
            self.test_results.append({
                "test": "npc_context_persistence",
                "status": "success",
                "message": f"上下文持久化分析完成，共分析{total_sessions}个会话",
                "details": {
                    "total_sessions": total_sessions,
                    "sessions_with_messages": sessions_with_messages,
                    "sessions_with_npc_info": sessions_with_npc_info,
                    "sessions_with_persona": sessions_with_persona,
                    "context_analysis": context_analysis[:3]  # 只保存前3个作为示例
                }
            })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 上下文持久化测试失败: {e}")
            self.test_results.append({
                "test": "npc_context_persistence",
                "status": "failed",
                "message": f"测试失败: {str(e)}"
            })
            return False
    
    async def run_integration_tests(self):
        """运行集成测试"""
        logger.info("🚀 开始NPC集成测试...")
        
        # 初始化Supabase
        if not await self.initialize_supabase():
            logger.error("❌ Supabase初始化失败，无法继续测试")
            return False
        
        # 运行测试序列
        tests = [
            ("完整NPC工作流程", self.test_complete_npc_workflow),
            ("NPC上下文持久化", self.test_npc_context_persistence)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*60}")
            logger.info(f"🧪 执行集成测试: {test_name}")
            logger.info(f"{'='*60}")
            
            try:
                if await test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    failed += 1
                    logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                failed += 1
                logger.error(f"❌ {test_name} - 异常: {e}")
        
        # 生成测试报告
        logger.info(f"\n{'='*60}")
        logger.info("📊 集成测试总结")
        logger.info(f"{'='*60}")
        logger.info(f"总测试数: {len(tests)}")
        logger.info(f"通过: {passed}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {(passed/len(tests)*100):.1f}%")
        
        # 保存测试结果
        report = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "integration",
            "summary": {
                "total_tests": len(tests),
                "passed": passed,
                "failed": failed,
                "success_rate": f"{(passed/len(tests)*100):.1f}%"
            },
            "test_results": self.test_results
        }
        
        with open("npc_integration_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("📄 集成测试报告已保存到 npc_integration_test_report.json")
        
        if failed == 0:
            logger.info("🎉 所有集成测试通过！NPC信息调用和上下文存取功能完全正常")
        else:
            logger.error("💥 部分集成测试失败，请检查报告")
        
        return failed == 0

async def main():
    """主函数"""
    tester = NPCIntegrationTester()
    success = await tester.run_integration_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)