[project]
name = "backend"
version = "0.1.0"
description = "Real-time Voice Chat API Backend"
readme = "README.md"
requires-python = ">=3.12,<3.13"
dependencies = [
    "fastapi>=0.115.0",
    "uvicorn>=0.30.0",
    "openai>=1.0.0",
    "websockets>=12.0",
    "supabase>=2.0.0",
    "python-multipart>=0.0.6",
    "onnxruntime==1.17.0",
    "numpy==1.26.4",
    "scipy",
    "librosa",
    "soundfile",
    "requests>=2.31.0",
    "aiofiles>=23.2.1",
    "python-dotenv>=1.0.0",
    "pydantic>=2.7.2",
    "transformers",
    "bcrypt>=4.0.1",
    "volcengine-python-sdk[ark]==4.0.9",
    "google-generativeai>=0.8.0",
    "google-cloud-aiplatform>=1.60.0",
    "google-auth>=2.30.0",
    "google-auth-oauthlib>=1.2.0",
    "google-auth-httplib2>=0.2.0",
    "dashscope",
    "torch>=2.0.0,<2.3.0",
    "torchaudio>=2.0.0,<2.3.0",
    "mcp>=1.12.0",
]
