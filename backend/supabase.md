Read all rows

curl 'http://8.152.125.193:8000/rest/v1/npc_persona?select=*' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer SUPABASE_CLIENT_ANON_KEY"
Read specific columns

curl 'http://8.152.125.193:8000/rest/v1/npc_persona?select=some_column,other_column' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer SUPABASE_CLIENT_ANON_KEY"
Read referenced tables

curl 'http://8.152.125.193:8000/rest/v1/npc_persona?select=some_column,other_table(foreign_key)' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer SUPABASE_CLIENT_ANON_KEY"
With pagination

curl 'http://8.152.125.193:8000/rest/v1/npc_persona?select=*' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer <PERSON>ABASE_CLIENT_ANON_KEY" \
-H "Range: 0-9"
Filtering
Documentation
Supabase provides a wide range of filters

With filtering

curl --get 'http://8.152.125.193:8000/rest/v1/npc_persona' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer SUPABASE_CLIENT_ANON_KEY" \
-H "Range: 0-9" \
-d "select=*" \
\
`# Filters` \
-d "column=eq.Equal+to" \
-d "column=gt.Greater+than" \
-d "column=lt.Less+than" \
-d "column=gte.Greater+than+or+equal+to" \
-d "column=lte.Less+than+or+equal+to" \
-d "column=like.*CaseSensitive*" \
-d "column=ilike.*CaseInsensitive*" \
-d "column=is.null" \
-d "column=in.(Array,Values)" \
-d "column=neq.Not+equal+to" \
\
`# Arrays` \
-d "array_column=cs.{array,contains}" \
-d "array_column=cd.{contained,by}" \
\
`# Logical operators` \
-d "column=not.like.Negate+filter" \
-d "or=(some_column.eq.Some+value,other_column.eq.Other+value)"
Insert rows
Documentation
insert lets you insert into your tables. You can also insert in bulk and do UPSERT.

insert will also return the replaced values for UPSERT.

Insert a row

curl -X POST 'http://8.152.125.193:8000/rest/v1/npc_persona' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer SUPABASE_CLIENT_ANON_KEY" \
-H "Content-Type: application/json" \
-H "Prefer: return=minimal" \
-d '{ "some_column": "someValue", "other_column": "otherValue" }'
Insert many rows

curl -X POST 'http://8.152.125.193:8000/rest/v1/npc_persona' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer SUPABASE_CLIENT_ANON_KEY" \
-H "Content-Type: application/json" \
-d '[{ "some_column": "someValue" }, { "other_column": "otherValue" }]'
Upsert matching rows

curl -X POST 'http://8.152.125.193:8000/rest/v1/npc_persona' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer SUPABASE_CLIENT_ANON_KEY" \
-H "Content-Type: application/json" \
-H "Prefer: resolution=merge-duplicates" \
-d '{ "some_column": "someValue", "other_column": "otherValue" }'
Update rows
Documentation
update lets you update rows. update will match all rows by default. You can update specific rows using horizontal filters, e.g. eq, lt, and is.

update will also return the replaced values for UPDATE.

Update matching rows

curl -X PATCH 'http://8.152.125.193:8000/rest/v1/npc_persona?some_column=eq.someValue' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer SUPABASE_CLIENT_ANON_KEY" \
-H "Content-Type: application/json" \
-H "Prefer: return=minimal" \
-d '{ "other_column": "otherValue" }'
Delete rows
Documentation
delete lets you delete rows. delete will match all rows by default, so remember to specify your filters!

Delete matching rows

curl -X DELETE 'http://8.152.125.193:8000/rest/v1/npc_persona?some_column=eq.someValue' \
-H "apikey: SUPABASE_CLIENT_ANON_KEY" \
-H "Authorization: Bearer SUPABASE_CLIENT_ANON_KEY"
Subscribe to changes
Documentation
Supabase provides realtime functionality and broadcasts database changes to authorized users depending on Row Level Security (RLS) policies.

Subscribe to all events

# Realtime streams are only supported by our client libraries
Subscribe to inserts

# Realtime streams are only supported by our client libraries
Subscribe to updates

# Realtime streams are only supported by our client libraries
Subscribe to deletes

# Realtime streams are only supported by our client libraries
Subscribe to specific rows

# Realtime streams are only supported by our client libraries