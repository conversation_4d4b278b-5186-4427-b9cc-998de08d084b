#!/usr/bin/env python3
"""
简化版后端服务 - 包含TTS功能
专注于核心功能，避免复杂依赖
"""

import asyncio
import os
import logging
import json
import base64
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
from pydantic import BaseModel
from dotenv import load_dotenv
import uvicorn
from datetime import datetime
from typing import Dict, List

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Simple Voice Chat API with TTS", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Pydantic models
class TTSRequest(BaseModel):
    text: str
    emotion: str = "neutral"
    speed: float = 1.0
    voice_id: str = None

class LoginRequest(BaseModel):
    username: str
    password: str

# Simple in-memory user storage
SIMPLE_USERS = {
    "test_user": {
        "id": 1,
        "username": "test_user",
        "password": "test_password",
        "email": "<EMAIL>",
        "nickname": "测试用户",
        "is_active": True
    },
    "admin": {
        "id": 2,
        "username": "admin", 
        "password": "admin123",
        "email": "<EMAIL>",
        "nickname": "管理员",
        "is_active": True
    }
}

# Mock services for testing
class MockTTSService:
    def __init__(self):
        self.configured = True
    
    async def synthesize_speech(self, text, emotion="neutral", speed=1.0, voice_id=None, output_file=None):
        """Mock TTS synthesis"""
        logger.info(f"🔊 Mock TTS: {text[:50]}...")
        
        # Generate mock audio data (simple sine wave)
        import numpy as np
        import wave
        import io
        
        # Generate a simple tone
        sample_rate = 16000
        duration = min(len(text) * 0.1, 5.0)  # Max 5 seconds
        t = np.linspace(0, duration, int(sample_rate * duration))
        frequency = 440  # A4 note
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
        
        # Convert to 16-bit PCM
        audio_data = (audio_data * 32767).astype(np.int16)
        
        # Create WAV file in memory
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        wav_data = wav_buffer.getvalue()
        
        return {
            "success": True,
            "audio_data": wav_data,
            "size": len(wav_data),
            "duration": duration,
            "mock_mode": True
        }
    
    def get_available_voices(self):
        return {
            "default": {"name": "Default Voice", "language": "zh-CN"},
            "female": {"name": "Female Voice", "language": "zh-CN"},
            "male": {"name": "Male Voice", "language": "zh-CN"}
        }

class MockLLMService:
    def __init__(self):
        self.configured = True
    
    async def generate_response(self, user_input, conversation_history=None, system_prompt=None, use_tools=False):
        """Mock LLM response"""
        logger.info(f"🤖 Mock LLM: {user_input[:50]}...")
        
        responses = [
            "这是一个模拟的AI响应，用于测试TTS功能。",
            "我是一个测试用的AI助手，正在为您提供模拟回答。",
            "感谢您的问题，这是一个用于验证系统功能的测试回复。",
            "您好！我正在正常工作，这是一个测试响应。"
        ]
        
        import random
        response_text = random.choice(responses)
        
        return {
            "success": True,
            "speak_content": {
                "text": response_text,
                "emotion": "neutral",
                "speed": 1.0
            },
            "mock_mode": True
        }

# Initialize mock services
tts_service = MockTTSService()
llm_service = MockLLMService()

logger.info("✅ Mock services initialized")

# WebSocket Connection Manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, Dict] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_sessions[user_id] = {
            "connected_at": datetime.now(),
            "session_id": f"session_{user_id}_{int(datetime.now().timestamp())}",
            "is_recording": False
        }
        logger.info(f"🔌 WebSocket connected for user {user_id}")

    def disconnect(self, user_id: str):
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        logger.info(f"🔌 WebSocket disconnected for user {user_id}")

    async def send_personal_message(self, message: dict, user_id: str):
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(message))
                return True
            except Exception as e:
                logger.error(f"Error sending message to user {user_id}: {e}")
                return False
        return False

    async def process_audio_chunk(self, user_id: str, audio_data: str):
        """处理音频数据块"""
        try:
            # 解码base64音频数据
            audio_bytes = base64.b64decode(audio_data)
            logger.info(f"🎤 Received audio chunk from user {user_id}: {len(audio_bytes)} bytes")

            # 模拟语音识别
            mock_transcription = "这是模拟的语音识别结果"

            # 发送转录结果
            await self.send_personal_message({
                "type": "transcription",
                "text": mock_transcription,
                "timestamp": datetime.now().isoformat()
            }, user_id)

            # 模拟AI响应
            if llm_service:
                response = await llm_service.generate_response(
                    user_input=mock_transcription,
                    conversation_history=[],
                    system_prompt="你是一个友好的AI助手。",
                    use_tools=False
                )

                if response.get("success"):
                    speak_content = response.get("speak_content", {})
                    ai_text = speak_content.get("text", "")

                    # 发送AI响应
                    await self.send_personal_message({
                        "type": "ai_response",
                        "text": ai_text,
                        "timestamp": datetime.now().isoformat()
                    }, user_id)

                    # 生成TTS音频
                    if tts_service and ai_text:
                        tts_result = await tts_service.synthesize_speech(
                            text=ai_text,
                            emotion="neutral",
                            speed=1.0,
                            voice_id="default"
                        )

                        if tts_result.get("success") and tts_result.get("audio_data"):
                            audio_base64 = base64.b64encode(tts_result["audio_data"]).decode('utf-8')
                            await self.send_personal_message({
                                "type": "audio_chunk",
                                "audio_data": audio_base64,
                                "timestamp": datetime.now().isoformat()
                            }, user_id)

        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")
            await self.send_personal_message({
                "type": "error",
                "message": f"音频处理错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }, user_id)

manager = ConnectionManager()

# API Routes
@app.get("/")
async def root():
    return {"message": "Simple Voice Chat API with TTS", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "tts": "configured" if tts_service else "not configured",
            "llm": "configured" if llm_service else "not configured"
        },
        "version": "1.0.0"
    }

@app.post("/auth/login")
async def login_user(request: LoginRequest):
    """User login endpoint"""
    global SIMPLE_USERS
    
    try:
        logger.info(f"🔐 登录请求: {request.username}")
        
        if request.username not in SIMPLE_USERS:
            raise HTTPException(status_code=401, detail="用户不存在")
        
        user = SIMPLE_USERS[request.username]
        if user["password"] != request.password:
            raise HTTPException(status_code=401, detail="密码错误")
        
        if not user["is_active"]:
            raise HTTPException(status_code=401, detail="用户已被禁用")
        
        # Return user info (excluding password)
        user_info = {k: v for k, v in user.items() if k != "password"}
        
        logger.info(f"✅ 登录成功: {request.username}")
        return {
            "success": True,
            "message": "登录成功",
            "user": user_info,
            "token": f"mock_token_{user['id']}"  # Mock token
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="登录失败")

@app.get("/npcs")
async def get_npcs():
    """Get available NPCs"""
    fake_npcs = [
        {
            "id": 1,
            "name": "小助手",
            "description": "友好的AI助手",
            "avatar": "assistant.png",
            "system_prompt": "你是一个友好的AI助手，请用简洁明了的语言回答问题。",
            "voice_id": "female",
            "is_active": True
        },
        {
            "id": 2,
            "name": "专业顾问",
            "description": "专业的咨询顾问",
            "avatar": "consultant.png", 
            "system_prompt": "你是一个专业的咨询顾问，请提供详细和专业的建议。",
            "voice_id": "male",
            "is_active": True
        }
    ]
    
    return {"npcs": fake_npcs, "source": "mock_data"}

# TTS API Routes
@app.post("/api/tts/synthesize")
async def synthesize_speech(request: TTSRequest):
    """合成语音"""
    if not tts_service:
        raise HTTPException(status_code=503, detail="TTS服务未配置")
    
    try:
        logger.info(f"🔊 TTS请求: {request.text[:50]}...")
        
        result = await tts_service.synthesize_speech(
            text=request.text,
            emotion=request.emotion,
            speed=request.speed,
            voice_id=request.voice_id,
            output_file=None
        )
        
        if result.get("success") and result.get("audio_data"):
            audio_data = result["audio_data"]
            logger.info(f"✅ TTS合成成功: {len(audio_data)} 字节")
            
            return Response(
                content=audio_data,
                media_type="audio/wav",
                headers={
                    "Content-Disposition": "attachment; filename=tts_output.wav",
                    "Content-Length": str(len(audio_data))
                }
            )
        else:
            error_msg = result.get("error", "TTS合成失败")
            logger.info(f"❌ TTS合成失败: {error_msg}")
            raise HTTPException(status_code=500, detail=error_msg)
            
    except Exception as e:
        logger.error(f"❌ TTS API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/tts/voices")
async def get_available_voices():
    """获取可用的语音"""
    if not tts_service:
        return {"voices": {}}
    
    return {"voices": tts_service.get_available_voices()}

@app.post("/api/tts/test")
async def test_tts():
    """测试TTS服务"""
    test_text = "这是一个TTS测试，请生成语音。"
    
    request = TTSRequest(
        text=test_text,
        emotion="neutral",
        speed=1.0
    )
    
    return await synthesize_speech(request)

@app.get("/test")
async def test_endpoint():
    """Test endpoint for verifying connectivity"""
    test_question = "你好，这是一个测试"
    
    try:
        # Test LLM
        response = await llm_service.generate_response(
            user_input=test_question,
            conversation_history=[],
            system_prompt="你是一个友好的AI助手。",
            use_tools=False
        )
        
        if response.get("success"):
            speak_content = response.get("speak_content", {})
            ai_text = speak_content.get("text", "")
            
            return {
                "question": test_question,
                "response": ai_text,
                "status": "success",
                "mock_mode": response.get("mock_mode", False),
                "services": {
                    "llm": "working",
                    "tts": "working"
                }
            }
        else:
            return {
                "question": test_question,
                "response": "服务暂时不可用",
                "status": "fallback"
            }
            
    except Exception as e:
        logger.error(f"测试端点错误: {e}")
        return {"error": f"测试端点错误: {str(e)}"}

def main():
    """主函数"""
    print("🚀 启动简化版语音聊天API服务器（包含TTS）...")
    print("📍 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔊 TTS测试: http://localhost:8000/api/tts/test")
    print("🧪 系统测试: http://localhost:8000/test")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )

if __name__ == "__main__":
    main()
