#!/usr/bin/env python3
"""
简单的认证补丁 - 绕过数据库，使用内存存储
"""
import bcrypt

# 内存中的用户存储
USERS_DB = {}

def init_simple_auth():
    """初始化简单认证系统"""
    global USERS_DB
    
    # 创建默认测试用户
    password_hash = bcrypt.hashpw("test_password".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    USERS_DB = {
        "test_user": {
            "id": 1,
            "username": "test_user",
            "password_hash": password_hash,
            "email": "<EMAIL>",
            "nickname": "测试用户",
            "is_active": True
        },
        "admin": {
            "id": 2,
            "username": "admin",
            "password_hash": bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8'),
            "email": "<EMAIL>",
            "nickname": "管理员",
            "is_active": True
        }
    }
    
    print("✅ 简单认证系统初始化完成")
    print("   测试用户: test_user / test_password")
    print("   管理员: admin / admin123")

def simple_register(username: str, password: str, email: str = None, nickname: str = None):
    """简单注册功能"""
    global USERS_DB
    
    if username in USERS_DB:
        raise Exception("Username already exists")
    
    password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    user_id = len(USERS_DB) + 1
    USERS_DB[username] = {
        "id": user_id,
        "username": username,
        "password_hash": password_hash,
        "email": email,
        "nickname": nickname or username,
        "is_active": True
    }
    
    user = USERS_DB[username]
    return {
        "user": {
            "id": user["id"],
            "username": user["username"],
            "nickname": user["nickname"]
        },
        "token": f"token_{user['id']}"
    }

def simple_login(username: str, password: str):
    """简单登录功能"""
    global USERS_DB
    
    if username not in USERS_DB:
        raise Exception("Invalid username or password")
    
    user = USERS_DB[username]
    
    if not user.get("is_active", True):
        raise Exception("Account is disabled")
    
    if not bcrypt.checkpw(password.encode('utf-8'), user["password_hash"].encode('utf-8')):
        raise Exception("Invalid username or password")
    
    return {
        "user": {
            "id": user["id"],
            "username": user["username"],
            "nickname": user["nickname"]
        },
        "token": f"token_{user['id']}"
    }

if __name__ == "__main__":
    init_simple_auth()
    
    # 测试注册
    try:
        result = simple_register("new_user", "new_password", "<EMAIL>", "新用户")
        print(f"✅ 注册测试成功: {result}")
    except Exception as e:
        print(f"❌ 注册测试失败: {e}")
    
    # 测试登录
    try:
        result = simple_login("test_user", "test_password")
        print(f"✅ 登录测试成功: {result}")
    except Exception as e:
        print(f"❌ 登录测试失败: {e}")