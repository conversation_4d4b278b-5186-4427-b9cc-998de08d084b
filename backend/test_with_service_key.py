#!/usr/bin/env python3
"""
使用SUPABASE_SERVICE_KEY测试NPC功能
"""

import asyncio
import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加backend目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ServiceKeyTester:
    """使用Service Key的测试器"""
    
    def __init__(self):
        self.supabase = None
        self.test_results = []
        
    async def initialize_supabase_with_service_key(self):
        """使用Service Key初始化Supabase"""
        logger.info("🔑 使用Service Key初始化Supabase...")
        
        try:
            supabase_url = os.getenv("SUPABASE_URL")
            # 使用你提供的Service Key
            service_key = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiYW5vbiIsInJlZiI6InNicC13bGp2ajh6bWkxcXFvdmhoIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NTMxOTAzNjQsImV4cCI6MjA2ODc2NjM2NH0.A3O1gtaZRdTnpe1T7wHJzYSlw9UCqACVFFM35ju1J5Y"
            
            if not supabase_url:
                logger.error("❌ SUPABASE_URL未设置")
                return False
                
            self.supabase = create_client(supabase_url, service_key)
            logger.info("✅ Supabase客户端初始化成功 (使用Service Key)")
            
            self.test_results.append({
                "test": "supabase_service_key_init",
                "status": "success",
                "message": "使用Service Key成功初始化Supabase客户端"
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ Supabase初始化失败: {e}")
            self.test_results.append({
                "test": "supabase_service_key_init",
                "status": "failed",
                "message": f"初始化失败: {str(e)}"
            })
            return False
    
    async def test_npcs_table(self):
        """测试NPCs表"""
        logger.info("🎭 测试NPCs表...")
        
        try:
            # 查询NPCs表
            result = self.supabase.table("npcs").select("*").execute()
            
            logger.info(f"✅ NPCs表查询成功，返回{len(result.data)}条记录")
            
            # 显示NPC信息
            for npc in result.data:
                logger.info(f"  - NPC ID: {npc.get('id')}, 名称: {npc.get('name')}, 活跃: {npc.get('is_active')}")
            
            self.test_results.append({
                "test": "npcs_table_query",
                "status": "success",
                "message": f"成功查询NPCs表，返回{len(result.data)}条记录",
                "details": {
                    "record_count": len(result.data),
                    "npcs": [{"id": npc.get("id"), "name": npc.get("name")} for npc in result.data[:3]]
                }
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ NPCs表查询失败: {e}")
            self.test_results.append({
                "test": "npcs_table_query",
                "status": "failed",
                "message": f"查询失败: {str(e)}"
            })
            return False
    
    async def test_users_table(self):
        """测试Users表"""
        logger.info("👥 测试Users表...")
        
        try:
            # 查询Users表
            result = self.supabase.table("users").select("*").execute()
            
            logger.info(f"✅ Users表查询成功，返回{len(result.data)}条记录")
            
            # 显示用户信息（隐藏敏感信息）
            for user in result.data:
                logger.info(f"  - 用户 ID: {user.get('id')}, 用户名: {user.get('username')}, 昵称: {user.get('nickname')}")
            
            self.test_results.append({
                "test": "users_table_query",
                "status": "success",
                "message": f"成功查询Users表，返回{len(result.data)}条记录",
                "details": {
                    "record_count": len(result.data),
                    "users": [{"id": user.get("id"), "username": user.get("username")} for user in result.data[:3]]
                }
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ Users表查询失败: {e}")
            self.test_results.append({
                "test": "users_table_query",
                "status": "failed",
                "message": f"查询失败: {str(e)}"
            })
            return False
    
    async def test_conversation_sessions_table(self):
        """测试会话表"""
        logger.info("💬 测试会话表...")
        
        try:
            # 查询会话表
            result = self.supabase.table("conversation_sessions").select("*").execute()
            
            logger.info(f"✅ 会话表查询成功，返回{len(result.data)}条记录")
            
            # 显示会话信息
            for session in result.data:
                logger.info(f"  - 会话 ID: {session.get('id')}, 用户ID: {session.get('user_id')}, NPC ID: {session.get('npc_id')}")
            
            self.test_results.append({
                "test": "conversation_sessions_query",
                "status": "success",
                "message": f"成功查询会话表，返回{len(result.data)}条记录",
                "details": {
                    "record_count": len(result.data)
                }
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ 会话表查询失败: {e}")
            self.test_results.append({
                "test": "conversation_sessions_query",
                "status": "failed",
                "message": f"查询失败: {str(e)}"
            })
            return False
    
    async def test_conversation_messages_table(self):
        """测试消息表"""
        logger.info("📝 测试消息表...")
        
        try:
            # 查询消息表
            result = self.supabase.table("conversation_messages").select("*").limit(10).execute()
            
            logger.info(f"✅ 消息表查询成功，返回{len(result.data)}条记录")
            
            # 显示消息信息
            for message in result.data:
                content_preview = message.get('content', '')[:50] + "..." if len(message.get('content', '')) > 50 else message.get('content', '')
                logger.info(f"  - 消息 ID: {message.get('id')}, 角色: {message.get('role')}, 内容: {content_preview}")
            
            self.test_results.append({
                "test": "conversation_messages_query",
                "status": "success",
                "message": f"成功查询消息表，返回{len(result.data)}条记录",
                "details": {
                    "record_count": len(result.data)
                }
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ 消息表查询失败: {e}")
            self.test_results.append({
                "test": "conversation_messages_query",
                "status": "failed",
                "message": f"查询失败: {str(e)}"
            })
            return False
    
    async def test_npc_context_retrieval(self):
        """测试NPC上下文检索"""
        logger.info("🔍 测试NPC上下文检索...")
        
        try:
            # 获取第一个NPC
            npc_result = self.supabase.table("npcs").select("*").eq("is_active", True).limit(1).execute()
            
            if not npc_result.data:
                logger.warning("⚠️ 没有找到活跃的NPC")
                self.test_results.append({
                    "test": "npc_context_retrieval",
                    "status": "warning",
                    "message": "没有找到活跃的NPC"
                })
                return False
            
            npc = npc_result.data[0]
            npc_id = npc["id"]
            
            logger.info(f"📋 测试NPC: {npc['name']} (ID: {npc_id})")
            logger.info(f"📝 系统提示词: {npc['system_prompt'][:100]}...")
            
            # 查找与此NPC相关的会话
            sessions_result = self.supabase.table("conversation_sessions").select("*").eq("npc_id", npc_id).execute()
            
            logger.info(f"💬 找到{len(sessions_result.data)}个相关会话")
            
            # 如果有会话，查找消息
            if sessions_result.data:
                session_id = sessions_result.data[0]["id"]
                messages_result = self.supabase.table("conversation_messages").select("*").eq("session_id", session_id).order("created_at").execute()
                
                logger.info(f"📨 会话 {session_id} 中有{len(messages_result.data)}条消息")
                
                # 显示对话历史
                for msg in messages_result.data[:5]:  # 只显示前5条
                    content_preview = msg.get('content', '')[:50] + "..." if len(msg.get('content', '')) > 50 else msg.get('content', '')
                    logger.info(f"  - {msg.get('role')}: {content_preview}")
            
            self.test_results.append({
                "test": "npc_context_retrieval",
                "status": "success",
                "message": f"成功检索NPC上下文，NPC: {npc['name']}",
                "details": {
                    "npc_id": npc_id,
                    "npc_name": npc["name"],
                    "sessions_count": len(sessions_result.data),
                    "has_system_prompt": bool(npc.get("system_prompt"))
                }
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ NPC上下文检索失败: {e}")
            self.test_results.append({
                "test": "npc_context_retrieval",
                "status": "failed",
                "message": f"检索失败: {str(e)}"
            })
            return False
    
    async def test_create_test_session(self):
        """测试创建测试会话"""
        logger.info("🆕 测试创建测试会话...")
        
        try:
            # 获取第一个用户和NPC
            user_result = self.supabase.table("users").select("*").limit(1).execute()
            npc_result = self.supabase.table("npcs").select("*").eq("is_active", True).limit(1).execute()
            
            if not user_result.data or not npc_result.data:
                logger.warning("⚠️ 缺少用户或NPC数据，跳过会话创建测试")
                self.test_results.append({
                    "test": "create_test_session",
                    "status": "skipped",
                    "message": "缺少用户或NPC数据"
                })
                return False
            
            user_id = user_result.data[0]["id"]
            npc_id = npc_result.data[0]["id"]
            
            # 创建测试会话
            session_data = {
                "user_id": user_id,
                "npc_id": npc_id,
                "is_active": True
            }
            
            session_result = self.supabase.table("conversation_sessions").insert(session_data).execute()
            
            if session_result.data:
                session_id = session_result.data[0]["id"]
                logger.info(f"✅ 成功创建测试会话: {session_id}")
                
                # 添加测试消息
                test_message = {
                    "session_id": session_id,
                    "role": "user",
                    "content": "这是一条测试消息，用于验证NPC上下文存取功能"
                }
                
                message_result = self.supabase.table("conversation_messages").insert(test_message).execute()
                
                if message_result.data:
                    logger.info("✅ 成功添加测试消息")
                    
                    # 清理测试数据
                    self.supabase.table("conversation_messages").delete().eq("session_id", session_id).execute()
                    self.supabase.table("conversation_sessions").delete().eq("id", session_id).execute()
                    logger.info("🗑️ 清理测试数据完成")
                
                self.test_results.append({
                    "test": "create_test_session",
                    "status": "success",
                    "message": f"成功创建和清理测试会话: {session_id}",
                    "details": {
                        "session_id": session_id,
                        "user_id": user_id,
                        "npc_id": npc_id
                    }
                })
                return True
            else:
                logger.error("❌ 会话创建失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 创建测试会话失败: {e}")
            self.test_results.append({
                "test": "create_test_session",
                "status": "failed",
                "message": f"创建失败: {str(e)}"
            })
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始Service Key测试...")
        
        # 初始化Supabase
        if not await self.initialize_supabase_with_service_key():
            logger.error("❌ Supabase初始化失败，无法继续测试")
            return False
        
        # 运行测试序列
        tests = [
            ("NPCs表查询", self.test_npcs_table),
            ("Users表查询", self.test_users_table),
            ("会话表查询", self.test_conversation_sessions_table),
            ("消息表查询", self.test_conversation_messages_table),
            ("NPC上下文检索", self.test_npc_context_retrieval),
            ("创建测试会话", self.test_create_test_session)
        ]
        
        passed = 0
        failed = 0
        skipped = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 执行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                if result:
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    # 检查是否是跳过状态
                    last_result = self.test_results[-1] if self.test_results else {}
                    if last_result.get("status") == "skipped":
                        skipped += 1
                        logger.warning(f"⏭️ {test_name} - 跳过")
                    else:
                        failed += 1
                        logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                failed += 1
                logger.error(f"❌ {test_name} - 异常: {e}")
        
        # 生成测试报告
        logger.info(f"\n{'='*60}")
        logger.info("📊 测试总结")
        logger.info(f"{'='*60}")
        logger.info(f"总测试数: {len(tests)}")
        logger.info(f"通过: {passed}")
        logger.info(f"跳过: {skipped}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {(passed/(len(tests))*100):.1f}%")
        
        # 保存测试结果
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": len(tests),
                "passed": passed,
                "skipped": skipped,
                "failed": failed,
                "success_rate": f"{(passed/len(tests)*100):.1f}%"
            },
            "test_results": self.test_results
        }
        
        with open("service_key_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("📄 测试报告已保存到 service_key_test_report.json")
        
        return failed == 0

async def main():
    """主函数"""
    tester = ServiceKeyTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有测试通过！NPC上下文存取功能正常")
        return 0
    else:
        logger.error("💥 部分测试失败，请检查报告")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)