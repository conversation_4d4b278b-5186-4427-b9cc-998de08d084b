#!/usr/bin/env python3
"""
最终诊断报告 - 明确识别问题并提供解决方案
"""
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def final_diagnosis_report():
    """最终诊断报告"""
    print("📋 最终诊断报告")
    print("=" * 50)
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    print("\n🔍 问题诊断:")
    
    # 1. 表存在性问题
    print("\n1. 表存在性问题:")
    tables_to_check = [
        "users", "npcs", "conversation_sessions", "conversation_messages",
        "0802_users", "0802_npcs", "0802_conversation_sessions", "0802_conversation_messages"
    ]
    
    existing_tables = []
    missing_tables = []
    
    for table_name in tables_to_check:
        try:
            result = supabase.table(table_name).select("*").limit(1).execute()
            print(f"   ✅ 表 '{table_name}' 存在")
            existing_tables.append(table_name)
        except Exception as e:
            print(f"   ❌ 表 '{table_name}' 不存在: {e}")
            missing_tables.append(table_name)
    
    # 2. 字段结构问题
    print("\n2. 字段结构问题:")
    field_issues = []
    
    # 检查 users 表字段
    if "users" in existing_tables:
        try:
            test_user = {
                "username": "field_test_user",
                "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                "email": "<EMAIL>",
                "nickname": "字段测试用户",
                "is_active": True
            }
            
            result = supabase.table("users").insert(test_user).execute()
            if result.data:
                print("   ✅ users 表字段结构正确")
                # 清理测试数据
                supabase.table("users").delete().eq("username", "field_test_user").execute()
            else:
                print("   ❌ users 表字段结构有问题")
                field_issues.append("users 表字段结构问题")
        except Exception as e:
            print(f"   ❌ users 表字段检查失败: {e}")
            field_issues.append(f"users 表字段检查失败: {e}")
    
    # 检查 npcs 表字段
    if "npcs" in existing_tables:
        try:
            test_npc = {
                "name": "field_test_npc",
                "description": "字段测试NPC",
                "system_prompt": "字段测试提示",
                "is_active": True
            }
            
            result = supabase.table("npcs").insert(test_npc).execute()
            if result.data:
                print("   ✅ npcs 表字段结构正确")
                # 清理测试数据
                supabase.table("npcs").delete().eq("name", "field_test_npc").execute()
            else:
                print("   ❌ npcs 表字段结构有问题")
                field_issues.append("npcs 表字段结构问题")
        except Exception as e:
            print(f"   ❌ npcs 表字段检查失败: {e}")
            field_issues.append(f"npcs 表字段检查失败: {e}")
    
    # 3. 权限问题
    print("\n3. 权限问题:")
    try:
        # 检查是否可以查询表结构
        result = supabase.table("users").select("id").limit(1).execute()
        print("   ✅ 具有基本查询权限")
    except Exception as e:
        print(f"   ❌ 查询权限不足: {e}")
        
    try:
        # 检查是否可以插入数据
        test_data = {"username": "permission_test"}
        result = supabase.table("users").insert(test_data).execute()
        if result.data:
            print("   ✅ 具有数据插入权限")
            # 清理测试数据
            supabase.table("users").delete().eq("username", "permission_test").execute()
        else:
            print("   ❌ 数据插入权限不足")
    except Exception as e:
        print(f"   ❌ 数据插入权限检查失败: {e}")
    
    # 4. RLS (Row Level Security) 问题
    print("\n4. RLS (Row Level Security) 问题:")
    try:
        # 尝试插入数据来检查RLS
        test_data = {"username": "rls_test"}
        result = supabase.table("users").insert(test_data).execute()
        if result.data:
            print("   ✅ RLS 配置正确")
            # 清理测试数据
            supabase.table("users").delete().eq("username", "rls_test").execute()
        else:
            print("   ❌ RLS 配置可能有问题")
    except Exception as e:
        if "row-level security policy" in str(e):
            print("   ❌ RLS 策略阻止了数据插入")
            print(f"      错误详情: {e}")
        else:
            print(f"   ❌ RLS 检查失败: {e}")
    
    # 问题总结
    print("\n📝 问题总结:")
    problems = []
    
    if missing_tables:
        problems.append(f"缺失 {len(missing_tables)} 个表: {missing_tables}")
    
    if field_issues:
        problems.append(f"字段结构问题: {field_issues}")
    
    if not problems:
        print("   ✅ 暂未发现明显问题")
    else:
        for problem in problems:
            print(f"   ❌ {problem}")
    
    # 解决方案建议
    print("\n💡 解决方案建议:")
    print("   1. 检查 Supabase 项目中的表结构定义")
    print("   2. 确认数据库迁移脚本是否正确执行")
    print("   3. 检查 RLS (Row Level Security) 策略配置")
    print("   4. 确认 Supabase 服务密钥权限")
    print("   5. 重新运行数据库初始化脚本")
    print("   6. 如果问题持续存在，考虑重建数据库表")
    
    # 技术细节
    print("\n🔧 技术细节:")
    print("   - 错误代码 PGRST204: 模式缓存中找不到列")
    print("   - 错误代码 42P01: 表不存在")
    print("   - 错误代码 42501: RLS 策略阻止操作")
    print("   - 这些错误通常表明:")
    print("     * 表结构与代码期望不匹配")
    print("     * 数据库模式缓存未更新")
    print("     * RLS 策略配置不正确")
    print("     * 权限不足")
    
    return True

if __name__ == "__main__":
    print("🚀 生成最终诊断报告...")
    
    if final_diagnosis_report():
        print("\n✅ 诊断报告生成完成！")
        sys.exit(0)
    else:
        print("\n❌ 诊断报告生成失败")
        sys.exit(1)
