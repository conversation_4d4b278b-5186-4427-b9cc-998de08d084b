{"servers": [{"name": "test_server", "url": "http://localhost:3000", "registered_at": "2025-08-07T20:57:35.407033", "info": {}}, {"name": "12306-mcp-12306-mcp", "url": "local://npx -y 12306-mcp", "registered_at": "2025-08-07T20:57:59.128283", "info": {"command": "npx", "args": ["-y", "12306-mcp"], "env": {}, "type": "local_command", "source_config": "12306-mcp"}}, {"name": "howtocook-mcp-howtocook-mcp", "url": "local://npx -y howtocook-mcp", "registered_at": "2025-08-07T20:57:59.128803", "info": {"command": "npx", "args": ["-y", "howtocook-mcp"], "env": {}, "type": "local_command", "source_config": "howtocook-mcp"}}, {"name": "bilibili-mcp-bilibili-mcp", "url": "local://npx -y @mcp_hub_org/cli@latest run bilibili-mcp", "registered_at": "2025-08-07T20:57:59.128968", "info": {"command": "npx", "args": ["-y", "@mcp_hub_org/cli@latest", "run", "bilibili-mcp"], "env": {}, "type": "local_command", "source_config": "bilibili-mcp"}}, {"name": "mcp-trends-hub-trends-hub", "url": "local://npx -y mcp-trends-hub@1.6.2", "registered_at": "2025-08-07T20:57:59.129130", "info": {"command": "npx", "args": ["-y", "mcp-trends-hub@1.6.2"], "env": {}, "type": "local_command", "source_config": "mcp-trends-hub"}}, {"name": "arxiv-paper-mcp-arxiv-paper-mcp", "url": "local://npx -y @langgpt/arxiv-paper-mcp@latest", "registered_at": "2025-08-07T20:57:59.129512", "info": {"command": "npx", "args": ["-y", "@langgpt/arxiv-paper-mcp@latest"], "env": {}, "type": "local_command", "source_config": "arxiv-paper-mcp"}}, {"name": "amap-mcp-server-amap-mcp-server", "url": "local://uvx amap-mcp-server", "registered_at": "2025-08-07T20:57:59.129702", "info": {"command": "uvx", "args": ["amap-mcp-server"], "env": {"AMAP_MAPS_API_KEY": "ca65265e1d4a885dd1fd4926725c11a9"}, "type": "local_command", "source_config": "amap-mcp-server"}}], "default_server": null}