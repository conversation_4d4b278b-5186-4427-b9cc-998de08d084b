#!/usr/bin/env python3
"""
工具调用功能快速测试启动脚本
"""
import asyncio
import sys
import os
import logging
from datetime import datetime

# 添加backend目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def quick_tool_test():
    """快速工具测试"""
    logger.info("🚀 开始快速工具调用测试...")
    
    try:
        # 导入服务
        from services.tool_manager_service import tool_manager_service
        from services.enhanced_llm_service import get_enhanced_llm_service
        
        logger.info("✅ 服务导入成功")
        
        # 1. 测试工具枚举
        logger.info("\n📋 测试工具枚举...")
        tools = tool_manager_service.enumerate_all_tools(use_cache=False)
        logger.info(f"   找到 {len(tools)} 个工具")
        
        for i, tool in enumerate(tools[:3]):
            logger.info(f"   {i+1}. {tool.get('name')} - {tool.get('description', '')[:50]}...")
        
        # 2. 测试工具相关性排序
        logger.info("\n🔍 测试工具相关性排序...")
        test_query = "我想了解最新的科技新闻"
        ranked_tools = tool_manager_service.rank_tools_by_relevance(test_query, top_k=3)
        
        logger.info(f"   查询: {test_query}")
        logger.info(f"   找到 {len(ranked_tools)} 个相关工具")
        
        for i, tool in enumerate(ranked_tools):
            score = tool.get('relevance_score', 0)
            logger.info(f"   {i+1}. {tool.get('name')} (相关性: {score:.3f})")
        
        # 3. 测试工具执行
        logger.info("\n⚙️ 测试工具执行...")
        from services.mcp_service import mcp_service
        
        result = mcp_service.execute_tool(
            "fetch_news",
            "builtin",
            category="technology",
            limit=2
        )
        
        if result.get("success"):
            logger.info("   ✅ 工具执行成功")
            news_count = len(result.get("result", {}).get("news", []))
            logger.info(f"   获取到 {news_count} 条新闻")
        else:
            logger.info(f"   ❌ 工具执行失败: {result.get('error')}")
        
        # 4. 测试增强LLM服务
        logger.info("\n🤖 测试增强LLM服务...")
        enhanced_llm = get_enhanced_llm_service("test_key", "test_endpoint")
        
        test_input = "帮我搜索最新的AI技术发展"
        should_use_tools = enhanced_llm._should_use_tools(test_input, [])
        logger.info(f"   输入: {test_input}")
        logger.info(f"   应该使用工具: {should_use_tools}")
        
        if should_use_tools:
            relevant_tools = enhanced_llm._get_relevant_tools_for_llm(test_input)
            logger.info(f"   选择了 {len(relevant_tools)} 个工具")
            
            for tool in relevant_tools:
                logger.info(f"   - {tool['function']['name']}")
        
        # 5. 测试统计信息
        logger.info("\n📊 测试统计信息...")
        stats = tool_manager_service.get_tool_statistics()
        logger.info(f"   总工具数: {stats.get('total_tools', 0)}")
        logger.info(f"   类别数: {len(stats.get('categories', {}))}")
        logger.info(f"   服务器数: {len(stats.get('servers', {}))}")
        
        logger.info("\n🎉 快速测试完成！所有功能正常运行。")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_api_endpoints():
    """测试API端点"""
    logger.info("\n🌐 测试API端点...")
    
    try:
        import requests
        import json
        
        # 假设服务运行在localhost:8000
        base_url = "http://localhost:8000"
        
        # 测试工具枚举端点
        logger.info("   测试 /api/tools/enumerate")
        try:
            response = requests.get(f"{base_url}/api/tools/enumerate", timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"   ✅ 成功获取 {data.get('total', 0)} 个工具")
            else:
                logger.info(f"   ❌ 请求失败: {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.info(f"   ⚠️ 无法连接到服务器 (这是正常的，如果服务器未运行)")
        
        # 测试工具排序端点
        logger.info("   测试 /api/tools/rank")
        try:
            payload = {"query": "搜索新闻", "top_k": 3}
            response = requests.post(
                f"{base_url}/api/tools/rank", 
                json=payload, 
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                logger.info(f"   ✅ 成功排序 {data.get('total', 0)} 个工具")
            else:
                logger.info(f"   ❌ 请求失败: {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.info(f"   ⚠️ 无法连接到服务器")
        
        logger.info("   💡 要测试API端点，请先启动服务器: python backend/main.py")
        
    except ImportError:
        logger.info("   ⚠️ requests库未安装，跳过API测试")
    except Exception as e:
        logger.error(f"   ❌ API测试失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    logger.info("\n📖 使用示例:")
    logger.info("="*50)
    
    examples = [
        {
            "title": "1. 启动服务器",
            "command": "python backend/main.py",
            "description": "启动包含工具调用功能的主服务器"
        },
        {
            "title": "2. 测试工具枚举",
            "command": "curl http://localhost:8000/api/tools/enumerate",
            "description": "获取所有可用工具列表"
        },
        {
            "title": "3. 测试工具排序",
            "command": 'curl -X POST http://localhost:8000/api/tools/rank -H "Content-Type: application/json" -d \'{"query": "搜索新闻", "top_k": 3}\'',
            "description": "根据查询对工具进行相关性排序"
        },
        {
            "title": "4. 测试增强对话",
            "command": 'curl -X POST http://localhost:8000/api/tools/enhanced-chat -H "Content-Type: application/json" -d \'{"user_input": "帮我搜索最新AI新闻", "use_tools": true}\'',
            "description": "使用工具增强的对话功能"
        },
        {
            "title": "5. 运行完整测试",
            "command": "python backend/test_tool_pipeline.py",
            "description": "运行完整的工具调用流程测试"
        }
    ]
    
    for example in examples:
        logger.info(f"\n{example['title']}")
        logger.info(f"   命令: {example['command']}")
        logger.info(f"   说明: {example['description']}")

async def main():
    """主函数"""
    logger.info("🔧 工具调用功能测试")
    logger.info("="*50)
    
    # 加载环境变量
    from dotenv import load_dotenv
    load_dotenv()
    
    # 检查关键环境变量
    dashscope_key = os.getenv("DASHSCOPE_API_KEY")
    if dashscope_key:
        logger.info(f"✅ DASHSCOPE_API_KEY已加载: {dashscope_key[:10]}...")
    else:
        logger.warning("⚠️ DASHSCOPE_API_KEY未设置")
    
    # 设置测试环境变量（如果没有的话）
    os.environ.setdefault("VOLCANO_API_KEY", "test_key")
    os.environ.setdefault("VOLCANO_ENDPOINT", "test_endpoint")
    
    # 运行快速测试
    success = await quick_tool_test()
    
    # 测试API端点
    await test_api_endpoints()
    
    # 显示使用示例
    show_usage_examples()
    
    if success:
        logger.info("\n🎉 工具调用功能测试完成！")
        logger.info("💡 现在可以启动服务器并测试API端点了。")
        return 0
    else:
        logger.error("\n❌ 测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)