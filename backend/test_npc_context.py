#!/usr/bin/env python3
"""
NPC信息调用和上下文存取测试
基于supabase的单元化测试，逐步排除错误
"""

import asyncio
import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加backend目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('npc_test.log')
    ]
)
logger = logging.getLogger(__name__)

class NPCContextTester:
    """NPC上下文测试器"""
    
    def __init__(self):
        self.supabase = None
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {},
            "summary": {}
        }
        
    async def initialize_supabase(self):
        """初始化Supabase连接"""
        try:
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_KEY")
            
            if not supabase_url or not supabase_key:
                logger.error("❌ Supabase环境变量未设置")
                return False
                
            self.supabase = create_client(supabase_url, supabase_key)
            logger.info("✅ Supabase客户端初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ Supabase初始化失败: {e}")
            return False
    
    async def test_database_connection(self):
        """测试数据库连接"""
        test_name = "database_connection"
        logger.info("🔌 测试数据库连接...")
        
        try:
            # 简单查询测试连接
            result = self.supabase.table("npcs").select("count", count="exact").limit(1).execute()
            
            self.test_results["tests"][test_name] = {
                "status": "success",
                "message": "数据库连接正常",
                "details": f"查询执行成功，返回count: {result.count}"
            }
            logger.info("✅ 数据库连接测试通过")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "failed",
                "message": f"数据库连接失败: {str(e)}",
                "details": str(e)
            }
            logger.error(f"❌ 数据库连接测试失败: {e}")
            return False
    
    async def test_npc_table_structure(self):
        """测试NPC表结构"""
        test_name = "npc_table_structure"
        logger.info("🏗️ 测试NPC表结构...")
        
        try:
            # 查询NPC表的所有列
            result = self.supabase.table("npcs").select("*").limit(1).execute()
            
            if not result.data:
                # 表为空，尝试获取表结构信息
                logger.warning("⚠️ NPC表为空，检查表是否存在...")
                
                # 尝试插入测试数据来验证表结构
                test_npc = {
                    "name": "测试NPC",
                    "description": "用于测试的NPC",
                    "system_prompt": "你是一个测试NPC",
                    "is_active": True
                }
                
                insert_result = self.supabase.table("npcs").insert(test_npc).execute()
                
                if insert_result.data:
                    logger.info("✅ 成功插入测试NPC，表结构正常")
                    # 删除测试数据
                    self.supabase.table("npcs").delete().eq("name", "测试NPC").execute()
                    
                    self.test_results["tests"][test_name] = {
                        "status": "success",
                        "message": "NPC表结构正常",
                        "details": "成功插入和删除测试数据"
                    }
                    return True
            else:
                # 表有数据，检查字段
                npc_data = result.data[0]
                required_fields = ["id", "name", "description", "system_prompt", "is_active"]
                missing_fields = [field for field in required_fields if field not in npc_data]
                
                if missing_fields:
                    self.test_results["tests"][test_name] = {
                        "status": "failed",
                        "message": f"NPC表缺少必要字段: {missing_fields}",
                        "details": f"当前字段: {list(npc_data.keys())}"
                    }
                    logger.error(f"❌ NPC表缺少字段: {missing_fields}")
                    return False
                else:
                    self.test_results["tests"][test_name] = {
                        "status": "success",
                        "message": "NPC表结构完整",
                        "details": f"包含字段: {list(npc_data.keys())}"
                    }
                    logger.info("✅ NPC表结构测试通过")
                    return True
                    
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "failed",
                "message": f"NPC表结构测试失败: {str(e)}",
                "details": str(e)
            }
            logger.error(f"❌ NPC表结构测试失败: {e}")
            return False
    
    async def test_get_npc_by_id(self):
        """测试根据ID获取NPC"""
        test_name = "get_npc_by_id"
        logger.info("🎭 测试获取NPC信息...")
        
        try:
            # 首先确保有测试数据
            await self.ensure_test_npcs()
            
            # 获取第一个NPC
            result = self.supabase.table("npcs").select("*").eq("is_active", True).limit(1).execute()
            
            if not result.data:
                self.test_results["tests"][test_name] = {
                    "status": "failed",
                    "message": "没有找到活跃的NPC",
                    "details": "数据库中没有is_active=True的NPC记录"
                }
                logger.error("❌ 没有找到活跃的NPC")
                return False
            
            npc = result.data[0]
            npc_id = npc["id"]
            
            # 测试根据ID获取NPC
            npc_result = self.supabase.table("npcs").select("*").eq("id", npc_id).execute()
            
            if npc_result.data:
                retrieved_npc = npc_result.data[0]
                self.test_results["tests"][test_name] = {
                    "status": "success",
                    "message": f"成功获取NPC: {retrieved_npc['name']}",
                    "details": {
                        "npc_id": npc_id,
                        "npc_name": retrieved_npc["name"],
                        "has_system_prompt": bool(retrieved_npc.get("system_prompt")),
                        "is_active": retrieved_npc.get("is_active")
                    }
                }
                logger.info(f"✅ 成功获取NPC: {retrieved_npc['name']}")
                return True
            else:
                self.test_results["tests"][test_name] = {
                    "status": "failed",
                    "message": f"无法根据ID {npc_id} 获取NPC",
                    "details": "查询返回空结果"
                }
                logger.error(f"❌ 无法根据ID {npc_id} 获取NPC")
                return False
                
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "failed",
                "message": f"获取NPC测试失败: {str(e)}",
                "details": str(e)
            }
            logger.error(f"❌ 获取NPC测试失败: {e}")
            return False
    
    async def test_conversation_session_creation(self):
        """测试会话创建"""
        test_name = "conversation_session_creation"
        logger.info("💬 测试会话创建...")
        
        try:
            # 确保有测试用户和NPC
            await self.ensure_test_users()
            await self.ensure_test_npcs()
            
            # 获取测试用户和NPC
            user_result = self.supabase.table("users").select("*").limit(1).execute()
            npc_result = self.supabase.table("npcs").select("*").eq("is_active", True).limit(1).execute()
            
            if not user_result.data or not npc_result.data:
                self.test_results["tests"][test_name] = {
                    "status": "failed",
                    "message": "缺少测试用户或NPC数据",
                    "details": f"用户数: {len(user_result.data) if user_result.data else 0}, NPC数: {len(npc_result.data) if npc_result.data else 0}"
                }
                logger.error("❌ 缺少测试用户或NPC数据")
                return False
            
            user_id = user_result.data[0]["id"]
            npc_id = npc_result.data[0]["id"]
            
            # 创建会话
            session_data = {
                "user_id": user_id,
                "npc_id": npc_id,
                "is_active": True
            }
            
            session_result = self.supabase.table("conversation_sessions").insert(session_data).execute()
            
            if session_result.data:
                session = session_result.data[0]
                session_id = session["id"]
                
                self.test_results["tests"][test_name] = {
                    "status": "success",
                    "message": f"成功创建会话: {session_id}",
                    "details": {
                        "session_id": session_id,
                        "user_id": user_id,
                        "npc_id": npc_id,
                        "is_active": session.get("is_active")
                    }
                }
                logger.info(f"✅ 成功创建会话: {session_id}")
                
                # 清理测试数据
                self.supabase.table("conversation_sessions").delete().eq("id", session_id).execute()
                return True
            else:
                self.test_results["tests"][test_name] = {
                    "status": "failed",
                    "message": "会话创建失败",
                    "details": "插入操作返回空结果"
                }
                logger.error("❌ 会话创建失败")
                return False
                
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "failed",
                "message": f"会话创建测试失败: {str(e)}",
                "details": str(e)
            }
            logger.error(f"❌ 会话创建测试失败: {e}")
            return False
    
    async def test_message_storage(self):
        """测试消息存储"""
        test_name = "message_storage"
        logger.info("📝 测试消息存储...")
        
        try:
            # 创建临时会话用于测试
            await self.ensure_test_users()
            await self.ensure_test_npcs()
            
            user_result = self.supabase.table("users").select("*").limit(1).execute()
            npc_result = self.supabase.table("npcs").select("*").eq("is_active", True).limit(1).execute()
            
            user_id = user_result.data[0]["id"]
            npc_id = npc_result.data[0]["id"]
            
            # 创建临时会话
            session_data = {
                "user_id": user_id,
                "npc_id": npc_id,
                "is_active": True
            }
            session_result = self.supabase.table("conversation_sessions").insert(session_data).execute()
            session_id = session_result.data[0]["id"]
            
            # 测试存储用户消息
            user_message = {
                "session_id": session_id,
                "role": "user",
                "content": "这是一条测试消息"
            }
            
            user_msg_result = self.supabase.table("conversation_messages").insert(user_message).execute()
            
            # 测试存储助手消息
            assistant_message = {
                "session_id": session_id,
                "role": "assistant",
                "content": "这是助手的回复",
                "emotion": "neutral",
                "speed": 1.0
            }
            
            assistant_msg_result = self.supabase.table("conversation_messages").insert(assistant_message).execute()
            
            if user_msg_result.data and assistant_msg_result.data:
                # 验证消息检索
                messages_result = self.supabase.table("conversation_messages").select("*").eq("session_id", session_id).order("created_at").execute()
                
                if len(messages_result.data) == 2:
                    self.test_results["tests"][test_name] = {
                        "status": "success",
                        "message": "消息存储和检索成功",
                        "details": {
                            "session_id": session_id,
                            "messages_count": len(messages_result.data),
                            "user_message": messages_result.data[0]["content"],
                            "assistant_message": messages_result.data[1]["content"]
                        }
                    }
                    logger.info("✅ 消息存储测试通过")
                    
                    # 清理测试数据
                    self.supabase.table("conversation_messages").delete().eq("session_id", session_id).execute()
                    self.supabase.table("conversation_sessions").delete().eq("id", session_id).execute()
                    return True
                else:
                    self.test_results["tests"][test_name] = {
                        "status": "failed",
                        "message": f"消息数量不匹配，期望2条，实际{len(messages_result.data)}条",
                        "details": messages_result.data
                    }
                    logger.error("❌ 消息数量不匹配")
                    return False
            else:
                self.test_results["tests"][test_name] = {
                    "status": "failed",
                    "message": "消息插入失败",
                    "details": "用户消息或助手消息插入失败"
                }
                logger.error("❌ 消息插入失败")
                return False
                
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "failed",
                "message": f"消息存储测试失败: {str(e)}",
                "details": str(e)
            }
            logger.error(f"❌ 消息存储测试失败: {e}")
            return False
    
    async def ensure_test_users(self):
        """确保有测试用户"""
        try:
            result = self.supabase.table("users").select("*").limit(1).execute()
            if not result.data:
                # 创建测试用户
                test_user = {
                    "username": "test_user",
                    "email": "<EMAIL>",
                    "nickname": "测试用户",
                    "is_active": True
                }
                self.supabase.table("users").insert(test_user).execute()
                logger.info("✅ 创建测试用户")
        except Exception as e:
            logger.error(f"❌ 创建测试用户失败: {e}")
    
    async def ensure_test_npcs(self):
        """确保有测试NPC"""
        try:
            result = self.supabase.table("npcs").select("*").eq("is_active", True).limit(1).execute()
            if not result.data:
                # 创建测试NPC
                test_npc = {
                    "name": "测试助手",
                    "description": "用于测试的AI助手",
                    "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。",
                    "is_active": True
                }
                self.supabase.table("npcs").insert(test_npc).execute()
                logger.info("✅ 创建测试NPC")
        except Exception as e:
            logger.error(f"❌ 创建测试NPC失败: {e}")
    
    async def test_context_retrieval(self):
        """测试上下文检索"""
        test_name = "context_retrieval"
        logger.info("🔍 测试上下文检索...")
        
        try:
            # 创建完整的测试场景
            await self.ensure_test_users()
            await self.ensure_test_npcs()
            
            user_result = self.supabase.table("users").select("*").limit(1).execute()
            npc_result = self.supabase.table("npcs").select("*").eq("is_active", True).limit(1).execute()
            
            user_id = user_result.data[0]["id"]
            npc_id = npc_result.data[0]["id"]
            
            # 创建会话
            session_data = {
                "user_id": user_id,
                "npc_id": npc_id,
                "is_active": True
            }
            session_result = self.supabase.table("conversation_sessions").insert(session_data).execute()
            session_id = session_result.data[0]["id"]
            
            # 添加多条历史消息
            messages = [
                {"session_id": session_id, "role": "user", "content": "你好"},
                {"session_id": session_id, "role": "assistant", "content": "你好！很高兴见到你。"},
                {"session_id": session_id, "role": "user", "content": "今天天气怎么样？"},
                {"session_id": session_id, "role": "assistant", "content": "我无法获取实时天气信息，建议你查看天气应用。"}
            ]
            
            for msg in messages:
                self.supabase.table("conversation_messages").insert(msg).execute()
            
            # 测试上下文检索
            context_result = self.supabase.table("conversation_messages").select("*").eq("session_id", session_id).order("created_at").execute()
            
            if len(context_result.data) == 4:
                # 验证消息顺序和内容
                retrieved_messages = context_result.data
                
                self.test_results["tests"][test_name] = {
                    "status": "success",
                    "message": "上下文检索成功",
                    "details": {
                        "session_id": session_id,
                        "messages_count": len(retrieved_messages),
                        "conversation_flow": [
                            f"{msg['role']}: {msg['content']}" 
                            for msg in retrieved_messages
                        ]
                    }
                }
                logger.info("✅ 上下文检索测试通过")
                
                # 清理测试数据
                self.supabase.table("conversation_messages").delete().eq("session_id", session_id).execute()
                self.supabase.table("conversation_sessions").delete().eq("id", session_id).execute()
                return True
            else:
                self.test_results["tests"][test_name] = {
                    "status": "failed",
                    "message": f"上下文消息数量不匹配，期望4条，实际{len(context_result.data)}条",
                    "details": context_result.data
                }
                logger.error("❌ 上下文消息数量不匹配")
                return False
                
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "failed",
                "message": f"上下文检索测试失败: {str(e)}",
                "details": str(e)
            }
            logger.error(f"❌ 上下文检索测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始NPC上下文测试...")
        
        # 初始化Supabase
        if not await self.initialize_supabase():
            logger.error("❌ Supabase初始化失败，无法继续测试")
            return False
        
        # 运行测试序列
        tests = [
            ("数据库连接", self.test_database_connection),
            ("NPC表结构", self.test_npc_table_structure),
            ("获取NPC信息", self.test_get_npc_by_id),
            ("会话创建", self.test_conversation_session_creation),
            ("消息存储", self.test_message_storage),
            ("上下文检索", self.test_context_retrieval)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 执行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if await test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    failed += 1
                    logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                failed += 1
                logger.error(f"❌ {test_name} - 异常: {e}")
        
        # 生成测试总结
        self.test_results["summary"] = {
            "total_tests": len(tests),
            "passed": passed,
            "failed": failed,
            "success_rate": f"{(passed/len(tests)*100):.1f}%"
        }
        
        logger.info(f"\n{'='*60}")
        logger.info("📊 测试总结")
        logger.info(f"{'='*60}")
        logger.info(f"总测试数: {len(tests)}")
        logger.info(f"通过: {passed}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {(passed/len(tests)*100):.1f}%")
        
        # 保存测试结果
        with open("npc_context_test_results.json", "w", encoding="utf-8") as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        logger.info("📄 测试结果已保存到 npc_context_test_results.json")
        
        return failed == 0

async def main():
    """主函数"""
    tester = NPCContextTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有测试通过！")
        return 0
    else:
        logger.error("💥 部分测试失败，请检查日志")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)