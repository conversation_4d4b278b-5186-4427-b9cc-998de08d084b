#!/usr/bin/env python3
"""
测试真实的amap和didi MCP服务
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.mcp_client import SimpleMCPClient
from services.mcp_service import MCPService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_amap_service():
    """测试amap服务"""
    print("🗺️ 测试高德地图MCP服务...")
    
    mcp_client = SimpleMCPClient()
    server_name = "amap-maps-streamableHTTP"
    
    if server_name not in mcp_client.server_configs:
        print(f"❌ 未找到 {server_name} 服务配置")
        return
    
    # 启动服务器
    success = await mcp_client.start_server(server_name)
    print(f"连接结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        # 获取工具列表
        tools = await mcp_client.get_server_tools(server_name)
        print(f"📋 获取到 {len(tools)} 个工具:")
        for i, tool in enumerate(tools[:5]):  # 只显示前5个工具
            print(f"  {i+1}. {tool['name']}: {tool['description'][:50]}...")
        
        if len(tools) > 5:
            print(f"  ... 还有 {len(tools) - 5} 个工具")
        
        # 测试天气查询工具
        weather_tool = next((tool for tool in tools if tool['name'] == 'maps_weather'), None)
        if weather_tool:
            print(f"\n🌤️ 测试天气查询工具...")
            result = await mcp_client.execute_tool(
                'maps_weather', 
                server_name, 
                city="北京"
            )
            print(f"查询结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
            if result.get('success'):
                content = result.get('result', {}).get('content', '')
                print(f"天气信息: {content[:200]}...")
            else:
                print(f"错误信息: {result.get('error')}")
        
        # 测试地址解析工具
        geo_tool = next((tool for tool in tools if tool['name'] == 'maps_geo'), None)
        if geo_tool:
            print(f"\n📍 测试地址解析工具...")
            result = await mcp_client.execute_tool(
                'maps_geo', 
                server_name, 
                address="北京市朝阳区三里屯",
                city="北京"
            )
            print(f"解析结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
            if result.get('success'):
                content = result.get('result', {}).get('content', '')
                print(f"坐标信息: {content[:200]}...")
            else:
                print(f"错误信息: {result.get('error')}")
    
    await mcp_client.stop_server(server_name)
    await mcp_client.cleanup()

async def test_didi_service():
    """测试didi服务"""
    print("\n🚗 测试滴滴出行MCP服务...")
    
    mcp_client = SimpleMCPClient()
    server_name = "didi-streamableHTTP"
    
    if server_name not in mcp_client.server_configs:
        print(f"❌ 未找到 {server_name} 服务配置")
        return
    
    # 启动服务器
    success = await mcp_client.start_server(server_name)
    print(f"连接结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        # 获取工具列表
        tools = await mcp_client.get_server_tools(server_name)
        print(f"📋 获取到 {len(tools)} 个工具:")
        for i, tool in enumerate(tools):
            print(f"  {i+1}. {tool['name']}: {tool['description'][:50]}...")
        
        # 测试地图搜索工具
        search_tool = next((tool for tool in tools if tool['name'] == 'maps_textsearch'), None)
        if search_tool:
            print(f"\n🔍 测试地图搜索工具...")
            result = await mcp_client.execute_tool(
                'maps_textsearch', 
                server_name, 
                keywords="北京大学",
                city="北京"
            )
            print(f"搜索结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
            if result.get('success'):
                content = result.get('result', {}).get('content', '')
                print(f"搜索信息: {content[:200]}...")
            else:
                print(f"错误信息: {result.get('error')}")
    
    await mcp_client.stop_server(server_name)
    await mcp_client.cleanup()

async def test_integration_with_enhanced_llm():
    """测试与增强LLM服务的集成"""
    print("\n🧠 测试与增强LLM服务的集成...")
    
    mcp_service = MCPService()
    
    # 获取所有工具
    all_tools = await mcp_service.get_all_tools_for_reranker_async()
    print(f"📋 总共获取到 {len(all_tools)} 个工具")
    
    # 查找新增的服务工具
    amap_tools = [tool for tool in all_tools if tool.get('server') == 'amap-maps-streamableHTTP']
    didi_tools = [tool for tool in all_tools if tool.get('server') == 'didi-streamableHTTP']
    
    print(f"🗺️ 高德地图工具数量: {len(amap_tools)}")
    print(f"🚗 滴滴出行工具数量: {len(didi_tools)}")
    
    # 测试高德地图工具执行
    if amap_tools:
        weather_tool = next((tool for tool in amap_tools if tool['name'] == 'maps_weather'), None)
        if weather_tool:
            print(f"\n🌤️ 通过MCP服务测试天气查询...")
            try:
                result = await mcp_service.execute_tool_async(
                    'maps_weather', 
                    'amap-maps-streamableHTTP',
                    city="上海"
                )
                print(f"执行结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
                if result.get('success'):
                    content = result.get('result', {}).get('content', '')
                    print(f"天气信息: {content[:200]}...")
            except Exception as e:
                print(f"执行失败: {e}")
    
    # 测试滴滴工具执行
    if didi_tools:
        search_tool = next((tool for tool in didi_tools if tool['name'] == 'maps_textsearch'), None)
        if search_tool:
            print(f"\n🔍 通过MCP服务测试地图搜索...")
            try:
                result = await mcp_service.execute_tool_async(
                    'maps_textsearch', 
                    'didi-streamableHTTP',
                    keywords="清华大学",
                    city="北京"
                )
                print(f"执行结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
                if result.get('success'):
                    content = result.get('result', {}).get('content', '')
                    print(f"搜索信息: {content[:200]}...")
            except Exception as e:
                print(f"执行失败: {e}")

async def main():
    """主函数"""
    try:
        # 测试amap服务
        await test_amap_service()
        
        # 测试didi服务
        await test_didi_service()
        
        # 测试与增强LLM服务的集成
        await test_integration_with_enhanced_llm()
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
