#!/usr/bin/env python3
"""
详细调试howtocook-mcp服务器
"""

import subprocess
import json
import time
import logging
import select
import sys

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def detailed_debug_howtocook():
    """详细调试howtocook-mcp"""
    print("🔍 详细调试howtocook-mcp服务器...")
    
    try:
        # 启动服务器进程
        command = ["npx", "-y", "howtocook-mcp"]
        print(f"📝 启动命令: {' '.join(command)}")
        
        process = subprocess.Popen(
            command,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        print(f"✅ 进程启动成功，PID: {process.pid}")
        
        # 等待服务器初始化
        time.sleep(2)
        
        # 检查进程状态
        if process.poll() is not None:
            print(f"❌ 进程已退出，返回码: {process.returncode}")
            stderr_output = process.stderr.read()
            if stderr_output:
                print(f"错误输出: {stderr_output}")
            return
        
        # 读取并跳过启动消息
        print("📥 读取启动消息...")
        startup_messages = []
        for i in range(5):  # 最多读取5行启动消息
            ready, _, _ = select.select([process.stdout], [], [], 0.5)
            if not ready:
                break
            
            line = process.stdout.readline()
            if not line:
                break
                
            startup_messages.append(line.strip())
            print(f"启动消息 {i+1}: {repr(line.strip())}")
            
            # 检查是否是JSON响应
            try:
                json.loads(line.strip())
                print(f"发现JSON响应，停止读取启动消息")
                break
            except json.JSONDecodeError:
                continue
        
        # 发送initialize请求
        print("\n📤 发送initialize请求...")
        init_request = {
            "jsonrpc": "2.0",
            "id": 0,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "clientInfo": {
                    "name": "debug-client",
                    "version": "1.0.0"
                }
            }
        }
        
        init_json = json.dumps(init_request) + "\n"
        print(f"发送内容: {init_json.strip()}")
        process.stdin.write(init_json)
        process.stdin.flush()
        
        # 读取initialize响应
        print("📥 等待initialize响应...")
        init_response = process.stdout.readline()
        print(f"收到响应: {repr(init_response)}")
        
        if init_response.strip():
            try:
                init_data = json.loads(init_response.strip())
                print(f"✅ Initialize响应解析成功:")
                print(json.dumps(init_data, indent=2, ensure_ascii=False))
            except json.JSONDecodeError as e:
                print(f"❌ Initialize响应JSON解析失败: {e}")
                return
        else:
            print("❌ Initialize响应为空")
            return
        
        # 发送initialized通知
        print("\n📤 发送initialized通知...")
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized",
            "params": {}
        }
        
        notification_json = json.dumps(initialized_notification) + "\n"
        print(f"发送内容: {notification_json.strip()}")
        process.stdin.write(notification_json)
        process.stdin.flush()
        
        # 等待一下
        time.sleep(0.5)
        
        # 发送tools/list请求
        print("\n📤 发送tools/list请求...")
        tools_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list",
            "params": {}
        }
        
        tools_json = json.dumps(tools_request) + "\n"
        print(f"发送内容: {tools_json.strip()}")
        process.stdin.write(tools_json)
        process.stdin.flush()
        
        # 读取tools/list响应
        print("📥 等待tools/list响应...")
        tools_response = process.stdout.readline()
        print(f"收到响应: {repr(tools_response)}")
        
        if tools_response.strip():
            try:
                tools_data = json.loads(tools_response.strip())
                print(f"✅ Tools/list响应解析成功:")
                print(json.dumps(tools_data, indent=2, ensure_ascii=False))
                
                # 分析响应内容
                if "result" in tools_data:
                    result = tools_data["result"]
                    if "tools" in result:
                        tools = result["tools"]
                        print(f"\n🔧 找到 {len(tools)} 个工具:")
                        for i, tool in enumerate(tools):
                            print(f"  {i+1}. {tool.get('name', 'Unknown')}")
                            print(f"     描述: {tool.get('description', 'No description')}")
                            print(f"     输入: {tool.get('inputSchema', {})}")
                    else:
                        print("⚠️ 响应中没有'tools'字段")
                        print(f"可用字段: {list(result.keys())}")
                else:
                    print("⚠️ 响应中没有'result'字段")
                    if "error" in tools_data:
                        print(f"错误信息: {tools_data['error']}")
                        
            except json.JSONDecodeError as e:
                print(f"❌ Tools/list响应JSON解析失败: {e}")
        else:
            print("❌ Tools/list响应为空")
        
        # 检查是否还有其他响应
        print("\n📋 检查是否有其他响应...")
        for i in range(3):
            ready, _, _ = select.select([process.stdout], [], [], 0.5)
            if ready:
                extra_response = process.stdout.readline()
                if extra_response.strip():
                    print(f"额外响应 {i+1}: {repr(extra_response.strip())}")
            else:
                break
        
        # 检查stderr
        print("\n📋 检查错误输出...")
        ready, _, _ = select.select([process.stderr], [], [], 0.1)
        if ready:
            stderr_data = process.stderr.read()
            if stderr_data:
                print(f"错误输出: {stderr_data}")
        
        # 清理
        print("\n🧹 清理进程...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        
        print("✅ 详细调试完成")
        
    except Exception as e:
        print(f"❌ 详细调试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🐛 HowToCook MCP详细调试")
    print("=" * 60)
    
    detailed_debug_howtocook()
    
    print("\n" + "=" * 60)
    print("🎯 分析建议:")
    print("1. 检查服务器是否正确实现tools/list方法")
    print("2. 确认服务器版本和协议兼容性")
    print("3. 查看服务器文档了解正确的调用方式")
    print("4. 检查是否需要特定的参数或配置")

if __name__ == "__main__":
    main()
