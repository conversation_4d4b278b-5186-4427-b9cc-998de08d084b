{"timestamp": "2025-08-11T11:28:06.237651", "diagnostics": {"environment_variables": {"missing": [], "present": {"SUPABASE_URL": "http://8.1...", "SUPABASE_KEY": "eyJ0eXAiOi..."}, "status": "success"}, "proxy_settings": {"detected_proxies": {}, "has_proxy": false}, "basic_connectivity": {"https://www.google.com": {"status": "success", "status_code": 200, "response_time": 0.809181}, "https://httpbin.org/get": {"status": "success", "status_code": 200, "response_time": 1.808845}, "https://api.github.com": {"status": "success", "status_code": 200, "response_time": 0.73872}}, "supabase_base_url": {"status": "success", "status_code": 401, "accessible": true}, "supabase_rest_api": {"status": "failed", "status_code": 401, "response_size": 52}, "supabase_tables": {"npcs": {"status": "auth_failed", "status_code": 401}, "users": {"status": "auth_failed", "status_code": 401}, "conversation_sessions": {"status": "auth_failed", "status_code": 401}, "conversation_messages": {"status": "auth_failed", "status_code": 401}}, "proxy_bypass_commands": {"临时禁用代理 (当前会话)": ["unset HTTP_PROXY", "unset HTTPS_PROXY", "unset http_proxy", "unset https_proxy", "unset ALL_PROXY"], "使用直连运行Python脚本": ["env -u HTTP_PROXY -u HTTPS_PROXY -u http_proxy -u https_proxy python test_npc_functions.py"], "设置代理白名单 (如果使用代理)": ["export NO_PROXY=localhost,127.0.0.1,*************", "export no_proxy=localhost,127.0.0.1,*************"]}}, "recommendations": ["Supabase API密钥认证失败，请检查SUPABASE_KEY是否正确", "以下表认证失败: npcs, users, conversation_sessions, conversation_messages，请检查API密钥权限", "没有可访问的表，请检查数据库配置和权限设置"], "summary": {"total_tests": 6, "passed": 4, "failed": 2, "success_rate": "66.7%"}}