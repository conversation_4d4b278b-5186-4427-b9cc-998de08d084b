# main.py 工具调用集成说明

## 概述

已成功将工具调用功能集成到 `main.py` 中，现在主应用支持：

1. **WebSocket 实时工具调用** - 在语音对话中自动调用工具
2. **API 端点工具调用** - 通过REST API测试工具调用
3. **降级机制** - 工具调用失败时自动降级到普通LLM

## 🔧 集成的功能

### 1. WebSocket 工具调用
- **位置**: `process_complete_audio_data()` 函数
- **触发**: 用户语音输入被转录后
- **流程**: 
  1. ASR转录用户语音
  2. 增强LLM服务判断是否需要工具
  3. 自动调用相关工具
  4. 将工具结果集成到响应中
  5. TTS合成语音回复

### 2. 新增API端点
- **端点**: `POST /api/test-tool-calling`
- **功能**: 测试工具调用功能
- **参数**:
  ```json
  {
    "user_input": "BJP 到 SHH 2025-04-15的火车票有哪些",
    "npc_id": 1
  }
  ```

### 3. 工具信息推送
- **WebSocket消息类型**: `tools_info`
- **内容**: 包含使用的工具列表和调用次数
- **时机**: 工具调用完成后发送给客户端

## 🚀 使用方法

### 启动服务器
```bash
cd backend
python main.py
```

### 测试工具调用
```bash
# 测试main.py集成
python backend/test_main_tool_integration.py

# 或者直接调用API
curl -X POST http://localhost:8000/api/test-tool-calling \
  -H "Content-Type: application/json" \
  -d '{"user_input": "BJP 到 SHH 2025-04-15的火车票有哪些", "npc_id": 1}'
```

### WebSocket 使用
客户端连接到 `ws://localhost:8000/ws/{user_id}` 后：

1. **发送语音数据**:
   ```json
   {
     "type": "audio_complete",
     "data": "base64_encoded_audio_data"
   }
   ```

2. **接收工具信息**:
   ```json
   {
     "type": "tools_info",
     "tools_used": ["get-tickets"],
     "tool_calls_made": 1
   }
   ```

3. **接收语音回复**:
   ```json
   {
     "type": "audio_chunk",
     "data": "base64_encoded_audio_data"
   }
   ```

## 📋 支持的工具

### 1. get-tickets (火车票查询)
- **描述**: 查询火车票信息，包括车次、时间、价格等
- **参数**:
  - `from_station`: 出发站点
  - `to_station`: 到达站点  
  - `date`: 出发日期
  - `train_type`: 列车类型（可选）

### 2. search_and_summarize (搜索总结)
- **描述**: 搜索网络信息并生成摘要
- **参数**:
  - `query`: 搜索查询
  - `max_results`: 最大结果数

### 3. fetch_news (新闻获取)
- **描述**: 获取最新新闻资讯
- **参数**:
  - `category`: 新闻类别
  - `limit`: 返回数量

## 🔄 工作流程

### WebSocket 工具调用流程
```
用户语音输入 
    ↓
ASR转录文本
    ↓
增强LLM服务判断是否需要工具
    ↓
[需要工具] → 工具相关性排序 → 工具执行 → 结果集成
    ↓
生成包含工具结果的回复
    ↓
TTS合成语音
    ↓
发送给客户端
```

### 降级机制
```
增强LLM工具调用
    ↓
[失败] → 普通LLM流式处理
    ↓
正常语音回复
```

## 🛠️ 技术细节

### 关键修改点

1. **LLM调用替换**:
   ```python
   # 原来
   async for chunk in llm_service.generate_response_stream(...)
   
   # 现在
   enhanced_response = await enhanced_llm_service.generate_response_with_tools(...)
   ```

2. **新增函数**:
   - `process_llm_chunk()` - 处理LLM响应
   - `process_regular_llm_stream()` - 降级处理

3. **WebSocket消息扩展**:
   - 新增 `tools_info` 消息类型
   - 包含工具使用信息

### 错误处理
- **工具调用失败**: 自动降级到普通LLM
- **网络超时**: 重试机制和错误提示
- **WebSocket断开**: 优雅处理连接中断

## 📊 测试结果

运行 `test_main_tool_integration.py` 的预期结果：

```
🧪 测试main.py中的工具调用集成
==================================================
✅ 服务器已启动

🔍 测试 1: 12306火车票查询
   输入: BJP 到 SHH 2025-04-15的火车票有哪些
   ✅ 测试成功
   🔧 使用工具: ['get-tickets']
   📞 工具调用次数: 1
   🗣️ 响应: 根据查询，2025年4月15日从北京南站（BJP）到上海虹桥站（SHH）的火车票信息如下...
   😊 情感: neutral
   ⚡ 语速: 1.0

==================================================
测试总结
==================================================
总测试数: 3
成功测试: 3
失败测试: 0
成功率: 100.0%

🎉 所有测试通过！main.py中的工具调用功能正常工作。
```

## 🎯 使用场景

### 1. 语音助手
用户通过语音询问火车票信息，系统自动调用工具查询并语音回复。

### 2. 智能客服
在对话中自动识别用户需求，调用相应工具提供准确信息。

### 3. 多模态交互
结合语音、文本和工具调用，提供丰富的交互体验。

## 🔮 扩展性

### 添加新工具
1. 在 `mcp_service.py` 中添加工具定义
2. 在 `tool_manager_service.py` 中添加参数配置
3. 系统自动识别和使用新工具

### 自定义工具调用逻辑
可以修改 `enhanced_llm_service.py` 中的工具选择和执行逻辑。

---

**🎉 工具调用功能已完全集成到main.py中，现在可以在实际应用中使用了！**