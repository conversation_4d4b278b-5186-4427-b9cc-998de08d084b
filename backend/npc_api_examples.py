#!/usr/bin/env python3
"""
NPC API使用示例
展示如何使用新的NPC相关端点
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def example_get_npcs():
    """示例：获取NPCs列表"""
    print("\n" + "="*50)
    print("📋 示例：获取NPCs列表")
    print("="*50)
    
    response = requests.get(f"{BASE_URL}/npcs")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 成功获取 {data.get('count', 0)} 个NPC")
        print(f"数据源: {data.get('source', 'unknown')}")
        
        for npc in data.get('npcs', []):
            print(f"  - {npc.get('name')}: {npc.get('description', 'No description')}")
        
        return data.get('npcs', [])
    else:
        print(f"❌ 获取失败: {response.status_code}")
        return []

def example_get_npc_detail(npc_id):
    """示例：获取NPC详情"""
    print("\n" + "="*50)
    print(f"🔍 示例：获取NPC详情 (ID: {npc_id})")
    print("="*50)
    
    response = requests.get(f"{BASE_URL}/npcs/{npc_id}")
    
    if response.status_code == 200:
        data = response.json()
        npc = data.get('npc', {})
        
        print(f"✅ NPC详情:")
        print(f"  名称: {npc.get('name', 'Unknown')}")
        print(f"  描述: {npc.get('description', 'No description')}")
        print(f"  活跃状态: {npc.get('is_active', False)}")
        print(f"  系统提示词长度: {len(npc.get('system_prompt', ''))} 字符")
        print(f"  有persona数据: {bool(npc.get('persona_data'))}")
        
        return npc
    else:
        print(f"❌ 获取失败: {response.status_code}")
        return None

def example_get_npc_persona(npc_id):
    """示例：获取NPC persona数据"""
    print("\n" + "="*50)
    print(f"👤 示例：获取NPC persona (ID: {npc_id})")
    print("="*50)
    
    response = requests.get(f"{BASE_URL}/npcs/{npc_id}/persona")
    
    if response.status_code == 200:
        data = response.json()
        persona = data.get('persona', {})
        
        print(f"✅ NPC Persona:")
        print(f"  名称: {data.get('npc_name', 'Unknown')}")
        print(f"  年龄: {persona.get('age', 'Unknown')}")
        print(f"  性别: {persona.get('gender', 'Unknown')}")
        print(f"  职业: {persona.get('occupation', 'Unknown')}")
        print(f"  性格: {persona.get('personality', 'Unknown')}")
        print(f"  教育背景: {persona.get('education', 'Unknown')}")
        print(f"  兴趣爱好: {', '.join(persona.get('hobbies', []))}")
        print(f"  生成的描述: {data.get('generated_description', 'No description')}")
        
        return persona
    elif response.status_code == 404:
        print("⚠️ 该NPC没有persona数据")
        return None
    else:
        print(f"❌ 获取失败: {response.status_code}")
        return None

def example_create_chat_session(npc_id, user_id=14):
    """示例：创建聊天会话"""
    print("\n" + "="*50)
    print(f"💬 示例：创建聊天会话 (NPC: {npc_id}, User: {user_id})")
    print("="*50)
    
    response = requests.post(f"{BASE_URL}/npcs/{npc_id}/chat", params={"user_id": user_id})
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"✅ 聊天会话创建成功:")
        print(f"  会话ID: {data.get('session_id')}")
        print(f"  NPC: {data.get('npc_name', 'Unknown')}")
        print(f"  用户ID: {data.get('user_id')}")
        print(f"  创建时间: {data.get('created_at')}")
        
        return data.get('session_id')
    else:
        print(f"❌ 创建失败: {response.status_code}")
        print(f"错误信息: {response.text}")
        return None

def example_add_message(session_id, role="user", content="你好，请介绍一下自己"):
    """示例：添加消息到会话"""
    print("\n" + "="*50)
    print(f"📝 示例：添加消息到会话 (Session: {session_id})")
    print("="*50)
    
    params = {
        "role": role,
        "content": content,
        "emotion": "neutral",
        "speed": 1.0
    }
    
    response = requests.post(f"{BASE_URL}/sessions/{session_id}/messages", params=params)
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"✅ 消息添加成功:")
        print(f"  角色: {data.get('role')}")
        print(f"  内容: {data.get('content')}")
        print(f"  情感: {data.get('emotion')}")
        print(f"  语速: {data.get('speed')}")
        
        return True
    else:
        print(f"❌ 添加失败: {response.status_code}")
        print(f"错误信息: {response.text}")
        return False

def example_get_session_context(session_id):
    """示例：获取会话上下文"""
    print("\n" + "="*50)
    print(f"🔍 示例：获取会话上下文 (Session: {session_id})")
    print("="*50)
    
    response = requests.get(f"{BASE_URL}/sessions/{session_id}/context")
    
    if response.status_code == 200:
        data = response.json()
        messages = data.get('messages', [])
        npc = data.get('npc', {})
        
        print(f"✅ 会话上下文:")
        print(f"  会话ID: {data.get('session_id')}")
        print(f"  NPC: {npc.get('name', 'Unknown')}")
        print(f"  消息总数: {data.get('message_count', 0)}")
        print(f"  有NPC信息: {data.get('has_npc_info', False)}")
        
        print(f"\n📨 消息历史:")
        for i, msg in enumerate(messages[-5:], 1):  # 显示最后5条消息
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            content_preview = content[:50] + "..." if len(content) > 50 else content
            print(f"  {i}. {role}: {content_preview}")
        
        return data
    else:
        print(f"❌ 获取失败: {response.status_code}")
        return None

def example_check_cache_status():
    """示例：检查缓存状态"""
    print("\n" + "="*50)
    print("🗄️ 示例：检查NPC缓存状态")
    print("="*50)
    
    response = requests.get(f"{BASE_URL}/npcs/cache/status")
    
    if response.status_code == 200:
        data = response.json()
        cached_npcs = data.get('cached_npcs', {})
        
        print(f"✅ 缓存状态:")
        print(f"  缓存大小: {data.get('cache_size', 0)}")
        print(f"  TTL: {data.get('cache_ttl_seconds', 0)} 秒")
        
        if cached_npcs:
            print(f"\n📦 缓存的NPCs:")
            for npc_id, cache_info in cached_npcs.items():
                print(f"  - {cache_info.get('npc_name', 'Unknown')}: 缓存 {cache_info.get('cache_age_seconds', 0):.1f} 秒")
        else:
            print("  (缓存为空)")
        
        return data
    else:
        print(f"❌ 获取失败: {response.status_code}")
        return None

def run_complete_example():
    """运行完整的使用示例"""
    print("🚀 NPC API 完整使用示例")
    print("="*60)
    
    # 1. 获取NPCs列表
    npcs = example_get_npcs()
    if not npcs:
        print("❌ 无法获取NPCs，停止示例")
        return
    
    # 使用第一个NPC进行示例
    npc_id = npcs[0].get('id')
    print(f"\n🎯 使用NPC进行示例: {npc_id}")
    
    # 2. 获取NPC详情
    npc_detail = example_get_npc_detail(npc_id)
    
    # 3. 获取NPC persona
    persona = example_get_npc_persona(npc_id)
    
    # 4. 创建聊天会话
    session_id = example_create_chat_session(npc_id)
    
    if session_id:
        # 5. 添加用户消息
        example_add_message(session_id, "user", "你好，请介绍一下自己")
        
        # 6. 模拟添加NPC回复
        if persona:
            name = persona.get('name', '我')
            age = persona.get('age', '')
            occupation = persona.get('occupation', '')
            
            reply = f"你好！我是{name}"
            if age:
                reply += f"，{age}岁"
            if occupation:
                reply += f"，目前是{occupation}"
            reply += "。很高兴认识你！"
            
            example_add_message(session_id, "assistant", reply)
        
        # 7. 获取会话上下文
        example_get_session_context(session_id)
    
    # 8. 检查缓存状态
    example_check_cache_status()
    
    print("\n" + "="*60)
    print("🎉 完整示例运行完成！")
    print("="*60)

if __name__ == "__main__":
    print("⚠️ 请确保后端服务正在运行 (python main.py)")
    print("🔗 API Base URL: http://localhost:8000")
    
    try:
        # 检查服务器是否运行
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器正在运行，开始示例")
            run_complete_example()
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保后端正在运行")
        print("启动命令: cd backend && python main.py")
        
        # 尝试直接运行示例（可能是健康检查的问题）
        print("\n🔄 尝试直接运行示例...")
        try:
            run_complete_example()
        except Exception as ex:
            print(f"❌ 示例运行失败: {ex}")