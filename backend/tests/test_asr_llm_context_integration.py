#!/usr/bin/env python3
"""
测试ASR与LLM的上下文集成功能
"""

import os
import sys
import logging
import numpy as np
import asyncio
import json
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from services.enhanced_asr_service import EnhancedASRService
from services.llm_service import LLMService
from services.conversation_service import ConversationService
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestASRLLMContextIntegration:
    """测试ASR与LLM的上下文集成"""
    
    def __init__(self):
        """初始化测试"""
        # 初始化ASR服务（启用上下文）
        self.asr_service = EnhancedASRService(
            qwen_timeout=10,
            gemini_timeout=15,
            gemini_first=True,
            enable_context=True
        )
        
        # 初始化LLM服务
        volcano_api_key = os.getenv("VOLCANO_API_KEY")
        volcano_endpoint = os.getenv("VOLCANO_ENDPOINT")
        
        if volcano_api_key and volcano_endpoint:
            self.llm_service = LLMService(volcano_api_key, volcano_endpoint)
            self.llm_available = True
            logger.info("✅ LLM服务初始化成功")
        else:
            logger.warning("⚠️ LLM服务环境变量未设置，将使用模拟模式")
            self.llm_service = LLMService("mock_key", "mock_endpoint")
            self.llm_available = False
        
        # 模拟对话场景
        self.conversation_scenarios = [
            {
                "name": "天气询问场景",
                "turns": [
                    {"user_audio": "今天天气怎么样", "expected_context": "天气"},
                    {"user_audio": "明天呢", "expected_context": "天气"},
                    {"user_audio": "需要带伞吗", "expected_context": "天气"}
                ]
            },
            {
                "name": "购物咨询场景", 
                "turns": [
                    {"user_audio": "我想买一台笔记本电脑", "expected_context": "购物"},
                    {"user_audio": "预算在五千左右", "expected_context": "笔记本电脑"},
                    {"user_audio": "有什么推荐的吗", "expected_context": "笔记本电脑推荐"}
                ]
            }
        ]
    
    def generate_test_audio(self, text: str, duration: float = 2.0, sample_rate: int = 16000) -> np.ndarray:
        """生成模拟音频数据（实际项目中这里会是真实的音频）"""
        # 生成基于文本长度的音频长度
        actual_duration = max(1.0, len(text) * 0.1)  # 每个字符0.1秒
        t = np.linspace(0, actual_duration, int(sample_rate * actual_duration), False)
        
        # 生成复合频率的音频信号来模拟语音
        frequencies = [200, 400, 800]  # 模拟语音的基频和谐波
        audio = np.zeros_like(t)
        for freq in frequencies:
            audio += np.sin(2 * np.pi * freq * t) * (0.1 / len(frequencies))
        
        return audio.astype(np.float32)
    
    async def simulate_conversation_turn(self, session_id: str, user_text: str, system_prompt: str) -> Dict[str, Any]:
        """模拟一轮完整的对话（ASR + LLM）"""
        logger.info(f"🎭 模拟对话轮次: '{user_text}'")
        
        # 1. 生成模拟音频
        audio_data = self.generate_test_audio(user_text)
        
        # 2. ASR转录（带上下文）
        asr_result = self.asr_service.transcribe(
            audio_data=audio_data,
            sample_rate=16000,
            session_id=session_id
        )
        
        logger.info(f"📝 ASR结果: {json.dumps(asr_result, ensure_ascii=False)}")
        
        # 3. 获取对话历史（模拟）
        conversation_history = []
        context = self.asr_service.conversation_contexts.get(session_id)
        if context and context.conversation_history:
            # 将ASR历史转换为对话历史格式
            for turn in context.conversation_history[-3:]:  # 最近3轮
                conversation_history.extend([
                    {"role": "user", "content": turn["asr_result"]},
                    {"role": "assistant", "content": f"关于'{turn['asr_result']}'的回复"}
                ])
        
        # 4. LLM生成回复
        if asr_result.get('success'):
            transcribed_text = asr_result.get('text', user_text)  # 如果ASR失败，使用原文本
        else:
            transcribed_text = user_text  # 回退到原文本
        
        llm_response = await self.llm_service.generate_response(
            user_input=transcribed_text,
            conversation_history=conversation_history,
            system_prompt=system_prompt
        )
        
        logger.info(f"🤖 LLM回复: {json.dumps(llm_response, ensure_ascii=False)}")
        
        return {
            "user_input": user_text,
            "asr_result": asr_result,
            "llm_response": llm_response,
            "context_used": asr_result.get('context_used', False),
            "conversation_history_length": len(conversation_history)
        }
    
    async def test_single_conversation_scenario(self, scenario: Dict[str, Any]) -> bool:
        """测试单个对话场景"""
        logger.info(f"🎬 测试对话场景: {scenario['name']}")
        
        session_id = f"test_scenario_{scenario['name'].replace(' ', '_')}"
        system_prompt = f"你是一个智能助手，专门处理{scenario['name']}相关的问题。请根据对话上下文提供有用的回复。"
        
        scenario_results = []
        
        for i, turn in enumerate(scenario['turns']):
            logger.info(f"\n--- 第 {i+1} 轮对话 ---")
            
            turn_result = await self.simulate_conversation_turn(
                session_id=session_id,
                user_text=turn['user_audio'],
                system_prompt=system_prompt
            )
            
            scenario_results.append(turn_result)
            
            # 验证上下文使用情况
            if i > 0:  # 从第二轮开始应该有上下文
                if turn_result['context_used']:
                    logger.info("✅ 正确使用了对话上下文")
                else:
                    logger.warning("⚠️ 未使用对话上下文（可能是ASR失败）")
            
            # 短暂延迟模拟真实对话
            await asyncio.sleep(0.5)
        
        # 分析场景结果
        context_usage_rate = sum(1 for r in scenario_results[1:] if r['context_used']) / max(1, len(scenario_results) - 1)
        logger.info(f"📊 场景 '{scenario['name']}' 上下文使用率: {context_usage_rate:.2%}")
        
        return True
    
    async def test_context_persistence(self) -> bool:
        """测试上下文持久性"""
        logger.info("🧪 测试上下文持久性...")
        
        session_id = "persistence_test"
        system_prompt = "你是一个记忆力很好的助手。"
        
        # 第一轮对话
        result1 = await self.simulate_conversation_turn(
            session_id=session_id,
            user_text="我叫张三",
            system_prompt=system_prompt
        )
        
        # 等待一段时间
        await asyncio.sleep(1)
        
        # 第二轮对话（应该记住用户姓名）
        result2 = await self.simulate_conversation_turn(
            session_id=session_id,
            user_text="你还记得我的名字吗",
            system_prompt=system_prompt
        )
        
        # 检查上下文统计
        context_stats = self.asr_service.get_context_stats()
        session_stats = context_stats['sessions'].get(session_id)
        
        if session_stats:
            logger.info(f"📊 会话历史长度: {session_stats['history_length']}")
            assert session_stats['history_length'] >= 2, "应该有至少2轮对话历史"
        
        logger.info("✅ 上下文持久性测试通过")
        return True
    
    async def test_multiple_sessions(self) -> bool:
        """测试多会话隔离"""
        logger.info("🧪 测试多会话隔离...")
        
        sessions = ["session_A", "session_B", "session_C"]
        system_prompt = "你是一个智能助手。"
        
        # 为每个会话创建不同的对话
        session_topics = {
            "session_A": "我想学习Python编程",
            "session_B": "今天想做红烧肉",
            "session_C": "计划去北京旅游"
        }
        
        # 并发处理多个会话
        tasks = []
        for session_id, topic in session_topics.items():
            task = self.simulate_conversation_turn(
                session_id=session_id,
                user_text=topic,
                system_prompt=system_prompt
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # 检查会话隔离
        context_stats = self.asr_service.get_context_stats()
        assert context_stats['total_sessions'] >= len(sessions), "应该有正确数量的会话"
        
        for session_id in sessions:
            assert session_id in context_stats['sessions'], f"会话 {session_id} 应该存在"
        
        logger.info("✅ 多会话隔离测试通过")
        return True
    
    async def test_context_window_management(self) -> bool:
        """测试上下文窗口管理"""
        logger.info("🧪 测试上下文窗口管理...")
        
        session_id = "window_test"
        system_prompt = "你是一个助手。"
        
        # 创建超过最大历史长度的对话
        max_history = 10
        for i in range(max_history + 5):  # 超过最大历史长度
            await self.simulate_conversation_turn(
                session_id=session_id,
                user_text=f"这是第{i+1}条消息",
                system_prompt=system_prompt
            )
        
        # 检查历史长度是否被正确限制
        context_stats = self.asr_service.get_context_stats()
        session_stats = context_stats['sessions'].get(session_id)
        
        if session_stats:
            logger.info(f"📊 最终历史长度: {session_stats['history_length']}")
            assert session_stats['history_length'] <= max_history, f"历史长度应该不超过{max_history}"
        
        logger.info("✅ 上下文窗口管理测试通过")
        return True
    
    async def run_all_tests(self):
        """运行所有集成测试"""
        logger.info("🚀 开始运行ASR-LLM上下文集成测试...")
        
        tests = [
            ("上下文持久性", self.test_context_persistence),
            ("多会话隔离", self.test_multiple_sessions),
            ("上下文窗口管理", self.test_context_window_management),
        ]
        
        # 添加对话场景测试
        for scenario in self.conversation_scenarios:
            tests.append((f"对话场景: {scenario['name']}", 
                         lambda s=scenario: self.test_single_conversation_scenario(s)))
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n{'='*60}")
                logger.info(f"🧪 运行测试: {test_name}")
                logger.info(f"{'='*60}")
                
                result = await test_func()
                if result:
                    logger.info(f"✅ 测试通过: {test_name}")
                    passed += 1
                else:
                    logger.error(f"❌ 测试失败: {test_name}")
                    failed += 1
                    
            except Exception as e:
                logger.error(f"❌ 测试异常: {test_name} - {e}")
                failed += 1
        
        # 输出最终统计
        logger.info(f"\n{'='*60}")
        logger.info(f"🎯 集成测试总结")
        logger.info(f"{'='*60}")
        logger.info(f"✅ 通过: {passed}")
        logger.info(f"❌ 失败: {failed}")
        logger.info(f"📊 总计: {passed + failed}")
        
        # 输出上下文统计
        context_stats = self.asr_service.get_context_stats()
        logger.info(f"\n🧠 最终上下文统计:")
        logger.info(f"总会话数: {context_stats['total_sessions']}")
        logger.info(f"上下文启用: {context_stats['context_enabled']}")
        
        if failed == 0:
            logger.info("🎉 所有集成测试都通过了！")
        else:
            logger.warning(f"⚠️ 有 {failed} 个测试失败")
        
        return failed == 0


async def main():
    """主函数"""
    logger.info("🎯 ASR-LLM上下文集成测试")
    
    # 检查环境变量
    required_vars = ["GEMINI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.warning(f"⚠️ 缺少环境变量: {missing_vars}，某些测试可能会失败")
    
    # 运行测试
    test_runner = TestASRLLMContextIntegration()
    success = await test_runner.run_all_tests()
    
    if success:
        logger.info("🎉 所有集成测试完成，功能正常！")
        return 0
    else:
        logger.error("❌ 部分测试失败，请检查日志")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
