#!/usr/bin/env python3
"""
测试增强LLM服务的上下文功能
"""

import os
import sys
import logging
import asyncio
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from services.enhanced_llm_service import EnhancedLLMService, ConversationMemory, ConversationTurn
from dotenv import load_dotenv
from datetime import datetime

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestEnhancedLLMContext:
    """测试增强LLM服务的上下文功能"""
    
    def __init__(self):
        """初始化测试"""
        self.llm_service = EnhancedLLMService(
            api_key=os.getenv("VOLCANO_API_KEY", "test_key"),
            endpoint=os.getenv("VOLCANO_ENDPOINT", "test_endpoint"),
            enable_context=True
        )
        
    def test_conversation_memory(self):
        """测试对话记忆管理"""
        logger.info("🧪 测试对话记忆管理...")
        
        # 创建测试记忆
        session_id = "test_memory_001"
        memory = self.llm_service.get_or_create_memory(session_id)
        
        assert memory is not None, "应该能够创建对话记忆"
        assert memory.session_id == session_id, "会话ID应该匹配"
        assert len(memory.turns) == 0, "初始记忆应该为空"
        
        # 添加对话轮次
        memory.add_turn("你好", "你好！很高兴见到你。", ["greeting_tool"])
        memory.add_turn("我喜欢吃苹果", "苹果是很健康的水果选择！", [])
        memory.add_turn("推荐一些水果", "基于你喜欢苹果，我推荐香蕉、橙子等。", ["fruit_recommendation"])
        
        assert len(memory.turns) == 3, "应该有3轮对话记忆"
        
        # 测试上下文摘要生成
        context_summary = memory.get_context_summary()
        assert "苹果" in context_summary, "上下文摘要应该包含用户偏好"
        
        logger.info("✅ 对话记忆管理测试通过")
        return True
    
    def test_memory_pruning(self):
        """测试记忆修剪功能"""
        logger.info("🧪 测试记忆修剪功能...")
        
        session_id = "test_pruning_001"
        memory = self.llm_service.get_or_create_memory(session_id)
        memory.max_turns = 5  # 设置较小的限制用于测试
        
        # 添加超过限制的对话轮次
        for i in range(8):
            importance = 2.0 if i % 3 == 0 else 1.0  # 某些轮次更重要
            user_input = f"测试消息 {i+1}"
            assistant_response = f"回复 {i+1}"
            memory.add_turn(user_input, assistant_response, [])
            # 手动设置重要性分数
            if memory.turns:
                memory.turns[-1].importance_score = importance
        
        # 检查修剪结果
        assert len(memory.turns) <= memory.max_turns, f"记忆轮次应该不超过{memory.max_turns}"
        
        # 检查是否保留了重要的轮次
        important_turns = [turn for turn in memory.turns if turn.importance_score > 1.5]
        assert len(important_turns) > 0, "应该保留重要的轮次"
        
        logger.info("✅ 记忆修剪功能测试通过")
        return True
    
    def test_context_enhanced_prompt(self):
        """测试上下文增强提示"""
        logger.info("🧪 测试上下文增强提示...")
        
        session_id = "test_prompt_001"
        memory = self.llm_service.get_or_create_memory(session_id)
        
        # 添加一些对话历史
        memory.add_turn("我是张三", "你好张三！", [])
        memory.add_turn("我喜欢编程", "编程是很有趣的技能！", [])
        memory.add_turn("推荐学习资源", "我推荐Python官方文档", ["resource_search"])
        
        # 测试增强提示生成
        system_prompt = "你是一个智能助手。"
        user_input = "还有其他推荐吗？"
        
        enhanced_prompt = self.llm_service.get_context_enhanced_prompt(
            session_id, user_input, system_prompt
        )
        
        assert len(enhanced_prompt) > len(system_prompt), "增强提示应该比原提示更长"
        assert "张三" in enhanced_prompt, "增强提示应该包含用户信息"
        assert "编程" in enhanced_prompt, "增强提示应该包含用户偏好"
        assert "推荐学习资源" in enhanced_prompt, "增强提示应该包含最近对话"
        
        logger.info("✅ 上下文增强提示测试通过")
        return True
    
    async def test_context_aware_response(self):
        """测试上下文感知响应生成"""
        logger.info("🧪 测试上下文感知响应生成...")
        
        session_id = "test_response_001"
        system_prompt = "你是一个智能助手，请根据对话历史提供个性化回复。"
        
        # 第一轮对话
        response1 = await self.llm_service.generate_response_with_tools(
            user_input="我叫李四，是一名教师",
            conversation_history=[],
            system_prompt=system_prompt,
            session_id=session_id
        )
        
        logger.info(f"第一轮响应: {json.dumps(response1, ensure_ascii=False)}")
        
        # 检查响应结构
        assert 'success' in response1, "响应应该包含success字段"
        assert 'context_used' in response1, "响应应该包含context_used字段"
        assert 'session_id' in response1, "响应应该包含session_id字段"
        
        # 第二轮对话（应该使用上下文）
        response2 = await self.llm_service.generate_response_with_tools(
            user_input="你还记得我的职业吗？",
            conversation_history=[],
            system_prompt=system_prompt,
            session_id=session_id
        )
        
        logger.info(f"第二轮响应: {json.dumps(response2, ensure_ascii=False)}")
        
        # 检查上下文使用情况
        if response2.get('success'):
            # 第二轮应该使用了上下文
            memory_stats = self.llm_service.get_memory_stats()
            session_memory = memory_stats['sessions'].get(session_id)
            if session_memory:
                assert session_memory['turns_count'] > 0, "应该有对话历史"
                logger.info(f"会话记忆轮次: {session_memory['turns_count']}")
        
        logger.info("✅ 上下文感知响应生成测试完成")
        return True
    
    def test_memory_stats(self):
        """测试记忆统计功能"""
        logger.info("🧪 测试记忆统计功能...")
        
        # 创建一些测试会话
        test_sessions = ["stats_test_1", "stats_test_2", "stats_test_3"]
        for i, session_id in enumerate(test_sessions):
            memory = self.llm_service.get_or_create_memory(session_id)
            # 添加不同数量的对话轮次
            for j in range(i + 2):
                memory.add_turn(f"输入{j}", f"输出{j}", [])
        
        # 获取统计信息
        stats = self.llm_service.get_memory_stats()
        
        logger.info(f"记忆统计信息: {json.dumps(stats, ensure_ascii=False, indent=2)}")
        
        # 验证统计信息
        assert stats["context_enabled"] == True, "上下文应该是启用状态"
        assert stats["total_sessions"] >= len(test_sessions), "会话数量应该正确"
        
        # 检查每个会话的统计信息
        for i, session_id in enumerate(test_sessions):
            session_stats = stats["sessions"].get(session_id)
            assert session_stats is not None, f"会话 {session_id} 应该存在统计信息"
            assert session_stats["turns_count"] == i + 2, f"会话 {session_id} 轮次数应该是 {i + 2}"
            assert not session_stats["is_expired"], f"会话 {session_id} 不应该过期"
        
        logger.info("✅ 记忆统计功能测试通过")
        return True
    
    def test_context_disabled(self):
        """测试禁用上下文功能"""
        logger.info("🧪 测试禁用上下文功能...")
        
        # 创建禁用上下文的LLM服务
        llm_no_context = EnhancedLLMService(
            api_key="test_key",
            endpoint="test_endpoint",
            enable_context=False
        )
        
        # 尝试获取记忆（应该返回None）
        memory = llm_no_context.get_or_create_memory("test_session")
        assert memory is None, "禁用上下文时应该返回None"
        
        # 测试增强提示（应该返回原提示）
        system_prompt = "你是一个助手。"
        enhanced_prompt = llm_no_context.get_context_enhanced_prompt(
            "test_session", "测试输入", system_prompt
        )
        assert enhanced_prompt == system_prompt, "禁用上下文时应该返回原提示"
        
        logger.info("✅ 禁用上下文功能测试通过")
        return True
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始运行增强LLM上下文功能测试...")
        
        tests = [
            ("对话记忆管理", self.test_conversation_memory),
            ("记忆修剪功能", self.test_memory_pruning),
            ("上下文增强提示", self.test_context_enhanced_prompt),
            ("上下文感知响应生成", self.test_context_aware_response),
            ("记忆统计功能", self.test_memory_stats),
            ("禁用上下文功能", self.test_context_disabled),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n{'='*50}")
                logger.info(f"🧪 运行测试: {test_name}")
                logger.info(f"{'='*50}")
                
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                if result:
                    logger.info(f"✅ 测试通过: {test_name}")
                    passed += 1
                else:
                    logger.error(f"❌ 测试失败: {test_name}")
                    failed += 1
                    
            except Exception as e:
                logger.error(f"❌ 测试异常: {test_name} - {e}")
                failed += 1
        
        # 输出测试总结
        logger.info(f"\n{'='*50}")
        logger.info(f"🎯 测试总结")
        logger.info(f"{'='*50}")
        logger.info(f"✅ 通过: {passed}")
        logger.info(f"❌ 失败: {failed}")
        logger.info(f"📊 总计: {passed + failed}")
        
        if failed == 0:
            logger.info("🎉 所有测试都通过了！")
        else:
            logger.warning(f"⚠️ 有 {failed} 个测试失败")
        
        return failed == 0


async def main():
    """主函数"""
    logger.info("🎯 增强LLM服务上下文功能测试")
    
    # 检查环境变量
    if not os.getenv("VOLCANO_API_KEY"):
        logger.warning("⚠️ VOLCANO_API_KEY未设置，某些测试可能会失败")
    
    # 运行测试
    test_runner = TestEnhancedLLMContext()
    success = await test_runner.run_all_tests()
    
    if success:
        logger.info("🎉 所有测试完成，功能正常！")
        return 0
    else:
        logger.error("❌ 部分测试失败，请检查日志")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
