"""
工具管理服务单元测试
"""
import unittest
import asyncio
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.tool_manager_service import ToolManagerService

class TestToolManagerService(unittest.TestCase):
    """工具管理服务测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.tool_manager = ToolManagerService()
        
        # 模拟真实MCP工具数据（不再包含内置工具）
        self.mock_tools = [
            {
                "name": "query_train_schedule",
                "description": "查询火车时刻表",
                "server": "12306-mcp",
                "category": "travel"
            },
            {
                "name": "search_bilibili_videos",
                "description": "搜索B站视频",
                "server": "bilibili-mcp",
                "category": "entertainment"
            },
            {
                "name": "get_cooking_recipe",
                "description": "获取烹饪食谱",
                "server": "howtocook-mcp",
                "category": "cooking"
            }
        ]
    
    def test_enumerate_all_tools(self):
        """测试枚举所有工具"""
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            tools = self.tool_manager.enumerate_all_tools(use_cache=False)
            
            self.assertIsInstance(tools, list)
            self.assertGreater(len(tools), 0)
            
            # 检查工具是否被增强
            for tool in tools:
                self.assertIn('name', tool)
                self.assertIn('description', tool)
                self.assertIn('last_updated', tool)
                self.assertIn('version', tool)
    
    def test_get_tool_by_name(self):
        """测试根据名称获取工具"""
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            # 测试存在的工具
            tool = self.tool_manager.get_tool_by_name("query_train_schedule")
            self.assertIsNotNone(tool)
            self.assertEqual(tool['name'], "query_train_schedule")

            # 测试不存在的工具
            tool = self.tool_manager.get_tool_by_name("nonexistent_tool")
            self.assertIsNone(tool)
    
    def test_search_tools_by_category(self):
        """测试按类别搜索工具"""
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            # 测试搜索存在的类别
            tools = self.tool_manager.search_tools_by_category("travel")
            self.assertIsInstance(tools, list)
            self.assertGreater(len(tools), 0)

            for tool in tools:
                self.assertEqual(tool['category'], "travel")

            # 测试搜索不存在的类别
            tools = self.tool_manager.search_tools_by_category("nonexistent")
            self.assertEqual(len(tools), 0)
    
    def test_rank_tools_by_relevance(self):
        """测试工具相关性排序"""
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            # 模拟reranker服务返回结果
            mock_ranked_tools = [
                {
                    "name": "query_train_schedule",
                    "description": "查询火车时刻表",
                    "relevance_score": 0.9
                },
                {
                    "name": "search_bilibili_videos",
                    "description": "搜索B站视频",
                    "relevance_score": 0.7
                }
            ]
            
            with patch.object(self.tool_manager.reranker_service, 'rank_tools_by_relevance', return_value=mock_ranked_tools):
                ranked_tools = self.tool_manager.rank_tools_by_relevance("搜索最新信息", top_k=3)
                
                self.assertIsInstance(ranked_tools, list)
                self.assertLessEqual(len(ranked_tools), 3)
                
                # 检查是否包含相关性得分
                for tool in ranked_tools:
                    self.assertIn('relevance_score', tool)
                    self.assertIsInstance(tool['relevance_score'], (int, float))
    
    def test_get_top_relevant_tool(self):
        """测试获取最相关的工具"""
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            mock_ranked_tools = [
                {
                    "name": "query_train_schedule",
                    "description": "查询火车时刻表",
                    "relevance_score": 0.9
                }
            ]

            with patch.object(self.tool_manager.reranker_service, 'rank_tools_by_relevance', return_value=mock_ranked_tools):
                top_tool = self.tool_manager.get_top_relevant_tool("查询火车")

                self.assertIsNotNone(top_tool)
                self.assertEqual(top_tool['name'], "query_train_schedule")
                self.assertIn('relevance_score', top_tool)
    
    def test_vector_search_tools(self):
        """测试向量搜索工具"""
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            mock_ranked_tools = [
                {
                    "name": "search_bilibili_videos",
                    "description": "搜索B站视频",
                    "relevance_score": 0.9
                }
            ]
            
            with patch.object(self.tool_manager.reranker_service, 'rank_tools_by_relevance', return_value=mock_ranked_tools):
                # 测试异步方法
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    result = loop.run_until_complete(
                        self.tool_manager.vector_search_tools("搜索视频", top_k=2)
                    )
                    
                    self.assertIsInstance(result, list)
                    self.assertLessEqual(len(result), 2)
                finally:
                    loop.close()
    
    def test_get_tool_statistics(self):
        """测试获取工具统计信息"""
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            stats = self.tool_manager.get_tool_statistics()
            
            self.assertIsInstance(stats, dict)
            self.assertIn('total_tools', stats)
            self.assertIn('categories', stats)
            self.assertIn('servers', stats)
            self.assertIn('cache_status', stats)
            
            # 检查统计数据的正确性
            self.assertEqual(stats['total_tools'], len(self.mock_tools))
            self.assertIsInstance(stats['categories'], dict)
            self.assertIsInstance(stats['servers'], dict)
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools) as mock_get_tools:
            # 第一次调用，应该从MCP服务获取
            tools1 = self.tool_manager.enumerate_all_tools(use_cache=True)
            self.assertEqual(mock_get_tools.call_count, 1)
            
            # 第二次调用，应该使用缓存
            tools2 = self.tool_manager.enumerate_all_tools(use_cache=True)
            self.assertEqual(mock_get_tools.call_count, 1)  # 调用次数不应该增加
            
            # 验证结果一致
            self.assertEqual(len(tools1), len(tools2))
    
    def test_refresh_tool_cache(self):
        """测试刷新工具缓存"""
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            # 先清空缓存
            self.tool_manager._tool_cache = {}
            self.tool_manager._cache_timestamp = None
            
            # 刷新缓存
            result = self.tool_manager.refresh_tool_cache()
            self.assertTrue(result)
            
            # 验证缓存已更新（检查缓存中是否有数据）
            self.assertGreater(len(self.tool_manager._tool_cache), 0)
            
            # 验证缓存时间戳已设置
            self.assertIsNotNone(self.tool_manager._cache_timestamp)
    
    def test_enhance_tool_info(self):
        """测试工具信息增强"""
        # 测试query_train_schedule工具的增强
        original_tool = {
            "name": "query_train_schedule",
            "description": "查询火车时刻表",
            "server": "12306-mcp",
            "category": "travel"
        }
        
        enhanced_tool = self.tool_manager._enhance_tool_info(original_tool)
        
        # 检查是否添加了参数信息
        self.assertIn('parameters', enhanced_tool)
        self.assertIn('usage_examples', enhanced_tool)
        self.assertIn('last_updated', enhanced_tool)
        self.assertIn('version', enhanced_tool)
        
        # 检查参数结构（火车查询工具的参数）
        parameters = enhanced_tool['parameters']
        self.assertIn('departure', parameters)
        self.assertIn('destination', parameters)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试MCP服务异常时的处理
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', side_effect=Exception("MCP服务错误")):
            tools = self.tool_manager.enumerate_all_tools(use_cache=False)
            self.assertEqual(len(tools), 0)
        
        # 测试reranker服务异常时的处理
        with patch.object(self.tool_manager.mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            with patch.object(self.tool_manager.reranker_service, 'rank_tools_by_relevance', return_value=None):
                ranked_tools = self.tool_manager.rank_tools_by_relevance("测试查询")
                # 应该返回原始工具列表的前几个
                self.assertIsInstance(ranked_tools, list)

if __name__ == '__main__':
    unittest.main()