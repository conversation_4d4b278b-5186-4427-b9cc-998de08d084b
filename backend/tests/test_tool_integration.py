"""
工具集成测试
测试整个工具调用流程的集成
"""
import unittest
import asyncio
from unittest.mock import Mock, patch, MagicMock
import sys
import os
import json

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.tool_manager_service import tool_manager_service
from services.enhanced_llm_service import get_enhanced_llm_service
from utils.reranker_service import reranker_service
from services.mcp_service import mcp_service

class TestToolIntegration(unittest.TestCase):
    """工具集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.tool_manager = tool_manager_service
        self.enhanced_llm = get_enhanced_llm_service("test_key", "test_endpoint")
        
        # 模拟完整的工具数据
        self.mock_tools = [
            {
                "name": "search_and_summarize",
                "description": "搜索并总结网络信息。当用户需要了解实时信息或超出个人知识范围的内容时使用。",
                "server": "builtin",
                "category": "search",
                "parameters": {
                    "query": {
                        "type": "string",
                        "description": "搜索查询关键词",
                        "required": True
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "最大结果数量",
                        "default": 5,
                        "required": False
                    }
                }
            },
            {
                "name": "fetch_news",
                "description": "获取新闻。可以根据多种条件进行筛选和排序，比如获取用户已读的、未读的、最近读过的，或者根据特定主题进行搜索。",
                "server": "builtin",
                "category": "news",
                "parameters": {
                    "category": {
                        "type": "string",
                        "description": "新闻类别",
                        "enum": ["technology", "science", "business", "sports", "entertainment"],
                        "default": "general",
                        "required": False
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回新闻数量",
                        "default": 5,
                        "required": False
                    }
                }
            },
            {
                "name": "recall_current_activity",
                "description": "回忆当前活动。用于获取角色当前的状态和正在进行的活动。",
                "server": "builtin",
                "category": "memory",
                "parameters": {}
            }
        ]
    
    def test_end_to_end_tool_workflow(self):
        """测试端到端的工具调用工作流"""
        user_query = "我想了解最新的科技新闻"
        
        # 模拟工具枚举
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            # 模拟重排序结果
            mock_reranked_tools = [
                {
                    "name": "fetch_news",
                    "description": "获取新闻。可以根据多种条件进行筛选和排序，比如获取用户已读的、未读的、最近读过的，或者根据特定主题进行搜索。",
                    "relevance_score": 0.95
                },
                {
                    "name": "search_and_summarize",
                    "description": "搜索并总结网络信息。当用户需要了解实时信息或超出个人知识范围的内容时使用。",
                    "relevance_score": 0.85
                }
            ]
            
            with patch.object(reranker_service, 'rank_tools_by_relevance', return_value=mock_reranked_tools):
                # 测试工具枚举
                all_tools = self.tool_manager.enumerate_all_tools(use_cache=False)
                self.assertGreater(len(all_tools), 0)
                
                # 测试工具排序
                ranked_tools = self.tool_manager.rank_tools_by_relevance(user_query, top_k=5)
                self.assertGreater(len(ranked_tools), 0)
                self.assertEqual(ranked_tools[0]['name'], 'fetch_news')
                
                # 验证相关性得分
                self.assertGreater(ranked_tools[0]['relevance_score'], 0.9)
    
    def test_tool_execution_integration(self):
        """测试工具执行集成"""
        # 模拟工具执行
        mock_execution_result = {
            "success": True,
            "result": {
                "news": [
                    {
                        "title": "最新AI技术突破",
                        "summary": "人工智能领域取得重大进展",
                        "url": "https://example.com/news1"
                    }
                ],
                "category": "technology",
                "total": 1
            }
        }
        
        with patch.object(mcp_service, 'execute_tool', return_value=mock_execution_result):
            # 执行工具
            result = mcp_service.execute_tool(
                "fetch_news",
                "builtin",
                category="technology",
                limit=5
            )
            
            self.assertTrue(result['success'])
            self.assertIn('result', result)
            self.assertIn('news', result['result'])
    
    def test_llm_tool_integration(self):
        """测试LLM与工具的集成"""
        user_input = "帮我搜索最新的AI技术发展"
        conversation_history = []
        system_prompt = "你是一个友善的AI助手。"
        
        # 模拟工具数据
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            # 模拟重排序
            mock_reranked_tools = [
                {
                    "name": "search_and_summarize",
                    "description": "搜索并总结网络信息",
                    "relevance_score": 0.9
                }
            ]
            
            with patch.object(reranker_service, 'rank_tools_by_relevance', return_value=mock_reranked_tools):
                # 测试工具选择逻辑
                should_use_tools = self.enhanced_llm._should_use_tools(user_input, conversation_history)
                self.assertTrue(should_use_tools)
                
                # 测试获取相关工具
                relevant_tools = self.enhanced_llm._get_relevant_tools_for_llm(user_input)
                self.assertGreater(len(relevant_tools), 0)
                
                # 验证工具定义格式
                tool_def = relevant_tools[0]
                self.assertEqual(tool_def["type"], "function")
                self.assertIn("function", tool_def)
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 测试MCP服务异常
        with patch.object(mcp_service, 'get_all_tools_for_reranker', side_effect=Exception("MCP服务异常")):
            tools = self.tool_manager.enumerate_all_tools(use_cache=False)
            self.assertEqual(len(tools), 0)
        
        # 测试重排序服务异常
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            with patch.object(reranker_service, 'rank_tools_by_relevance', return_value=None):
                ranked_tools = self.tool_manager.rank_tools_by_relevance("测试查询")
                # 应该返回备用结果
                self.assertIsInstance(ranked_tools, list)
    
    def test_tool_parameter_validation(self):
        """测试工具参数验证"""
        # 获取工具信息
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            tool = self.tool_manager.get_tool_by_name("search_and_summarize")
            self.assertIsNotNone(tool)
            
            # 验证参数结构
            parameters = tool.get('parameters', {})
            self.assertIn('query', parameters)
            self.assertTrue(parameters['query']['required'])
            
            self.assertIn('max_results', parameters)
            self.assertFalse(parameters['max_results']['required'])
    
    def test_tool_caching_integration(self):
        """测试工具缓存集成"""
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools) as mock_get_tools:
            # 第一次调用
            tools1 = self.tool_manager.enumerate_all_tools(use_cache=True)
            call_count_1 = mock_get_tools.call_count
            
            # 第二次调用（应该使用缓存）
            tools2 = self.tool_manager.enumerate_all_tools(use_cache=True)
            call_count_2 = mock_get_tools.call_count
            
            # 验证缓存生效
            self.assertEqual(call_count_1, call_count_2)
            self.assertEqual(len(tools1), len(tools2))
    
    def test_tool_statistics_integration(self):
        """测试工具统计集成"""
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            stats = self.tool_manager.get_tool_statistics()
            
            # 验证统计信息
            self.assertEqual(stats['total_tools'], len(self.mock_tools))
            self.assertIn('search', stats['categories'])
            self.assertIn('news', stats['categories'])
            self.assertIn('memory', stats['categories'])
            self.assertIn('builtin', stats['servers'])
    
    def test_async_tool_operations(self):
        """测试异步工具操作"""
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            mock_reranked_tools = [
                {
                    "name": "search_and_summarize",
                    "description": "搜索并总结网络信息",
                    "relevance_score": 0.9
                }
            ]
            
            with patch.object(reranker_service, 'rank_tools_by_relevance', return_value=mock_reranked_tools):
                # 测试异步向量搜索
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    result = loop.run_until_complete(
                        self.tool_manager.vector_search_tools("搜索信息", top_k=3)
                    )
                    
                    self.assertIsInstance(result, list)
                    self.assertLessEqual(len(result), 3)
                finally:
                    loop.close()
    
    def test_tool_relevance_threshold(self):
        """测试工具相关性阈值"""
        user_input = "随便聊聊"
        
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.mock_tools):
            # 模拟低相关性得分
            mock_low_relevance_tools = [
                {
                    "name": "search_and_summarize",
                    "description": "搜索并总结网络信息",
                    "relevance_score": 0.1  # 低于阈值
                }
            ]
            
            with patch.object(reranker_service, 'rank_tools_by_relevance', return_value=mock_low_relevance_tools):
                # 获取相关工具（应该被过滤掉）
                relevant_tools = self.enhanced_llm._get_relevant_tools_for_llm(user_input)
                
                # 由于相关性得分低于阈值，应该没有工具被选中
                self.assertEqual(len(relevant_tools), 0)

class TestToolPerformance(unittest.TestCase):
    """工具性能测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建独立的工具管理器实例
        from services.tool_manager_service import ToolManagerService
        self.tool_manager = ToolManagerService()
        
        # 创建大量模拟工具用于性能测试
        self.large_tool_set = []
        for i in range(100):
            self.large_tool_set.append({
                "name": f"tool_{i}",
                "description": f"这是第{i}个测试工具，用于性能测试",
                "server": f"server_{i % 10}",
                "category": f"category_{i % 5}"
            })
    
    def test_large_tool_set_enumeration(self):
        """测试大量工具的枚举性能"""
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.large_tool_set):
            import time
            
            start_time = time.time()
            tools = self.tool_manager.enumerate_all_tools(use_cache=False)
            end_time = time.time()
            
            # 验证结果
            self.assertEqual(len(tools), 100)
            
            # 验证性能（应该在合理时间内完成）
            execution_time = end_time - start_time
            self.assertLess(execution_time, 5.0)  # 应该在5秒内完成
    
    def test_tool_search_performance(self):
        """测试工具搜索性能"""
        with patch.object(mcp_service, 'get_all_tools_for_reranker', return_value=self.large_tool_set):
            # 模拟重排序结果 - 确保名称匹配large_tool_set中的工具名称
            mock_ranked_tools = [
                {
                    "name": f"tool_{i}",
                    "description": f"这是第{i}个测试工具，用于性能测试",
                    "relevance_score": 0.9 - i * 0.01
                }
                for i in range(10)
            ]
            
            with patch.object(reranker_service, 'rank_tools_by_relevance', return_value=mock_ranked_tools):
                import time
                
                start_time = time.time()
                ranked_tools = self.tool_manager.rank_tools_by_relevance("测试查询", top_k=10)
                end_time = time.time()
                
                # 验证结果
                self.assertEqual(len(ranked_tools), 10)
                
                # 验证工具名称匹配
                for i, tool in enumerate(ranked_tools):
                    self.assertEqual(tool['name'], f"tool_{i}")
                    self.assertIn('relevance_score', tool)
                
                # 验证性能
                execution_time = end_time - start_time
                self.assertLess(execution_time, 2.0)  # 应该在2秒内完成

if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)