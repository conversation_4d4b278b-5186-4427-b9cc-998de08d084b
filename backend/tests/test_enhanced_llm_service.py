"""
增强LLM服务单元测试
"""
import unittest
import asyncio
from unittest.mock import Mock, patch, MagicMock, AsyncMock
import sys
import os
import json

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.enhanced_llm_service import EnhancedLLMService, ToolCall, ToolResult

class TestEnhancedLLMService(unittest.TestCase):
    """增强LLM服务测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.llm_service = EnhancedLLMService("test_api_key", "test_endpoint")
        
        # 模拟对话历史
        self.conversation_history = [
            {"role": "user", "content": "你好"},
            {"role": "assistant", "content": "你好！有什么可以帮助你的吗？"}
        ]
        
        self.system_prompt = "你是一个友善的AI助手。"
    
    def test_should_use_tools(self):
        """测试是否应该使用工具的判断"""
        # 测试包含工具关键词的输入
        self.assertTrue(self.llm_service._should_use_tools("帮我搜索最新新闻", []))
        self.assertTrue(self.llm_service._should_use_tools("查找关于AI的信息", []))
        self.assertTrue(self.llm_service._should_use_tools("今天天气怎么样？", []))
        
        # 测试不包含工具关键词的输入
        self.llm_service.enable_tool_calling = True
        self.assertFalse(self.llm_service._should_use_tools("你好", []))
        self.assertFalse(self.llm_service._should_use_tools("谢谢", []))
        
        # 测试工具调用被禁用的情况
        self.llm_service.enable_tool_calling = False
        self.assertFalse(self.llm_service._should_use_tools("帮我搜索信息", []))
    
    def test_extract_tool_calls_from_response(self):
        """测试从响应中提取工具调用"""
        # 测试包含工具调用的响应
        response_with_tools = """
        我需要为你搜索信息。
        <TOOL_CALL>
        name: search_and_summarize
        parameters: {"query": "最新AI技术", "max_results": 5}
        </TOOL_CALL>
        让我为你查找相关信息。
        """
        
        tool_calls = self.llm_service._extract_tool_calls_from_response(response_with_tools)
        
        self.assertEqual(len(tool_calls), 1)
        self.assertEqual(tool_calls[0].name, "search_and_summarize")
        self.assertEqual(tool_calls[0].parameters["query"], "最新AI技术")
        self.assertEqual(tool_calls[0].parameters["max_results"], 5)
        
        # 测试不包含工具调用的响应
        response_without_tools = "这是一个普通的回复，没有工具调用。"
        tool_calls = self.llm_service._extract_tool_calls_from_response(response_without_tools)
        self.assertEqual(len(tool_calls), 0)
    
    def test_execute_tool_call(self):
        """测试执行单个工具调用"""
        # 创建测试工具调用
        tool_call = ToolCall(
            name="search_and_summarize",
            parameters={"query": "测试查询", "max_results": 3}
        )
        
        # 模拟工具信息
        mock_tool_info = {
            "name": "search_and_summarize",
            "description": "搜索并总结信息",
            "server": "builtin"
        }
        
        # 模拟工具执行结果
        mock_execution_result = {
            "success": True,
            "result": {
                "summary": "测试摘要",
                "sources": ["https://example.com"]
            }
        }
        
        with patch.object(self.llm_service.tool_manager, 'get_tool_by_name', return_value=mock_tool_info):
            with patch.object(self.llm_service.mcp_service, 'execute_tool', return_value=mock_execution_result):
                # 测试异步方法
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    result = loop.run_until_complete(
                        self.llm_service._execute_tool_call(tool_call)
                    )
                    
                    self.assertIsInstance(result, ToolResult)
                    self.assertTrue(result.success)
                    self.assertEqual(result.tool_name, "search_and_summarize")
                    self.assertIsNotNone(result.result)
                finally:
                    loop.close()
    
    def test_execute_tool_call_failure(self):
        """测试工具调用失败的情况"""
        tool_call = ToolCall(
            name="nonexistent_tool",
            parameters={"query": "测试"}
        )
        
        # 模拟工具不存在
        with patch.object(self.llm_service.tool_manager, 'get_tool_by_name', return_value=None):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    self.llm_service._execute_tool_call(tool_call)
                )
                
                self.assertIsInstance(result, ToolResult)
                self.assertFalse(result.success)
                self.assertIsNotNone(result.error)
            finally:
                loop.close()
    
    def test_execute_multiple_tools(self):
        """测试并行执行多个工具调用"""
        tool_calls = [
            ToolCall(name="tool1", parameters={"param": "value1"}),
            ToolCall(name="tool2", parameters={"param": "value2"})
        ]
        
        # 模拟工具执行结果
        mock_results = [
            ToolResult(tool_name="tool1", success=True, result={"data": "result1"}),
            ToolResult(tool_name="tool2", success=True, result={"data": "result2"})
        ]
        
        with patch.object(self.llm_service, '_execute_tool_call', side_effect=mock_results):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                results = loop.run_until_complete(
                    self.llm_service._execute_multiple_tools(tool_calls)
                )
                
                self.assertEqual(len(results), 2)
                self.assertTrue(all(isinstance(r, ToolResult) for r in results))
                self.assertTrue(all(r.success for r in results))
            finally:
                loop.close()
    
    def test_build_tool_context(self):
        """测试构建工具上下文"""
        tool_results = [
            ToolResult(tool_name="tool1", success=True, result={"data": "result1"}),
            ToolResult(tool_name="tool2", success=False, result=None, error="执行失败")
        ]
        
        context = self.llm_service._build_tool_context(tool_results)
        
        self.assertIsInstance(context, str)
        self.assertIn("工具执行结果", context)
        self.assertIn("tool1 执行成功", context)
        self.assertIn("tool2 执行失败", context)
        
        # 测试空结果列表
        empty_context = self.llm_service._build_tool_context([])
        self.assertEqual(empty_context, "")
    
    def test_get_relevant_tools_for_llm(self):
        """测试获取相关工具用于LLM"""
        # 模拟相关工具
        mock_relevant_tools = [
            {
                "name": "search_tool",
                "description": "搜索工具",
                "relevance_score": 0.8,
                "parameters": {
                    "query": {
                        "type": "string",
                        "description": "搜索查询",
                        "required": True
                    }
                }
            }
        ]
        
        with patch.object(self.llm_service.tool_manager, 'rank_tools_by_relevance', return_value=mock_relevant_tools):
            tools = self.llm_service._get_relevant_tools_for_llm("搜索信息")
            
            self.assertIsInstance(tools, list)
            self.assertEqual(len(tools), 1)
            
            # 检查工具定义格式
            tool_def = tools[0]
            self.assertEqual(tool_def["type"], "function")
            self.assertIn("function", tool_def)
            self.assertIn("name", tool_def["function"])
            self.assertIn("description", tool_def["function"])
            self.assertIn("parameters", tool_def["function"])
    
    def test_process_function_calls(self):
        """测试处理函数调用"""
        # 模拟函数调用
        function_calls = [
            {
                "id": "call_1",
                "function": {
                    "name": "search_tool",
                    "arguments": '{"query": "测试查询"}'
                }
            }
        ]
        
        # 模拟执行结果
        mock_execution_result = {
            "success": True,
            "result": {"data": "搜索结果"}
        }
        
        with patch.object(self.llm_service.mcp_service, 'execute_tool', return_value=mock_execution_result):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                results = loop.run_until_complete(
                    self.llm_service._process_function_calls(function_calls)
                )
                
                self.assertEqual(len(results), 1)
                result = results[0]
                
                self.assertEqual(result["tool_call_id"], "call_1")
                self.assertEqual(result["role"], "tool")
                self.assertEqual(result["name"], "search_tool")
                self.assertIn("content", result)
            finally:
                loop.close()
    
    def test_generate_response_with_tools_no_tools_needed(self):
        """测试不需要工具时的响应生成"""
        user_input = "你好"
        
        # 模拟父类方法
        mock_response = {
            "success": True,
            "full_response": "你好！有什么可以帮助你的吗？",
            "speak_content": {
                "emotion": "friendly",
                "speed": 1.0,
                "text": "你好！有什么可以帮助你的吗？"
            }
        }
        
        async def mock_generate_response(*args, **kwargs):
            return mock_response
        
        with patch.object(self.llm_service, 'generate_response', side_effect=mock_generate_response):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    self.llm_service.generate_response_with_tools(
                        user_input, self.conversation_history, self.system_prompt
                    )
                )
                
                self.assertTrue(result["success"])
                self.assertIn("speak_content", result)
            finally:
                loop.close()
    
    def test_get_tool_usage_statistics(self):
        """测试获取工具使用统计"""
        mock_stats = {
            "total_tools": 5,
            "categories": {"search": 2, "news": 1},
            "servers": {"builtin": 3, "external": 2}
        }
        
        with patch.object(self.llm_service.tool_manager, 'get_tool_statistics', return_value=mock_stats):
            stats = self.llm_service.get_tool_usage_statistics()
            
            self.assertEqual(stats, mock_stats)
    
    def test_refresh_tools(self):
        """测试刷新工具"""
        with patch.object(self.llm_service.tool_manager, 'refresh_tool_cache', return_value=True):
            result = self.llm_service.refresh_tools()
            self.assertTrue(result)
    
    def test_error_handling_in_tool_execution(self):
        """测试工具执行中的错误处理"""
        tool_call = ToolCall(
            name="error_tool",
            parameters={"param": "value"}
        )
        
        # 模拟工具执行异常
        with patch.object(self.llm_service.tool_manager, 'get_tool_by_name', side_effect=Exception("测试异常")):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    self.llm_service._execute_tool_call(tool_call)
                )
                
                self.assertIsInstance(result, ToolResult)
                self.assertFalse(result.success)
                self.assertIn("测试异常", result.error)
            finally:
                loop.close()

if __name__ == '__main__':
    unittest.main()