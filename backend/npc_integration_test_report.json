{"timestamp": "2025-08-11T11:38:03.291615", "test_type": "integration", "summary": {"total_tests": 2, "passed": 2, "failed": 0, "success_rate": "100.0%"}, "test_results": [{"test": "complete_npc_workflow", "status": "success", "message": "完整的NPC工作流程测试成功", "details": {"npc_id": "6c0be9ce-55f6-4bb5-8728-08c31a422f6b", "npc_name": "周可心", "user_id": 14, "session_id": "48", "messages_count": 1, "has_persona_data": true}}, {"test": "npc_context_persistence", "status": "success", "message": "上下文持久化分析完成，共分析5个会话", "details": {"total_sessions": 5, "sessions_with_messages": 0, "sessions_with_npc_info": 0, "sessions_with_persona": 0, "context_analysis": [{"session_id": 3, "npc_id": "1", "npc_name": "未知", "user_id": "1", "message_count": 0, "has_npc_info": false, "has_persona_data": false}, {"session_id": 4, "npc_id": "1", "npc_name": "未知", "user_id": "1", "message_count": 0, "has_npc_info": false, "has_persona_data": false}, {"session_id": 5, "npc_id": "999", "npc_name": "未知", "user_id": "999", "message_count": 0, "has_npc_info": false, "has_persona_data": false}]}}]}