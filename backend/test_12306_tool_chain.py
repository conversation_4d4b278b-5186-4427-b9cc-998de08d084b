#!/usr/bin/env python3
"""
12306工具调用链路测试
测试从用户查询到工具执行的完整流程
"""
import asyncio
import sys
import os
import json
import requests
from datetime import datetime

# 添加backend目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from dotenv import load_dotenv
load_dotenv()

async def test_12306_tool_chain():
    """测试12306工具调用完整链路"""
    print("🚄 测试12306工具调用链路")
    print("=" * 50)
    
    # 用户查询 - 使用更明确的查询格式
    user_query = "BJP 到 SHH 2025-04-15的火车票有哪些"
    print(f"👤 用户查询: {user_query}")
    
    try:
        # 1. 测试工具枚举
        print(f"\n📋 步骤1: 枚举所有工具")
        from services.tool_manager_service import tool_manager_service
        
        all_tools = tool_manager_service.enumerate_all_tools(use_cache=False)
        print(f"   找到 {len(all_tools)} 个工具")
        
        # 查找12306相关工具
        train_tools = [tool for tool in all_tools if '12306' in tool.get('server', '') or 'train' in tool.get('name', '').lower()]
        print(f"   找到 {len(train_tools)} 个火车相关工具")
        
        for tool in train_tools:
            print(f"   - {tool.get('name')}: {tool.get('description', '')[:50]}...")
        
        # 2. 测试工具相关性排序
        print(f"\n🔍 步骤2: 工具相关性排序")
        ranked_tools = tool_manager_service.rank_tools_by_relevance(user_query, top_k=5)
        
        print(f"   排序结果 (前5个):")
        for i, tool in enumerate(ranked_tools):
            score = tool.get('relevance_score', 0)
            print(f"   {i+1}. {tool.get('name')} (相关性: {score:.3f})")
        
        # 3. 选择最相关的工具
        if ranked_tools:
            selected_tool = ranked_tools[0]
            print(f"\n⚡ 步骤3: 选择工具执行")
            print(f"   选择工具: {selected_tool.get('name')}")
            print(f"   相关性得分: {selected_tool.get('relevance_score', 0):.3f}")
            
            # 4. 执行工具
            print(f"\n🔧 步骤4: 执行工具")
            from services.mcp_service import mcp_service
            
            # 构建工具参数
            tool_params = {
                "departure": "北京",
                "destination": "上海",
                "date": datetime.now().strftime("%Y-%m-%d")
            }
            
            print(f"   工具参数: {tool_params}")
            
            result = mcp_service.execute_tool(
                selected_tool.get('name'),
                selected_tool.get('server', 'builtin'),
                **tool_params
            )
            
            print(f"   执行结果:")
            if result.get('success'):
                print(f"   ✅ 执行成功")
                tool_result = result.get('result', {})
                
                # 显示火车信息
                if 'trains' in tool_result:
                    trains = tool_result['trains']
                    print(f"   🚄 找到 {len(trains)} 班高铁:")
                    for train in trains:
                        print(f"      {train.get('train_number')}: {train.get('departure_time')} → {train.get('arrival_time')} ({train.get('price')})")
                else:
                    print(f"   📊 结果: {json.dumps(tool_result, ensure_ascii=False, indent=6)}")
            else:
                print(f"   ❌ 执行失败: {result.get('error', '未知错误')}")
        
        # 5. 测试增强LLM集成
        print(f"\n🤖 步骤5: 测试增强LLM集成")
        from services.enhanced_llm_service import get_enhanced_llm_service
        
        enhanced_llm = get_enhanced_llm_service()
        
        # 判断是否应该使用工具
        should_use_tools = enhanced_llm._should_use_tools(user_query, [])
        print(f"   应该使用工具: {should_use_tools}")
        
        if should_use_tools:
            # 获取相关工具用于LLM
            llm_tools = enhanced_llm._get_relevant_tools_for_llm(user_query)
            print(f"   为LLM选择了 {len(llm_tools)} 个工具")
            
            for tool in llm_tools:
                print(f"   - {tool['function']['name']}")
        
        # 6. 生成完整响应
        print(f"\n💬 步骤6: 生成完整响应")
        try:
            response = await enhanced_llm.generate_response_with_tools(
                user_query,
                [],
                "你是一个友善的AI助手，可以帮助用户查询火车信息。"
            )
            
            if response.get('success'):
                print(f"   ✅ 响应生成成功")
                print(f"   使用了 {response.get('tool_calls_made', 0)} 个工具调用")
                print(f"   使用的工具: {response.get('tools_used', [])}")
                
                speak_content = response.get('speak_content', {})
                response_text = speak_content.get('text', '')
                print(f"   🗣️ 响应内容: {response_text[:200]}...")
            else:
                print(f"   ❌ 响应生成失败")
        
        except Exception as e:
            print(f"   ⚠️ LLM响应生成异常: {e}")
        
        print(f"\n🎉 12306工具调用链路测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def test_api_endpoint():
    """测试API端点"""
    print(f"\n🌐 测试API端点")
    print("-" * 30)
    
    base_url = "http://localhost:8000"
    
    # 测试增强对话API
    try:
        payload = {
            "user_input": "查询今天从北京到上海的高铁信息",
            "use_tools": True,
            "system_prompt": "你是一个友善的AI助手，可以帮助用户查询火车信息。"
        }
        
        print(f"📡 调用增强对话API...")
        response = requests.post(
            f"{base_url}/api/tools/enhanced-chat",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功")
            
            if result.get('success'):
                api_response = result.get('response', {})
                print(f"工具调用次数: {api_response.get('tool_calls_made', 0)}")
                print(f"使用的工具: {api_response.get('tools_used', [])}")
                
                speak_content = api_response.get('speak_content', {})
                response_text = speak_content.get('text', '')
                print(f"响应内容: {response_text[:200]}...")
            else:
                print(f"❌ API响应失败")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误: {response.text}")
    
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到服务器")
        print(f"💡 请确保服务器正在运行: cd backend && python main.py")
    except Exception as e:
        print(f"❌ API测试异常: {e}")

async def main():
    """主函数"""
    print("🚄 12306工具调用链路完整测试")
    print("=" * 60)
    
    # 测试本地服务
    success = await test_12306_tool_chain()
    
    # 测试API端点
    await test_api_endpoint()
    
    if success:
        print(f"\n🎉 测试完成！12306工具调用链路运行正常。")
        return 0
    else:
        print(f"\n❌ 测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)