#!/usr/bin/env python3
"""
测试MCP初始化修复
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_howtocook_mcp():
    """测试修复后的howtocook-mcp服务"""
    print("🍳 测试修复后的howtocook-mcp服务...")
    
    try:
        from services.mcp_client import SimpleMCPClient
        
        # 创建MCP客户端
        mcp_client = SimpleMCPClient()
        
        server_name = "howtocook-mcp"
        
        # 启动服务器
        print(f"🚀 启动 {server_name} 服务器...")
        success = await mcp_client.start_server(server_name)
        print(f"启动结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            # 获取工具列表
            print("📋 获取工具列表...")
            tools = await mcp_client.get_server_tools(server_name)
            print(f"获取到 {len(tools)} 个工具:")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description'][:50]}...")
            
            # 测试工具调用（如果有工具的话）
            if tools:
                tool_name = tools[0]['name']
                print(f"\n🔧 测试工具调用: {tool_name}")
                try:
                    result = await mcp_client.execute_tool(tool_name, server_name)
                    print(f"工具调用结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
                    if result.get('success'):
                        content = result.get('result', {}).get('content', '')
                        print(f"内容: {content[:100]}...")
                    else:
                        print(f"错误: {result.get('error')}")
                except Exception as e:
                    print(f"工具调用异常: {e}")
        
        # 停止服务器
        await mcp_client.stop_server(server_name)
        await mcp_client.cleanup()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_multiple_servers():
    """测试多个MCP服务器"""
    print("\n🔧 测试多个MCP服务器...")
    
    try:
        from services.mcp_client import SimpleMCPClient
        
        mcp_client = SimpleMCPClient()
        
        # 测试的服务器列表
        test_servers = [
            "howtocook-mcp",
            "12306-mcp",
            "amap-maps-streamableHTTP",
            "didi-streamableHTTP"
        ]
        
        results = {}
        
        for server_name in test_servers:
            if server_name not in mcp_client.server_configs:
                print(f"⚠️ 跳过未配置的服务器: {server_name}")
                continue
                
            print(f"\n🔍 测试服务器: {server_name}")
            
            # 启动服务器
            success = await mcp_client.start_server(server_name)
            results[server_name] = {
                "startup": success,
                "tools": 0,
                "type": mcp_client.server_configs[server_name].get("type", "local_command")
            }
            
            if success:
                # 获取工具列表
                tools = await mcp_client.get_server_tools(server_name)
                results[server_name]["tools"] = len(tools)
                print(f"  ✅ 启动成功，{len(tools)} 个工具")
            else:
                print(f"  ❌ 启动失败")
            
            # 停止服务器
            await mcp_client.stop_server(server_name)
        
        # 显示结果总结
        print("\n📊 测试结果总结:")
        for server_name, result in results.items():
            status = "✅" if result["startup"] else "❌"
            server_type = result["type"]
            tools_count = result["tools"]
            print(f"  {status} {server_name} ({server_type}): {tools_count} 个工具")
        
        await mcp_client.cleanup()
        
    except Exception as e:
        print(f"❌ 多服务器测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("🔧 MCP初始化修复测试")
    print("=" * 60)
    
    try:
        # 测试howtocook-mcp服务
        await test_howtocook_mcp()
        
        # 测试多个服务器
        await test_multiple_servers()
        
        print("\n" + "=" * 60)
        print("🎉 测试完成！")
        
        print("\n📋 修复总结:")
        print("  ✅ 添加了MCP协议初始化流程")
        print("  ✅ 处理非JSON启动消息")
        print("  ✅ 发送initialize请求和initialized通知")
        print("  ✅ 改进了错误处理和日志记录")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
