# db_helper/es_helper.py

from elasticsearch import Elasticsearch, NotFoundError
from elasticsearch.helpers import bulk
from datetime import datetime, timedelta
import hashlib
# 从您的配置文件中导入所需变量
import sys
sys.path.append("../")
from configs import ELASTICSEARCH_CONFIG


class ESService:
    """
    一个经过重构的、通过配置驱动的 Elasticsearch 帮助类。
    它封装了针对 'news' 和 'npc_log' 索引的常用操作。
    """
    def __init__(self):
        """
        通过传入的配置参数初始化 Elasticsearch 客户端。
        
        :param host: ES 主机名或IP
        :param port: ES 端口
        :param scheme: 连接协议 ('http' or 'httpshttps')
        :param user: (可选) 用户名
        :param password: (可选) 密码
        :param news_index: 新闻索引的名称
        :param npc_log_index: NPC日志索引的名称
        """
        self.news_index = ELASTICSEARCH_CONFIG['news_index']
        self.npc_log_index = ELASTICSEARCH_CONFIG['npc_log_index']
        
        try:
            hosts_url = f"{ELASTICSEARCH_CONFIG['scheme']}://{ELASTICSEARCH_CONFIG['host']}:{ELASTICSEARCH_CONFIG['port']}"
            
            if ELASTICSEARCH_CONFIG['user'] and ELASTICSEARCH_CONFIG['password']:
                # 使用用户名和密码进行认证
                self.client = Elasticsearch([hosts_url], http_auth=(ELASTICSEARCH_CONFIG['user'], ELASTICSEARCH_CONFIG['password']))
            else:
                self.client = Elasticsearch([hosts_url])
            
            if not self.client.ping():
                raise ConnectionError("无法连接到 Elasticsearch 集群。")
            
            print(f"成功连接到 Elasticsearch ({hosts_url})！")
            print(f" - News Index: '{self.news_index}'")
            print(f" - NPC Log Index: '{self.npc_log_index}'")

        except Exception as e:
            print(f"初始化 Elasticsearch 客户端失败: {e}")
            self.client = None
    
    # --- 通用 News 索引方法 (已更新为使用 self.news_index) ---

    def get_news_by_id(self, news_id):
        if not self.client: return None
        try:
            res = self.client.get(index=self.news_index, id=news_id)
            return res.get('_source')
        except NotFoundError:
            return None
        except Exception as e:
            print(f"从索引 '{self.news_index}' 获取新闻失败: {e}")
            return None
    

    def _enrich_hits(self, hits: list) -> list[dict]:
        """一个内部辅助函数，将_id注入到_source中。"""
        enriched_results = []
        for hit in hits:
            source = hit.get('_source', {})
            source['id'] = hit.get('_id') # 将元数据_id注入到文档内容中
            enriched_results.append(source)
        return enriched_results

    def search_news(self, query_text, fields=["title", "summary", "content"], size=10):
        if not self.client: return []
        query = {"multi_match": {"query": query_text, "fields": fields}}
        try:
            res = self.client.search(index=self.news_index, body={"query": query}, size=size)
            return self._enrich_hits(res['hits']['hits'])
        except Exception as e:
            print(f"在索引 '{self.news_index}' 中搜索新闻失败: {e}")
            return []

    def search_with_dsl(self, dsl_query: dict, size: int = 10) -> list:
        if not self.client: return []
        try:
            res = self.client.search(index=self.news_index, body=dsl_query, size=size)
            # 使用新的辅助函数来丰富结果
            return self._enrich_hits(res['hits']['hits'])
        except Exception as e:
            print(f"在索引 '{self.news_index}' 中使用DSL搜索失败: {e}")
            return []
            
    def get_npc_current_activity(self, npc_name):
        """
        获取指定NPC当前正在进行的活动。(兼容 v7.x 版本)
        逻辑：查找时间上小于等于现在、并且离现在最近的一条记录。
        """
        if not self.client: return None
        
        query = {
            "bool": {
                "filter": [
                    {"term": {"npc_name": npc_name}},
                    {"range": {"event_time": {"lte": datetime.now().strftime("%Y-%m-%dT%H:%M:%S")}}}
                ]
            }
        }
        sort = [{"event_time": {"order": "desc"}}]
        
        try:
            # --- 关键修改：将 query 和 sort 放入 body 字典中 ---
            res = self.client.search(
                index=self.npc_log_index,
                body={
                    "query": query,
                    "sort": sort
                },
                size=1
            )
            return res['hits']['hits'][0]['_source'] if res['hits']['hits'] else None
        except Exception as e:
            print(f"从 '{self.npc_log_index}' 查询NPC当前活动失败: {e}")
            return None

    def _get_reflection_doc_id(self, npc_name: str, reflect_date_str: str) -> str:
        """
        为每日反思生成一个确定的、唯一的文档ID。
        格式: NPC名字-日期
        """
        return f"{npc_name}-{reflect_date_str}"

    def add_or_update_reflection(self, npc_name: str, reflect_date_str: str, reflection_text: str) -> bool:
        """
        为指定NPC添加或更新某一天的反思。
        此操作是幂等的，对同一天重复调用会直接覆盖。

        :param npc_name: NPC的名字。
        :param reflect_date_str: 反思的日期，格式 'YYYY-MM-DD'。
        :param reflection_text: 完整的反思文本内容。
        :return: 操作是否成功。
        """
        if not self.client: return False

        doc_id = self._get_reflection_doc_id(npc_name, reflect_date_str)
        document = {
            "npc_name": npc_name,
            "reflect_date": reflect_date_str,
            "reflection_text": reflection_text,
            "add_time": datetime.now()
        }

        try:
            self.client.index(
                index="npc_daily_reflect", # 使用新的索引名
                id=doc_id,
                document=document,
                refresh=True # 确保写入后可立即被读取
            )
            print(f"成功为NPC '{npc_name}' 添加/更新了 '{reflect_date_str}' 的反思。")
            return True
        except Exception as e:
            print(f"为NPC '{npc_name}' 添加/更新反思失败: {e}")
            return False

    def get_reflection_by_date(self, npc_name: str, reflect_date_str: str) -> str | None:
        """
        获取指定NPC在特定日期的反思内容。

        :param npc_name: NPC的名字。
        :param reflect_date_str: 要查询的日期，格式 'YYYY-MM-DD'。
        :return: 反思的文本内容，如果不存在则返回 None。
        """
        if not self.client: return None

        doc_id = self._get_reflection_doc_id(npc_name, reflect_date_str)
        try:
            res = self.client.get(index="npc_daily_reflect", id=doc_id)
            return res['_source'].get('reflection_text')
        except NotFoundError:
            print(f"未找到NPC '{npc_name}' 在 '{reflect_date_str}' 的反思。")
            return None
        except Exception as e:
            print(f"获取NPC '{npc_name}' 在 '{reflect_date_str}' 的反思时出错: {e}")
            return None

    def get_previous_day_reflection(self, npc_name: str) -> str | None:
        """
        获取指定NPC昨天的反思内容。

        :param npc_name: NPC的名字。
        :return: 昨天反思的文本内容，如果不存在则返回 None。
        """
        # 计算昨天的日期
        yesterday = datetime.now() - timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y-%m-%d')
        
        return self.get_reflection_by_date(npc_name, yesterday_str)
    
    def search_npc_logs(self, npc_name: str, query_text: str, start_time: str, end_time: str = "now", size: int = 3) -> list:
        """
        在指定时间范围内，根据关键词搜索NPC的日志。

        :param npc_name: NPC的名字。
        :param query_text: 要搜索的关键词或句子。
        :param start_time: 搜索的开始时间 (支持ES日期数学表达式, e.g., "now-3d/d")。
        :param end_time: 搜索的结束时间 (默认为 "now")。
        :param size: 返回结果的数量。
        :return: 包含完整_source的日志文档列表。
        """
        if not self.client: return []

        query = {
            "bool": {
                "must": [
                    {"multi_match": {"query": query_text, "fields": ["abstract^2", "content"]}}
                ],
                "filter": [
                    {"term": {"npc_name": npc_name}},
                    {"range": {"event_time": {"gte": start_time, "lte": end_time}}}
                ]
            }
        }
        sort = [{"event_time": {"order": "desc"}}]
        print({"query": query, "sort": sort})
        try:
            res = self.client.search(
                index=self.npc_log_index,
                body={"query": query, "sort": sort},
                size=size
            )
            # 使用我们之前创建的辅助函数来丰富结果
            return self._enrich_hits(res['hits']['hits'])
        except Exception as e:
            print(f"从 '{self.npc_log_index}' 搜索NPC日志失败: {e}")
            return []

es_service = ESService()