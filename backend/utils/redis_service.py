# redis_service.py

import redis
import sys
sys.path.append("../")
from configs import REDIS_CONFIG

class RedisService:
    """
    一个用于处理所有 Redis 交互的基础服务库。
    它通过连接池管理连接，并提供常用的 Redis 命令封装。
    """
    def __init__(self, redis_config_dict=REDIS_CONFIG):
        self.config = redis_config_dict
        self._pool = None
        self.client = None
        self._initialize_pool()

    def _initialize_pool(self):
        """根据配置初始化 Redis 连接池。"""
        if not self.config:
            print("Redis 配置错误。请检查 configs.py 文件。")
            return
        try:
            # 创建一个连接池，这比每次都创建新连接要高效得多
            self._pool = redis.ConnectionPool(**self.config)
            self.client = redis.Redis(connection_pool=self._pool)
            # 测试连接
            self.ping()
            print("Redis 服务连接成功。")
        except redis.exceptions.AuthenticationError:
            print(f"Redis 认证失败 (host: {self.config.get('host')})。请检查密码。")
        except redis.exceptions.ConnectionError as e:
            print(f"Redis 连接失败 (host: {self.config.get('host')}): {e}")
        except Exception as e:
            print(f"初始化 Redis 连接池时发生未知错误: {e}")

    def ping(self) -> bool:
        """测试 Redis 连接是否正常。"""
        if not self.client: return False
        try:
            return self.client.ping()
        except redis.exceptions.ConnectionError:
            return False

    def get(self, key: str) -> str | None:
        """获取指定 key 的值。"""
        if not self.client: return None
        try:
            return self.client.get(key)
        except Exception as e:
            print(f"Redis GET 操作失败 (key: {key}): {e}")
            return None

    def set(self, key: str, value: str, ex: int = None) -> bool:
        """
        设置 key 的值。
        
        Args:
            key (str): 键。
            value (str): 值。
            ex (int, optional): 过期时间（秒）。默认为 None (不过期)。
        
        Returns:
            bool: 操作是否成功。
        """
        if not self.client: return False
        try:
            return self.client.set(key, value, ex=ex)
        except Exception as e:
            print(f"Redis SET 操作失败 (key: {key}): {e}")
            return False

    def delete(self, *keys: str) -> int:
        """删除一个或多个 key。"""
        if not self.client: return 0
        try:
            return self.client.delete(*keys)
        except Exception as e:
            print(f"Redis DELETE 操作失败 (keys: {keys}): {e}")
            return 0
    
    def hgetall(self, name: str) -> dict:
        """获取哈希表中所有字段和值。"""
        if not self.client: return {}
        try:
            return self.client.hgetall(name)
        except Exception as e:
            print(f"Redis HGETALL 操作失败 (name: {name}): {e}")
            return {}

    def hset(self, name: str, key: str, value: str) -> int:
        """在哈希表中设置一个字段的值。"""
        if not self.client: return 0
        try:
            return self.client.hset(name, key, value)
        except Exception as e:
            print(f"Redis HSET 操作失败 (name: {name}, key: {key}): {e}")
            return 0

# 创建一个全局实例，方便其他模块直接导入使用
redis_service = RedisService()