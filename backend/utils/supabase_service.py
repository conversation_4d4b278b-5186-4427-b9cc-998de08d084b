import os
from supabase import create_client, Client
from typing import List, Dict, Any, Optional
import json

class SupabaseService:
    """
    Supabase服务类，用于处理与Supabase数据库的交互
    """
    
    def __init__(self, table_name: str = "npc_persona"):
        """
        初始化Supabase客户端
        从环境变量中获取SUPABASE_URL和SUPABASE_KEY
        
        Args:
            table_name (str): 数据库表名，默认为"npc_persona"
        """
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.table_name = table_name
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL和SUPABASE_KEY环境变量未设置")
            
        self.client: Client = create_client(self.supabase_url, self.supabase_key)
    
    def get_npc_personna_by_id(self, npc_id: str) -> Optional[Dict[str, Any]]:
        """
        根据NPC ID获取NPC角色信息
        
        Args:
            npc_id (str): NPC的唯一标识符
            
        Returns:
            Optional[Dict[str, Any]]: NPC角色信息，如果未找到则返回None
        """
        try:
            response = self.client.table(self.table_name).select("*").eq("npc_id", npc_id).execute()
            if response.data:
                return response.data[0]
            return None
        except Exception as e:
            print(f"查询NPC角色信息时出错: {e}")
            return None
    
    def get_npc_personna_by_user_id(self, user_id: str) -> List[Dict[str, Any]]:
        """
        根据用户ID获取该用户的所有NPC角色信息
        
        Args:
            user_id (str): 用户的唯一标识符
            
        Returns:
            List[Dict[str, Any]]: NPC角色信息列表
        """
        try:
            response = self.client.table(self.table_name).select("*").eq("user_id", user_id).execute()
            return response.data if response.data else []
        except Exception as e:
            print(f"查询用户NPC角色信息时出错: {e}")
            return []
    
    def get_all_npc_personnas(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取所有NPC角色信息
        
        Args:
            limit (Optional[int]): 限制返回的记录数
            
        Returns:
            List[Dict[str, Any]]: NPC角色信息列表
        """
        try:
            query = self.client.table(self.table_name).select("*")
            if limit:
                query = query.limit(limit)
            response = query.execute()
            return response.data if response.data else []
        except Exception as e:
            print(f"查询所有NPC角色信息时出错: {e}")
            return []
    
    def insert_npc_personna(self, npc_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        插入新的NPC角色信息
        
        Args:
            npc_data (Dict[str, Any]): NPC角色数据
            
        Returns:
            Optional[Dict[str, Any]]: 插入后的NPC角色信息，如果失败则返回None
        """
        try:
            response = self.client.table(self.table_name).insert(npc_data).execute()
            if response.data:
                return response.data[0]
            return None
        except Exception as e:
            print(f"插入NPC角色信息时出错: {e}")
            return None
    
    def update_npc_personna(self, npc_id: str, npc_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        更新NPC角色信息
        
        Args:
            npc_id (str): NPC的唯一标识符
            npc_data (Dict[str, Any]): 要更新的NPC角色数据
            
        Returns:
            Optional[Dict[str, Any]]: 更新后的NPC角色信息，如果失败则返回None
        """
        try:
            response = self.client.table(self.table_name).update(npc_data).eq("npc_id", npc_id).execute()
            if response.data:
                return response.data[0]
            return None
        except Exception as e:
            print(f"更新NPC角色信息时出错: {e}")
            return None
    
    def delete_npc_personna(self, npc_id: str) -> bool:
        """
        删除NPC角色信息
        
        Args:
            npc_id (str): NPC的唯一标识符
            
        Returns:
            bool: 删除是否成功
        """
        try:
            response = self.client.table(self.table_name).delete().eq("npc_id", npc_id).execute()
            return True
        except Exception as e:
            print(f"删除NPC角色信息时出错: {e}")
            return False

# 创建全局实例，方便其他模块直接导入使用
supabase_service = SupabaseService()
