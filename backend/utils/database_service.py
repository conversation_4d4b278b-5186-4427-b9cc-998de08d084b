# database_service.py

import pymysql
from pymysql.cursors import DictCursor
# 从您的配置文件中导入所需变量
import sys
sys.path.append("../")
from configs import NEWS_MYSQL_CONFIG, CHAT_HISTORY_CONFIG

class DatabaseService:
    """
    一个用于处理所有数据库交互的基础服务库。
    它会自动从 configs.py 文件中读取数据库连接信息。
    """
    def __init__(self, db_config_dict=NEWS_MYSQL_CONFIG):
        self.db_config_dict = db_config_dict
        self.db_config = self._parse_db_config()
        if not self.db_config:
            print(f"{self.db_config_dict['database']}数据库配置错误。请检查 configs.py 文件。")

    def _parse_db_config(self) -> dict:
        """从 hostname 字符串中解析出 host 和 port。"""
        try:
            return {
                'host': self.db_config_dict['host'],
                'user': self.db_config_dict['user'],
                'password': self.db_config_dict['password'],
                'database': self.db_config_dict['database'],
                'port': self.db_config_dict['port'],
                'cursorclass': pymysql.cursors.DictCursor
            }
        except (ValueError, IndexError) as e:
            print(f"解析{self.db_config_dict['database']}数据库主机名和端口失败 ('{hostname}'): {e}")
            return None

    def _get_connection(self, autocommit=False):
        """建立数据库连接"""
        if not self.db_config:
            return None
        try:
            # 将autocommit参数传递给连接函数
            return pymysql.connect(**self.db_config, autocommit=autocommit)
        except pymysql.MySQLError as e:
            print(f"{self.db_config_dict['database']}数据库连接失败: {e}")
            return None

    def fetch_all(self, sql: str, params: tuple = None) -> list[dict]:
        """执行 SELECT 查询并返回所有结果。"""
        conn = self._get_connection(autocommit=True)
        if not conn: return []
        
        try:
            with conn.cursor(DictCursor) as cursor:
                cursor.execute(sql, params)
                results = cursor.fetchall()
                return results if results else []
        except pymysql.MySQLError as e:
            print(f"{self.db_config_dict['database']}查询执行失败: {e}")
            return []
        finally:
            if conn: conn.close()

    def execute_query(self, sql: str, params: tuple = None) -> int:
        """执行 INSERT, UPDATE, DELETE 等修改性查询。"""
        conn = self._get_connection(autocommit=True)
        if not conn: return 0
        
        try:
            with conn.cursor() as cursor:
                affected_rows = cursor.execute(sql, params)
                conn.commit()
                return affected_rows
        except pymysql.MySQLError as e:
            print(f"{self.db_config_dict['database']}修改查询执行失败: {e}")
            if conn: conn.rollback()
            return 0
        finally:
            if conn: conn.close()

# 创建一个全局实例，方便其他模块直接导入使用
db_service = DatabaseService()
user_hist_db_service = DatabaseService(CHAT_HISTORY_CONFIG)