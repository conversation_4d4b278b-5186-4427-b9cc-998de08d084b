import logging
import sys
import os
import json
from datetime import datetime
try:
    from volcenginesdkarkruntime import Ark
except ImportError:
    print("错误：volcenginesdk-ark-runtime 库未安装。")
    print("请使用 'pip install volcenginesdk-ark-runtime' 命令进行安装。")
    sys.exit(1)
sys.path.append("../")
import configs

def to_ark_format(gemini_tools_list):
    """一个辅助函数，将Gemini格式的工具声明转换为Ark格式。"""
    return [{"type": "function", "function": gemini_tool} for gemini_tool in gemini_tools_list]

def to_ark_response_api_format(gemini_tools_list):
    """一个辅助函数，将Gemini格式的工具声明转换为Ark格式，不修改原始输入。"""
    res = []
    for gemini_tool in gemini_tools_list:
        copied_tool = gemini_tool.copy()
        copied_tool['type'] = 'function'
        res.append(copied_tool)
    return res

def setup_logger(log_prefix="run_log"):
    """
    Configures a logger to write to a timestamped file in a dedicated 'logs' directory
    at the project root.
    """
    try:
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    except NameError:
        project_root = os.path.abspath("..")

    logs_dir = os.path.join(project_root, 'logs')
    os.makedirs(logs_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_filename = f"{log_prefix}_{timestamp}.log"
    log_filepath = os.path.join(logs_dir, log_filename)
    
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    if logger.hasHandlers():
        logger.handlers.clear()

    # File Handler
    file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    # Stream Handler (for console output)
    # stream_handler = logging.StreamHandler(sys.stdout)
    # stream_handler.setLevel(logging.INFO)
    # stream_formatter = logging.Formatter('%(message)s')
    # stream_handler.setFormatter(stream_formatter)
    # logger.addHandler(stream_handler)
    
    print(f"Logging to file: {log_filepath}") # Helpful message to confirm path
    return logger

# You can create a global logger instance to be imported by other modules
logger = setup_logger()


def create_ark_session_and_get_id(system_prompt):
    """
    创建一个新的会话上下文，并从中解析出 ID。
    """
    client = Ark(api_key=configs.VOLC_ACCESSKEY)

    model_id = configs.ARK_MODEL_ENDPOINT 

    print(f"----- 正在为模型 '{model_id}' 创建会话上下文 -----")
    
    try:
        # 4. 发起请求
        response = client.context.create(
            model=model_id,
            mode="session",
            messages=[{"content": system_prompt, "role": "system"}],
            ttl=3600,
            truncation_strategy={
                "type": "rolling_tokens",
                "rolling_tokens": True,
                "max_window_tokens": 65536,
                "rolling_window_tokens": 4096
            }
        )

        context_id = response.id
        configs.ARK_SESSION_ID = context_id

        logger.info("\n----- 解析结果 -----")
        logger.info(f"✅ 成功提取到 Context ID: {context_id}")

    except Exception as e:
        # 捕获并打印 SDK 特定的异常，获取更详细的错误信息
        logger.error(f"\n❌ API 请求失败: {e}")

class SFTLogger:
    """
    一个专门用于记录SFT（Supervised Fine-Tuning）训练数据的记录器。
    它将每个完整的对话回合（turn）格式化为一行JSON，并写入指定文件。
    """
    def __init__(self, file_path: str):
        """
        初始化记录器。

        :param file_path: SFT数据要保存的文件路径。
        """
        try:
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        except NameError:
            project_root = os.path.abspath("..")

        logs_dir = os.path.join(project_root, 'sft-logs')
        os.makedirs(logs_dir, exist_ok=True)
        log_prefix = configs.SFT_LOG_PREFIX

        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self.log_filename = f"{log_prefix}_{timestamp}.jsonl"
        self.log_filepath = os.path.join(logs_dir, self.log_filename)
        self.file_path = self.log_filepath
        
        # 创建一个专用的logger，避免与其他logger冲突
        self.logger = logging.getLogger('SFTLogger')
        self.logger.setLevel(logging.INFO)
        
        # 防止将日志消息传播到根记录器
        self.logger.propagate = False
        
        # 如果已经有处理器，则先移除，防止重复添加
        if self.logger.hasHandlers():
            self.logger.handlers.clear()

        # 创建一个只输出到文件的处理器
        handler = logging.FileHandler(self.file_path, encoding='utf-8')
        
        # SFT格式要求每行就是纯JSON，不需要时间戳、日志级别等前缀
        # 所以我们创建一个只输出消息本身的格式化器
        formatter = logging.Formatter('%(message)s')
        handler.setFormatter(formatter)
        
        self.logger.addHandler(handler)
        print(f"SFTLogger initialized. Data will be saved to {self.file_path}")

    def log_turn(self, messages: list, tools: list):
        """
        记录一个完整的对话回合。

        :param messages: 一个包含对话消息（system, user, assistant, tool）的列表。
        :param tools: 本次对话可用的工具列表。
        """
        if not messages:
            return
            
        sft_record = {
            "messages": messages,
            "tools": tools
        }
        
        try:
            # ensure_ascii=False 确保中文字符不被转义
            json_string = json.dumps(sft_record, ensure_ascii=False)
            self.logger.info(json_string)
        except Exception as e:
            # 使用常规日志记录SFT日志记录本身的错误
            logging.error(f"Failed to log SFT data: {e}", exc_info=True)