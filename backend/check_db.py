import os
from supabase import create_client, Client
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_database_connection():
    """
    Connects to Supabase and fetches NPCs to verify the connection and credentials.
    """
    logger.info("🚀 Starting Supabase connection test...")

    # Load environment variables from .env file
    load_dotenv()

    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")

    if not supabase_url or not supabase_key:
        logger.error("❌ ERROR: SUPABASE_URL and SUPABASE_KEY must be set in the .env file.")
        return

    logger.info(f"Attempting to connect to Supabase URL: {supabase_url}")

    try:
        # Initialize Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)
        logger.info("✅ Supabase client initialized.")

        # Perform a test query to fetch NPCs
        logger.info("Attempting to fetch NPCs from the 'npcs' table...")
        response = supabase.table("npcs").select("*").limit(5).execute()

        # Check for errors in the response
        if hasattr(response, 'data'):
             logger.info("✅ Successfully executed query.")
             if response.data:
                 logger.info(f"🎉 SUCCESS! Found {len(response.data)} NPCs:")
                 for npc in response.data:
                     logger.info(f"  - ID: {npc.get('id')}, Name: {npc.get('name')}")
             else:
                 logger.warning("⚠️ Query was successful, but no NPCs were found in the table.")
        else:
            logger.error(f"🔥 FAILED: The query did not return valid data. Response: {response}")


    except Exception as e:
        logger.error("🔥 DATABASE CONNECTION FAILED")
        logger.error(f"   Error details: {e}")
        logger.error("   Please check your SUPABASE_URL and SUPABASE_KEY in the .env file.")

if __name__ == "__main__":
    check_database_connection()
