#!/usr/bin/env python3
"""
创建基础数据库表脚本
"""
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def create_base_tables():
    """创建基础数据库表"""
    print("🔧 创建基础数据库表...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    # 创建基础表
    try:
        print("\n📝 创建基础表...")
        
        # 创建users表
        test_user = {
            "username": "base_test_user",
            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
            "email": "<EMAIL>",
            "nickname": "基础测试用户",
            "is_active": True
        }
        
        result = supabase.table("users").insert(test_user).execute()
        if result.data:
            print("✅ users 表创建成功")
            # 删除测试数据
            supabase.table("users").delete().eq("username", "base_test_user").execute()
        else:
            print("⚠️ users 表可能已存在")
    except Exception as e:
        print(f"✅ users 表已存在或创建成功: {e}")
    
    # 创建npcs表
    try:
        test_npc = {
            "name": "base_test_npc",
            "description": "基础测试NPC",
            "system_prompt": "基础测试提示",
            "is_active": True
        }
        
        result = supabase.table("npcs").insert(test_npc).execute()
        if result.data:
            print("✅ npcs 表创建成功")
            # 删除测试数据
            supabase.table("npcs").delete().eq("name", "base_test_npc").execute()
        else:
            print("⚠️ npcs 表可能已存在")
    except Exception as e:
        print(f"✅ npcs 表已存在或创建成功: {e}")
    
    # 创建conversation_sessions表
    try:
        # 先确保有用户和NPC
        user_data = {
            "username": "base_session_test_user",
            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
            "email": "<EMAIL>",
            "nickname": "基础会话测试用户",
            "is_active": True
        }
        user_result = supabase.table("users").insert(user_data).execute()
        user_id = user_result.data[0]["id"] if user_result.data else 1
        
        npc_data = {
            "name": "base_session_test_npc",
            "description": "基础会话测试NPC",
            "system_prompt": "基础测试提示",
            "is_active": True
        }
        npc_result = supabase.table("npcs").insert(npc_data).execute()
        npc_id = npc_result.data[0]["id"] if npc_result.data else 1
        
        # 创建会话表
        test_session = {
            "user_id": user_id,
            "npc_id": npc_id,
            "is_active": True
        }
        
        result = supabase.table("conversation_sessions").insert(test_session).execute()
        if result.data:
            print("✅ conversation_sessions 表创建成功")
            # 删除测试数据
            supabase.table("conversation_sessions").delete().eq("user_id", user_id).execute()
            supabase.table("users").delete().eq("username", "base_session_test_user").execute()
            supabase.table("npcs").delete().eq("name", "base_session_test_npc").execute()
        else:
            print("⚠️ conversation_sessions 表可能已存在")
    except Exception as e:
        print(f"✅ conversation_sessions 表已存在或创建成功: {e}")
    
    # 创建conversation_messages表
    try:
        # 先创建用户、NPC和会话
        user_data = {
            "username": "base_message_test_user",
            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
            "email": "<EMAIL>",
            "nickname": "基础消息测试用户",
            "is_active": True
        }
        user_result = supabase.table("users").insert(user_data).execute()
        user_id = user_result.data[0]["id"] if user_result.data else 1
        
        npc_data = {
            "name": "base_message_test_npc",
            "description": "基础消息测试NPC",
            "system_prompt": "基础测试提示",
            "is_active": True
        }
        npc_result = supabase.table("npcs").insert(npc_data).execute()
        npc_id = npc_result.data[0]["id"] if npc_result.data else 1
        
        session_data = {
            "user_id": user_id,
            "npc_id": npc_id,
            "is_active": True
        }
        session_result = supabase.table("conversation_sessions").insert(session_data).execute()
        session_id = session_result.data[0]["id"] if session_result.data else "test-session-id"
        
        # 创建消息表
        test_message = {
            "session_id": session_id,
            "role": "user",
            "content": "基础测试消息"
        }
        
        result = supabase.table("conversation_messages").insert(test_message).execute()
        if result.data:
            print("✅ conversation_messages 表创建成功")
            # 删除测试数据
            supabase.table("conversation_messages").delete().eq("session_id", session_id).execute()
            supabase.table("conversation_sessions").delete().eq("user_id", user_id).execute()
            supabase.table("users").delete().eq("username", "base_message_test_user").execute()
            supabase.table("npcs").delete().eq("name", "base_message_test_npc").execute()
        else:
            print("⚠️ conversation_messages 表可能已存在")
    except Exception as e:
        print(f"✅ conversation_messages 表已存在或创建成功: {e}")
    
    # 插入默认NPC数据
    try:
        result = supabase.table("npcs").select("*").execute()
        if not result.data:
            default_npcs = [
                {
                    "name": "默认助手",
                    "description": "通用AI助手",
                    "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "is_active": True
                },
                {
                    "name": "朋友",
                    "description": "亲密朋友角色",
                    "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "is_active": True
                }
            ]
            
            for npc in default_npcs:
                supabase.table("npcs").insert(npc).execute()
                print(f"✅ 插入默认NPC: {npc['name']}")
        else:
            print("✅ 默认NPC数据已存在")
    except Exception as e:
        print(f"⚠️ 插入默认NPC数据时出错: {e}")
    
    print("\n🎉 基础数据库表创建完成！")
    return True

if __name__ == "__main__":
    print("🚀 开始创建基础数据库表...")
    
    if create_base_tables():
        print("\n✅ 所有基础表创建成功！")
        sys.exit(0)
    else:
        print("\n❌ 基础表创建失败")
        sys.exit(1)
