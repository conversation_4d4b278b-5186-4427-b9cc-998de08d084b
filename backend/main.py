import asyncio
import os
import logging
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, Response
import uvicorn
from supabase import create_client, Client
import numpy as np
import librosa
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import base64
import wave
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel

from services.vad_service import VADService
from services.multimodal_asr_service import MultimodalASRService
from services.enhanced_asr_service import EnhancedASRService
from services.llm_service import LLMService
from services.enhanced_llm_service import get_enhanced_llm_service
from services.tts_service import TTSService
from services.mcp_service import MCPService
from services.tool_manager_service import tool_manager_service

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Real-time Voice Chat API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Include tool endpoints
from api.tool_endpoints import router as tool_router
app.include_router(tool_router)

# 简单的聊天API端点用于测试工具调用
@app.post("/api/chat/test")
async def test_chat_with_tools(request: dict):
    """
    测试聊天和工具调用功能
    """
    try:
        message = request.get("message", "")
        if not message:
            raise HTTPException(status_code=400, detail="消息不能为空")

        # 调用增强LLM服务
        response = await enhanced_llm_service.generate_response_with_tools(
            user_input=message,
            conversation_history=[],
            system_prompt="你是一个有用的AI助手，可以使用各种工具来帮助用户。"
        )

        return {
            "success": True,
            "message": message,
            "response": response
        }

    except Exception as e:
        logger.error(f"测试聊天时出错: {e}")
        raise HTTPException(status_code=500, detail=f"聊天测试失败: {str(e)}")

# Add request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = datetime.now()
    logger.info(f"📥 {request.method} {request.url} - Headers: {dict(request.headers)}")

    response = await call_next(request)

    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"📤 {request.method} {request.url} - Status: {response.status_code} - Time: {process_time:.3f}s")

    return response

# Initialize Supabase client with error handling
try:
    supabase_url = os.getenv("SUPABASE_URL")
    # supabase_key = os.getenv("SUPABASE_KEY")
    supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if supabase_url and supabase_key:
        supabase: Client = create_client(supabase_url, supabase_key)
        logger.info("✅ Supabase client initialized successfully")
    else:
        logger.warning("⚠️ Supabase credentials not found, using fallback mode")
        supabase = None
except Exception as e:
    logger.error(f"❌ Failed to initialize Supabase client: {e}")
    supabase = None

# Initialize services
vad_service = VADService(os.getenv("SILERO_VAD_MODEL_PATH", "./models/silero_vad.onnx"))
asr_service = MultimodalASRService(
    model="Qwen2-Audio-7B-Instruct",
    api_key="EMPTY",
    api_base="http://172.16.1.151:20257/v1"
    # os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
)
# 增强的ASR服务 (支持Qwen + Gemini备用，Gemini优先)
enhanced_asr_service = EnhancedASRService(
    qwen_timeout=5,      # Qwen超时5秒
    gemini_timeout=10,   # Gemini超时10秒
    gemini_first=True    # Gemini优先模式
)
llm_service = LLMService(os.getenv("VOLCANO_API_KEY"), os.getenv("VOLCANO_ENDPOINT"))
enhanced_llm_service = get_enhanced_llm_service(os.getenv("VOLCANO_API_KEY"), os.getenv("VOLCANO_ENDPOINT"))
tts_service = TTSService(os.getenv("MINIMAX_API_KEY"), os.getenv("MINIMAX_GROUP_ID"))
mcp_service = MCPService()

# Simple in-memory user storage for testing
SIMPLE_USERS = {
    "test_user": {
        "id": 1,
        "username": "test_user",
        "password": "test_password",  # 简化版本，直接存储明文密码用于测试
        "email": "<EMAIL>",
        "nickname": "测试用户",
        "is_active": True
    },
    "admin": {
        "id": 2,
        "username": "admin", 
        "password": "admin123",
        "email": "<EMAIL>",
        "nickname": "管理员",
        "is_active": True
    }
}

# Simple in-memory session and message storage for fallback
MEMORY_SESSIONS = {}
MEMORY_MESSAGES = {}

logger.info("Simple auth system initialized with test users")

# Pydantic models for TTS
class TTSRequest(BaseModel):
    text: str
    emotion: str = "neutral"
    speed: float = 1.0
    voice_id: str = None

# Audio processing helper functions
async def convert_audio_to_numpy(audio_data: bytes, content_type: str = None) -> np.ndarray:
    """
    Convert various audio formats to numpy array with proper resource management
    """
    import io
    import tempfile

    logger.info(f"🎵 转换音频格式: {content_type}, 大小: {len(audio_data)} bytes")

    # Validate input
    if not audio_data:
        raise ValueError("Audio data is empty")

    temp_file_path = None
    try:
        # Create temporary file with proper cleanup
        with tempfile.NamedTemporaryFile(delete=False, suffix='.audio') as temp_file:
            temp_file.write(audio_data)
            temp_file_path = temp_file.name

        # Use librosa to load audio (supports many formats)
        audio_array, sample_rate = librosa.load(temp_file_path, sr=16000, mono=True)
        logger.info(f"📊 音频转换成功: shape={audio_array.shape}, sr={sample_rate}, duration={len(audio_array)/sample_rate:.2f}s")

        # Validate audio data
        if len(audio_array) == 0:
            raise ValueError("Audio conversion resulted in empty array")

        # Ensure audio is in float32 format with proper range
        if audio_array.dtype != np.float32:
            audio_array = audio_array.astype(np.float32)

        # Ensure audio is in [-1, 1] range
        max_abs = np.max(np.abs(audio_array))
        if max_abs > 1.0:
            audio_array = audio_array / max_abs
        elif max_abs == 0:
            logger.warning("⚠️ Audio array contains only zeros")

        return audio_array

    except Exception as librosa_error:
        logger.error(f"❌ Librosa failed to load audio: {librosa_error}")
        # Add a specific check for a common dependency issue with librosa
        if "audioread" in str(librosa_error):
            logger.error("🐍 This might be caused by missing the 'ffmpeg' backend for audio decoding.")
            logger.error("🐍 Please ensure 'ffmpeg' is installed on your system (e.g., 'brew install ffmpeg' on macOS).")
        
        # Fallback: try to parse as WAV directly
        try:
            logger.info("🔄 Attempting to parse as WAV as a fallback...")
            return await _parse_wav_fallback(audio_data)
        except Exception as wav_error:
            logger.error(f"❌ WAV fallback also failed: {wav_error}")
            raise ValueError(f"Unable to convert audio. Librosa error: {librosa_error}, WAV error: {wav_error}")

    finally:
        # Ensure temporary file is always cleaned up
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                logger.debug(f"🗑️ Cleaned up temporary file: {temp_file_path}")
            except Exception as cleanup_error:
                logger.warning(f"⚠️ Failed to cleanup temporary file {temp_file_path}: {cleanup_error}")

async def _parse_wav_fallback(audio_data: bytes) -> np.ndarray:
    """
    Fallback WAV parser with proper error handling
    """
    import io

    audio_io = io.BytesIO(audio_data)
    with wave.open(audio_io, 'rb') as wav_file:
        frames = wav_file.readframes(-1)
        sample_rate = wav_file.getframerate()
        sample_width = wav_file.getsampwidth()

        if sample_width == 2:  # 16-bit
            audio_array = np.frombuffer(frames, dtype=np.int16).astype(np.float32) / 32768.0
        elif sample_width == 4:  # 32-bit
            audio_array = np.frombuffer(frames, dtype=np.int32).astype(np.float32) / 2147483648.0
        else:
            raise ValueError(f"Unsupported sample width: {sample_width}")

        # Resample to 16kHz if needed
        if sample_rate != 16000:
            audio_array = librosa.resample(audio_array, orig_sr=sample_rate, target_sr=16000)

        logger.info(f"📊 WAV解析成功: shape={audio_array.shape}, 原始sr={sample_rate}")
        return audio_array


# Global connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, Dict] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_sessions[user_id] = {
            "session_id": None,
            "npc_id": None,
            "conversation_history": [],
            "is_speaking": False,
            "audio_buffer": []
        }
        logger.info(f"User {user_id} connected")
    
    def disconnect(self, user_id: str):
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        logger.info(f"User {user_id} disconnected")
    
    async def send_message(self, user_id: str, message: dict):
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {user_id}: {e}")

manager = ConnectionManager()

# Helper functions
async def get_user_by_id(user_id: int) -> Optional[Dict]:
    """Get user from database"""
    if supabase is None:
        logger.warning("⚠️ Supabase not available for user lookup")
        return None
    
    try:
        result = supabase.table("users").select("*").eq("id", user_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        logger.error(f"Error getting user: {e}")
        return None

# NPC缓存系统
NPC_CACHE = {}
NPC_CACHE_TTL = 300  # 5分钟缓存

# 默认NPC ID (使用一个固定的UUID作为fallback)
DEFAULT_NPC_UUID = "00000000-0000-0000-0000-000000000001"

async def get_available_npc_ids():
    """获取所有可用的NPC ID"""
    try:
        result = supabase.table("npcs").select("id, name, is_active").eq("is_active", True).execute()
        if result.data:
            npc_list = [(npc["id"], npc["name"]) for npc in result.data]
            logger.info(f"📋 找到 {len(npc_list)} 个可用的NPC")
            for npc_id, name in npc_list:
                logger.info(f"  - {name}: {npc_id}")
            return npc_list
        else:
            logger.warning("⚠️ 数据库中没有找到可用的NPC")
            return []
    except Exception as e:
        logger.error(f"获取NPC列表失败: {e}")
        return []

def get_valid_npc_id(npc_id):
    """将整数NPC ID转换为有效的UUID格式"""
    if isinstance(npc_id, int) or (isinstance(npc_id, str) and npc_id.isdigit()):
        # 对于整数ID，返回默认UUID
        return DEFAULT_NPC_UUID
    return npc_id

async def get_first_available_npc_id():
    """获取第一个可用的NPC ID"""
    available_npcs = await get_available_npc_ids()
    if available_npcs:
        return available_npcs[0][0]  # 返回第一个NPC的ID
    return DEFAULT_NPC_UUID

def generate_description_from_persona(persona_data):
    """从persona_data生成描述"""
    try:
        if isinstance(persona_data, str):
            persona = json.loads(persona_data)
        else:
            persona = persona_data
        
        name = persona.get("name", "未知")
        age = persona.get("age", "未知")
        occupation = persona.get("occupation", "未知")
        personality = persona.get("personality", "友善")
        background = persona.get("background", "")
        
        description = f"{name}，{age}岁，{occupation}。{personality}。{background}"
        return description.strip()
        
    except Exception as e:
        logger.warning(f"⚠️ 解析persona_data失败: {e}")
        return "AI助手角色"

def generate_system_prompt_from_persona(persona_data):
    """从persona_data生成系统提示词"""
    try:
        if isinstance(persona_data, str):
            persona = json.loads(persona_data)
        else:
            persona = persona_data
        
        name = persona.get("name", "助手")
        personality = persona.get("personality", "友善")
        background = persona.get("background", "")
        hobbies = persona.get("hobbies", [])
        education = persona.get("education", "")
        
        # 构建系统提示词
        prompt_parts = [
            f"你是{name}，{personality}。",
            f"背景：{background}" if background else "",
            f"教育背景：{education}" if education else "",
            f"兴趣爱好：{', '.join(hobbies)}" if hobbies else "",
            "请用自然的语调回复用户，保持角色一致性。",
            "回复格式必须为：<turn>\\n<THINK>\\n## 1. 意图分析: [分析用户意图]\\n## 2. 行动规划: [规划回应策略]\\n## 3. 工具选择与参数构建: [如需要]\\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\\n## 5. 最终输出序列: <SPEAK>\\n</THINK>\\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\\n<JUDGE>\\n本轮表现：[评分]/10分\\n优点：[优点分析]\\n缺点：[缺点分析]\\n重大失误：[如有]\\n</JUDGE>\\n</turn>"
        ]
        
        return " ".join([part for part in prompt_parts if part])
        
    except Exception as e:
        logger.warning(f"⚠️ 生成系统提示词失败: {e}")
        return "你是一个友善的AI助手，请用自然的语调回复用户。"

async def get_npc_by_id(npc_id) -> Optional[Dict]:
    """Get NPC from database with caching and persona enhancement"""
    # 支持字符串和整数ID
    cache_key = str(npc_id)
    
    # 检查缓存
    if cache_key in NPC_CACHE:
        cached_npc, cache_time = NPC_CACHE[cache_key]
        if datetime.now().timestamp() - cache_time < NPC_CACHE_TTL:
            logger.debug(f"🎯 从缓存获取NPC: {cached_npc.get('name', 'Unknown')}")
            return cached_npc
    
    if supabase is None:
        logger.warning("⚠️ Supabase not available for NPC lookup")
        return None
    
    try:
        # 检查npc_id是否为有效的UUID格式
        import uuid
        try:
            # 如果是整数，尝试转换为UUID格式
            if isinstance(npc_id, int) or (isinstance(npc_id, str) and npc_id.isdigit()):
                # 对于整数ID，我们需要查找一个默认的NPC或创建一个fallback
                logger.warning(f"⚠️ NPC ID {npc_id} is not UUID format, using fallback")
                return None

            # 验证UUID格式
            uuid.UUID(str(npc_id))
        except ValueError:
            logger.warning(f"⚠️ Invalid UUID format for NPC ID: {npc_id}")
            return None

        result = supabase.table("npcs").select("*").eq("id", npc_id).execute()

        if result.data:
            npc = result.data[0]

            # 处理persona数据，生成描述和系统提示词
            persona_data = npc.get('persona_data')
            if persona_data:
                description = generate_description_from_persona(persona_data)
                system_prompt = generate_system_prompt_from_persona(persona_data)
            else:
                description = "AI助手角色"
                system_prompt = "你是一个友善的AI助手，请用自然的语调回复用户。"

            # 返回增强的NPC对象
            enhanced_npc = {
                "id": npc.get("id"),
                "name": npc.get("name"),
                "description": description,
                "system_prompt": system_prompt,
                "is_active": npc.get("is_active"),
                "persona_data": persona_data,
                "avatar_url": npc.get("avatar_url")  # 可能为None
            }

            # 缓存结果
            NPC_CACHE[cache_key] = (enhanced_npc, datetime.now().timestamp())
            logger.info(f"✅ 获取并缓存NPC: {enhanced_npc['name']}")

            return enhanced_npc
        else:
            return None

    except Exception as e:
        logger.error(f"Error getting NPC: {e}")
        return None

async def create_conversation_session(user_id: int, npc_id: int) -> Optional[str]:
    """Create new conversation session"""
    # 首先检查supabase是否可用
    if supabase is None:
        logger.warning("⚠️ Supabase not available, using memory fallback")
    else:
        try:
            # Add timeout to prevent hanging
            import asyncio
            result = await asyncio.wait_for(
                asyncio.to_thread(
                    lambda: supabase.table("conversation_sessions").insert({
                        "user_id": user_id,
                        "npc_id": npc_id,
                        "is_active": True
                    }).execute()
                ),
                timeout=5.0  # 5 second timeout
            )
            session_id = result.data[0]["id"] if result.data else None
            if session_id:
                logger.info(f"✅ Database session created: {session_id}")
                return session_id
        except asyncio.TimeoutError:
            logger.error("❌ Database timeout when creating session")
        except Exception as e:
            logger.error(f"❌ Error creating session: {e}")
    
    # Fallback to memory storage
    import uuid
    from datetime import datetime
    session_id = str(uuid.uuid4())
    MEMORY_SESSIONS[session_id] = {
        "id": session_id,
        "user_id": user_id,
        "npc_id": npc_id,
        "is_active": True,
        "created_at": datetime.now().isoformat()
    }
    logger.info(f"💾 Using memory fallback session_id: {session_id}")
    return session_id

def save_audio_to_wav(audio_data: np.ndarray, filename: str, sample_rate: int = 16000):
    """Save audio data to WAV file for debugging"""
    try:
        # Ensure audio_recordings directory exists
        audio_dir = Path("audio_recordings")
        audio_dir.mkdir(exist_ok=True)
        
        # Full path for the audio file
        file_path = audio_dir / filename
        
        # Convert float32 audio data to int16
        if audio_data.dtype == np.float32:
            # Scale from [-1, 1] to [-32768, 32767]
            audio_int16 = (audio_data * 32767).astype(np.int16)
        else:
            audio_int16 = audio_data.astype(np.int16)
        
        # Save as WAV file
        with wave.open(str(file_path), 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        logger.info(f"📁 Audio saved: {filename} ({len(audio_data)} samples)")
        
    except Exception as e:
        logger.error(f"❌ Failed to save audio {filename}: {e}")

async def save_message(session_id: str, role: str, content: str, audio_url: Optional[str] = None, 
                      emotion: Optional[str] = None, speed: Optional[float] = None):
    """Save message to database with improved error handling"""
    if supabase is not None:
        try:
            import asyncio
            
            # 处理speed字段的类型问题
            speed_value = None
            if speed is not None:
                try:
                    # 确保speed是数字类型，数据库期望整数类型
                    speed_value = float(speed)
                    # 数据库字段是整数类型，需要转换
                    speed_value = int(speed_value * 10) if speed_value is not None else None  # 转换为整数（乘以10保留一位小数精度）
                except (ValueError, TypeError):
                    logger.warning(f"Invalid speed value: {speed}, using None")
                    speed_value = None
            
            message_data = {
                "session_id": session_id,
                "role": role,
                "content": content,
                "audio_url": audio_url,
                "emotion": emotion
            }
            
            # 只有当speed_value不为None时才添加到数据中
            if speed_value is not None:
                message_data["speed"] = speed_value
            
            await asyncio.wait_for(
                asyncio.to_thread(
                    lambda: supabase.table("conversation_messages").insert(message_data).execute()
                ),
                timeout=3.0  # 3 second timeout
            )
            logger.info(f"✅ Message saved to database for session {session_id}")
            return True
        except asyncio.TimeoutError:
            logger.warning(f"⚠️ Timeout saving message to database for session {session_id}")
        except Exception as e:
            logger.error(f"❌ Error saving message to database: {e}")
    else:
        logger.warning("⚠️ Supabase not available for message saving")
    
    # Fallback to memory storage
    try:
        from datetime import datetime
        if session_id not in MEMORY_MESSAGES:
            MEMORY_MESSAGES[session_id] = []
        
        message = {
            "session_id": session_id,
            "role": role,
            "content": content,
            "audio_url": audio_url,
            "emotion": emotion,
            "speed": speed,
            "created_at": datetime.now().isoformat()
        }
        MEMORY_MESSAGES[session_id].append(message)
        logger.info(f"Message saved to memory for session {session_id}")
        return True
    except Exception as e:
        logger.error(f"Error saving message to memory: {e}")
        return False

# API Routes
@app.get("/")
async def root():
    return {"message": "Real-time Voice Chat API", "status": "running"}

@app.get("/debug/users")
async def debug_users():
    """Debug endpoint to check users"""
    global SIMPLE_USERS
    return {"users": list(SIMPLE_USERS.keys()), "count": len(SIMPLE_USERS)}

@app.get("/debug/sessions")
async def debug_sessions():
    """Debug endpoint to check sessions"""
    global MEMORY_SESSIONS, MEMORY_MESSAGES
    return {
        "memory_sessions": MEMORY_SESSIONS,
        "memory_messages": {k: len(v) for k, v in MEMORY_MESSAGES.items()},
        "active_websocket_sessions": list(manager.user_sessions.keys())
    }

@app.get("/test")
async def test_endpoint():
    """Test endpoint for verifying connectivity"""
    test_question = "1+1等于几"
    
    # 创建假的 NPC 数据用于测试
    fake_npc = {
        "id": 1,
        "name": "测试助手",
        "description": "用于测试的AI助手",
        "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。对于数学问题，请直接给出答案。",
        "is_active": True
    }
    
    try:
        # 首先尝试从数据库获取 NPC
        npc = None
        db_available = False
        
        try:
            npc = await get_npc_by_id(1)
            if npc:
                db_available = True
                logger.info("从数据库获取到 NPC 数据")
        except Exception as db_e:
            logger.warning(f"数据库不可用，使用假数据: {db_e}")
        
        # 如果数据库不可用或没有数据，使用假数据
        if not npc:
            npc = fake_npc
            logger.info("使用假 NPC 数据进行测试")
        
        # 测试 LLM 响应
        try:
            response = await llm_service.generate_response(
                test_question,
                [],
                npc["system_prompt"]
            )
            
            if not response.get("success"):
                return {
                    "error": "LLM 处理失败", 
                    "details": response,
                    "database_status": "available" if db_available else "unavailable"
                }
                
            return {
                "question": test_question,
                "response": response["speak_content"]["text"],
                "npc_id": npc["id"],
                "npc_name": npc["name"],
                "database_status": "available" if db_available else "unavailable (using fake data)",
                "status": "success"
            }
        except Exception as llm_e:
            logger.error(f"LLM 服务错误: {llm_e}")
            return {
                "error": f"LLM 服务错误: {str(llm_e)}",
                "database_status": "available" if db_available else "unavailable"
            }
            
    except Exception as e:
        logger.error(f"测试端点错误: {e}")
        return {"error": f"测试端点错误: {str(e)}"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    services_status = {}
    
    # Check VAD service
    try:
        vad_status = "loaded" if hasattr(vad_service, 'session') and vad_service.session else "not loaded"
        services_status["vad"] = vad_status
    except Exception as e:
        services_status["vad"] = f"error: {str(e)}"
    
    # Check ASR service
    try:
        asr_status = "ready" if hasattr(asr_service, 'client') and asr_service.client else "configured"
        services_status["asr"] = asr_status
    except Exception as e:
        services_status["asr"] = f"error: {str(e)}"
    
    # Check LLM service
    try:
        llm_status = "configured" if llm_service else "not configured"
        services_status["llm"] = llm_status
    except Exception as e:
        services_status["llm"] = f"error: {str(e)}"
    
    # Check TTS service
    try:
        tts_status = "configured" if tts_service else "not configured"
        services_status["tts"] = tts_status
    except Exception as e:
        services_status["tts"] = f"error: {str(e)}"
    
    # Check MCP service
    try:
        mcp_status = "ready" if mcp_service else "not configured"
        services_status["mcp"] = mcp_status
    except Exception as e:
        services_status["mcp"] = f"error: {str(e)}"
    
    # Check Tool Manager service
    try:
        tool_stats = tool_manager_service.get_tool_statistics()
        services_status["tool_manager"] = {
            "status": "ready",
            "total_tools": tool_stats.get("total_tools", 0),
            "cache_valid": tool_stats.get("cache_status", {}).get("is_cached", False)
        }
    except Exception as e:
        services_status["tool_manager"] = f"error: {str(e)}"
    
    # Check Enhanced LLM service
    try:
        enhanced_llm_status = "ready" if enhanced_llm_service else "not configured"
        services_status["enhanced_llm"] = enhanced_llm_status
    except Exception as e:
        services_status["enhanced_llm"] = f"error: {str(e)}"
    
    # Check database connection
    try:
        if supabase is not None:
            result = supabase.table("npcs").select("count", count="exact").limit(1).execute()
            services_status["database"] = "connected"
        else:
            services_status["database"] = "not configured"
    except Exception as e:
        services_status["database"] = f"error: {str(e)}"
    
    # Check Enhanced ASR service
    try:
        enhanced_asr_test = enhanced_asr_service.test_connection()
        services_status["enhanced_asr"] = {
            "qwen": enhanced_asr_test.get("qwen", False),
            "gemini": enhanced_asr_test.get("gemini", False),
            "available": enhanced_asr_test.get("available_services", [])
        }
    except Exception as e:
        services_status["enhanced_asr"] = f"error: {str(e)}"

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": services_status,
        "version": "1.0.0"
    }

@app.get("/api/asr/test")
async def test_asr_connection():
    """测试ASR服务连接"""
    try:
        logger.info("🔌 测试ASR服务连接...")
        
        # 测试增强ASR服务连接
        connection_result = enhanced_asr_service.test_connection()
        
        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "asr_services": {
                "qwen_available": connection_result.get("qwen", False),
                "gemini_available": connection_result.get("gemini", False),
                "available_services": connection_result.get("available_services", [])
            },
            "message": f"可用ASR服务: {', '.join(connection_result.get('available_services', []))}"
        }
        
    except Exception as e:
        logger.error(f"❌ ASR连接测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"ASR connection test failed: {str(e)}")

@app.get("/npcs")
async def get_npcs():
    """Get available NPCs with enhanced persona information"""
    try:
        result = supabase.table("npcs").select("*").eq("is_active", True).execute()
        
        if result.data:
            enhanced_npcs = []
            for npc_data in result.data:
                # 使用增强的get_npc_by_id函数
                enhanced_npc = await get_npc_by_id(npc_data["id"])
                if enhanced_npc:
                    # 只返回必要的字段给前端
                    enhanced_npcs.append({
                        "id": enhanced_npc["id"],
                        "name": enhanced_npc["name"],
                        "description": enhanced_npc["description"],
                        "avatar_url": enhanced_npc.get("avatar_url"),
                        "is_active": enhanced_npc["is_active"]
                    })
            
            return {
                "npcs": enhanced_npcs, 
                "source": "database",
                "count": len(enhanced_npcs)
            }
        else:
            # 假数据作为备用
            fake_npcs = [
                {
                    "id": "fake-1",
                    "name": "默认助手",
                    "description": "通用AI助手，用于测试",
                    "avatar_url": None,
                    "is_active": True
                }
            ]
            logger.warning("数据库中没有 NPC 数据，返回假数据")
            return {"npcs": fake_npcs, "source": "fake_data", "count": len(fake_npcs)}
    except Exception as e:
        logger.warning(f"数据库获取 NPCs 失败: {e}")
        # 假数据作为备用
        fake_npcs = [
            {
                "id": "fake-1",
                "name": "默认助手",
                "description": "通用AI助手，用于测试",
                "avatar_url": None,
                "is_active": True
            }
        ]
        return {"npcs": fake_npcs, "source": "fake_data", "count": len(fake_npcs)}

@app.get("/npcs/{npc_id}")
async def get_npc_detail(npc_id: str):
    """Get detailed NPC information by ID"""
    try:
        npc = await get_npc_by_id(npc_id)
        
        if not npc:
            raise HTTPException(status_code=404, detail="NPC not found")
        
        return {
            "npc": npc,
            "source": "database"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取NPC详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get NPC details: {str(e)}")

@app.get("/npcs/{npc_id}/persona")
async def get_npc_persona(npc_id: str):
    """Get NPC persona data"""
    try:
        npc = await get_npc_by_id(npc_id)
        
        if not npc:
            raise HTTPException(status_code=404, detail="NPC not found")
        
        persona_data = npc.get('persona_data')
        if not persona_data:
            raise HTTPException(status_code=404, detail="NPC persona data not found")
        
        # 解析persona数据
        if isinstance(persona_data, str):
            persona = json.loads(persona_data)
        else:
            persona = persona_data
        
        return {
            "npc_id": npc_id,
            "npc_name": npc.get("name"),
            "persona": persona,
            "generated_description": npc.get("description"),
            "system_prompt_length": len(npc.get("system_prompt", ""))
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取NPC persona失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get NPC persona: {str(e)}")

@app.post("/npcs/{npc_id}/chat")
async def create_npc_chat_session(npc_id: str, user_id: int):
    """Create a new chat session with NPC"""
    try:
        # 验证NPC存在
        npc = await get_npc_by_id(npc_id)
        if not npc:
            raise HTTPException(status_code=404, detail="NPC not found")
        
        if not npc.get("is_active"):
            raise HTTPException(status_code=400, detail="NPC is not active")
        
        # 创建会话
        session_id = await create_conversation_session(user_id, npc_id)
        
        if not session_id:
            raise HTTPException(status_code=500, detail="Failed to create chat session")
        
        return {
            "session_id": session_id,
            "npc_id": npc_id,
            "npc_name": npc.get("name"),
            "user_id": user_id,
            "created_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建NPC聊天会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create chat session: {str(e)}")

@app.get("/sessions/{session_id}/context")
async def get_session_context(session_id: str, limit: int = 20):
    """Get conversation context for a session"""
    try:
        # 获取会话信息
        session_result = supabase.table("conversation_sessions").select("*").eq("id", session_id).execute()
        
        if not session_result.data:
            raise HTTPException(status_code=404, detail="Session not found")
        
        session = session_result.data[0]
        npc_id = session.get("npc_id")
        
        # 获取NPC信息
        npc = await get_npc_by_id(npc_id) if npc_id else None
        
        # 获取消息历史
        messages_result = supabase.table("conversation_messages").select("*").eq("session_id", session_id).order("created_at").limit(limit).execute()
        
        return {
            "session_id": session_id,
            "npc": {
                "id": npc.get("id") if npc else None,
                "name": npc.get("name") if npc else "Unknown",
                "description": npc.get("description") if npc else None
            },
            "messages": messages_result.data if messages_result.data else [],
            "message_count": len(messages_result.data) if messages_result.data else 0,
            "has_npc_info": bool(npc)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话上下文失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get session context: {str(e)}")

@app.post("/sessions/{session_id}/messages")
async def add_message_to_session(session_id: str, role: str, content: str, emotion: str = "neutral", speed: float = 1.0):
    """Add a message to a conversation session"""
    try:
        # 验证会话存在
        session_result = supabase.table("conversation_sessions").select("*").eq("id", session_id).execute()
        
        if not session_result.data:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # 保存消息
        success = await save_message(session_id, role, content, emotion=emotion, speed=speed)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to save message")
        
        return {
            "session_id": session_id,
            "role": role,
            "content": content,
            "emotion": emotion,
            "speed": speed,
            "created_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加消息失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to add message: {str(e)}")

@app.get("/npcs/cache/status")
async def get_npc_cache_status():
    """Get NPC cache status for debugging"""
    try:
        cache_info = {}
        current_time = datetime.now().timestamp()
        
        for cache_key, (npc_data, cache_time) in NPC_CACHE.items():
            age = current_time - cache_time
            cache_info[cache_key] = {
                "npc_name": npc_data.get("name", "Unknown"),
                "cache_age_seconds": round(age, 2),
                "is_expired": age > NPC_CACHE_TTL,
                "cached_at": datetime.fromtimestamp(cache_time).isoformat()
            }
        
        return {
            "cache_size": len(NPC_CACHE),
            "cache_ttl_seconds": NPC_CACHE_TTL,
            "cached_npcs": cache_info
        }
        
    except Exception as e:
        logger.error(f"获取缓存状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get cache status: {str(e)}")

@app.delete("/npcs/cache")
async def clear_npc_cache():
    """Clear NPC cache"""
    try:
        global NPC_CACHE
        cache_size = len(NPC_CACHE)
        NPC_CACHE.clear()
        
        logger.info(f"🗑️ 清空NPC缓存，清理了{cache_size}个条目")
        
        return {
            "message": f"Cache cleared successfully",
            "cleared_entries": cache_size
        }
        
    except Exception as e:
        logger.error(f"清空缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")

@app.post("/auth/register")
@app.get("/auth/register")
@app.options("/auth/register")
async def register_user(username: str, password: str, email: str = None, nickname: str = None):
    """User registration endpoint"""
    global SIMPLE_USERS
    
    try:
        print("hahahh")
        print("SIMPLE_USERS:",SIMPLE_USERS)
        if username in SIMPLE_USERS:
            raise HTTPException(status_code=400, detail="Username already exists")
        
        user_id = len(SIMPLE_USERS) + 1
        SIMPLE_USERS[username] = {
            "id": user_id,
            "username": username,
            "password": password,
            "email": email,
            "nickname": nickname or username,
            "is_active": True
        }
        print("SIMPLE_USERS:",SIMPLE_USERS)
        user = SIMPLE_USERS[username]
        from datetime import datetime
        now = datetime.now().isoformat()
        return {
            "user": {
                "id": user["id"],
                "uuid": f"user_{user['id']}_{username}",
                "username": user["username"],
                "email": user.get("email"),
                "nickname": user["nickname"],
                "avatar_url": user.get("avatar_url"),
                "created_at": now,
                "updated_at": now
            },
            "token": f"token_{user['id']}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(status_code=500, detail="Registration failed")

@app.post("/auth/login")
@app.get("/auth/login")
@app.options("/auth/login")
async def login_user(username: str, password: str):
    """User login endpoint"""
    global SIMPLE_USERS
    
    logger.info(f"🔐 Login attempt: username={username}, password={'*' * len(password)}")
    logger.info(f"🔐 Available users: {list(SIMPLE_USERS.keys())}")
    try:
        
        logger.info(f"Available users: {list(SIMPLE_USERS.keys())}")
        
        if username not in SIMPLE_USERS:
            logger.warning(f"User not found: {username}")
            raise HTTPException(status_code=401, detail="Invalid username or password")
        
        user = SIMPLE_USERS[username]
        
        if not user.get("is_active", True):
            logger.warning(f"User account disabled: {username}")
            raise HTTPException(status_code=401, detail="Account is disabled")
        
        if user["password"] != password:
            logger.warning(f"Invalid password for user: {username}")
            raise HTTPException(status_code=401, detail="Invalid username or password")
        
        logger.info(f"✅ Login successful for user: {username}")
        from datetime import datetime
        now = datetime.now().isoformat()
        return {
            "user": {
                "id": user["id"],
                "uuid": f"user_{user['id']}_{username}",
                "username": user["username"],
                "email": user.get("email"),
                "nickname": user["nickname"],
                "avatar_url": user.get("avatar_url"),
                "created_at": now,
                "updated_at": now
            },
            "token": f"token_{user['id']}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Login failed")

@app.post("/api/asr/transcribe")
async def transcribe_audio(file: UploadFile = File(...)):
    """专门的ASR转录API端点"""
    try:
        logger.info(f"📥 收到ASR转录请求: {file.filename}, 大小: {file.size}")
        
        # Read audio file
        audio_data = await file.read()
        logger.info(f"📊 音频数据大小: {len(audio_data)} bytes")
        
        # Convert to numpy array (assuming WAV format)
        audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
        logger.info(f"📊 音频数组形状: {audio_array.shape}, 时长: {len(audio_array)/16000:.2f}秒")
        
        # VAD processing
        speech_segments = vad_service.detect_speech_segments(audio_array)
        
        if not speech_segments:
            logger.warning("⚠️ 未检测到语音片段")
            return {
                "success": False,
                "error": "No speech detected",
                "transcription": "",
                "provider": "none"
            }
        
        # Extract speech audio
        start_sample = int(speech_segments[0][0] * 16000)
        end_sample = int(speech_segments[0][1] * 16000)
        speech_audio = audio_array[start_sample:end_sample]
        logger.info(f"📊 提取语音片段: {len(speech_audio)/16000:.2f}秒")
        
        # 使用增强的ASR服务
        transcription_result = enhanced_asr_service.transcribe(speech_audio)
        
        if not transcription_result.get("success"):
            logger.error("❌ ASR转录失败")
            return {
                "success": False,
                "error": transcription_result.get("error", "ASR transcription failed"),
                "transcription": "",
                "provider": "none"
            }
        
        logger.info(f"✅ ASR转录成功: '{transcription_result['text']}'")
        return {
            "success": True,
            "transcription": transcription_result["text"],
            "confidence": transcription_result.get("confidence", 0.0),
            "duration": transcription_result.get("duration", 0.0),
            "provider": transcription_result.get("provider", "unknown"),
            "tokens": transcription_result.get("tokens", 0)
        }
        
    except Exception as e:
        logger.error(f"❌ ASR API错误: {e}")
        raise HTTPException(status_code=500, detail=f"ASR transcription failed: {str(e)}")

@app.post("/process-audio")
async def process_audio_file(
    file: UploadFile = File(...),
    user_id: int = 1,
    npc_id: int = 1,
    session_id: str = Form(None)
):
    """Process uploaded audio file - 完整流水线，支持上下文感知"""
    import time
    start_time = time.time()

    try:
        # 生成会话ID（如果未提供）
        if not session_id:
            session_id = f"audio_session_{user_id}_{npc_id}_{int(time.time())}"

        logger.info(f"📥 [TIMING] Received full audio processing request: {file.filename}")
        logger.info(f"🧠 [CONTEXT] Session ID: {session_id}")

        # Read audio file
        audio_data = await file.read()
        read_time = time.time()
        logger.info(f"⏱️ [TIMING] File read in {read_time - start_time:.2f}s. Size: {len(audio_data)} bytes")

        # Convert audio to numpy array with proper format handling
        try:
            audio_array = await convert_audio_to_numpy(audio_data, file.content_type)
            convert_time = time.time()
            logger.info(f"⏱️ [TIMING] Audio converted in {convert_time - read_time:.2f}s.")
        except Exception as audio_error:
            logger.error(f"❌ 音频格式转换失败: {audio_error}")
            return {"error": f"Audio format conversion failed: {str(audio_error)}"}

        # VAD processing (Temporarily disabled for debugging)
        # speech_segments = vad_service.detect_speech_segments(audio_array)
        # vad_time = time.time()
        # logger.info(f"⏱️ [TIMING] VAD processed in {vad_time - convert_time:.2f}s.")

        # if not speech_segments:
        #     return {"error": "No speech detected"}

        # # Extract speech audio
        # start_sample = int(speech_segments[0][0] * 16000)
        # end_sample = int(speech_segments[0][1] * 16000)
        # speech_audio = audio_array[start_sample:end_sample]

        # Since VAD is disabled, use the whole audio array
        speech_audio = audio_array
        vad_time = time.time() # Reset timer for accurate ASR timing

        # 使用增强的ASR服务（支持上下文）
        logger.info("🗣️ [TIMING] Calling enhanced_asr_service.transcribe()... (VAD is disabled)")
        transcription_result = enhanced_asr_service.transcribe(
            audio_data=speech_audio,
            sample_rate=16000,
            session_id=session_id
        )
        asr_time = time.time()
        logger.info(f"⏱️ [TIMING] ASR processed in {asr_time - vad_time:.2f}s.")
        logger.info(f"🔧 enhanced_asr_service返回: {transcription_result}")

        # 记录上下文使用情况
        if transcription_result.get("context_used"):
            logger.info("🧠 [CONTEXT] ASR使用了对话上下文")
        else:
            logger.info("🧠 [CONTEXT] ASR未使用对话上下文（首次对话或上下文禁用）")
        
        if not transcription_result.get("success"):
            logger.error(f"❌ enhanced_asr_service失败: {transcription_result}")
            return {"error": f"ASR failed: {transcription_result.get('error', 'Unknown error')}"}
        
        transcription_text = transcription_result["text"]
        logger.info(f"📝 ASR转录结果: '{transcription_text}'")
        
        # Get NPC and conversation history (with fallback)
        npc = await get_npc_by_id(npc_id)
        if not npc:
            # Use fallback NPC data
            fake_npc = {
                "id": npc_id,
                "name": "测试助手",
                "description": "用于测试的AI助手",
                "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。",
                "is_active": True
            }
            npc = fake_npc
            logger.info(f"使用假 NPC 数据进行音频处理，NPC ID: {npc_id}")
        
        get_npc_time = time.time()
        logger.info(f"⏱️ [TIMING] NPC data retrieved in {get_npc_time - asr_time:.2f}s.")

        # LLM processing - 使用ASR转录的文本
        logger.info("🤖 [TIMING] Calling llm_service.generate_response()...")
        response = await llm_service.generate_response(
            transcription_text,
            [],  # Empty history for this example
            npc["system_prompt"]
        )
        llm_time = time.time()
        logger.info(f"⏱️ [TIMING] LLM processed in {llm_time - get_npc_time:.2f}s.")
        
        if not response.get("success"):
            return {"error": "LLM processing failed"}
        
        # TTS processing
        logger.info("🔊 [TIMING] Calling tts_service.synthesize_speech()...")
        speak_content = response["speak_content"]
        tts_result = await tts_service.synthesize_speech(
            speak_content["text"],
            speak_content["emotion"],
            speak_content["speed"]
        )
        tts_time = time.time()
        logger.info(f"⏱️ [TIMING] TTS processed in {tts_time - llm_time:.2f}s.")
        
        if not tts_result["success"]:
            return {"error": "TTS processing failed"}
        
        total_time = time.time() - start_time
        logger.info(f"✅ [TIMING] Total processing time: {total_time:.2f}s.")

        # 获取上下文统计信息
        context_stats = enhanced_asr_service.get_context_stats()
        session_context = context_stats['sessions'].get(session_id, {})

        # Return results with context information
        return {
            "transcription": transcription_text,
            "asr_provider": transcription_result.get("provider", "unknown"),
            "asr_confidence": transcription_result.get("confidence", 0.0),
            "response_text": speak_content["text"],
            "emotion": speak_content["emotion"],
            "speed": speak_content["speed"],
            "audio_size": tts_result["size"],
            # 上下文相关信息
            "session_id": session_id,
            "context_used": transcription_result.get("context_used", False),
            "conversation_history_length": session_context.get("history_length", 0),
            "context_enabled": context_stats.get("context_enabled", False),
            "processing_time": {
                "total": total_time,
                "asr": asr_time - vad_time,
                "llm": llm_time - get_npc_time,
                "tts": tts_time - llm_time
            }
        }
        
    except Exception as e:
        logger.error(f"Audio processing error: {e}")
        raise HTTPException(status_code=500, detail="Audio processing failed")

@app.get("/asr/context/stats")
async def get_asr_context_stats():
    """获取ASR上下文统计信息"""
    try:
        stats = enhanced_asr_service.get_context_stats()
        return {
            "success": True,
            "stats": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取ASR上下文统计失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to get ASR context stats")

@app.get("/asr/context/sessions/{session_id}")
async def get_session_context_info(session_id: str):
    """获取特定会话的上下文信息"""
    try:
        stats = enhanced_asr_service.get_context_stats()
        session_info = stats['sessions'].get(session_id)

        if not session_info:
            raise HTTPException(status_code=404, detail="Session not found")

        # 获取详细的对话历史
        context = enhanced_asr_service.conversation_contexts.get(session_id)
        conversation_history = []
        if context:
            conversation_history = context.conversation_history

        return {
            "success": True,
            "session_id": session_id,
            "stats": session_info,
            "conversation_history": conversation_history,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话上下文信息失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session context info")

@app.post("/asr/context/sessions/{session_id}/clear")
async def clear_session_context(session_id: str):
    """清除特定会话的上下文"""
    try:
        if session_id in enhanced_asr_service.conversation_contexts:
            del enhanced_asr_service.conversation_contexts[session_id]
            logger.info(f"🧹 已清除会话 {session_id} 的上下文")
            return {
                "success": True,
                "message": f"Session {session_id} context cleared",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=404, detail="Session not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清除会话上下文失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear session context")

@app.get("/llm/memory/stats")
async def get_llm_memory_stats():
    """获取LLM记忆统计信息"""
    try:
        stats = enhanced_llm_service.get_memory_stats()
        return {
            "success": True,
            "stats": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取LLM记忆统计失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to get LLM memory stats")

@app.get("/llm/memory/sessions/{session_id}")
async def get_session_memory_info(session_id: str):
    """获取特定会话的记忆信息"""
    try:
        stats = enhanced_llm_service.get_memory_stats()
        session_info = stats['sessions'].get(session_id)

        if not session_info:
            raise HTTPException(status_code=404, detail="Session not found")

        # 获取详细的对话记忆
        memory = enhanced_llm_service.conversation_memories.get(session_id)
        conversation_turns = []
        if memory:
            conversation_turns = [
                {
                    "timestamp": turn.timestamp.isoformat(),
                    "user_input": turn.user_input,
                    "assistant_response": turn.assistant_response[:200] + "..." if len(turn.assistant_response) > 200 else turn.assistant_response,
                    "tools_used": turn.tools_used,
                    "importance_score": turn.importance_score
                }
                for turn in memory.turns
            ]

        return {
            "success": True,
            "session_id": session_id,
            "stats": session_info,
            "conversation_turns": conversation_turns,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话记忆信息失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session memory info")

@app.post("/llm/memory/sessions/{session_id}/clear")
async def clear_session_memory(session_id: str):
    """清除特定会话的记忆"""
    try:
        if session_id in enhanced_llm_service.conversation_memories:
            del enhanced_llm_service.conversation_memories[session_id]
            logger.info(f"🧹 已清除会话 {session_id} 的记忆")
            return {
                "success": True,
                "message": f"Session {session_id} memory cleared",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=404, detail="Session not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清除会话记忆失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear session memory")

# MCP API Routes
@app.get("/mcp/servers")
async def get_mcp_servers():
    """Get all registered MCP servers"""
    try:
        servers = mcp_service.list_servers()
        return {"servers": servers}
    except Exception as e:
        logger.error(f"获取MCP服务器列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取MCP服务器列表失败")

@app.get("/mcp/tools")
async def get_mcp_tools():
    """Get all tools from all MCP servers for reranker"""
    try:
        tools = await mcp_service.get_all_tools_for_reranker_async()
        return {"tools": tools}
    except Exception as e:
        logger.error(f"获取MCP工具列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取MCP工具列表失败")

@app.get("/mcp/servers/{server_name}/tools")
async def get_server_tools(server_name: str):
    """Get tools from specific MCP server"""
    try:
        tools = await mcp_service.get_server_tools(server_name)
        return {"server_name": server_name, "tools": tools}
    except Exception as e:
        logger.error(f"获取服务器 '{server_name}' 工具列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取服务器 '{server_name}' 工具列表失败")

@app.post("/mcp/tools/execute")
async def execute_mcp_tool(tool_name: str, server_name: str = None, parameters: Dict[str, Any] = None):
    """Execute MCP tool"""
    try:
        if parameters is None:
            parameters = {}
        result = mcp_service.execute_tool(tool_name, server_name, **parameters)
        return result
    except Exception as e:
        logger.error(f"执行MCP工具 '{tool_name}' 失败: {e}")
        raise HTTPException(status_code=500, detail=f"执行MCP工具 '{tool_name}' 失败")

# TTS API Routes
@app.post("/api/tts/synthesize")
async def synthesize_speech(request: TTSRequest):
    """合成语音"""
    if not tts_service:
        raise HTTPException(status_code=503, detail="TTS服务未配置")

    try:
        logger.info(f"🔊 TTS请求: {request.text[:50]}...")

        result = await tts_service.synthesize_speech(
            text=request.text,
            emotion=request.emotion,
            speed=request.speed,
            voice_id=request.voice_id,
            output_file=None
        )

        if result.get("success") and result.get("audio_data"):
            audio_data = result["audio_data"]
            logger.info(f"✅ TTS合成成功: {len(audio_data)} 字节")

            return Response(
                content=audio_data,
                media_type="audio/wav",
                headers={
                    "Content-Disposition": "attachment; filename=tts_output.wav",
                    "Content-Length": str(len(audio_data))
                }
            )
        else:
            error_msg = result.get("error", "TTS合成失败")
            logger.info(f"❌ TTS合成失败: {error_msg}")
            raise HTTPException(status_code=500, detail=error_msg)

    except Exception as e:
        logger.error(f"❌ TTS API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/tts/voices")
async def get_available_voices():
    """获取可用的语音"""
    if not tts_service:
        return {"voices": {}}

    return {"voices": tts_service.get_available_voices()}

@app.post("/api/tts/test")
async def test_tts():
    """测试TTS服务"""
    test_text = "这是一个TTS测试，请生成语音。"

    request = TTSRequest(
        text=test_text,
        emotion="neutral",
        speed=1.0
    )

    return await synthesize_speech(request)

# WebSocket endpoint for real-time communication
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """修复的WebSocket端点，避免死循环问题"""
    logger.info(f"🔌 WebSocket connection attempt for user: {user_id}")
    
    try:
        await manager.connect(websocket, user_id)
        
        # 发送连接成功消息
        await manager.send_message(user_id, {
            "type": "connected",
            "message": "WebSocket connected successfully",
            "user_id": user_id,
            "timestamp": datetime.now().isoformat()
        })
        
        # 主消息循环 - 修复死循环问题
        while user_id in manager.active_connections:
            try:
                # 设置合理的超时时间
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                logger.info(f"📥 Received message from {user_id}: {len(data)} chars")
                
                # 解析消息
                try:
                    message = json.loads(data)
                    await handle_websocket_message(user_id, message)
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON decode error from {user_id}: {e}")
                    await manager.send_message(user_id, {
                        "type": "error",
                        "message": "Invalid JSON format"
                    })
                
            except asyncio.TimeoutError:
                # 发送心跳检查连接状态
                try:
                    await manager.send_message(user_id, {
                        "type": "heartbeat",
                        "timestamp": datetime.now().isoformat()
                    })
                    logger.debug(f"💓 Heartbeat sent to {user_id}")
                except Exception:
                    logger.info(f"💔 Heartbeat failed for {user_id}, disconnecting")
                    break
                    
            except WebSocketDisconnect:
                logger.info(f"🔌 WebSocket disconnect for {user_id}")
                break
                
            except Exception as e:
                logger.error(f"❌ Unexpected error in WebSocket loop for {user_id}: {e}")
                try:
                    await manager.send_message(user_id, {
                        "type": "error",
                        "message": f"Server error: {str(e)}"
                    })
                except Exception:
                    break
    
    except WebSocketDisconnect:
        logger.info(f"🔌 WebSocket disconnected during setup for {user_id}")
    except Exception as e:
        logger.error(f"❌ WebSocket setup error for {user_id}: {e}")
    finally:
        manager.disconnect(user_id)
        logger.info(f"🧹 Cleanup completed for {user_id}")

async def handle_websocket_message(user_id: str, message: dict):
    """处理WebSocket消息"""
    message_type = message.get("type", "unknown")
    logger.info(f"🔄 Processing message type '{message_type}' from {user_id}")
    
    try:
        if message_type == "start_session":
            npc_id = message.get("npc_id", 1)
            logger.info(f"Starting session for user {user_id} with NPC {npc_id}")
            
            session_id = await create_conversation_session(int(user_id), npc_id)
            
            if session_id:
                manager.user_sessions[user_id]["session_id"] = session_id
                manager.user_sessions[user_id]["npc_id"] = npc_id
                
                await manager.send_message(user_id, {
                    "type": "session_started",
                    "session_id": session_id,
                    "npc_id": npc_id
                })
            else:
                await manager.send_message(user_id, {
                    "type": "error",
                    "message": "Failed to start session"
                })
        
        elif message_type == "audio_chunk":
            audio_data = base64.b64decode(message["data"])
            await process_audio_chunk(user_id, audio_data)
        
        elif message_type == "audio_complete":
            # 处理完整的音频数据
            audio_data = base64.b64decode(message["data"])
            logger.info(f"🎤 Received complete audio from {user_id}: {len(audio_data)} bytes")
            await process_complete_audio_data(user_id, audio_data)
        
        elif message_type == "interrupt":
            manager.user_sessions[user_id]["is_speaking"] = False
            await manager.send_message(user_id, {
                "type": "interrupted",
                "message": "Assistant interrupted"
            })
        
        elif message_type == "end_session":
            session_id = manager.user_sessions[user_id].get("session_id")
            if session_id:
                try:
                    supabase.table("conversation_sessions").update({
                        "ended_at": datetime.now().isoformat(),
                        "is_active": False
                    }).eq("id", session_id).execute()
                except Exception as e:
                    logger.error(f"Error ending session: {e}")
            
            await manager.send_message(user_id, {
                "type": "session_ended"
            })
        
        elif message_type == "ping":
            await manager.send_message(user_id, {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            })
        
        elif message_type == "text_message":
            # 处理文本消息（支持工具调用）
            text_input = message.get("text", "")
            if text_input.strip():
                logger.info(f"💬 Received text message from {user_id}: {text_input}")
                
                # 模拟转录结果
                transcription = {
                    "text": text_input,
                    "confidence": 1.0
                }
                
                session = manager.user_sessions.get(user_id)
                if session and not session.get("is_speaking", False):
                    session["is_speaking"] = True
                    
                    # 获取NPC信息
                    npc_id = session.get("npc_id", 1)

                    # 如果是整数ID，先尝试获取真实的NPC ID
                    if isinstance(npc_id, int) or (isinstance(npc_id, str) and npc_id.isdigit()):
                        logger.info(f"🔍 检测到整数NPC ID: {npc_id}，尝试获取真实NPC...")
                        real_npc_id = await get_first_available_npc_id()
                        logger.info(f"📋 使用真实NPC ID: {real_npc_id}")
                        npc = await get_npc_by_id(real_npc_id)
                    else:
                        npc = await get_npc_by_id(npc_id)
                    
                    if not npc:
                        npc = {
                            "id": npc_id,
                            "name": "Assistant",
                            "system_prompt": "你是一个友善的AI助手，可以帮助用户查询信息。"
                        }
                    
                    # 使用增强LLM处理文本消息
                    try:
                        enhanced_response = await enhanced_llm_service.generate_response_with_tools(
                            text_input,
                            session.get("conversation_history", []),
                            npc["system_prompt"]
                        )
                        
                        if enhanced_response.get("success"):
                            speak_content = enhanced_response.get("speak_content", {})
                            
                            # 发送文本响应
                            await manager.send_message(user_id, {
                                "type": "text_response",
                                "text": speak_content.get("text", ""),
                                "emotion": speak_content.get("emotion", "neutral"),
                                "tools_used": enhanced_response.get("tools_used", []),
                                "tool_calls_made": enhanced_response.get("tool_calls_made", 0)
                            })
                            
                            # 更新对话历史
                            session["conversation_history"].extend([
                                {"role": "user", "content": text_input},
                                {"role": "assistant", "content": speak_content.get("text", "")}
                            ])
                        
                    except Exception as e:
                        logger.error(f"❌ Text message processing error: {e}")
                        await manager.send_message(user_id, {
                            "type": "error",
                            "message": "文本处理失败"
                        })
                    finally:
                        session["is_speaking"] = False
        
        else:
            logger.warning(f"❓ Unknown message type '{message_type}' from {user_id}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            })
    
    except Exception as e:
        logger.error(f"❌ Error handling message '{message_type}' from {user_id}: {e}")
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Error processing {message_type}: {str(e)}"
        })

async def process_audio_chunk(user_id: str, audio_data: bytes):
    """
    增强的音频块处理
    注意：只累积音频数据，不进行ASR处理
    ASR处理由audio_complete消息触发，避免重复处理
    """
    try:
        logger.info(f"🎤 Processing audio chunk for user {user_id}: {len(audio_data)} bytes")
        
        session = manager.user_sessions.get(user_id)
        if not session:
            logger.warning(f"⚠️ No session found for user {user_id}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": "No active session found"
            })
            return
        
        # 多格式音频解析
        audio_array = None
        
        # 尝试解析为16位PCM
        if len(audio_data) % 2 == 0:
            try:
                audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
                logger.info(f"✅ Parsed as int16: {audio_array.shape}")
            except Exception as e:
                logger.warning(f"⚠️ int16 parsing failed: {e}")
        
        # 尝试解析为8位PCM
        if audio_array is None:
            try:
                audio_uint8 = np.frombuffer(audio_data, dtype=np.uint8)
                audio_array = (audio_uint8.astype(np.float32) - 128.0) / 128.0
                logger.info(f"✅ Parsed as uint8: {audio_array.shape}")
            except Exception as e:
                logger.warning(f"⚠️ uint8 parsing failed: {e}")
        
        # 尝试解析为32位浮点
        if audio_array is None and len(audio_data) % 4 == 0:
            try:
                audio_array = np.frombuffer(audio_data, dtype=np.float32)
                logger.info(f"✅ Parsed as float32: {audio_array.shape}")
            except Exception as e:
                logger.warning(f"⚠️ float32 parsing failed: {e}")
        
        if audio_array is None:
            logger.error(f"❌ Failed to parse audio data from {user_id}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": "Invalid audio data format"
            })
            return
        
        # 验证音频数据
        if len(audio_array) == 0:
            logger.warning(f"⚠️ Empty audio array from {user_id}")
            return
        
        # 保存音频块用于调试
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        chunk_filename = f"chunk_{user_id}_{timestamp}.wav"
        save_audio_to_wav(audio_array, chunk_filename)
        
        # 添加到缓冲区
        session["audio_buffer"].extend(audio_array)
        buffer_size = len(session["audio_buffer"])
        
        logger.info(f"📊 Audio buffer size: {buffer_size} samples ({buffer_size/16000:.2f}s)")
        
        # 不在chunk阶段处理ASR，只累积音频数据
        # 等待audio_complete消息来触发最终处理
        if buffer_size > 48000:  # 3秒最大缓冲区，防止内存溢出
            logger.warning(f"⚠️ Buffer too large for {user_id} ({buffer_size} samples), clearing buffer")
            session["audio_buffer"] = session["audio_buffer"][-32000:]  # 保留最后2秒
    
    except Exception as e:
        logger.error(f"❌ Error processing audio chunk from {user_id}: {e}")
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Audio processing error: {str(e)}"
        })

async def process_complete_audio_data(user_id: str, audio_data: bytes):
    """处理完整的音频数据（从audio_complete消息）"""
    try:
        session = manager.user_sessions.get(user_id)
        if not session:
            logger.warning(f"⚠️ No session found for user {user_id}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": "No active session found"
            })
            return
        
        # 检查是否正在处理中
        if session.get("is_speaking", False):
            logger.info(f"⏸️ Skipping audio processing for {user_id} - already processing")
            return
        
        # 清空音频缓冲区，避免与audio_complete重复处理
        session["audio_buffer"].clear()
        session["is_speaking"] = True
        
        # 解析音频数据
        try:
            # 尝试解析为16位PCM
            audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            logger.info(f"✅ Parsed complete audio as int16: {audio_array.shape}")
        except Exception as e:
            logger.error(f"❌ Failed to parse complete audio data: {e}")
            session["is_speaking"] = False
            await manager.send_message(user_id, {
                "type": "error",
                "message": "Invalid audio data format"
            })
            return
        
        if len(audio_array) == 0:
            logger.warning(f"⚠️ Empty audio array from {user_id}")
            session["is_speaking"] = False
            return
        
        logger.info(f"🎵 Processing complete audio: {len(audio_array)} samples ({len(audio_array)/16000:.2f}s)")
        
        # 分析音频质量
        rms = np.sqrt(np.mean(audio_array**2))
        max_amplitude = np.max(np.abs(audio_array))
        logger.info(f"📊 Audio quality - RMS: {rms:.4f}, Max: {max_amplitude:.4f}")
        
        if max_amplitude < 0.01:
            logger.warning(f"⚠️ Very low audio volume detected for {user_id}")
        elif max_amplitude > 0.95:
            logger.warning(f"⚠️ Audio clipping detected for {user_id}")
        
        # 保存音频用于调试
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        complete_filename = f"complete_{user_id}_{timestamp}.wav"
        save_audio_to_wav(audio_array, complete_filename)
        logger.info(f"📁 Audio saved for debugging: {complete_filename}")
        
        # 使用增强的ASR服务
        logger.info(f"🗣️ Calling enhanced ASR service for complete audio from {user_id}")
        transcription_result = enhanced_asr_service.transcribe(audio_array)
        
        if transcription_result.get("success"):
            transcription_text = transcription_result["text"]
            confidence = transcription_result.get("confidence", 0.0)
            
            logger.info(f"📝 ✅ FINAL Complete audio transcription: '{transcription_text}' (confidence: {confidence:.2f})")
            
            # 发送转录结果
            await manager.send_message(user_id, {
                "type": "transcription",
                "text": transcription_text,
                "confidence": confidence
            })
            
            # 检查转录质量
            if not transcription_text.strip():
                logger.warning(f"⚠️ Empty transcription for {user_id}, skipping LLM processing")
                session["is_speaking"] = False
                await manager.send_message(user_id, {
                    "type": "response_complete",
                    "message": "No speech detected"
                })
                return
            
            # 获取NPC信息
            npc_id = session.get("npc_id", 1)

            # 如果是整数ID，先尝试获取真实的NPC ID
            if isinstance(npc_id, int) or (isinstance(npc_id, str) and npc_id.isdigit()):
                logger.info(f"🔍 检测到整数NPC ID: {npc_id}，尝试获取真实NPC...")
                real_npc_id = await get_first_available_npc_id()
                logger.info(f"📋 使用真实NPC ID: {real_npc_id}")
                npc = await get_npc_by_id(real_npc_id)
            else:
                npc = await get_npc_by_id(npc_id)

            if not npc:
                npc = {
                    "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。"
                }
                logger.info(f"Using fallback NPC data for {user_id}")

            # 获取或生成session_id
            session_id = session.get("session_id")
            if not session_id:
                # 如果没有session_id，生成一个临时的
                import uuid
                session_id = f"temp_session_{user_id}_{int(datetime.now().timestamp())}"
                session["session_id"] = session_id
                logger.info(f"Generated temporary session_id: {session_id}")

            # LLM处理
            logger.info(f"🤖 Calling LLM service for {user_id}")

            # response = await llm_service.generate_response(
            #     transcription_text,
            #     session.get("conversation_history", []),
            #     npc["system_prompt"]
            # )
            response = await enhanced_llm_service.generate_response_with_tools(
                transcription_text,
                session.get("conversation_history", []),
                npc["system_prompt"],
                session_id  # 传递会话ID用于上下文管理
            )
            
            if response.get("success"):
                print("response:",response)
                speak_content = response["speak_content"]
                response_text = speak_content["text"]
                logger.info(f"💬 LLM response: '{response_text[:100]}...'")
                
                # TTS处理
                logger.info(f"🔊 Calling TTS service for {user_id}")
                tts_result = await tts_service.synthesize_speech(
                    response_text,
                    speak_content.get("emotion", "neutral"),
                    speak_content.get("speed", 1.0)
                )
                
                if tts_result.get("success"):
                    # 发送音频响应
                    audio_b64 = base64.b64encode(tts_result["audio_data"]).decode('utf-8')
                    await manager.send_message(user_id, {
                        "type": "audio_chunk",
                        "data": audio_b64
                    })
                    logger.info(f"🎵 Audio response sent to {user_id}: {len(tts_result['audio_data'])} bytes")
                else:
                    logger.error(f"❌ TTS failed for {user_id}: {tts_result}")
                
                # 更新对话历史
                session.setdefault("conversation_history", []).extend([
                    {"role": "user", "content": transcription_text},
                    {"role": "assistant", "content": response_text}
                ])
                
                # 保存消息到数据库
                session_id = session.get("session_id")
                if session_id:
                    await save_message(session_id, "user", transcription_text)
                    await save_message(session_id, "assistant", response_text)
                
                # 发送完成信号
                await manager.send_message(user_id, {
                    "type": "response_complete",
                    "message": "Processing completed successfully"
                })
                
            else:
                logger.error(f"❌ LLM failed for {user_id}: {response}")
                await manager.send_message(user_id, {
                    "type": "error",
                    "message": "Language model processing failed"
                })
        
        else:
            logger.error(f"❌ ASR failed for {user_id}: {transcription_result}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": f"Speech recognition failed: {transcription_result.get('error', 'Unknown error')}"
            })
        
        session["is_speaking"] = False
        
    except Exception as e:
        logger.error(f"❌ Error processing complete audio from {user_id}: {e}")
        session = manager.user_sessions.get(user_id)
        if session:
            session["is_speaking"] = False
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Audio processing error: {str(e)}"
        })

async def process_complete_audio(user_id: str):
    """处理完整的音频缓冲区（从audio_chunk累积）"""
    try:
        session = manager.user_sessions.get(user_id)
        if not session or not session["audio_buffer"]:
            return
        
        # 检查是否正在处理中
        if session.get("is_speaking", False):
            logger.info(f"⏸️ Skipping audio processing for {user_id} - already processing")
            return
        
        # 获取音频数据
        audio_array = np.array(session["audio_buffer"])
        session["audio_buffer"].clear()
        session["is_speaking"] = True
        
        logger.info(f"🎵 Processing complete audio: {len(audio_array)} samples")
        
        # 保存音频用于调试
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        segment_filename = f"segment_{user_id}_{timestamp}.wav"
        save_audio_to_wav(audio_array, segment_filename)
        
        # 使用增强的ASR服务
        logger.info(f"🗣️ Calling enhanced ASR service for {user_id}")
        transcription_result = enhanced_asr_service.transcribe(audio_array)
        
        if transcription_result.get("success"):
            transcription_text = transcription_result["text"]
            confidence = transcription_result.get("confidence", 0.0)
            
            logger.info(f"📝 Transcription: '{transcription_text}' (confidence: {confidence:.2f})")
            
            # 发送转录结果
            await manager.send_message(user_id, {
                "type": "transcription",
                "text": transcription_text,
                "confidence": confidence
            })
            
            # 检查转录质量
            if not transcription_text.strip() or confidence < 0.3:
                logger.warning(f"⚠️ Low quality transcription for {user_id}, skipping LLM processing")
                session["is_speaking"] = False
                return
            
            # 获取NPC信息
            npc_id = session.get("npc_id", 1)

            # 如果是整数ID，先尝试获取真实的NPC ID
            if isinstance(npc_id, int) or (isinstance(npc_id, str) and npc_id.isdigit()):
                logger.info(f"🔍 检测到整数NPC ID: {npc_id}，尝试获取真实NPC...")
                real_npc_id = await get_first_available_npc_id()
                logger.info(f"📋 使用真实NPC ID: {real_npc_id}")
                npc = await get_npc_by_id(real_npc_id)
            else:
                npc = await get_npc_by_id(npc_id)
            
            if not npc:
                npc = {
                    "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。"
                }
                logger.info(f"Using fallback NPC data for {user_id}")
            
            # LLM处理
            logger.info(f"🤖 Calling LLM service for {user_id}")
            response = await llm_service.generate_response(
                transcription_text,
                session.get("conversation_history", []),
                npc["system_prompt"]
            )
            
            if response.get("success"):
                speak_content = response["speak_content"]
                response_text = speak_content["text"]
                
                logger.info(f"💬 LLM response: '{response_text[:100]}...'")
                
                # TTS处理
                logger.info(f"🔊 Calling TTS service for {user_id}")
                tts_result = await tts_service.synthesize_speech(
                    response_text,
                    speak_content.get("emotion", "neutral"),
                    speak_content.get("speed", 1.0)
                )
                
                if tts_result.get("success"):
                    # 发送音频响应
                    audio_b64 = base64.b64encode(tts_result["audio_data"]).decode('utf-8')
                    await manager.send_message(user_id, {
                        "type": "audio_chunk",
                        "data": audio_b64
                    })
                    logger.info(f"🎵 Audio response sent to {user_id}: {len(tts_result['audio_data'])} bytes")
                else:
                    logger.error(f"❌ TTS failed for {user_id}: {tts_result}")
                
                # 更新对话历史
                session.setdefault("conversation_history", []).extend([
                    {"role": "user", "content": transcription_text},
                    {"role": "assistant", "content": response_text}
                ])
                
                # 保存消息到数据库
                session_id = session.get("session_id")
                if session_id:
                    await save_message(session_id, "user", transcription_text)
                    await save_message(session_id, "assistant", response_text)
                
                # 发送完成信号
                await manager.send_message(user_id, {
                    "type": "response_complete",
                    "message": "Processing completed successfully"
                })
                
            else:
                logger.error(f"❌ LLM failed for {user_id}: {response}")
                await manager.send_message(user_id, {
                    "type": "error",
                    "message": "Language model processing failed"
                })
        
        else:
            logger.error(f"❌ ASR failed for {user_id}: {transcription_result}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": "Speech recognition failed"
            })
    
    except Exception as e:
        logger.error(f"❌ Error processing complete audio for {user_id}: {e}")
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Audio processing error: {str(e)}"
        })
    finally:
        # 确保重置处理状态
        if user_id in manager.user_sessions:
            manager.user_sessions[user_id]["is_speaking"] = False

async def process_speech_segment(user_id: str, audio_data: np.ndarray):
    """Process complete speech segment"""
    try:
        logger.info(f"🏁 [TRACE] Entered process_speech_segment for user {user_id}.")
        session = manager.user_sessions.get(user_id)
        if not session or session["is_speaking"]:
            logger.warning(f"⚠️ [TRACE] Skipping speech segment for user {user_id} because session is missing or assistant is speaking.")
            return
        
        session["is_speaking"] = True
        
        # ASR processing
        logger.info(f"🗣️ [TRACE] >>> Calling enhanced_asr_service.transcribe for user {user_id}. Audio length: {len(audio_data)} samples.")
        transcription_result = enhanced_asr_service.transcribe(audio_data)
        logger.info(f"✅ [TRACE] <<< Returned from enhanced_asr_service.transcribe for user {user_id}. Result: {transcription_result}")
        
        if not transcription_result.get("success"):
            logger.error(f"❌ Enhanced ASR transcription failed: {transcription_result.get('error')}")
            session["is_speaking"] = False
            return

        transcription = transcription_result
        
        if not transcription.get("text") or transcription.get("confidence", 1.0) < 0.5:
            logger.warning(f"⚠️ [PIPELINE] ASR text is empty or confidence is too low. Text: '{transcription.get('text')}', Confidence: {transcription.get('confidence', 0.0)}. Aborting.")
            session["is_speaking"] = False
            return
        
        # Check if WebSocket connection is still active before sending messages
        if user_id not in manager.active_connections:
            logger.warning(f"⚠️ [PIPELINE] WebSocket connection for user {user_id} is not active. Aborting.")
            session["is_speaking"] = False
            return
            
        # Send transcription to client
        await manager.send_message(user_id, {
            "type": "transcription",
            "text": transcription["text"],
            "confidence": transcription["confidence"]
        })
        
        # Get NPC prompt
        npc_id = session.get("npc_id", 1)

        # 如果是整数ID，先尝试获取真实的NPC ID
        if isinstance(npc_id, int) or (isinstance(npc_id, str) and npc_id.isdigit()):
            logger.info(f"🔍 [PIPELINE] 检测到整数NPC ID: {npc_id}，尝试获取真实NPC...")
            real_npc_id = await get_first_available_npc_id()
            logger.info(f"📋 [PIPELINE] 使用真实NPC ID: {real_npc_id}")
            npc = await get_npc_by_id(real_npc_id)
        else:
            logger.info(f"ℹ️ [PIPELINE] Fetching NPC data for npc_id: {npc_id}")
            npc = await get_npc_by_id(npc_id)
        
        if not npc:
            logger.warning(f"⚠️ [PIPELINE] Failed to get NPC data for npc_id: {npc_id}. Using fallback data to continue.")
            # Use fallback NPC data to allow the pipeline to continue
            npc = {
                "id": npc_id,
                "name": "Fallback Assistant",
                "description": "A fallback assistant for testing.",
                "system_prompt": "You are a helpful assistant. Please respond clearly and concisely.",
                "is_active": True
            }
        
        logger.info(f"✅ [PIPELINE] Successfully fetched NPC: {npc.get('name')}")

        # LLM processing with enhanced tool calling
        logger.info(f"🤖 [PIPELINE] Preparing to call Enhanced LLM with tool support. Transcription: '{transcription['text']}'")
        
        # 使用增强LLM服务进行工具调用
        try:
            # 首先尝试使用工具增强的响应生成
            logger.info(f"🚀 [ENHANCED] Calling enhanced LLM with tool support...")
            
            enhanced_response = await enhanced_llm_service.generate_response_with_tools(
                transcription["text"],
                session["conversation_history"],
                npc["system_prompt"]
            )
            
            if enhanced_response.get("success"):
                tools_used = enhanced_response.get('tools_used', [])
                tool_calls_made = enhanced_response.get('tool_calls_made', 0)
                
                logger.info(f"✅ [ENHANCED] Success! Tools used: {tools_used}, Calls made: {tool_calls_made}")
                
                # 提取响应内容
                speak_content = enhanced_response.get("speak_content", {})
                response_text = speak_content.get("text", "")
                emotion = speak_content.get("emotion", "neutral")
                speed = speak_content.get("speed", 1.0)
                
                # 验证响应内容
                if not response_text.strip():
                    logger.warning("⚠️ [ENHANCED] Empty response text, falling back to regular LLM")
                    await process_regular_llm_stream(user_id, session, transcription, npc)
                    return
                
                # 创建单个chunk用于后续处理
                chunk = {
                    "type": "speak",
                    "text": response_text,
                    "emotion": emotion,
                    "speed": speed,
                    "tools_used": tools_used,
                    "tool_calls_made": tool_calls_made,
                    "enhanced_mode": True
                }
                
                # 处理这个chunk
                await process_llm_chunk(user_id, session, transcription, chunk, npc)
                
                # 发送完成信号
                if user_id in manager.active_connections:
                    await manager.send_message(user_id, {
                        "type": "response_complete",
                        "enhanced_mode": True,
                        "tools_used": tools_used,
                        "tool_calls_made": tool_calls_made
                    })
                
            else:
                error_msg = enhanced_response.get("error", "Unknown error")
                logger.warning(f"⚠️ [ENHANCED] Failed: {error_msg}, falling back to regular LLM")
                # 降级到普通LLM流式处理
                await process_regular_llm_stream(user_id, session, transcription, npc)
                
        except Exception as e:
            logger.error(f"❌ [ENHANCED] Exception: {e}, falling back to regular LLM")
            import traceback
            logger.error(f"❌ [ENHANCED] Traceback: {traceback.format_exc()}")
            # 降级到普通LLM流式处理
            await process_regular_llm_stream(user_id, session, transcription, npc)
    
    except Exception as e:
        logger.error(f"Error processing complete audio data: {e}")
        session = manager.user_sessions.get(user_id)
        if session:
            session["is_speaking"] = False

async def process_llm_chunk(user_id: str, session: dict, transcription: dict, chunk: dict, npc: dict):
    """处理LLM响应chunk"""
    # Check if WebSocket connection is still active
    if user_id not in manager.active_connections:
        logger.warning(f"⚠️ [PIPELINE] WebSocket connection for user {user_id} closed during LLM processing. Aborting.")
        session["is_speaking"] = False
        return
        
    if chunk["type"] == "speak":
        # 发送工具使用信息（如果有的话）
        tools_used = chunk.get("tools_used", [])
        tool_calls_made = chunk.get("tool_calls_made", 0)
        enhanced_mode = chunk.get("enhanced_mode", False)
        fallback_mode = chunk.get("fallback_mode", False)
        
        if tools_used or enhanced_mode:
            await manager.send_message(user_id, {
                "type": "tools_info",
                "tools_used": tools_used,
                "tool_calls_made": tool_calls_made,
                "enhanced_mode": enhanced_mode,
                "fallback_mode": fallback_mode,
                "message": f"{'使用了工具: ' + ', '.join(tools_used) if tools_used else '增强模式处理'}"
            })
        
        # TTS processing
        async for audio_chunk in tts_service.synthesize_speech_stream(
            chunk["text"],
            chunk["emotion"],
            chunk["speed"]
        ):
            # Check if WebSocket connection is still active
            if user_id not in manager.active_connections:
                logger.warning(f"⚠️ [PIPELINE] WebSocket connection for user {user_id} closed during TTS processing. Aborting.")
                session["is_speaking"] = False
                return
                
            if audio_chunk and session["is_speaking"]:
                # Send audio chunk to client
                audio_b64 = base64.b64encode(audio_chunk).decode()
                await manager.send_message(user_id, {
                    "type": "audio_chunk",
                    "data": audio_b64
                })
        
        # Save messages to database
        session_id = session.get("session_id")
        if session_id:
            await save_message(session_id, "user", transcription["text"])
            await save_message(session_id, "assistant", chunk["text"], 
                             emotion=chunk["emotion"], speed=chunk["speed"])
        
        # Update conversation history
        session["conversation_history"].extend([
            {"role": "user", "content": transcription["text"]},
            {"role": "assistant", "content": chunk["text"]}
        ])

async def process_regular_llm_stream(user_id: str, session: dict, transcription: dict, npc: dict):
    """处理普通LLM流式响应（降级方案）"""
    try:
        logger.info(f"🔄 [FALLBACK] Using regular LLM stream for {user_id}")
        
        async for chunk in llm_service.generate_response_stream(
            transcription["text"],
            session["conversation_history"],
            npc["system_prompt"]
        ):
            # Check if WebSocket connection is still active
            if user_id not in manager.active_connections:
                logger.warning(f"⚠️ [PIPELINE] WebSocket connection for user {user_id} closed during LLM processing. Aborting.")
                session["is_speaking"] = False
                return
                
            if chunk["type"] == "speak":
                # 为降级方案添加标识
                chunk["tools_used"] = []
                chunk["tool_calls_made"] = 0
                chunk["fallback_mode"] = True
                
                await process_llm_chunk(user_id, session, transcription, chunk, npc)
                break
        
        session["is_speaking"] = False
        
        # Check if WebSocket connection is still active before sending final message
        if user_id in manager.active_connections:
            await manager.send_message(user_id, {
                "type": "response_complete",
                "fallback_mode": True
            })
        else:
            logger.warning(f"⚠️ [PIPELINE] WebSocket connection for user {user_id} closed before sending response_complete.")
            
    except Exception as e:
        logger.error(f"❌ Regular LLM stream error: {e}")
        session["is_speaking"] = False
        if user_id in manager.active_connections:
            await manager.send_message(user_id, {
                "type": "error",
                "message": "语音处理失败，请重试"
            })

# 新增：工具调用测试端点
class ToolCallingRequest(BaseModel):
    user_input: str = "BJP 到 SHH 2025-04-15的火车票有哪些"
    npc_id: int = 1

@app.post("/api/test-tool-calling")
async def test_tool_calling(request: ToolCallingRequest):
    """
    测试工具调用功能的API端点
    """
    try:
        user_input = request.user_input
        npc_id = request.npc_id
        
        logger.info(f"🧪 Testing tool calling with input: {user_input}")
        
        # 获取NPC信息
        npc = await get_npc_by_id(npc_id)
        if not npc:
            npc = {
                "id": npc_id,
                "name": "测试助手",
                "system_prompt": "你是一个友善的AI助手，可以帮助用户查询火车票信息。"
            }
        
        # 使用增强LLM服务进行工具调用
        response = await enhanced_llm_service.generate_response_with_tools(
            user_input,
            [],  # 空的对话历史
            npc["system_prompt"]
        )
        
        if response.get("success"):
            return {
                "success": True,
                "user_input": user_input,
                "npc_id": npc_id,
                "npc_name": npc.get("name"),
                "response": response["speak_content"]["text"],
                "emotion": response["speak_content"]["emotion"],
                "speed": response["speak_content"]["speed"],
                "tools_used": response.get("tools_used", []),
                "tool_calls_made": response.get("tool_calls_made", 0),
                "mock_mode": response.get("mock_mode", False)
            }
        else:
            return {
                "success": False,
                "error": "Enhanced LLM response failed",
                "user_input": user_input
            }
            
    except Exception as e:
        logger.error(f"❌ Tool calling test error: {e}")
        return {
            "success": False,
            "error": str(e),
            "user_input": getattr(request, 'user_input', '')
        }

async def startup_event():
    """应用启动时的初始化事件"""
    logger.info("🚀 启动语音聊天应用...")

    # 初始化MCP服务器
    logger.info("🔧 初始化MCP服务器...")
    try:
        from services.mcp_client import mcp_client

        # 获取所有可用的MCP工具（这会自动启动服务器）
        tools = await tool_manager_service.enumerate_all_tools_async(use_cache=False)
        logger.info(f"✅ 成功加载 {len(tools)} 个MCP工具")

        # 按服务器分组显示工具
        server_tools = {}
        for tool in tools:
            server = tool.get('server', 'unknown')
            if server not in server_tools:
                server_tools[server] = []
            server_tools[server].append(tool)

        for server, tools_list in server_tools.items():
            logger.info(f"  📦 {server}: {len(tools_list)} 个工具")

    except Exception as e:
        logger.error(f"❌ MCP服务器初始化失败: {e}")
        logger.warning("⚠️ 应用将在没有MCP工具的情况下启动")

async def shutdown_event():
    """应用关闭时的清理事件"""
    logger.info("🛑 关闭语音聊天应用...")

    # 清理MCP服务器连接
    try:
        from services.mcp_client import mcp_client
        await mcp_client.cleanup()
        logger.info("✅ MCP服务器连接已清理")
    except Exception as e:
        logger.error(f"❌ MCP服务器清理失败: {e}")

# 注册启动和关闭事件
app.add_event_handler("startup", startup_event)
app.add_event_handler("shutdown", shutdown_event)

def main():
    """主函数"""
    logger.info("🎯 启动语音聊天后端服务")
    logger.info("📍 服务地址: http://localhost:8000")
    logger.info("📖 API文档: http://localhost:8000/docs")
    logger.info("🔧 工具管理: http://localhost:8000/api/tools/")
    logger.info("🧪 系统测试: http://localhost:8000/test")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )

if __name__ == "__main__":
    main()
