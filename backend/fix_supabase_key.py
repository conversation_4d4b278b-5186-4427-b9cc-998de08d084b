#!/usr/bin/env python3
"""
临时修复Supabase Key的脚本
"""

import os
import sys
from pathlib import Path

def fix_supabase_key():
    """修复Supabase Key"""
    env_file = Path(__file__).parent / ".env"
    
    # 正确的Service Key
    correct_key = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiYW5vbiIsInJlZiI6InNicC13bGp2ajh6bWkxcXFvdmhoIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NTMxOTAzNjQsImV4cCI6MjA2ODc2NjM2NH0.A3O1gtaZRdTnpe1T7wHJzYSlw9UCqACVFFM35ju1J5Y"
    
    if env_file.exists():
        # 读取现有内容
        with open(env_file, 'r') as f:
            content = f.read()
        
        # 查找并替换SUPABASE_KEY行
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('SUPABASE_KEY='):
                lines[i] = f'SUPABASE_KEY={correct_key}'
                print(f"✅ 更新了SUPABASE_KEY")
                break
        
        # 写回文件
        with open(env_file, 'w') as f:
            f.write('\n'.join(lines))
        
        print("✅ .env文件已更新")
        return True
    else:
        print("❌ .env文件不存在")
        return False

if __name__ == "__main__":
    if fix_supabase_key():
        print("🔄 请重启服务以应用更改")
        print("   pkill -f 'python main.py' && python main.py &")
    else:
        sys.exit(1)