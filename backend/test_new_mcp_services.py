#!/usr/bin/env python3
"""
测试新增的amap-streamablehttp和didi-streamablehttp MCP服务
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.mcp_client import SimpleMCPClient
from services.mcp_service import MCPService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mcp_client():
    """测试MCP客户端"""
    print("🚀 开始测试新增的MCP服务...")
    
    # 初始化MCP客户端
    mcp_client = SimpleMCPClient()
    
    print(f"📋 已加载的服务器配置: {list(mcp_client.server_configs.keys())}")
    
    # 测试amap-streamablehttp服务
    amap_server = "amap-maps-streamableHTTP"
    if amap_server in mcp_client.server_configs:
        print(f"\n🗺️ 测试 {amap_server} 服务...")
        
        # 启动服务器
        success = await mcp_client.start_server(amap_server)
        print(f"启动结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            # 获取工具列表
            tools = await mcp_client.get_server_tools(amap_server)
            print(f"获取到 {len(tools)} 个工具:")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
            
            # 测试工具调用（如果有工具的话）
            if tools:
                tool_name = tools[0]['name']
                print(f"\n🔧 测试工具调用: {tool_name}")
                try:
                    result = await mcp_client.execute_tool(tool_name, amap_server)
                    print(f"工具调用结果: {result}")
                except Exception as e:
                    print(f"工具调用失败: {e}")
        
        # 停止服务器
        await mcp_client.stop_server(amap_server)
    else:
        print(f"❌ 未找到 {amap_server} 服务配置")
    
    # 测试didi-streamablehttp服务
    didi_server = "didi-streamableHTTP"
    if didi_server in mcp_client.server_configs:
        print(f"\n🚗 测试 {didi_server} 服务...")
        
        # 启动服务器
        success = await mcp_client.start_server(didi_server)
        print(f"启动结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            # 获取工具列表
            tools = await mcp_client.get_server_tools(didi_server)
            print(f"获取到 {len(tools)} 个工具:")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
            
            # 测试工具调用（如果有工具的话）
            if tools:
                tool_name = tools[0]['name']
                print(f"\n🔧 测试工具调用: {tool_name}")
                try:
                    result = await mcp_client.execute_tool(tool_name, didi_server)
                    print(f"工具调用结果: {result}")
                except Exception as e:
                    print(f"工具调用失败: {e}")
        
        # 停止服务器
        await mcp_client.stop_server(didi_server)
    else:
        print(f"❌ 未找到 {didi_server} 服务配置")
    
    # 清理
    await mcp_client.cleanup()
    print("\n✅ 测试完成")

async def test_mcp_service():
    """测试MCP服务集成"""
    print("\n🔧 测试MCP服务集成...")
    
    mcp_service = MCPService()
    
    # 获取所有工具
    all_tools = await mcp_service.get_all_tools_for_reranker_async()
    print(f"📋 总共获取到 {len(all_tools)} 个工具")
    
    # 查找新增的服务工具
    amap_tools = [tool for tool in all_tools if tool.get('server') == 'amap-maps-streamableHTTP']
    didi_tools = [tool for tool in all_tools if tool.get('server') == 'didi-streamableHTTP']
    
    print(f"🗺️ Amap工具数量: {len(amap_tools)}")
    print(f"🚗 Didi工具数量: {len(didi_tools)}")
    
    # 测试工具执行
    if amap_tools:
        tool = amap_tools[0]
        print(f"\n🔧 测试Amap工具: {tool['name']}")
        try:
            result = await mcp_service.execute_tool_async(
                tool['name'], 
                tool['server']
            )
            print(f"执行结果: {result}")
        except Exception as e:
            print(f"执行失败: {e}")
    
    if didi_tools:
        tool = didi_tools[0]
        print(f"\n🔧 测试Didi工具: {tool['name']}")
        try:
            result = await mcp_service.execute_tool_async(
                tool['name'], 
                tool['server']
            )
            print(f"执行结果: {result}")
        except Exception as e:
            print(f"执行失败: {e}")

async def main():
    """主函数"""
    try:
        # 测试MCP客户端
        await test_mcp_client()
        
        # 测试MCP服务集成
        await test_mcp_service()
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
