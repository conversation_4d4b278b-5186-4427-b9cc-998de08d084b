#!/usr/bin/env python3
"""
测试后端API端点
"""
import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_USER_ID = "test_user_123"
TEST_NPC_ID = 1

def test_root_endpoint():
    """测试根端点"""
    print("1. 测试根端点 (/) ...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        if response.status_code == 200:
            print("   ✅ 根端点测试通过")
            return True
        else:
            print("   ❌ 根端点测试失败")
            return False
    except Exception as e:
        print(f"   ❌ 根端点测试失败: {e}")
        return False

def test_health_endpoint():
    """测试健康检查端点"""
    print("\n2. 测试健康检查端点 (/health) ...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        if response.status_code == 200:
            print("   ✅ 健康检查端点测试通过")
            return True
        else:
            print("   ❌ 健康检查端点测试失败")
            return False
    except Exception as e:
        print(f"   ❌ 健康检查端点测试失败: {e}")
        return False

def test_test_endpoint():
    """测试测试端点"""
    print("\n3. 测试测试端点 (/test) ...")
    try:
        response = requests.get(f"{BASE_URL}/test")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        if response.status_code == 200:
            print("   ✅ 测试端点测试通过")
            return True
        else:
            print("   ❌ 测试端点测试失败")
            return False
    except Exception as e:
        print(f"   ❌ 测试端点测试失败: {e}")
        return False

def test_npcs_endpoint():
    """测试NPCs端点"""
    print("\n4. 测试NPCs端点 (/npcs) ...")
    try:
        response = requests.get(f"{BASE_URL}/npcs")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        if response.status_code == 200:
            print("   ✅ NPCs端点测试通过")
            return True
        else:
            print("   ❌ NPCs端点测试失败")
            return False
    except Exception as e:
        print(f"   ❌ NPCs端点测试失败: {e}")
        return False

def test_register_endpoint():
    """测试注册端点"""
    print("\n5. 测试注册端点 (/auth/register) ...")
    try:
        # 测试数据
        test_data = {
            "username": f"test_user_{int(time.time())}",
            "password": "test_password_123",
            "email": f"test_{int(time.time())}@example.com",
            "nickname": "测试用户"
        }
        
        response = requests.post(f"{BASE_URL}/auth/register", data=test_data)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
            print("   ✅ 注册端点测试通过")
            return True
        elif response.status_code == 422:
            print("   ⚠️  注册端点返回422（参数验证错误），但端点本身工作正常")
            return True
        else:
            print("   ❌ 注册端点测试失败")
            return False
    except Exception as e:
        print(f"   ❌ 注册端点测试失败: {e}")
        return False

def test_login_endpoint():
    """测试登录端点"""
    print("\n6. 测试登录端点 (/auth/login) ...")
    try:
        # 测试数据
        test_data = {
            "username": "test_user",
            "password": "test_password"
        }
        
        response = requests.post(f"{BASE_URL}/auth/login", data=test_data)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 登录端点测试通过")
            return True
        elif response.status_code == 401:
            print("   ⚠️  登录端点返回401（用户名或密码错误），但端点本身工作正常")
            return True
        elif response.status_code == 422:
            print("   ⚠️  登录端点返回422（参数验证错误），但端点本身工作正常")
            return True
        else:
            print("   ❌ 登录端点测试失败")
            return False
    except Exception as e:
        print(f"   ❌ 登录端点测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始测试后端API端点...")
    print("=" * 50)
    print("🔍 测试后端API端点...")
    print(f"后端地址: {BASE_URL}")
    
    test_results = []
    
    # 运行各个测试
    test_results.append(test_root_endpoint())
    test_results.append(test_health_endpoint())
    test_results.append(test_test_endpoint())
    test_results.append(test_npcs_endpoint())
    test_results.append(test_register_endpoint())
    test_results.append(test_login_endpoint())
    
    # 统计结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有API端点测试通过！")
        return True
    else:
        print("⚠️  部分API端点测试失败。")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
