#!/usr/bin/env python3
"""
使用persona_data测试NPC功能
适配当前表结构，从persona_data生成描述和系统提示词
"""

import asyncio
import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加backend目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NPCPersonaTester:
    """基于persona_data的NPC测试器"""
    
    def __init__(self):
        self.supabase = None
        self.test_results = []
        
    async def initialize_supabase(self):
        """初始化Supabase连接"""
        logger.info("🔑 初始化Supabase连接...")
        
        try:
            supabase_url = os.getenv("SUPABASE_URL")
            # 使用Service Key
            service_key = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiYW5vbiIsInJlZiI6InNicC13bGp2ajh6bWkxcXFvdmhoIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NTMxOTAzNjQsImV4cCI6MjA2ODc2NjM2NH0.A3O1gtaZRdTnpe1T7wHJzYSlw9UCqACVFFM35ju1J5Y"
            
            if not supabase_url:
                logger.error("❌ SUPABASE_URL未设置")
                return False
                
            self.supabase = create_client(supabase_url, service_key)
            logger.info("✅ Supabase客户端初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ Supabase初始化失败: {e}")
            return False
    
    def generate_description_from_persona(self, persona_data):
        """从persona_data生成描述"""
        try:
            if isinstance(persona_data, str):
                persona = json.loads(persona_data)
            else:
                persona = persona_data
            
            name = persona.get("name", "未知")
            age = persona.get("age", "未知")
            occupation = persona.get("occupation", "未知")
            personality = persona.get("personality", "友善")
            background = persona.get("background", "")
            
            description = f"{name}，{age}岁，{occupation}。{personality}。{background}"
            return description.strip()
            
        except Exception as e:
            logger.warning(f"⚠️ 解析persona_data失败: {e}")
            return "AI助手角色"
    
    def generate_system_prompt_from_persona(self, persona_data):
        """从persona_data生成系统提示词"""
        try:
            if isinstance(persona_data, str):
                persona = json.loads(persona_data)
            else:
                persona = persona_data
            
            name = persona.get("name", "助手")
            personality = persona.get("personality", "友善")
            background = persona.get("background", "")
            hobbies = persona.get("hobbies", [])
            education = persona.get("education", "")
            
            # 构建系统提示词
            prompt_parts = [
                f"你是{name}，{personality}。",
                f"背景：{background}" if background else "",
                f"教育背景：{education}" if education else "",
                f"兴趣爱好：{', '.join(hobbies)}" if hobbies else "",
                "请用自然的语调回复用户，保持角色一致性。",
                "回复格式必须为：<turn>\\n<THINK>\\n## 1. 意图分析: [分析用户意图]\\n## 2. 行动规划: [规划回应策略]\\n## 3. 工具选择与参数构建: [如需要]\\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\\n## 5. 最终输出序列: <SPEAK>\\n</THINK>\\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\\n<JUDGE>\\n本轮表现：[评分]/10分\\n优点：[优点分析]\\n缺点：[缺点分析]\\n重大失误：[如有]\\n</JUDGE>\\n</turn>"
            ]
            
            return " ".join([part for part in prompt_parts if part])
            
        except Exception as e:
            logger.warning(f"⚠️ 生成系统提示词失败: {e}")
            return "你是一个友善的AI助手，请用自然的语调回复用户。"
    
    async def test_npc_table_with_persona(self):
        """测试NPC表和persona数据"""
        logger.info("🎭 测试NPC表和persona数据...")
        
        try:
            # 查询NPCs表
            result = self.supabase.table("npcs").select("*").execute()
            
            logger.info(f"✅ NPCs表查询成功，返回{len(result.data)}条记录")
            
            # 分析每个NPC的persona数据
            for npc in result.data:
                npc_id = npc.get('id')
                name = npc.get('name', '未知')
                is_active = npc.get('is_active')
                persona_data = npc.get('persona_data')
                
                logger.info(f"\n📋 NPC分析:")
                logger.info(f"  - ID: {npc_id}")
                logger.info(f"  - 名称: {name}")
                logger.info(f"  - 活跃状态: {is_active}")
                
                if persona_data:
                    try:
                        if isinstance(persona_data, str):
                            persona = json.loads(persona_data)
                        else:
                            persona = persona_data
                        
                        logger.info(f"  - 年龄: {persona.get('age', '未知')}")
                        logger.info(f"  - 性别: {persona.get('gender', '未知')}")
                        logger.info(f"  - 职业: {persona.get('occupation', '未知')}")
                        logger.info(f"  - 性格: {persona.get('personality', '未知')}")
                        logger.info(f"  - 兴趣: {', '.join(persona.get('hobbies', []))}")
                        
                        # 生成描述和系统提示词
                        description = self.generate_description_from_persona(persona_data)
                        system_prompt = self.generate_system_prompt_from_persona(persona_data)
                        
                        logger.info(f"  - 生成描述: {description}")
                        logger.info(f"  - 系统提示词长度: {len(system_prompt)} 字符")
                        
                    except Exception as e:
                        logger.warning(f"  ⚠️ 解析persona_data失败: {e}")
                else:
                    logger.warning(f"  ⚠️ 没有persona_data")
            
            self.test_results.append({
                "test": "npc_table_with_persona",
                "status": "success",
                "message": f"成功分析{len(result.data)}个NPC的persona数据",
                "details": {
                    "npc_count": len(result.data),
                    "npcs": [{"id": npc.get("id"), "name": npc.get("name")} for npc in result.data]
                }
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ NPC表查询失败: {e}")
            self.test_results.append({
                "test": "npc_table_with_persona",
                "status": "failed",
                "message": f"查询失败: {str(e)}"
            })
            return False
    
    async def test_get_npc_by_id_with_persona(self):
        """测试根据ID获取NPC并处理persona数据"""
        logger.info("🔍 测试根据ID获取NPC...")
        
        try:
            # 获取第一个活跃的NPC
            result = self.supabase.table("npcs").select("*").eq("is_active", True).limit(1).execute()
            
            if not result.data:
                logger.warning("⚠️ 没有找到活跃的NPC")
                self.test_results.append({
                    "test": "get_npc_by_id_with_persona",
                    "status": "warning",
                    "message": "没有找到活跃的NPC"
                })
                return False
            
            npc = result.data[0]
            npc_id = npc["id"]
            
            logger.info(f"✅ 成功获取NPC: {npc.get('name', '未知')} (ID: {npc_id})")
            
            # 处理persona数据
            persona_data = npc.get('persona_data')
            if persona_data:
                description = self.generate_description_from_persona(persona_data)
                system_prompt = self.generate_system_prompt_from_persona(persona_data)
                
                logger.info(f"📝 生成的描述: {description}")
                logger.info(f"🤖 生成的系统提示词: {system_prompt[:100]}...")
                
                # 创建完整的NPC对象（模拟main.py中的get_npc_by_id函数）
                enhanced_npc = {
                    "id": npc_id,
                    "name": npc.get("name"),
                    "description": description,
                    "system_prompt": system_prompt,
                    "is_active": npc.get("is_active"),
                    "persona_data": persona_data,
                    "avatar_url": None  # 当前表中没有这个字段
                }
                
                self.test_results.append({
                    "test": "get_npc_by_id_with_persona",
                    "status": "success",
                    "message": f"成功获取并增强NPC: {npc.get('name')}",
                    "details": {
                        "npc_id": npc_id,
                        "npc_name": npc.get("name"),
                        "has_persona_data": bool(persona_data),
                        "generated_description": description,
                        "system_prompt_length": len(system_prompt)
                    }
                })
                return enhanced_npc
            else:
                logger.warning("⚠️ NPC没有persona_data")
                return npc
                
        except Exception as e:
            logger.error(f"❌ 获取NPC失败: {e}")
            self.test_results.append({
                "test": "get_npc_by_id_with_persona",
                "status": "failed",
                "message": f"获取失败: {str(e)}"
            })
            return None
    
    async def test_conversation_context(self):
        """测试对话上下文"""
        logger.info("💬 测试对话上下文...")
        
        try:
            # 获取NPC
            npc = await self.test_get_npc_by_id_with_persona()
            if not npc:
                logger.error("❌ 无法获取NPC，跳过上下文测试")
                return False
            
            npc_id = npc["id"]
            
            # 查找与此NPC相关的会话
            sessions_result = self.supabase.table("conversation_sessions").select("*").eq("npc_id", npc_id).limit(5).execute()
            
            logger.info(f"💬 找到{len(sessions_result.data)}个相关会话")
            
            context_data = []
            
            # 分析每个会话的消息
            for session in sessions_result.data:
                session_id = session["id"]
                user_id = session.get("user_id")
                
                # 获取会话消息
                messages_result = self.supabase.table("conversation_messages").select("*").eq("session_id", session_id).order("created_at").limit(10).execute()
                
                if messages_result.data:
                    logger.info(f"  📨 会话 {session_id} (用户: {user_id}) - {len(messages_result.data)}条消息")
                    
                    # 显示最近的几条消息
                    for msg in messages_result.data[-3:]:  # 显示最后3条消息
                        role = msg.get('role', 'unknown')
                        content = msg.get('content', '')
                        content_preview = content[:50] + "..." if len(content) > 50 else content
                        timestamp = msg.get('created_at', '')
                        
                        logger.info(f"    - {role}: {content_preview} ({timestamp})")
                    
                    context_data.append({
                        "session_id": session_id,
                        "user_id": user_id,
                        "message_count": len(messages_result.data),
                        "latest_messages": messages_result.data[-3:]
                    })
            
            # 测试上下文检索功能
            if context_data:
                logger.info(f"✅ 成功检索NPC上下文，共{len(context_data)}个会话")
                
                self.test_results.append({
                    "test": "conversation_context",
                    "status": "success",
                    "message": f"成功检索{len(context_data)}个会话的上下文",
                    "details": {
                        "npc_id": npc_id,
                        "npc_name": npc.get("name"),
                        "session_count": len(context_data),
                        "total_messages": sum(ctx["message_count"] for ctx in context_data)
                    }
                })
                return True
            else:
                logger.info("ℹ️ 该NPC暂无对话历史")
                self.test_results.append({
                    "test": "conversation_context",
                    "status": "info",
                    "message": "NPC暂无对话历史"
                })
                return True
                
        except Exception as e:
            logger.error(f"❌ 上下文测试失败: {e}")
            self.test_results.append({
                "test": "conversation_context",
                "status": "failed",
                "message": f"测试失败: {str(e)}"
            })
            return False
    
    async def test_create_enhanced_session(self):
        """测试创建增强会话（使用persona数据）"""
        logger.info("🆕 测试创建增强会话...")
        
        try:
            # 获取用户和NPC
            user_result = self.supabase.table("users").select("*").limit(1).execute()
            npc = await self.test_get_npc_by_id_with_persona()
            
            if not user_result.data or not npc:
                logger.warning("⚠️ 缺少用户或NPC数据")
                self.test_results.append({
                    "test": "create_enhanced_session",
                    "status": "skipped",
                    "message": "缺少用户或NPC数据"
                })
                return False
            
            user_id = user_result.data[0]["id"]
            npc_id = npc["id"]
            
            # 创建测试会话
            session_data = {
                "user_id": user_id,
                "npc_id": npc_id,
                "is_active": True
            }
            
            session_result = self.supabase.table("conversation_sessions").insert(session_data).execute()
            
            if session_result.data:
                session_id = session_result.data[0]["id"]
                logger.info(f"✅ 成功创建会话: {session_id}")
                
                # 添加测试消息，包含NPC的persona信息
                test_message = {
                    "session_id": session_id,
                    "role": "user",
                    "content": f"你好{npc.get('name', '')}，请介绍一下自己"
                }
                
                message_result = self.supabase.table("conversation_messages").insert(test_message).execute()
                
                if message_result.data:
                    logger.info("✅ 成功添加测试消息")
                    
                    # 模拟NPC回复（基于persona数据）
                    persona_data = npc.get('persona_data')
                    if persona_data:
                        try:
                            if isinstance(persona_data, str):
                                persona = json.loads(persona_data)
                            else:
                                persona = persona_data
                            
                            # 生成基于persona的回复
                            name = persona.get("name", "我")
                            age = persona.get("age", "")
                            occupation = persona.get("occupation", "")
                            personality = persona.get("personality", "")
                            hobbies = persona.get("hobbies", [])
                            
                            reply_content = f"你好！我是{name}"
                            if age:
                                reply_content += f"，{age}岁"
                            if occupation:
                                reply_content += f"，目前是{occupation}"
                            if personality:
                                reply_content += f"。我的性格{personality}"
                            if hobbies:
                                reply_content += f"，平时喜欢{', '.join(hobbies[:3])}"
                            reply_content += "。很高兴认识你！"
                            
                            npc_message = {
                                "session_id": session_id,
                                "role": "assistant",
                                "content": reply_content,
                                "emotion": "friendly",
                                "speed": 1.0
                            }
                            
                            npc_reply_result = self.supabase.table("conversation_messages").insert(npc_message).execute()
                            
                            if npc_reply_result.data:
                                logger.info(f"✅ NPC回复: {reply_content}")
                        
                        except Exception as e:
                            logger.warning(f"⚠️ 生成NPC回复失败: {e}")
                    
                    # 清理测试数据
                    self.supabase.table("conversation_messages").delete().eq("session_id", session_id).execute()
                    self.supabase.table("conversation_sessions").delete().eq("id", session_id).execute()
                    logger.info("🗑️ 清理测试数据完成")
                
                self.test_results.append({
                    "test": "create_enhanced_session",
                    "status": "success",
                    "message": f"成功创建和测试增强会话: {session_id}",
                    "details": {
                        "session_id": session_id,
                        "user_id": user_id,
                        "npc_id": npc_id,
                        "npc_name": npc.get("name"),
                        "has_persona": bool(npc.get("persona_data"))
                    }
                })
                return True
            else:
                logger.error("❌ 会话创建失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 创建增强会话失败: {e}")
            self.test_results.append({
                "test": "create_enhanced_session",
                "status": "failed",
                "message": f"创建失败: {str(e)}"
            })
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始基于persona的NPC测试...")
        
        # 初始化Supabase
        if not await self.initialize_supabase():
            logger.error("❌ Supabase初始化失败，无法继续测试")
            return False
        
        # 运行测试序列
        tests = [
            ("NPC表和persona数据", self.test_npc_table_with_persona),
            ("根据ID获取NPC", self.test_get_npc_by_id_with_persona),
            ("对话上下文检索", self.test_conversation_context),
            ("创建增强会话", self.test_create_enhanced_session)
        ]
        
        passed = 0
        failed = 0
        skipped = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 执行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                if result:
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    # 检查是否是跳过状态
                    last_result = self.test_results[-1] if self.test_results else {}
                    if last_result.get("status") in ["skipped", "info", "warning"]:
                        skipped += 1
                        logger.warning(f"⏭️ {test_name} - 跳过/信息")
                    else:
                        failed += 1
                        logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                failed += 1
                logger.error(f"❌ {test_name} - 异常: {e}")
        
        # 生成测试报告
        logger.info(f"\n{'='*60}")
        logger.info("📊 测试总结")
        logger.info(f"{'='*60}")
        logger.info(f"总测试数: {len(tests)}")
        logger.info(f"通过: {passed}")
        logger.info(f"跳过/信息: {skipped}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {(passed/(len(tests))*100):.1f}%")
        
        # 保存测试结果
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": len(tests),
                "passed": passed,
                "skipped": skipped,
                "failed": failed,
                "success_rate": f"{(passed/len(tests)*100):.1f}%"
            },
            "test_results": self.test_results
        }
        
        with open("npc_persona_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("📄 测试报告已保存到 npc_persona_test_report.json")
        
        if failed == 0:
            logger.info("🎉 所有测试通过！NPC persona上下文存取功能正常")
        else:
            logger.error("💥 部分测试失败，请检查报告")
        
        return failed == 0

async def main():
    """主函数"""
    tester = NPCPersonaTester()
    success = await tester.run_all_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)