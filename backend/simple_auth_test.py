#!/usr/bin/env python3
"""
简单的认证测试脚本
"""
import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:8000"

def test_simple_auth():
    """简单测试认证功能"""
    print("🚀 开始简单认证测试...")
    print("=" * 50)
    print(f"后端地址: {BASE_URL}")
    
    try:
        # 测试根端点
        print("\n1. 测试根端点...")
        response = requests.get(f"{BASE_URL}/")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 根端点正常")
        else:
            print("   ❌ 根端点异常")
            return False
            
        # 测试健康检查端点
        print("\n2. 测试健康检查端点...")
        response = requests.get(f"{BASE_URL}/health")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 健康检查端点正常")
        else:
            print("   ❌ 健康检查端点异常")
            return False
            
        # 测试NPCs端点
        print("\n3. 测试NPCs端点...")
        response = requests.get(f"{BASE_URL}/npcs")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ NPCs端点正常")
        else:
            print("   ❌ NPCs端点异常")
            return False
            
        print("\n🎉 简单认证测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_auth()
    exit(0 if success else 1)
