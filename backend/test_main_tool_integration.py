#!/usr/bin/env python3
"""
测试main.py中的工具调用集成
"""
import requests
import json
import time
import sys

def test_main_tool_integration():
    """测试main.py中的工具调用集成"""
    print("🧪 测试main.py中的工具调用集成")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/health", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器已启动")
                break
        except:
            pass
        print(f"   尝试 {i+1}/10...")
        time.sleep(2)
    else:
        print("❌ 服务器启动超时")
        print("💡 请在另一个终端运行: cd backend && python main.py")
        return False
    
    # 测试用例
    test_cases = [
        {
            "name": "12306火车票查询",
            "payload": {
                "user_input": "BJP 到 SHH 2025-04-15的火车票有哪些",
                "npc_id": 1
            }
        },
        {
            "name": "普通对话测试",
            "payload": {
                "user_input": "你好，今天天气怎么样？",
                "npc_id": 1
            }
        },
        {
            "name": "新闻查询测试",
            "payload": {
                "user_input": "帮我查询最新的科技新闻",
                "npc_id": 1
            }
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}: {test_case['name']}")
        print(f"   输入: {test_case['payload']['user_input']}")
        
        try:
            response = requests.post(
                f"{base_url}/api/test-tool-calling",
                json=test_case['payload'],
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    print(f"   ✅ 测试成功")
                    print(f"   🔧 使用工具: {result.get('tools_used', [])}")
                    print(f"   📞 工具调用次数: {result.get('tool_calls_made', 0)}")
                    print(f"   🗣️ 响应: {result.get('response', '')[:100]}...")
                    print(f"   😊 情感: {result.get('emotion', 'neutral')}")
                    print(f"   ⚡ 语速: {result.get('speed', 1.0)}")
                    success_count += 1
                else:
                    print(f"   ❌ 测试失败: {result.get('error', '未知错误')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                print(f"   错误内容: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    # 测试总结
    print(f"\n{'=' * 50}")
    print(f"测试总结")
    print(f"{'=' * 50}")
    print(f"总测试数: {len(test_cases)}")
    print(f"成功测试: {success_count}")
    print(f"失败测试: {len(test_cases) - success_count}")
    print(f"成功率: {success_count/len(test_cases)*100:.1f}%")
    
    if success_count == len(test_cases):
        print("\n🎉 所有测试通过！main.py中的工具调用功能正常工作。")
        return True
    else:
        print(f"\n⚠️ {len(test_cases) - success_count} 个测试失败")
        return False

def test_existing_endpoints():
    """测试现有的端点是否仍然正常工作"""
    print(f"\n🔍 测试现有端点")
    print("-" * 30)
    
    base_url = "http://localhost:8000"
    
    endpoints = [
        {"url": "/health", "method": "GET", "name": "健康检查"},
        {"url": "/", "method": "GET", "name": "根端点"},
        {"url": "/npcs", "method": "GET", "name": "NPC列表"},
        {"url": "/api/tools/enumerate", "method": "GET", "name": "工具枚举"},
        {"url": "/api/tools/statistics", "method": "GET", "name": "工具统计"}
    ]
    
    for endpoint in endpoints:
        try:
            if endpoint["method"] == "GET":
                response = requests.get(f"{base_url}{endpoint['url']}", timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ {endpoint['name']}: 正常")
            else:
                print(f"   ❌ {endpoint['name']}: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {endpoint['name']}: 异常 - {e}")

if __name__ == "__main__":
    print("🚀 main.py工具调用集成测试")
    print("=" * 60)
    
    # 测试工具调用功能
    success = test_main_tool_integration()
    
    # 测试现有端点
    test_existing_endpoints()
    
    if success:
        print(f"\n🎉 main.py工具调用集成测试完成！")
        print("💡 现在可以通过WebSocket或API使用工具调用功能了。")
        sys.exit(0)
    else:
        print(f"\n❌ 部分测试失败，请检查配置。")
        sys.exit(1)