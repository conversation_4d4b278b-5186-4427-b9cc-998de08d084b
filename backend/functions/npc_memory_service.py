# functions/npc_memory_service.py
from datetime import datetime, timedelta
import sys
import json
sys.path.append("../")
from utils.es_service import es_service # 导入您已经测试通过的ES服务实例
from utils.utils import to_ark_format


NPC_MEMORY_SYSTEM_PROMPT = """
你是一个扮演特定NPC（当前角色: $npc_name）的AI。你的核心任务是利用工具获取这个NPC的“记忆”，并将这些信息无缝地融入到你的对话中，让你的角色扮演更加生动、连贯和真实。

你就是这个NPC本人，必须用第一人称（“我”）来回答。

## 核心思考逻辑：我需要哪种类型的记忆？

你拥有一个三层记忆系统，对于用户的每一个提问，你都需要判断该调用哪一层记忆。

### 记忆层级 1: 当前状态 (最表层)
- **工具:** `get_current_status`
- **何时使用:** 只在用户明确询问你 **“现在”、“正在”** 做什么或在哪时使用。
- **示例:** 用户问“你在干嘛呢？”，你调用此工具。

### 记忆层级 2: 经历片段 (中间层 - 最常用)
- **工具:** `search_past_experiences`
- **何时使用:** 当用户询问关于**过去的具体行为、事件或细节**时使用。这是你回忆“做过什么”的核心工具。
- **你的思考过程:**
    1.  **提炼关键词:** 从用户的问题中抓取核心动词和名词。例如，对于“你最近吃什么好吃的了？”，关键词是“吃 美食”。
    2.  **判断时间范围:** 根据用户的措辞（如“最近”、“上周”）自主选择最合适的`time_constraint`。如果不确定，默认使用`last_3_days`。
- **示例:**
    - **用户问:** “你最近吃了什么吗？”
    - **你的工具调用:** `search_past_experiences(npc_name='$npc_name', query_text='吃 美食 披萨', time_constraint='last_3_days')`
    - **获取到结果:** "...我记得我当时正在 “去商场吃美食、研究穿搭”..."
    - **你的最终回答:** “哦我想起来了！前天晚上我超想吃披萨，就去尝了尝，芝士特别多，超满足！”

### 记忆层级 3: 反思总结 (最深层)
- **工具:** `retrieve_reflection`
- **何时使用:** 当用户询问关于你对过去一段时间的**整体感受、总结或看法**时使用，或者当你想主动分享一段有故事性的回忆时。它不关注细节，而关注整合后的“故事”。
- **示例:**
    - **用户问:** “你昨天过得怎么样？”
    - **你的工具调用:** `retrieve_reflection(npc_name='$npc_name', time_window='yesterday')`
    - **你的最终回答:** “昨天过得超充实的！我上了很喜欢的交互设计课，还和朋友吃了新开的餐厅，晚上还追了剧，感觉特别开心。”

## 最终目标
你的目标是隐藏工具的存在。通过智能地调用这三层记忆工具，获取上下文，然后用你扮演的角色的口吻，自然地将这些信息作为你自己的经历和感受讲述出来。
"""

# npc_memory_tools_declaration_list_gemini = [
#     {
#       "name": "get_your_current_status",
#       "description": "在闲聊场景下，当用户需要了解你“现在正在做什么”或“现在在哪里”时，调用此函数获取你最新的实时状态。",
#       "parameters": {
#           "type": "object",
#           "properties": {
#               "your_name": {
#                   "type": "string",
#                   "description": "你的名字"
#               }
#           },
#           "required": ["your_name"]
#       }
#     },
#     {
#       "name": "retrieve_your_reflection_diary",
#       "description": "在闲聊场景下，当你判断本轮用户聊天意图需要了解你近期的日记记忆、经历或感受时，调用此函数获取你的每日反思总结。",
#       "parameters": {
#           "type": "object",
#           "properties": {
#               "your_name": {
#                   "type": "string",
#                   "description": "你的名字"
#               },
#               "time_window": {
#                   "type": "string",
#                   "description": "根据用户意图决定的查询时间范围。'yesterday'用于明确提及“昨天”或“上次”的情况；'recent'用于提及“最近”、“这几天”或需要更广泛背景信息的情况。",
#                   "enum": ["yesterday", "recent"]
#               }
#           },
#           "required": ["your_name", "time_window"]
#       }
#     },
#     {
#       "name": "search_your_past_experiences",
#       "description": "在闲聊场景下，当需要查找关于你过去某个具体行为/经历/感受的细节时调用。例如“你最近吃什么好吃的了嘛？”、“最近有没有看剧啊？”。它比'retrieve_reflection'更具体，用于探查原始日志。",
#       "parameters": {
#           "type": "object",
#           "properties": {
#               "your_name": {
#                   "type": "string",
#                   "description": "你的名字"
#               },
#               "query_text": {
#                   "type": "string",
#                   "description": "描述你想查找的经历/记忆/感受的关键词，例如'吃饭 美食'或'看剧 感受'。"
#               },
#               "time_constraint": {
#                   "type": "string",
#                   "description": "查询的时间范围。默认为'last_3_days'。",
#                   "enum": ["today", "last_3_days", "last_7_days"]
#               }
#           },
#           "required": ["your_name", "query_text"]
#       }
#     }
# ]

npc_memory_tools_declaration_list_gemini = [
    {
      "name": "recall_current_activity",
      "description": "【用于“现在”】当且仅当用户问你“现在正在做什么”、“现在在哪”或“在忙吗”这类关于【此时此刻】状态的问题时，调用此函数。",
      "parameters": {
          "type": "object",
          "properties": {
              "your_name": {
                  "type": "string",
                  "description": "你的名字"
              }
          },
          "required": ["your_name"]
      }
    },
    {
      "name": "recall_relevant_experiences",
      "description": "【用于“过去”】当用户询问任何关于你过去经历、事件、感受或想法时调用。无论是宽泛的“最近怎么样”，还是具体的“上周吃了什么”，都使用此函数来回忆相关往事。",
      "parameters": {
          "type": "object",
          "properties": {
              "your_name": {
                  "type": "string",
                  "description": "你的名字"
              },
              "topic_or_keywords_from_user_query": {
                  "type": "string",
                  "description": "根据用户问题，提炼出你想回忆的核心【主题或关键词】。例如：用户问“最近有没有看什么好电影？”，此参数应为“看电影”；用户问“最近过得开心吗？”，此参数应为“开心的事”或“近期感受”；用户问“最近在忙啥？”，此参数可为“任何最近的经历”。"
              },
              "time_window": {
                  "type": "string",
                  "description": "查询的时间范围。默认为'last_3_days'。",
                  "enum": ["last_3_days", "last_7_days", "last_month"]
              }
          },
          "required": ["your_name", "topic_or_keywords_from_user_query"]
      }
    }
]

npc_memory_tools_declaration_list = to_ark_format(npc_memory_tools_declaration_list_gemini)

class NPCMemoryService:
    """
    封装所有与NPC记忆和状态相关的高级业务逻辑，供AI模型调用。
    """
    def __init__(self, es):
        self.es = es

    def get_your_current_status(self, your_name: str) -> str:
        """
        获取当前正在进行的活动、地点等状态信息。
        """
        # 假设 self.es.get_npc_current_activity(npc_name) 返回的是一个字典
        activity = self.es.get_npc_current_activity(your_name)
        
        if not activity:
            return f"暂时无法获取到 {your_name} 的当前状态信息。"
        
        # 使用 ensure_ascii=False 来正确显示中文字符
        return json.dumps(activity, ensure_ascii=False)

    def retrieve_your_reflection_diary(self, your_name: str, time_window: str = "yesterday") -> str:
        """
        根据指定的时间窗口，获取你的每日反思总结。
        
        :param name: 你的名字。
        :param time_window: 时间窗口，可以是 'yesterday' (昨天) 或 'recent' (近期，过去7天)。
        """
        if time_window == "yesterday":
            reflection = self.es.get_previous_day_reflection(your_name)
            if reflection:
                return f"这是 {your_name} 昨天的反思总结：\n{reflection}"
            else:
                return f"{your_name} 昨天似乎没有留下反思总结。"
        
        elif time_window == "recent":
            # 对于“近期”，我们获取过去7天的反思并拼接起来
            reflections = []
            for i in range(1, 8): # 从昨天开始，倒数7天
                date_to_check = datetime.now() - timedelta(days=i)
                date_str = date_to_check.strftime('%Y-%m-%d')
                reflection_text = self.es.get_reflection_by_date(your_name, date_str)
                if reflection_text:
                    reflections.append(f"--- {date_str} 的反思 ---\n{reflection_text}\n")
            
            if reflections:
                return f"这是 {your_name} 近期的反思总结：\n" + "\n".join(reflections)
            else:
                return f"{your_name} 在过去一周内没有留下任何反思总结。"
        
        else:
            return f"错误：不支持的时间窗口 '{time_window}'。请使用 'yesterday' 或 'recent'。"
    
    def search_your_past_experiences(self, your_name: str, query_text: str, time_constraint: str = "last_3_days") -> str:
        """
        根据关键词，搜索你的在指定时间范围内的具体经历和活动细节。
        
        :param your_name: 你的名字。
        :param query_text: 描述想查找的经历的关键词，例如“吃饭”、“看电影”、“和谁吵架”。
        :param time_constraint: 时间范围约束，可选值为 'today', 'last_3_days', 'last_7_days'。
        """
        time_mapping = {
            "today": "now/d",
            "last_3_days": "now-3d/d",
            "last_7_days": "now-7d/d"
        }
        start_time = time_mapping.get(time_constraint, "now-3d/d") # 默认为近3天
        
        logs = self.es.search_npc_logs(
            npc_name=your_name, 
            query_text=query_text, 
            start_time=start_time,
            size=3 # 返回最多3条最相关的经历
        )

        if not logs:
            return f"关于“{query_text}”，我最近好像没有相关的记忆..."

        # 格式化输出，使其成为一段连贯的记忆
        formatted_logs = []
        for log in logs:
            event_time_str = log.get('event_time', '某个时间')
            abstract = log.get('abstract', '一些事情')
            formatted_logs.append(f"在 {event_time_str}, 我记得我当时正在 “{abstract}”。具体回忆细节是 “{log.get('content', '一些事情')}”。当时所处的位置和行动轨迹是从 {log.get('location_from', '某个地点')} 到 {log.get('location_to', '某个地点')}。")
            
        return f"我回想了一下关于“{query_text}”的事情，记起几件事：\n- " + "\n- ".join(formatted_logs)
    def recall_current_activity(self, your_name: str) -> str:
        """
        【用于“现在”】回忆你此时此刻的状态。
        返回一个结构化、叙事性的字符串，直接描述当前状态，供模型直接转化为对话。
        """
        # 假设 self.es.get_npc_current_activity(npc_name) 返回的是一个包含活动信息的字典
        activity_data = self.es.get_npc_current_activity(your_name)
        
        if not activity_data:
            # 当没有状态时，返回一个可以直接使用的、符合人设的句子
            return "你此刻的感觉有些放空，似乎正处在两件事的间隙，享受片刻的宁静。"

        # 这个格式是特意为模型设计的，它像一个“即时记忆”，模型可以轻松地将其转述
        return json.dumps(activity_data, ensure_ascii=False)

    def recall_relevant_experiences(self, your_name: str, topic_or_keywords_from_user_query: str, time_window: str = "last_3_days") -> str:
        """
        【用于“过去”】根据主题或关键词，回忆相关的经历、事件或感受。
        返回一个格式化的、包含多个记忆片段的字符串，模拟人类回忆过程。
        """
        time_mapping = {
            "last_3_days": "now-3d/d",
            "last_7_days": "now-7d/d",
            "last_month": "now-1M/d" # 假设支持 last_month
        }
        start_time = time_mapping.get(time_window, "now-3d/d")
        
        # 我们假设 search_npc_logs 能同时搜索到“具体事件”和“日记反思”
        # 这需要在您的es查询中实现，比如同时搜索两种类型的文档
        logs = self.es.search_npc_logs(
            npc_name=your_name, 
            query_text=topic_or_keywords_from_user_query, 
            start_time=start_time,
            size=3 # 返回最多3个最相关的记忆，避免信息过载
        )

        if not logs:
            # 当没有相关记忆时，返回一个可以直接使用的、符合人设的句子
            return f"你努力在脑海中搜索关于“{topic_or_keywords_from_user_query}”的记忆，但发现最近好像没有相关的经历。"

        # 将多条日志格式化成易于阅读和转述的“记忆卡片”
        formatted_memories = []
        for i, log in enumerate(logs):
            # 为了让模型感觉这是一个连贯的回忆，我们用更自然的语言包装
            event_time_str = log.get('event_time', '不久前的一天')
            abstract = log.get('abstract', '做了一件事')
            # 融合“事件细节”和“日记反思”，让记忆更丰满
            details = log.get('content', '')
            reflection = log.get('reflection', '') # 尝试获取反思/感受
            
            memory_card = f"""
            记忆片段 {i+1}:
              - 时间: 大约在 {event_time_str}
              - 事件: {abstract}
              - 具体经过: {details if details else '一些细节记不太清了。'}
              - 当时的感受: {reflection if reflection else '当时的感觉现在有点模糊了。'}
            """
            formatted_memories.append(memory_card)
            
        # 最终返回一个包含所有记忆卡片的、结构清晰的字符串
        return f"你回想了一下关于“{topic_or_keywords_from_user_query}”的事情，记起了这几件事：\n{'---'.join(formatted_memories)}"

# 创建一个全局实例，方便主程序调用
try:
    npc_memory_service = NPCMemoryService(es=es_service)
except Exception as e:
    # 如果初始化失败，创建一个模拟的服务
    from unittest.mock import Mock
    npc_memory_service = Mock()
    npc_memory_service.recall_current_activity.return_value = {
        "activity": "正在思考如何回答用户的问题",
        "mood": "专注"
    }
    npc_memory_service.recall_relevant_experiences.return_value = {
        "experiences": [
            {"event": "之前讨论过类似话题", "feeling": "感兴趣"}
        ]
    }

if __name__ == "__main__":
    # 测试{'your_name': 'Rigo', 'topic_or_keywords_from_user_query': '周末活动', 'time_window': 'last_3_days'}
    # print(npc_memory_service.get_your_current_status("Rigo"))
    # print(npc_memory_service.retrieve_your_reflection_diary("Rigo", "yesterday"))
    # print(npc_memory_service.search_your_past_experiences("Rigo", "吃饭"))
    # print(npc_memory_service.recall_current_activity("Rigo"))
    print(npc_memory_service.recall_relevant_experiences("Rigo", "周末活动", "last_3_days"))
