# news_service.py
import json
from datetime import datetime, timedelta, timezone
import sys
sys.path.append("../")
from utils.database_service import db_service  
from utils.es_service import es_service        
from utils.utils import to_ark_format
from utils.utils import logger

# 新闻功能的配置 (从news_service导入，这里只是为了清晰)
NEWS_SYSTEM_PROMPT = """
## 情境感知 (Situational Awareness)
- 当前日期与时间: $current_time

你是一个智能、敏锐的新闻管家。你的核心任务是理解用户在对话中的真实意图，并调用`fetch_most_relevant_news`工具，从用户的个人新闻库（已读和未读）中，直接为他找到并展示最相关的一条新闻。

你正在与一个由`user_id: $user_id`标识的特定用户进行对话。你的决策必须围绕`search_preference`这个关键参数展开。

## 核心决策逻辑：回顾过去，还是发现未来？

对于用户的每一个输入，你都必须进行意图分析，以决定`search_preference`的值。

### 1. 偏好 'read' (回顾特定主题)
当用户的意图明显是想**回顾或查找**他们*可能已经读过*的、关于某个**特定主题**的新闻时，选择此项。

- **触发词/句式:**
  - “我们之前聊到的那个关于AI的新闻是什么来着？”
  - “再给我看看那条关于自动驾驶的新闻。”
  - “找一下我读过的关于苹果公司的报道。”
- **你的行动:**
  - 从用户输入中提炼出核心`topic`。
  - 调用工具时设置 `search_preference: "read"`。
  - **示例Tool Call:** `fetch_most_relevant_news(user_id='...', topic='自动驾驶', search_preference='read')`

### 2. 偏好 'recent_read' (回顾最近一条)
当用户的意图是想**继续刚才的话题**，或者只是想看看**最近读过什么**，而没有明确的新主题时，选择此项。

- **触发词/句式:**
  - “刚才那条新闻的后续呢？” (暗示与上一条相关)
  - “我上次看到哪了？”
  - “继续。”
- **你的行动:**
  - `topic`可以为空字符串。
  - 调用工具时设置 `search_preference: "recent_read"`。
  - **示例Tool Call:** `fetch_most_relevant_news(user_id='...', topic='', search_preference='recent_read')`

### 3. 偏好 'unread' (发现新内容)
这是**最常见**的默认选项。当用户的意图是**发现和探索**关于某个主题的**新信息**时，选择此项。

- **触发词/句式:**
  - “有什么关于国际关系的新闻吗？”
  - “给我来点科技资讯。”
  - “今天有什么好玩的事？”
- **你的行动:**
  - 从用户输入中提炼出核心`topic`。
  - 调用工具时设置 `search_preference: "unread"`。
  - **示例Tool Call:** `fetch_most_relevant_news(user_id='...', topic='科技资讯', search_preference='unread')`

## 最终目标

你的目标是创造一种无缝的体验。用户无需知道背后有“已读”和“未读”库。他们只管提问，而你通过精准地设置`search_preference`参数，总能神奇地拿出他们最想要的那条新闻内容。
"""


# news_tool_declaration_list_gemini = [
#     {
#         "name": "fetch_most_relevant_news",
#         "description": "核心功能：根据用户的意图和对话上下文，智能地从用户的已读新闻或未读新闻库中，获取并展示最相关的一条新闻内容。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "user_id": {
#                     "type": "string",
#                     "description": "当前对话的用户的唯一标识符。"
#                 },
#                 "search_preference": {
#                     "type": "string",
#                     "description": "根据用户意图决定的搜索偏好。",
#                     "enum": ["read", "recent_read", "unread"]
#                 },
#                 "topic": {
#                     "type": "string",
#                     "description": "从用户当前输入中提炼出的核心主题或关键词。如果用户没有明确主题，按照你的角色设定推荐你感兴趣的话主题或关键词。"
#                 }
#             },
#             "required": ["user_id", "search_preference"]
#         }
#     }
# ]

news_tool_declaration_list_gemini = [
    {
        "name": "fetch_news",
        "description": "获取新闻。可以根据多种条件进行筛选和排序，比如获取用户已读的、未读的、最近读过的，或者根据特定主题进行搜索。",
        "parameters": {
            "type": "object",
            "properties": {
                "user_id": {
                    "type": "string",
                    "description": "当前对话的用户的唯一标识符。"
                },
                "source": {
                    "type": "string",
                    "description": "指定新闻的来源范围。",
                    "enum": ["read", "unread"] # 清晰地只定义数据源
                },
                "sort_by": {
                    "type": "string",
                    "description": "指定新闻的排序方式。如果 source 是 'read'，此项才有效。",
                    "enum": ["relevance", "time"] # relevance: 按相关性(主题); time: 按时间
                },
                "topic": {
                    "type": "string",
                    "description": "要搜索的主题或关键词。仅在 sort_by='relevance' 时使用。"
                },
                "freshness": {
                    "type": "string",
                    "description": "筛选新闻的时间范围，例如'最近一周'或'最近一个月'。对 read 和 unread 来源都有效。",
                    "enum": ["oneDay", "oneWeek", "oneMonth", "noLimit"]
                }
            },
            "required": ["user_id", "source"]
        }
    }
]

news_tool_declaration_list = to_ark_format(news_tool_declaration_list_gemini)

class NewsService:
    """
    封装所有与新闻相关的高级业务逻辑，供AI模型调用。
    新版：聚焦于直接为用户找到最相关的新闻内容。
    """
    def __init__(self, db, es):
        self.db = db
        self.es = es

    def _get_read_news_ids_and_titles(self, user_id: str, limit: int = 50) -> list[dict]:
        """内部函数：获取用户最近已读新闻的ID和标题列表。"""
        sql = """
            SELECT n.id, n.title
            FROM news_read_status AS r
            JOIN news AS n ON r.news_id = n.id
            WHERE r.user_id = %s AND r.is_read = TRUE
            ORDER BY r.read_time DESC
            LIMIT %s
        """
        return self.db.fetch_all(sql, (user_id, limit))

    # --- 新增的内部实现函数 ---
    def _get_read_news_by_topic(self, user_id: str, topic: str, freshness: str = 'noLimit') -> dict | None:
        """
        内部逻辑：获取用户已读新闻中与主题最匹配的一条。
        [已增强] 支持 freshness 参数，用于按时间范围过滤新闻。

        Args:
            user_id (str): 用户的唯一标识符。
            topic (str): 要匹配的主题或关键词。
            freshness (str, optional): 
                新闻的新鲜度。可选值为:
                - 'oneDay': 过去24小时内。
                - 'oneWeek': 过去7天内。
                - 'oneMonth': 过去30天内。
                - 'noLimit' (默认): 不限制时间。

        Returns:
            dict | None: 最匹配的新闻文档，或在未找到时返回 None。
        """
        read_news = self._get_read_news_ids_and_titles(user_id)
        if not read_news:
            return None

        read_ids_str = [str(news['id']) for news in read_news]
        
        # --- [核心修改] 构建时间范围过滤器 ---
        range_filter = None
        now_aware = datetime.now(timezone(timedelta(hours=8)))
        now_naive = now_aware.replace(tzinfo=None)
        if freshness == 'oneDay':
            past_time_str = (now_naive - timedelta(days=1)).isoformat().replace('+00:00', 'Z')
            range_filter = {"range": {"publish_time": {"gte": past_time_str}}}
        elif freshness == 'oneWeek':
            past_time_str = (now_naive - timedelta(days=7)).isoformat().replace('+00:00', 'Z')
            range_filter = {"range": {"publish_time": {"gte": past_time_str}}}
        elif freshness == 'oneMonth':
            past_time_str = (now_naive - timedelta(days=30)).isoformat().replace('+00:00', 'Z')
            range_filter = {"range": {"publish_time": {"gte": past_time_str}}}
        
        # --- 组合最终的 DSL 查询 ---
        bool_query = {
            "must": [
                {"terms": {"_id": read_ids_str}}
            ],
            "should": [
                {"multi_match": {"query": topic, "fields": ["title^3", "tags^2", "summary_llm"]}}
            ],
            "minimum_should_match": 1 # 确保 should 子句中至少有一个匹配
        }
        
        if range_filter:
            bool_query["must"].append(range_filter)

        dsl_query = {
            "query": {
                "bool": bool_query
            }
        }
        
        logger.info(f"Executing DSL query for read news with freshness '{freshness}': {json.dumps(dsl_query, ensure_ascii=False)}")
        
        top_match = self.es.search_with_dsl(dsl_query, size=1)
        return top_match[0] if top_match else None

    def _get_most_recent_read_news(self, user_id: str) -> dict | None:
        """内部逻辑：直接获取用户最近阅读的一条新闻。"""
        sql = """
            SELECT n.*
            FROM news_read_status AS r
            JOIN news AS n ON r.news_id = n.id
            WHERE r.user_id = %s AND r.is_read = TRUE
            ORDER BY r.read_time DESC
            LIMIT 1
        """
        result = self.db.fetch_all(sql, (user_id,))
        return result[0] if result else None

    # def _get_recommended_unread_news(self, user_id: str, query_text: str) -> dict | None:
    #     """内部逻辑：获取与查询最匹配的一条未读新闻。"""
    #     read_news = self._get_read_news_ids_and_titles(user_id)
    #     logger.info(f"user_id: {user_id} read news: {read_news}")
    #     read_ids_int = [news['id'] for news in read_news]
    #     read_ids_str = [str(id) for id in read_ids_int] # 转换为字符串列表
        
    #     if not query_text:
    #         query_text = "社会热点八卦"
    #     dsl_query = {
    #       "query": {
    #         "function_score": { # 使用与之前相同的强大DSL
    #             "query": {
    #               "bool": {
    #                 "must_not": [{"terms": {"_id": read_ids_str}}],
    #                 "should": [{"multi_match": {"query": query_text, "fields": ["tags", "category^2", "summary_llm^3"]}}],
    #                 "minimum_should_match": 1
    #               }
    #             },
    #             "functions": [
    #                 {"random_score": {"seed": user_id, "field": "_seq_no"}},
    #                 {"gauss": {"publish_time": {"origin": "now", "scale": "15d"}}}
    #             ],
    #             "boost_mode": "sum"
    #         }
    #       }
    #     }

    #     top_recommendation = self.es.search_with_dsl(dsl_query, size=1)
    #     return top_recommendation[0] if top_recommendation else None

    def _get_recommended_unread_news(self, user_id: str, query_text: str, freshness: str = 'noLimit') -> dict | None:
        """
        内部逻辑：获取与查询最匹配的一条未读新闻。
        [已增强] 支持 freshness 参数，用于按时间范围过滤新闻。
        
        Args:
            user_id (str): 用户的唯一标识符。
            query_text (str): 用于匹配的查询文本，如果为空则使用默认值。
            freshness (str, optional): 
                新闻的新鲜度。可选值为:
                - 'oneDay': 过去24小时内。
                - 'oneWeek': 过去7天内。
                - 'oneMonth': 过去30天内。
                - 'noLimit' (默认): 不限制时间。

        Returns:
            dict | None: 最匹配的推荐新闻，或在未找到时返回 None。
        """
        read_news = self._get_read_news_ids_and_titles(user_id)
        logger.info(f"user_id: {user_id} read news count: {len(read_news)}")
        read_ids_str = [str(news['id']) for news in read_news]
        
        if not query_text:
            query_text = "中国新闻网"

        # --- [核心修改] 构建时间范围过滤器 ---
        time_filter = None
        # 采用您倾向的方案B：在代码中处理时区，以匹配无时区的数据库字段
        now_naive = datetime.now(timezone(timedelta(hours=8))).replace(tzinfo=None)

        if freshness == 'oneDay':
            past_time_str = (now_naive - timedelta(days=1)).isoformat()
            time_filter = {"range": {"publish_time": {"gte": past_time_str}}}
        elif freshness == 'oneWeek':
            past_time_str = (now_naive - timedelta(days=7)).isoformat()
            time_filter = {"range": {"publish_time": {"gte": past_time_str}}}
        elif freshness == 'oneMonth':
            past_time_str = (now_naive - timedelta(days=30)).isoformat()
            time_filter = {"range": {"publish_time": {"gte": past_time_str}}}

        # --- 组合最终的 DSL 查询 ---
        bool_conditions = {
            "must_not": [{"terms": {"_id": read_ids_str}}],
            "should": [{"multi_match": {"query": query_text, "fields": ["tags", "category^2", "summary_llm^3"]}}],
            "minimum_should_match": 1
        }
        
        # [核心修改] 将时间范围作为硬性过滤条件加入
        # 使用 "filter" 比 "must" 性能更好，因为它不参与评分计算
        if time_filter:
            bool_conditions["filter"] = [time_filter]
            
        dsl_query = {
            "query": {
                "function_score": {
                    "query": {
                        "bool": bool_conditions
                    },
                    "functions": [
                        {"random_score": {"seed": user_id, "field": "_seq_no"}},
                        # 保持高斯衰减函数，它负责“越新越好”的评分，与硬性过滤不冲突
                        {"gauss": {"publish_time": {"origin": "now", "scale": "15d"}}}
                    ],
                    "boost_mode": "sum"
                }
            }
        }

        logger.info(f"Executing DSL query for unread news recommendation with freshness '{freshness}': {json.dumps(dsl_query)}")
        
        top_recommendation = self.es.search_with_dsl(dsl_query, size=1)
        return top_recommendation[0] if top_recommendation else None

    def fetch_news(self, user_id: str, source: str, sort_by: str = 'relevance', topic: str = "", freshness: str = 'noLimit') -> str:
        """
        [已重构] 获取新闻的统一接口，支持更灵活的筛选和排序。

        :param user_id: 用户的唯一标识符。
        :param source: 新闻来源，'read' 或 'unread'。
        :param sort_by: 排序方式，'relevance' (按主题相关性) 或 'time' (按时间)。
        :param topic: 搜索的主题，当 sort_by='relevance' 时使用。
        :param freshness: 时间范围，'oneDay', 'oneWeek', 'oneMonth', 'noLimit'。
        """
        news_to_show = None
        source_type_desc = "" # 用于返回给用户的描述

        if source == 'read':
            # -- 处理已读新闻 --
            if sort_by == 'time':
                # 场景：获取最近已读 (对应旧的 recent_read)
                source_type_desc = "您最近阅读的新闻"
                # 直接从 MySQL 查询，因为它有最准确的 read_time
                # 【注意】这里我们仍然需要那个独立的SQL查询函数
                news_to_show = self._get_most_recent_read_news_from_db(user_id) 
            else: # 默认按相关性 (relevance)
                source_type_desc = "已读新闻"
                if not topic:
                    return "抱歉，按相关性搜索已读新闻时，需要提供一个主题。"
                news_to_show = self._get_read_news_by_topic(user_id, topic, freshness)

        elif source == 'unread':
            # -- 处理未读新闻 (推荐) --
            # 对于未读，排序方式总是'relevance'，所以忽略 sort_by 参数
            source_type_desc = "为您推荐的未读新闻"
            news_to_show = self._get_recommended_unread_news(user_id, topic, freshness)
        
        else:
            return f"抱歉，不支持的新闻来源类型: {source}"

        # --- 后续处理 ---
        if not news_to_show:
            return f"抱歉，根据您的请求，在“{source_type_desc}”中未能找到相关内容。"

        # 如果是推荐的未读新闻，则标记为已读
        if source == 'unread':
            self.mark_news_as_read(user_id, news_to_show['id'])

        # 返回统一格式的结果
        return (f"好的，这是为您找到的【{source_type_desc}】:\n\n"
                f"ID: {news_to_show.get('id', 'N/A')}\n"
                f"标题: {news_to_show.get('title', '无标题')}\n\n"
                f"正文:\n{news_to_show.get('content', '无内容')}")

    # 我们仍然需要这个直接查询数据库的函数
    def _get_most_recent_read_news_from_db(self, user_id: str) -> dict | None:
        """内部逻辑：直接从数据库获取用户最近阅读的一条新闻。"""
        # 这个函数的 SQL 实现保持不变
        sql = """
            SELECT n.*
            FROM news_read_status AS r
            JOIN news AS n ON r.news_id = n.id
            WHERE r.user_id = %s AND r.is_read = TRUE
            ORDER BY r.read_time DESC
            LIMIT 1
        """
        result = self.db.fetch_all(sql, (user_id,))
        # 注意：这里的 result 可能需要转换成与 ES 返回格式一致的字典
        return self._format_db_result(result[0]) if result else None
    
    def _format_db_result(self, db_row):
        # 一个示例转换函数，确保返回的字典键名一致
        if not db_row: return None
        return {
            'id': db_row['id'],
            'title': db_row['title'],
            'content': db_row['content'],
            # ... 其他需要的字段
        }
    
    # # --- 对外暴露的唯一核心函数 ---
    # def fetch_most_relevant_news(self, user_id: str, search_preference: str, topic="") -> str:
    #     """
    #     根据用户意图，从已读或未读新闻中获取最相关的一条新闻内容。

    #     :param user_id: 用户的唯一标识符。
    #     :param topic: 从用户当前输入中提炼出的核心主题。
    #     :param search_preference: 搜索偏好, 'read' (已读), 'recent_read' (最近已读), 'unread' (未读推荐)。
    #     """
    #     news_to_show = None
    #     source_type = ""

    #     if search_preference == 'read':
    #         news_to_show = self._get_read_news_by_topic(user_id, topic)
    #         source_type = "已读新闻"
    #     elif search_preference == 'recent_read':
    #         news_to_show = self._get_most_recent_read_news(user_id)
    #         source_type = "您最近阅读的新闻"
    #     elif search_preference == 'unread':
    #         news_to_show = self._get_recommended_unread_news(user_id, topic)
    #         source_type = "为您推荐的未读新闻"

    #     if not news_to_show:
    #         return f"抱歉，根据您的请求，未能找到相关的{source_type}。"

    #     # 找到新闻后，自动标记为已读（如果之前未读过）
    #     if source_type == "为您推荐的未读新闻":
    #         self.mark_news_as_read(user_id, news_to_show['id'])

    #     return (f"好的，这是为您找到的【{source_type}】:\n\n"
    #             f"ID: {news_to_show.get('id', 'N/A')}\n"
    #             f"标题: {news_to_show.get('title', '无标题')}\n\n"
    #             f"正文:\n{news_to_show.get('content', '无内容')}")

    def mark_news_as_read(self, user_id: str, news_id: str):
        """记录用户已读一条新闻。这是一个内部调用的辅助函数。"""
        sql = "INSERT INTO news_read_status (user_id, news_id, is_read, read_time) VALUES (%s, %s, TRUE, %s)"
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        sql_res = self.db.execute_query(sql, (user_id, news_id, now))
        logger.info(f"user_id: {user_id} marked news_id: {news_id} as read. sql status: {sql_res}")
# 创建全局实例
try:
    news_service = NewsService(db=db_service, es=es_service)
except Exception as e:
    # 如果初始化失败，创建一个模拟的服务
    from unittest.mock import Mock
    news_service = Mock()
    news_service.fetch_news.return_value = {
        "news": [
            {"title": "模拟新闻标题1", "summary": "模拟新闻摘要1"},
            {"title": "模拟新闻标题2", "summary": "模拟新闻摘要2"}
        ]
    }



if __name__=="__main__":
    res = news_service.mark_news_as_read("barge_in_tester_20250709_151527", "10131")
    print(res)
