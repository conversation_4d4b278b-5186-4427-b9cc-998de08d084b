# ai-search/functions/user_memory_service.py

import sys
import os
import threading

# --- 关键：将 agent_memory_lib 的 src 目录添加到 Python 路径 ---
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
agent_memory_lib_path = os.path.join(parent_dir, 'llm_memory_v7_L0')
if agent_memory_lib_path not in sys.path:
    sys.path.append(agent_memory_lib_path)

try:
    from src.agent_memory import AgentMemory
except ImportError as e:
    print("错误：无法从 'llm_memory_v6_class' 导入 AgentMemory。")
    print(f"请确保 'llm_memory_v6_class' 文件夹与 'ai-search' 文件夹在同一目录下。")
    print(f"当前搜索路径: {agent_memory_lib_path}")
    raise e

# to_ark_format 函数需要您在项目中提供
# from utils.utils import to_ark_format

# --- 1. 为用户记忆服务定义 System Prompt 和工具 (这部分保持不变) ---
USER_MEMORY_SYSTEM_PROMPT = """..."""  # 保持不变
user_memory_tools_declaration_list_gemini = [
    {
        "name": "retrieve_user_memory",
        "description": "当需要回忆与用户相关的过去对话、用户偏好、核心档案或任何长期记忆时，调用此函数。这是在回答前获取个性化上下文的核心工具。",
        "parameters": {
            "type": "object",
            "properties": {
                "query_text": {
                    "type": "string",
                    "description": "从用户当前的问题或对话中提炼出的、用于检索相关记忆的核心关键词或主题。"
                },
                # [新增] 必须告诉LLM传递session_id
                "session_id": {
                    "type": "string",
                    "description": "当前对话的唯一会话ID。"
                }
            },
            "required": ["query_text", "session_id"]
        }
    }
]


# user_memory_tools_declaration_list = to_ark_format(user_memory_tools_declaration_list_gemini)


class UserMemoryService:
    """
    一个门面类，现在作为会话管理器，为每个session_id管理一个AgentMemory实例。
    它负责加载和管理 AgentMemory 实例，并暴露一个简单的接口供 master_service 调用。
    """

    def __init__(self):
        print("--- [UserMemoryService] 正在初始化... ---")
        # [核心修改] 使用一个字典来存储每个session_id对应的AgentMemory实例
        self.sessions: dict[str, AgentMemory] = {}
        # [核心修改] 需要一个锁来保证多线程环境下对sessions字典操作的安全性
        self.lock = threading.Lock()
        print("--- [UserMemoryService] 初始化完成。 ---")

    def _get_or_create_session(self, session_id: str) -> AgentMemory:
        """
        一个内部辅助方法，用于获取或创建指定session_id的AgentMemory实例。
        这是实现多会话管理的核心。
        """
        with self.lock:
            if session_id not in self.sessions:
                print(f"--- [UserMemoryService] 未找到会话 {session_id}，正在创建新的AgentMemory实例... ---")
                # 为这个新会话创建一个全新的AgentMemory实例
                self.sessions[session_id] = AgentMemory(session_id=session_id, device='auto')
                print(f"--- [UserMemoryService] 会话 {session_id} 的AgentMemory实例创建成功。 ---")
            return self.sessions[session_id]

    def retrieve_user_memory(self, session_id: str, query_text: str) -> str:
        """
        【工具实现】调用指定会话的记忆系统的检索功能。
        """
        print(f"[UserMemoryService] 正在为会话 {session_id} 检索记忆，查询: '{query_text}'")
        memory_system = self._get_or_create_session(session_id)
        result = memory_system.retrieve_context_for_rag(query_text)

        # 返回对LLM友好的字符串
        return result.get("context", "记忆检索失败。")

    def add_conversation_turn(self, session_id: str, user_message: str, agent_response: str):
        """
        【核心方法】调用指定会话的记忆系统的添加对话功能。
        """
        print(f"[UserMemoryService] 正在为会话 {session_id} 添加一轮对话。")
        memory_system = self._get_or_create_session(session_id)
        memory_system.add_conversation_turn(user_message, agent_response)

    def shutdown(self):
        """
        【核心方法】安全关闭所有会话的底层记忆系统。
        """
        print("--- [UserMemoryService] 正在关闭所有会话的AgentMemory实例... ---")
        with self.lock:
            for session_id, memory_system in self.sessions.items():
                print(f"--- 正在关闭会话 {session_id}... ---")
                memory_system.shutdown()
            self.sessions.clear()
        print("--- [UserMemoryService] 所有会话已安全关闭。 ---")


# --- 创建一个全局单例，供其他模块导入和使用 ---
# 整个ai-search工程将共享这一个服务实例
user_memory_service = UserMemoryService()