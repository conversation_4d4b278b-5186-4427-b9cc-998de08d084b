import json
import requests
import sys
from unittest.mock import Mock
sys.path.append("../")
import configs
from utils.utils import to_ark_format

# 搜索功能的配置
SEARCH_PROMPT_TEMPLATE = """
## 情境感知 (Situational Awareness)
- 当前日期与时间: $current_time

你是一个高度智能的AI助手，配备了一个名为'search_and_summarize'的实时网络搜索工具。你的首要目标是通过有效利用此工具，为用户提供准确、全面且有帮助的回答。

你的核心职责是首先分析用户的意图，以决定**是否**需要进行搜索，然后才决定**如何**构建出最佳的查询。

## 核心指令：先分析、再决策、后执行

对于用户的每一个请求，你都必须遵循以下推理序列。

### 第一步：意图分析 —— 是否需要搜索？
- **SEARCH if:** The query is about current events, specific facts, or requires up-to-date information.
- **DO NOT SEARCH if:** The query is a creative task, general conversation, or a timeless, universal fact.

### 第二步：查询制定 —— 拆解与优化
- **Deconstruction:** Break down complex questions into core entities and intent keywords.
- **Multi-Step Recovery:** If a discovery search fails, you are mandated to initiate a second, speculative search based on your internal knowledge before reporting failure.

### 第三步：时间敏感性的策略性运用 (`freshness` 参数)
Analyze the query's *implicit* temporal nature on a spectrum (High, Moderate, Low/None) to autonomously choose the best `freshness` setting ('oneDay', 'oneMonth', 'noLimit', etc.). When in doubt, default to 'noLimit'.

### 第四步：批判性结果评估（安全网）
After a search, you must act as a filter. If results are irrelevant, identify the mismatch and report that no *relevant* information was found. Do not summarize irrelevant data.
"""


search_tool_declaration_list_gemini = [
    {
        "name": "search_and_summarize",
        "description": "执行实时网络搜索，用于查找时事、事实或在详细搜索前识别关键实体。",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "从用户问题中转换而来的一组搜索关键词和实体。例如：'Nvidia latest GPU' 或 'Nvidia B200 performance vs H100'"
                },
                "count": {
                    "type": "integer",
                    "description": "要检索的结果数量，默认为20。"
                },
                "freshness": {
                    "type": "string",
                    "description": "按特定时间段过滤搜索结果。例如'oneDay', 'oneWeek', 'noLimit', 或 'YYYY-MM-DD..YYYY-MM-DD'。",
                    "enum": ["noLimit", "oneDay", "oneWeek", "oneMonth", "oneYear"]
                }
            },
            "required": ["query"]
        }
    }
]

search_tool_declaration_list = to_ark_format(search_tool_declaration_list_gemini)

class SearchService:
    """
    一个用于调用博查AI Web Search API并解析结果的客户端。
    """
    def __init__(self, api_key: str):
        if not api_key:
            raise ValueError("API Key不能为空。")
        self.api_key = api_key
        self.search_url = "https://api.bochaai.com/v1/web-search"
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json', 'Accept': 'application/json'
        })

    # --- 已修改的函数 ---
    def search_and_summarize(self, query: str, count: int = 20, freshness: str = "noLimit") -> str:
        """
        【推荐】执行博查搜索，并直接返回格式化、对LLM友好的摘要字符串。
        
        :param query: 用户的搜索查询。
        :param count: 希望返回的结果数量 (1-50)。
        :param freshness: 时间范围过滤器。默认为 "noLimit"。
        """
        # print(f"  [BochaSearchClient] Sending query: '{query}' with freshness: '{freshness}'")
        
        # 使用传入的freshness参数
        payload = {"query": query, "summary": True, "count": count, "freshness": freshness}
        
        try:
            response = self.session.post(self.search_url, data=json.dumps(payload), timeout=20)
            response.raise_for_status()
            response_json = response.json()
        except requests.exceptions.HTTPError as e:
            response_json = e.response.json() if e.response else {"code": 503, "msg": f"HTTP Error: {str(e)}"}
        except requests.exceptions.RequestException as e:
            response_json = {"code": 504, "msg": f"Network Error: {str(e)}"}
            
        # ... (后续的解析逻辑保持不变)
        api_code = response_json.get("code")
        if api_code != 200:
            msg = response_json.get("msg") or response_json.get("message", "未知错误")
            return f"搜索失败：API返回错误码 {api_code} - {msg}"
        try: web_pages = response_json["data"]["webPages"]["value"]
        except (KeyError, TypeError): return "搜索成功，但响应中没有找到有效的网页结果。"
        if not web_pages: return "根据您的查询，未能找到相关的网页结果。"
        output_parts = [f"搜索完成，为您找到 {len(web_pages)} 条相关结果："]
        for i, result in enumerate(web_pages, 1):
            summary_text = result.get('summary') or result.get('snippet', '无可用摘要。')
            summary_clean = ' '.join(summary_text.split()) if summary_text else '无可用摘要。'
            item_str = (f"\n---\n[{i}] 标题: {result.get('name', '无标题')}\n    来源: {result.get('siteName', '未知来源')}\n    链接: {result.get('url', '#')}\n    发布时间: {result.get('dateLastCrawled', '未知时间')}\n    摘要: {summary_clean}")
            output_parts.append(item_str)
        return "\n".join(output_parts)

# 创建一个全局实例，方便主程序调用 
try:
    search_service = SearchService(api_key=configs.BOCHA_API_KEY)
except ValueError:
    # 如果没有API密钥，创建一个模拟的服务
    search_service = Mock()
    search_service.search_and_summarize.return_value = "这是模拟的搜索结果摘要"
