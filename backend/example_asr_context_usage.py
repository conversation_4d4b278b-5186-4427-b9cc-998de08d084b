#!/usr/bin/env python3
"""
ASR上下文功能使用示例
展示如何在实际项目中使用上下文感知的ASR服务
"""

import os
import sys
import logging
import numpy as np
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.enhanced_asr_service import EnhancedASRService
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_sample_audio(text: str, sample_rate: int = 16000) -> np.ndarray:
    """生成示例音频数据（实际使用中这里是真实的音频数据）"""
    duration = max(1.0, len(text) * 0.1)  # 根据文本长度估算音频长度
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 生成复合频率信号模拟语音
    frequencies = [200, 400, 800]
    audio = np.zeros_like(t)
    for freq in frequencies:
        audio += 0.1 * np.sin(2 * np.pi * freq * t)
    
    return audio.astype(np.float32)

def example_basic_usage():
    """示例1: 基本使用方法"""
    logger.info("📖 示例1: 基本使用方法")
    
    # 初始化增强ASR服务
    asr_service = EnhancedASRService(
        qwen_timeout=10,
        gemini_timeout=15,
        gemini_first=True,
        enable_context=True  # 启用上下文功能
    )
    
    # 模拟用户会话
    session_id = "user_001_conversation"
    
    # 第一轮对话
    logger.info("🎤 第一轮: 用户说 '你好'")
    audio1 = generate_sample_audio("你好")
    result1 = asr_service.transcribe(
        audio_data=audio1,
        sample_rate=16000,
        session_id=session_id
    )
    logger.info(f"📝 ASR结果: {result1.get('text', 'N/A')}")
    logger.info(f"🧠 使用上下文: {result1.get('context_used', False)}")
    
    # 第二轮对话
    logger.info("🎤 第二轮: 用户说 '我想订餐'")
    audio2 = generate_sample_audio("我想订餐")
    result2 = asr_service.transcribe(
        audio_data=audio2,
        sample_rate=16000,
        session_id=session_id
    )
    logger.info(f"📝 ASR结果: {result2.get('text', 'N/A')}")
    logger.info(f"🧠 使用上下文: {result2.get('context_used', False)}")
    
    # 第三轮对话
    logger.info("🎤 第三轮: 用户说 '要一份意大利面'")
    audio3 = generate_sample_audio("要一份意大利面")
    result3 = asr_service.transcribe(
        audio_data=audio3,
        sample_rate=16000,
        session_id=session_id
    )
    logger.info(f"📝 ASR结果: {result3.get('text', 'N/A')}")
    logger.info(f"🧠 使用上下文: {result3.get('context_used', False)}")
    
    # 查看上下文统计
    stats = asr_service.get_context_stats()
    logger.info(f"📊 上下文统计: 总会话数={stats['total_sessions']}")
    
    return asr_service

def example_multiple_sessions(asr_service):
    """示例2: 多会话管理"""
    logger.info("\n📖 示例2: 多会话管理")
    
    # 创建多个独立会话
    sessions = {
        "customer_A": ["我要点餐", "要一个汉堡", "加大薯条"],
        "customer_B": ["查询订单", "我的订单号是123", "什么时候送达"],
        "customer_C": ["投诉服务", "食物有问题", "要求退款"]
    }
    
    for session_id, messages in sessions.items():
        logger.info(f"👤 处理会话: {session_id}")
        
        for i, message in enumerate(messages, 1):
            logger.info(f"  🎤 第{i}轮: {message}")
            audio = generate_sample_audio(message)
            result = asr_service.transcribe(
                audio_data=audio,
                sample_rate=16000,
                session_id=session_id
            )
            logger.info(f"  📝 ASR结果: {result.get('text', 'N/A')}")
            logger.info(f"  🧠 使用上下文: {result.get('context_used', False)}")
    
    # 查看所有会话统计
    stats = asr_service.get_context_stats()
    logger.info(f"📊 最终统计: 总会话数={stats['total_sessions']}")
    for session_id, session_stats in stats['sessions'].items():
        logger.info(f"  📋 {session_id}: 历史长度={session_stats['history_length']}")

def example_context_management(asr_service):
    """示例3: 上下文管理"""
    logger.info("\n📖 示例3: 上下文管理")
    
    session_id = "context_demo"
    
    # 添加多轮对话
    messages = [
        "我想买手机",
        "预算三千左右", 
        "要拍照好的",
        "有什么推荐",
        "这个多少钱",
        "可以分期吗",
        "怎么付款",
        "什么时候到货"
    ]
    
    for i, message in enumerate(messages, 1):
        logger.info(f"🎤 第{i}轮: {message}")
        audio = generate_sample_audio(message)
        result = asr_service.transcribe(
            audio_data=audio,
            sample_rate=16000,
            session_id=session_id
        )
        
        # 获取当前会话的上下文信息
        context = asr_service.conversation_contexts.get(session_id)
        if context:
            logger.info(f"📝 ASR结果: {result.get('text', 'N/A')}")
            logger.info(f"🧠 使用上下文: {result.get('context_used', False)}")
            logger.info(f"📊 历史长度: {len(context.conversation_history)}")
            
            # 显示上下文提示（前几轮）
            if i <= 3:
                context_prompt = context.get_context_prompt()
                if context_prompt:
                    logger.info(f"💭 上下文提示: {context_prompt[:100]}...")

def example_context_disabled():
    """示例4: 禁用上下文功能"""
    logger.info("\n📖 示例4: 禁用上下文功能")
    
    # 创建禁用上下文的ASR服务
    asr_no_context = EnhancedASRService(
        qwen_timeout=10,
        gemini_timeout=15,
        gemini_first=True,
        enable_context=False  # 禁用上下文功能
    )
    
    session_id = "no_context_demo"
    messages = ["你好", "我想点餐", "要一份披萨"]
    
    for i, message in enumerate(messages, 1):
        logger.info(f"🎤 第{i}轮: {message}")
        audio = generate_sample_audio(message)
        result = asr_no_context.transcribe(
            audio_data=audio,
            sample_rate=16000,
            session_id=session_id
        )
        logger.info(f"📝 ASR结果: {result.get('text', 'N/A')}")
        logger.info(f"🧠 使用上下文: {result.get('context_used', False)}")
    
    # 验证没有创建上下文
    stats = asr_no_context.get_context_stats()
    logger.info(f"📊 统计: 总会话数={stats['total_sessions']} (应该为0)")

def example_error_handling():
    """示例5: 错误处理"""
    logger.info("\n📖 示例5: 错误处理")
    
    asr_service = EnhancedASRService(
        qwen_timeout=1,  # 很短的超时时间，可能导致失败
        gemini_timeout=2,
        gemini_first=True,
        enable_context=True
    )
    
    session_id = "error_demo"
    
    # 尝试转录（可能失败）
    logger.info("🎤 尝试转录音频...")
    audio = generate_sample_audio("测试音频")
    result = asr_service.transcribe(
        audio_data=audio,
        sample_rate=16000,
        session_id=session_id
    )
    
    if result.get('success'):
        logger.info(f"✅ 转录成功: {result.get('text', 'N/A')}")
    else:
        logger.warning(f"⚠️ 转录失败: {result.get('error', 'Unknown error')}")
        logger.info("💡 在实际应用中，可以实现重试机制或降级处理")

def main():
    """主函数"""
    logger.info("🎯 ASR上下文功能使用示例")
    logger.info("=" * 60)
    
    # 检查环境变量
    if not os.getenv("GEMINI_API_KEY"):
        logger.warning("⚠️ GEMINI_API_KEY未设置，ASR可能无法正常工作")
        logger.info("💡 这些示例主要展示API使用方法，不依赖真实的ASR结果")
    
    try:
        # 运行各种使用示例
        asr_service = example_basic_usage()
        example_multiple_sessions(asr_service)
        example_context_management(asr_service)
        example_context_disabled()
        example_error_handling()
        
        logger.info("\n🎉 所有示例运行完成！")
        logger.info("💡 在实际项目中，请根据具体需求调整配置和错误处理逻辑")
        
    except Exception as e:
        logger.error(f"❌ 示例运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
