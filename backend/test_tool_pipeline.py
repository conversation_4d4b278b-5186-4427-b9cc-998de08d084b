#!/usr/bin/env python3
"""
工具调用流程测试脚本
测试从工具枚举到LLM集成的完整流程
"""
import asyncio
import sys
import os
import json
import logging
from datetime import datetime

# 添加backend目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from services.tool_manager_service import tool_manager_service
from services.enhanced_llm_service import get_enhanced_llm_service
from utils.reranker_service import reranker_service
from services.mcp_service import mcp_service

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ToolPipelineTester:
    """工具调用流程测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.tool_manager = tool_manager_service
        self.enhanced_llm = get_enhanced_llm_service("test_key", "test_endpoint")
        self.test_results = {}
        
        logger.info("工具调用流程测试器初始化完成")
    
    def test_tool_enumeration(self):
        """测试工具枚举功能"""
        logger.info("🔍 测试工具枚举功能...")
        
        try:
            # 测试获取所有工具
            all_tools = self.tool_manager.enumerate_all_tools(use_cache=False)
            
            self.test_results["tool_enumeration"] = {
                "success": True,
                "total_tools": len(all_tools),
                "tools": [tool.get('name') for tool in all_tools[:5]]  # 只显示前5个
            }
            
            logger.info(f"✅ 工具枚举成功，共找到 {len(all_tools)} 个工具")
            
            # 显示工具详情
            for i, tool in enumerate(all_tools[:3]):
                logger.info(f"   {i+1}. {tool.get('name')} - {tool.get('description', '')[:50]}...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 工具枚举失败: {e}")
            self.test_results["tool_enumeration"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def test_tool_search_by_category(self):
        """测试按类别搜索工具"""
        logger.info("🔍 测试按类别搜索工具...")
        
        try:
            # 测试搜索不同类别的工具
            categories = ["search", "news", "memory", "travel"]
            category_results = {}
            
            for category in categories:
                tools = self.tool_manager.search_tools_by_category(category)
                category_results[category] = len(tools)
                logger.info(f"   类别 '{category}': {len(tools)} 个工具")
            
            self.test_results["tool_search_by_category"] = {
                "success": True,
                "category_results": category_results
            }
            
            logger.info("✅ 按类别搜索工具成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 按类别搜索工具失败: {e}")
            self.test_results["tool_search_by_category"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def test_tool_relevance_ranking(self):
        """测试工具相关性排序"""
        logger.info("🔍 测试工具相关性排序...")
        
        test_queries = [
            "我想了解最新的科技新闻",
            "帮我搜索关于AI的信息",
            "查询火车时刻表",
            "回忆一下我们之前的对话"
        ]
        
        try:
            ranking_results = {}
            
            for query in test_queries:
                logger.info(f"   测试查询: {query}")
                
                ranked_tools = self.tool_manager.rank_tools_by_relevance(query, top_k=3)
                
                if ranked_tools:
                    top_tool = ranked_tools[0]
                    ranking_results[query] = {
                        "top_tool": top_tool.get('name'),
                        "relevance_score": top_tool.get('relevance_score', 0),
                        "total_ranked": len(ranked_tools)
                    }
                    logger.info(f"      最相关工具: {top_tool.get('name')} (得分: {top_tool.get('relevance_score', 0):.3f})")
                else:
                    ranking_results[query] = {
                        "top_tool": None,
                        "relevance_score": 0,
                        "total_ranked": 0
                    }
                    logger.info("      没有找到相关工具")
            
            self.test_results["tool_relevance_ranking"] = {
                "success": True,
                "ranking_results": ranking_results
            }
            
            logger.info("✅ 工具相关性排序测试成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 工具相关性排序测试失败: {e}")
            self.test_results["tool_relevance_ranking"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def test_tool_execution(self):
        """测试工具执行"""
        logger.info("🔍 测试工具执行...")
        
        test_cases = [
            {
                "tool_name": "search_and_summarize",
                "parameters": {"query": "人工智能最新发展", "max_results": 3}
            },
            {
                "tool_name": "fetch_news",
                "parameters": {"category": "technology", "limit": 2}
            },
            {
                "tool_name": "recall_current_activity",
                "parameters": {}
            }
        ]
        
        try:
            execution_results = {}
            
            for test_case in test_cases:
                tool_name = test_case["tool_name"]
                parameters = test_case["parameters"]
                
                logger.info(f"   执行工具: {tool_name}")
                
                # 执行工具
                result = mcp_service.execute_tool(tool_name, "builtin", **parameters)
                
                execution_results[tool_name] = {
                    "success": result.get("success", False),
                    "has_result": "result" in result,
                    "result_type": type(result.get("result", None)).__name__
                }
                
                if result.get("success"):
                    logger.info(f"      ✅ 执行成功")
                else:
                    logger.info(f"      ❌ 执行失败: {result.get('error', '未知错误')}")
            
            self.test_results["tool_execution"] = {
                "success": True,
                "execution_results": execution_results
            }
            
            logger.info("✅ 工具执行测试完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 工具执行测试失败: {e}")
            self.test_results["tool_execution"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    async def test_enhanced_llm_integration(self):
        """测试增强LLM集成"""
        logger.info("🔍 测试增强LLM集成...")
        
        test_conversations = [
            {
                "user_input": "帮我搜索最新的AI技术发展",
                "conversation_history": [],
                "system_prompt": "你是一个友善的AI助手，可以使用工具帮助用户。"
            },
            {
                "user_input": "你好，今天天气怎么样？",
                "conversation_history": [],
                "system_prompt": "你是一个友善的AI助手。"
            }
        ]
        
        try:
            llm_results = {}
            
            for i, conversation in enumerate(test_conversations):
                user_input = conversation["user_input"]
                logger.info(f"   测试对话 {i+1}: {user_input}")
                
                # 测试是否应该使用工具
                should_use_tools = self.enhanced_llm._should_use_tools(
                    user_input, 
                    conversation["conversation_history"]
                )
                
                logger.info(f"      应该使用工具: {should_use_tools}")
                
                # 如果应该使用工具，测试工具选择
                if should_use_tools:
                    relevant_tools = self.enhanced_llm._get_relevant_tools_for_llm(user_input)
                    logger.info(f"      选择的工具数量: {len(relevant_tools)}")
                    
                    if relevant_tools:
                        tool_names = [tool["function"]["name"] for tool in relevant_tools]
                        logger.info(f"      工具列表: {tool_names}")
                
                # 生成响应（使用模拟模式）
                try:
                    response = await self.enhanced_llm.generate_response_with_tools(
                        user_input,
                        conversation["conversation_history"],
                        conversation["system_prompt"]
                    )
                    
                    llm_results[f"conversation_{i+1}"] = {
                        "success": response.get("success", False),
                        "should_use_tools": should_use_tools,
                        "tools_used": response.get("tools_used", []),
                        "mock_mode": response.get("mock_mode", True),
                        "response_length": len(response.get("speak_content", {}).get("text", ""))
                    }
                    
                    logger.info(f"      ✅ 响应生成成功 (长度: {llm_results[f'conversation_{i+1}']['response_length']})")
                    
                except Exception as e:
                    logger.info(f"      ❌ 响应生成失败: {e}")
                    llm_results[f"conversation_{i+1}"] = {
                        "success": False,
                        "error": str(e)
                    }
            
            self.test_results["enhanced_llm_integration"] = {
                "success": True,
                "llm_results": llm_results
            }
            
            logger.info("✅ 增强LLM集成测试完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 增强LLM集成测试失败: {e}")
            self.test_results["enhanced_llm_integration"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def test_tool_statistics(self):
        """测试工具统计功能"""
        logger.info("🔍 测试工具统计功能...")
        
        try:
            stats = self.tool_manager.get_tool_statistics()
            
            logger.info(f"   总工具数: {stats.get('total_tools', 0)}")
            logger.info(f"   类别统计: {stats.get('categories', {})}")
            logger.info(f"   服务器统计: {stats.get('servers', {})}")
            logger.info(f"   缓存状态: {stats.get('cache_status', {}).get('is_cached', False)}")
            
            self.test_results["tool_statistics"] = {
                "success": True,
                "statistics": stats
            }
            
            logger.info("✅ 工具统计功能测试成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 工具统计功能测试失败: {e}")
            self.test_results["tool_statistics"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        logger.info("🔍 测试缓存功能...")
        
        try:
            # 清除缓存并重新加载
            logger.info("   刷新工具缓存...")
            refresh_success = self.tool_manager.refresh_tool_cache()
            
            if refresh_success:
                logger.info("   ✅ 缓存刷新成功")
            else:
                logger.info("   ❌ 缓存刷新失败")
            
            # 测试缓存命中
            logger.info("   测试缓存命中...")
            tools1 = self.tool_manager.enumerate_all_tools(use_cache=True)
            tools2 = self.tool_manager.enumerate_all_tools(use_cache=True)
            
            cache_hit = len(tools1) == len(tools2)
            
            self.test_results["cache_functionality"] = {
                "success": True,
                "refresh_success": refresh_success,
                "cache_hit": cache_hit,
                "cached_tools_count": len(tools1)
            }
            
            logger.info(f"   ✅ 缓存功能测试完成 (缓存命中: {cache_hit})")
            return True
            
        except Exception as e:
            logger.error(f"❌ 缓存功能测试失败: {e}")
            self.test_results["cache_functionality"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始运行工具调用流程测试...")
        
        test_methods = [
            ("工具枚举", self.test_tool_enumeration),
            ("按类别搜索", self.test_tool_search_by_category),
            ("相关性排序", self.test_tool_relevance_ranking),
            ("工具执行", self.test_tool_execution),
            ("增强LLM集成", self.test_enhanced_llm_integration),
            ("工具统计", self.test_tool_statistics),
            ("缓存功能", self.test_cache_functionality)
        ]
        
        passed_tests = 0
        total_tests = len(test_methods)
        
        for test_name, test_method in test_methods:
            logger.info(f"\n{'='*50}")
            logger.info(f"测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if asyncio.iscoroutinefunction(test_method):
                    success = await test_method()
                else:
                    success = test_method()
                
                if success:
                    passed_tests += 1
                    logger.info(f"✅ {test_name} 测试通过")
                else:
                    logger.info(f"❌ {test_name} 测试失败")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {e}")
        
        # 生成测试报告
        logger.info(f"\n{'='*50}")
        logger.info("测试总结")
        logger.info(f"{'='*50}")
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        # 保存测试结果
        self.save_test_results()
        
        return passed_tests == total_tests
    
    def save_test_results(self):
        """保存测试结果到文件"""
        try:
            results = {
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_tests": len(self.test_results),
                    "passed_tests": sum(1 for r in self.test_results.values() if r.get("success", False)),
                    "failed_tests": sum(1 for r in self.test_results.values() if not r.get("success", False))
                },
                "detailed_results": self.test_results
            }
            
            output_file = f"tool_pipeline_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📄 测试结果已保存到: {output_file}")
            
        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")

async def main():
    """主函数"""
    tester = ToolPipelineTester()
    
    try:
        success = await tester.run_all_tests()
        
        if success:
            logger.info("🎉 所有测试通过！工具调用流程运行正常。")
            return 0
        else:
            logger.error("❌ 部分测试失败，请检查日志。")
            return 1
            
    except Exception as e:
        logger.error(f"测试运行异常: {e}")
        return 1

if __name__ == "__main__":
    # 设置环境变量（如果需要）
    os.environ.setdefault("DASHSCOPE_API_KEY", "test_key")
    
    # 运行测试
    exit_code = asyncio.run(main())