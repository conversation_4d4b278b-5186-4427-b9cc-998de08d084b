# 工具调用集成指南

## 概述

本项目已成功集成了完整的工具调用功能，包括：

1. **工具枚举** - 从MCP配置中枚举所有可用工具
2. **相关性排序** - 使用DashScope重排序服务对工具进行相关性排序
3. **工具执行** - 执行选定的工具并返回结果
4. **LLM集成** - 将工具调用集成到LLM对话流程中
5. **API端点** - 提供完整的REST API接口

## 快速启动

### 1. 启动服务器

在一个终端中运行：

```bash
cd backend
python main.py
```

### 2. 测试API端点

在另一个终端中运行：

```bash
cd backend
python test_tool_api.py
```

## 核心组件

### 1. 工具管理服务 (`services/tool_manager_service.py`)

- **功能**: 工具枚举、缓存管理、相关性排序
- **主要方法**:
  - `enumerate_all_tools()` - 枚举所有工具
  - `rank_tools_by_relevance()` - 相关性排序
  - `get_tool_by_name()` - 获取特定工具
  - `get_tool_statistics()` - 获取统计信息

### 2. 增强LLM服务 (`services/enhanced_llm_service.py`)

- **功能**: 集成工具调用的LLM服务
- **主要方法**:
  - `generate_response_with_tools()` - 带工具调用的响应生成
  - `_should_use_tools()` - 判断是否需要使用工具
  - `_get_relevant_tools_for_llm()` - 获取相关工具用于LLM

### 3. 重排序服务 (`utils/reranker_service.py`)

- **功能**: 使用DashScope API进行工具相关性排序
- **主要方法**:
  - `rank_tools_by_relevance()` - 对工具进行重排序
  - `get_top_relevant_tool()` - 获取最相关的工具

### 4. API端点 (`api/tool_endpoints.py`)

- **功能**: 提供REST API接口
- **端点列表**:
  - `GET /api/tools/enumerate` - 枚举工具
  - `POST /api/tools/rank` - 工具排序
  - `POST /api/tools/execute` - 执行工具
  - `POST /api/tools/enhanced-chat` - 增强对话
  - `GET /api/tools/statistics` - 统计信息

## API使用示例

### 1. 枚举所有工具

```bash
curl http://localhost:8000/api/tools/enumerate
```

### 2. 工具相关性排序

```bash
curl -X POST http://localhost:8000/api/tools/rank \
  -H "Content-Type: application/json" \
  -d '{"query": "我想了解最新的科技新闻", "top_k": 3}'
```

### 3. 执行工具

```bash
curl -X POST http://localhost:8000/api/tools/execute \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "fetch_news",
    "parameters": {
      "category": "technology",
      "limit": 2
    }
  }'
```

### 4. 增强对话

```bash
curl -X POST http://localhost:8000/api/tools/enhanced-chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_input": "帮我搜索最新的AI技术发展",
    "use_tools": true
  }'
```

## 工具调用流程

1. **用户输入** → 判断是否需要使用工具
2. **工具枚举** → 从MCP配置获取所有可用工具
3. **相关性排序** → 使用重排序服务选择最相关的工具
4. **工具执行** → 执行选定的工具并获取结果
5. **LLM集成** → 将工具结果集成到LLM响应中
6. **返回结果** → 返回包含工具调用结果的完整响应

## 配置要求

### 环境变量

确保以下环境变量已设置：

```bash
# DashScope API密钥（用于重排序）
DASHSCOPE_API_KEY=your_dashscope_api_key

# Volcano Engine API（用于LLM）
VOLCANO_API_KEY=your_volcano_api_key
VOLCANO_ENDPOINT=your_volcano_endpoint

# 其他必要的API密钥...
```

### MCP配置

工具从MCP配置中自动加载，确保MCP服务正常配置。

## 测试

### 单元测试

```bash
# 工具管理服务测试
python -m pytest backend/tests/test_tool_manager_service.py -v

# 增强LLM服务测试
python -m pytest backend/tests/test_enhanced_llm_service.py -v

# 集成测试
python -m pytest backend/tests/test_tool_integration.py -v
```

### 完整流程测试

```bash
# 运行完整的工具调用流程测试
python backend/test_tool_pipeline.py

# 快速功能测试
python backend/start_tool_test.py
```

### API端点测试

```bash
# 测试所有API端点
python backend/test_tool_api.py
```

## 性能特性

- **缓存机制**: 工具列表缓存5分钟，提高响应速度
- **并行执行**: 支持多个工具的并行执行
- **错误处理**: 完善的错误处理和降级机制
- **重试机制**: 重排序服务支持指数退避重试

## 扩展性

### 添加新工具

1. 在MCP配置中添加新的工具服务器
2. 工具会自动被枚举和集成
3. 无需修改代码

### 自定义重排序

可以替换`reranker_service.py`中的重排序逻辑，支持：
- 向量检索
- 自定义评分算法
- 多种排序策略

### 工具执行扩展

可以在`mcp_service.py`中扩展工具执行逻辑，支持：
- 异步工具执行
- 工具链调用
- 结果后处理

## 故障排除

### 常见问题

1. **重排序服务不可用**
   - 检查DASHSCOPE_API_KEY是否正确设置
   - 系统会自动降级到原始工具列表

2. **工具执行失败**
   - 检查MCP服务器是否正常运行
   - 查看工具参数是否正确

3. **API端点404错误**
   - 确保服务器在backend目录下启动
   - 检查路由配置是否正确

### 日志调试

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 总结

工具调用功能已完全集成到主应用中，提供了：

- ✅ 完整的工具管理和枚举
- ✅ 智能的相关性排序
- ✅ 可靠的工具执行
- ✅ 无缝的LLM集成
- ✅ 丰富的API接口
- ✅ 全面的测试覆盖
- ✅ 良好的错误处理
- ✅ 高性能缓存机制

现在可以在新终端中启动服务器并测试所有功能！