{"timestamp": "2025-08-11T11:32:08.466469", "summary": {"total_tests": 6, "passed": 5, "skipped": 0, "failed": 1, "success_rate": "83.3%"}, "test_results": [{"test": "supabase_service_key_init", "status": "success", "message": "使用Service Key成功初始化Supabase客户端"}, {"test": "npcs_table_query", "status": "success", "message": "成功查询NPCs表，返回1条记录", "details": {"record_count": 1, "npcs": [{"id": "6c0be9ce-55f6-4bb5-8728-08c31a422f6b", "name": "周可心"}]}}, {"test": "users_table_query", "status": "success", "message": "成功查询Users表，返回2条记录", "details": {"record_count": 2, "users": [{"id": 14, "username": "testuser"}, {"id": 15, "username": "testuser2"}]}}, {"test": "conversation_sessions_query", "status": "success", "message": "成功查询会话表，返回43条记录", "details": {"record_count": 43}}, {"test": "conversation_messages_query", "status": "success", "message": "成功查询消息表，返回5条记录", "details": {"record_count": 5}}, {"test": "npc_context_retrieval", "status": "failed", "message": "检索失败: 'system_prompt'"}, {"test": "create_test_session", "status": "success", "message": "成功创建和清理测试会话: 46", "details": {"session_id": 46, "user_id": 14, "npc_id": "6c0be9ce-55f6-4bb5-8728-08c31a422f6b"}}]}