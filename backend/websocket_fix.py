#!/usr/bin/env python3
"""
WebSocket端点修复版本
"""

# WebSocket endpoint for real-time communication - FIXED VERSION
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    
    try:
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                message_type = message.get("type")
                
                if message_type == "start_session":
                    # Start new conversation session
                    npc_id = message.get("npc_id", 1)
                    logger.info(f"Starting session for user {user_id} with NPC {npc_id}")
                    
                    session_id = await create_conversation_session(int(user_id), npc_id)
                    logger.info(f"Session creation result: {session_id}")
                    
                    if session_id:
                        manager.user_sessions[user_id]["session_id"] = session_id
                        manager.user_sessions[user_id]["npc_id"] = npc_id
                        
                        logger.info(f"Session started successfully: {session_id}")
                        await manager.send_message(user_id, {
                            "type": "session_started",
                            "session_id": session_id,
                            "npc_id": npc_id
                        })
                    else:
                        logger.error(f"Failed to create session for user {user_id}")
                        await manager.send_message(user_id, {
                            "type": "error",
                            "message": "Failed to start session"
                        })
                
                elif message_type == "audio_chunk":
                    # Process audio chunk
                    audio_data = base64.b64decode(message["data"])
                    await process_audio_chunk(user_id, audio_data)
                
                elif message_type == "interrupt":
                    # Handle user interruption
                    manager.user_sessions[user_id]["is_speaking"] = False
                    await manager.send_message(user_id, {
                        "type": "interrupted",
                        "message": "Assistant interrupted"
                    })
                
                elif message_type == "end_session":
                    # End conversation session
                    session_id = manager.user_sessions[user_id].get("session_id")
                    if session_id:
                        try:
                            supabase.table("conversation_sessions").update({
                                "ended_at": datetime.now().isoformat(),
                                "is_active": False
                            }).eq("id", session_id).execute()
                        except Exception as e:
                            logger.error(f"Error ending session: {e}")
                    
                    await manager.send_message(user_id, {
                        "type": "session_ended"
                    })
                    break
                    
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received from user {user_id}")
                await manager.send_message(user_id, {
                    "type": "error",
                    "message": "Invalid message format"
                })
            except Exception as e:
                logger.error(f"Error processing message from user {user_id}: {e}")
                break
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for user {user_id}")
        manager.disconnect(user_id)
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
        manager.disconnect(user_id)