#!/usr/bin/env python3
"""
测试main.py中的NPC相关函数
专门针对get_npc_by_id和create_conversation_session等函数
"""

import asyncio
import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加backend目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NPCFunctionTester:
    """NPC函数测试器"""
    
    def __init__(self):
        self.test_results = []
        
    async def test_import_main_functions(self):
        """测试导入main.py中的函数"""
        logger.info("📦 测试导入main.py中的函数...")
        
        try:
            # 导入main.py中的函数
            from main import get_npc_by_id, create_conversation_session, save_message, supabase
            
            self.get_npc_by_id = get_npc_by_id
            self.create_conversation_session = create_conversation_session
            self.save_message = save_message
            self.supabase = supabase
            
            logger.info("✅ 成功导入main.py中的函数")
            self.test_results.append({
                "test": "import_main_functions",
                "status": "success",
                "message": "成功导入main.py中的函数"
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ 导入main.py函数失败: {e}")
            self.test_results.append({
                "test": "import_main_functions",
                "status": "failed",
                "message": f"导入失败: {str(e)}"
            })
            return False
    
    async def test_supabase_connection(self):
        """测试Supabase连接状态"""
        logger.info("🔌 测试Supabase连接状态...")
        
        try:
            if self.supabase is None:
                logger.warning("⚠️ Supabase客户端为None，可能是环境变量未设置")
                self.test_results.append({
                    "test": "supabase_connection",
                    "status": "warning",
                    "message": "Supabase客户端为None，将使用内存模式"
                })
                return False
            
            # 测试简单查询
            result = self.supabase.table("npcs").select("count", count="exact").limit(1).execute()
            logger.info(f"✅ Supabase连接正常，NPC表记录数: {result.count}")
            
            self.test_results.append({
                "test": "supabase_connection",
                "status": "success",
                "message": f"Supabase连接正常，NPC表记录数: {result.count}"
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ Supabase连接测试失败: {e}")
            self.test_results.append({
                "test": "supabase_connection",
                "status": "failed",
                "message": f"连接失败: {str(e)}"
            })
            return False
    
    async def test_get_npc_by_id_function(self):
        """测试get_npc_by_id函数"""
        logger.info("🎭 测试get_npc_by_id函数...")
        
        try:
            # 测试获取NPC ID 1
            npc = await self.get_npc_by_id(1)
            
            if npc:
                logger.info(f"✅ 成功获取NPC: {npc.get('name', 'Unknown')}")
                self.test_results.append({
                    "test": "get_npc_by_id",
                    "status": "success",
                    "message": f"成功获取NPC: {npc.get('name', 'Unknown')}",
                    "details": {
                        "npc_id": npc.get("id"),
                        "npc_name": npc.get("name"),
                        "has_system_prompt": bool(npc.get("system_prompt")),
                        "is_active": npc.get("is_active")
                    }
                })
                return True
            else:
                logger.warning("⚠️ 未找到NPC ID 1，可能数据库为空")
                self.test_results.append({
                    "test": "get_npc_by_id",
                    "status": "warning",
                    "message": "未找到NPC ID 1，数据库可能为空"
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ get_npc_by_id函数测试失败: {e}")
            self.test_results.append({
                "test": "get_npc_by_id",
                "status": "failed",
                "message": f"函数调用失败: {str(e)}"
            })
            return False
    
    async def test_create_conversation_session_function(self):
        """测试create_conversation_session函数"""
        logger.info("💬 测试create_conversation_session函数...")
        
        try:
            # 测试创建会话
            session_id = await self.create_conversation_session(1, 1)  # user_id=1, npc_id=1
            
            if session_id:
                logger.info(f"✅ 成功创建会话: {session_id}")
                self.test_results.append({
                    "test": "create_conversation_session",
                    "status": "success",
                    "message": f"成功创建会话: {session_id}",
                    "details": {
                        "session_id": session_id,
                        "user_id": 1,
                        "npc_id": 1
                    }
                })
                return True
            else:
                logger.error("❌ 会话创建失败，返回None")
                self.test_results.append({
                    "test": "create_conversation_session",
                    "status": "failed",
                    "message": "会话创建失败，返回None"
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ create_conversation_session函数测试失败: {e}")
            self.test_results.append({
                "test": "create_conversation_session",
                "status": "failed",
                "message": f"函数调用失败: {str(e)}"
            })
            return False
    
    async def test_save_message_function(self):
        """测试save_message函数"""
        logger.info("📝 测试save_message函数...")
        
        try:
            # 首先创建一个会话
            session_id = await self.create_conversation_session(1, 1)
            
            if not session_id:
                logger.error("❌ 无法创建会话，跳过消息保存测试")
                self.test_results.append({
                    "test": "save_message",
                    "status": "skipped",
                    "message": "无法创建会话，跳过测试"
                })
                return False
            
            # 测试保存消息
            await self.save_message(
                session_id=session_id,
                role="user",
                content="这是一条测试消息",
                emotion="neutral",
                speed=1.0
            )
            
            logger.info("✅ 消息保存函数调用成功")
            self.test_results.append({
                "test": "save_message",
                "status": "success",
                "message": "消息保存函数调用成功",
                "details": {
                    "session_id": session_id,
                    "role": "user",
                    "content": "这是一条测试消息"
                }
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ save_message函数测试失败: {e}")
            self.test_results.append({
                "test": "save_message",
                "status": "failed",
                "message": f"函数调用失败: {str(e)}"
            })
            return False
    
    async def test_memory_fallback(self):
        """测试内存回退机制"""
        logger.info("🧠 测试内存回退机制...")
        
        try:
            # 导入内存存储变量
            from main import MEMORY_SESSIONS, MEMORY_MESSAGES
            
            logger.info(f"内存会话数: {len(MEMORY_SESSIONS)}")
            logger.info(f"内存消息组数: {len(MEMORY_MESSAGES)}")
            
            # 如果有内存数据，说明回退机制工作
            if MEMORY_SESSIONS or MEMORY_MESSAGES:
                logger.info("✅ 内存回退机制正在工作")
                self.test_results.append({
                    "test": "memory_fallback",
                    "status": "success",
                    "message": "内存回退机制正在工作",
                    "details": {
                        "memory_sessions": len(MEMORY_SESSIONS),
                        "memory_message_groups": len(MEMORY_MESSAGES)
                    }
                })
                return True
            else:
                logger.info("ℹ️ 当前没有内存数据，可能数据库正常工作")
                self.test_results.append({
                    "test": "memory_fallback",
                    "status": "info",
                    "message": "当前没有内存数据，数据库可能正常工作"
                })
                return True
                
        except Exception as e:
            logger.error(f"❌ 内存回退测试失败: {e}")
            self.test_results.append({
                "test": "memory_fallback",
                "status": "failed",
                "message": f"测试失败: {str(e)}"
            })
            return False
    
    async def test_npc_endpoint(self):
        """测试NPC端点"""
        logger.info("🌐 测试NPC端点...")
        
        try:
            # 导入FastAPI应用
            from main import app
            from fastapi.testclient import TestClient
            
            client = TestClient(app)
            
            # 测试获取NPCs端点
            response = client.get("/npcs")
            
            if response.status_code == 200:
                data = response.json()
                npcs = data.get("npcs", [])
                source = data.get("source", "unknown")
                
                logger.info(f"✅ NPCs端点正常，返回{len(npcs)}个NPC，数据源: {source}")
                self.test_results.append({
                    "test": "npc_endpoint",
                    "status": "success",
                    "message": f"NPCs端点正常，返回{len(npcs)}个NPC",
                    "details": {
                        "npc_count": len(npcs),
                        "data_source": source,
                        "npcs": [{"id": npc.get("id"), "name": npc.get("name")} for npc in npcs[:3]]  # 只显示前3个
                    }
                })
                return True
            else:
                logger.error(f"❌ NPCs端点返回错误状态码: {response.status_code}")
                self.test_results.append({
                    "test": "npc_endpoint",
                    "status": "failed",
                    "message": f"端点返回错误状态码: {response.status_code}"
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ NPC端点测试失败: {e}")
            self.test_results.append({
                "test": "npc_endpoint",
                "status": "failed",
                "message": f"端点测试失败: {str(e)}"
            })
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始NPC函数测试...")
        
        tests = [
            ("导入main函数", self.test_import_main_functions),
            ("Supabase连接", self.test_supabase_connection),
            ("获取NPC函数", self.test_get_npc_by_id_function),
            ("创建会话函数", self.test_create_conversation_session_function),
            ("保存消息函数", self.test_save_message_function),
            ("内存回退机制", self.test_memory_fallback),
            ("NPC端点", self.test_npc_endpoint)
        ]
        
        passed = 0
        failed = 0
        warnings = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 执行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                if result:
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    # 检查是否是警告状态
                    last_result = self.test_results[-1] if self.test_results else {}
                    if last_result.get("status") == "warning":
                        warnings += 1
                        logger.warning(f"⚠️ {test_name} - 警告")
                    else:
                        failed += 1
                        logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                failed += 1
                logger.error(f"❌ {test_name} - 异常: {e}")
        
        # 生成测试报告
        logger.info(f"\n{'='*60}")
        logger.info("📊 测试总结")
        logger.info(f"{'='*60}")
        logger.info(f"总测试数: {len(tests)}")
        logger.info(f"通过: {passed}")
        logger.info(f"警告: {warnings}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {(passed/(len(tests))*100):.1f}%")
        
        # 保存测试结果
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": len(tests),
                "passed": passed,
                "warnings": warnings,
                "failed": failed,
                "success_rate": f"{(passed/len(tests)*100):.1f}%"
            },
            "test_results": self.test_results
        }
        
        with open("npc_functions_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("📄 测试报告已保存到 npc_functions_test_report.json")
        
        return failed == 0

async def main():
    """主函数"""
    tester = NPCFunctionTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有测试通过！")
        return 0
    else:
        logger.error("💥 部分测试失败，请检查报告")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)