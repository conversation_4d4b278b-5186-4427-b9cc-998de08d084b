import asyncio
import os
import logging
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, Response
import uvicorn
from supabase import create_client, Client
import numpy as np
import librosa
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import base64
import wave
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel

from services.vad_service import VADService
from services.multimodal_asr_service import MultimodalASRService
from services.enhanced_asr_service import EnhancedASRService
from services.llm_service import LLMService
from services.tts_service import TTSService
from services.mcp_service import MCPService

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Real-time Voice Chat API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = datetime.now()
    logger.info(f"📥 {request.method} {request.url} - Headers: {dict(request.headers)}")

    response = await call_next(request)

    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"📤 {request.method} {request.url} - Status: {response.status_code} - Time: {process_time:.3f}s")

    return response

# Initialize Supabase client with error handling
try:
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    if supabase_url and supabase_key:
        supabase: Client = create_client(supabase_url, supabase_key)
        logger.info("✅ Supabase client initialized successfully")
    else:
        logger.warning("⚠️ Supabase credentials not found, using fallback mode")
        supabase = None
except Exception as e:
    logger.error(f"❌ Failed to initialize Supabase client: {e}")
    supabase = None

# Initialize services
vad_service = VADService(os.getenv("SILERO_VAD_MODEL_PATH", "./models/silero_vad.onnx"))
asr_service = MultimodalASRService(
    model="Qwen2-Audio-7B-Instruct",
    api_key="EMPTY",
    api_base="http://172.16.1.151:20257/v1"
    # os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
)
# 增强的ASR服务 (支持Qwen + Gemini备用，Gemini优先)
enhanced_asr_service = EnhancedASRService(
    qwen_timeout=5,      # Qwen超时5秒
    gemini_timeout=10,   # Gemini超时10秒
    gemini_first=True    # Gemini优先模式
)
llm_service = LLMService(os.getenv("VOLCANO_API_KEY"), os.getenv("VOLCANO_ENDPOINT"))
tts_service = TTSService(os.getenv("MINIMAX_API_KEY"), os.getenv("MINIMAX_GROUP_ID"))
mcp_service = MCPService()

# Simple in-memory user storage for testing
SIMPLE_USERS = {
    "test_user": {
        "id": 1,
        "username": "test_user",
        "password": "test_password",  # 简化版本，直接存储明文密码用于测试
        "email": "<EMAIL>",
        "nickname": "测试用户",
        "is_active": True
    },
    "admin": {
        "id": 2,
        "username": "admin",
        "password": "admin123",
        "email": "<EMAIL>",
        "nickname": "管理员",
        "is_active": True
    }
}

# Simple in-memory session and message storage for fallback
MEMORY_SESSIONS = {}
MEMORY_MESSAGES = {}

logger.info("Simple auth system initialized with test users")

# Pydantic models for TTS
class TTSRequest(BaseModel):
    text: str
    emotion: str = "neutral"
    speed: float = 1.0
    voice_id: str = None

# Audio processing helper functions
async def convert_audio_to_numpy(audio_data: bytes, content_type: str = None) -> np.ndarray:
    """
    Convert various audio formats to numpy array with proper resource management
    """
    import io
    import tempfile

    logger.info(f"🎵 转换音频格式: {content_type}, 大小: {len(audio_data)} bytes")

    # Validate input
    if not audio_data:
        raise ValueError("Audio data is empty")

    temp_file_path = None
    try:
        # Create temporary file with proper cleanup
        with tempfile.NamedTemporaryFile(delete=False, suffix='.audio') as temp_file:
            temp_file.write(audio_data)
            temp_file_path = temp_file.name

        # Use librosa to load audio (supports many formats)
        audio_array, sample_rate = librosa.load(temp_file_path, sr=16000, mono=True)
        logger.info(f"📊 音频转换成功: shape={audio_array.shape}, sr={sample_rate}, duration={len(audio_array)/sample_rate:.2f}s")

        # Validate audio data
        if len(audio_array) == 0:
            raise ValueError("Audio conversion resulted in empty array")

        # Ensure audio is in float32 format with proper range
        if audio_array.dtype != np.float32:
            audio_array = audio_array.astype(np.float32)

        # Ensure audio is in [-1, 1] range
        max_abs = np.max(np.abs(audio_array))
        if max_abs > 1.0:
            audio_array = audio_array / max_abs
        elif max_abs == 0:
            logger.warning("⚠️ Audio array contains only zeros")

        return audio_array

    except Exception as librosa_error:
        logger.error(f"❌ Librosa failed to load audio: {librosa_error}")
        # Add a specific check for a common dependency issue with librosa
        if "audioread" in str(librosa_error):
            logger.error("🐍 This might be caused by missing the 'ffmpeg' backend for audio decoding.")
            logger.error("🐍 Please ensure 'ffmpeg' is installed on your system (e.g., 'brew install ffmpeg' on macOS).")

        # Fallback: try to parse as WAV directly
        try:
            logger.info("🔄 Attempting to parse as WAV as a fallback...")
            return await _parse_wav_fallback(audio_data)
        except Exception as wav_error:
            logger.error(f"❌ WAV fallback also failed: {wav_error}")
            raise ValueError(f"Unable to convert audio. Librosa error: {librosa_error}, WAV error: {wav_error}")

    finally:
        # Ensure temporary file is always cleaned up
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                logger.debug(f"🗑️ Cleaned up temporary file: {temp_file_path}")
            except Exception as cleanup_error:
                logger.warning(f"⚠️ Failed to cleanup temporary file {temp_file_path}: {cleanup_error}")

async def _parse_wav_fallback(audio_data: bytes) -> np.ndarray:
    """
    Fallback WAV parser with proper error handling
    """
    import io

    audio_io = io.BytesIO(audio_data)
    with wave.open(audio_io, 'rb') as wav_file:
        frames = wav_file.readframes(-1)
        sample_rate = wav_file.getframerate()
        sample_width = wav_file.getsampwidth()

        if sample_width == 2:  # 16-bit
            audio_array = np.frombuffer(frames, dtype=np.int16).astype(np.float32) / 32768.0
        elif sample_width == 4:  # 32-bit
            audio_array = np.frombuffer(frames, dtype=np.int32).astype(np.float32) / 2147483648.0
        else:
            raise ValueError(f"Unsupported sample width: {sample_width}")

        # Resample to 16kHz if needed
        if sample_rate != 16000:
            audio_array = librosa.resample(audio_array, orig_sr=sample_rate, target_sr=16000)

        logger.info(f"📊 WAV解析成功: shape={audio_array.shape}, 原始sr={sample_rate}")
        return audio_array

def validate_audio_format(audio_data: bytes) -> bool:
    """Validate audio data format"""
    # 检查音频数据是否有效
    if not audio_data or len(audio_data) == 0:
        logger.warning("❌ Audio data is empty")
        return False
    
    # 检查数据长度是否合理 (至少100字节)
    if len(audio_data) < 100:
        logger.warning(f"⚠️ Audio data is too small: {len(audio_data)} bytes")
        return False
        
    return True

def save_audio_to_wav(audio_data: np.ndarray, filename: str):
    """Save audio data to WAV file for debugging"""
    try:
        # 确保目录存在
        os.makedirs("audio_recordings", exist_ok=True)
        filepath = os.path.join("audio_recordings", filename)
        
        # 转换为16位整数格式
        if audio_data.dtype != np.int16:
            audio_int16 = (audio_data * 32767).astype(np.int16)
        else:
            audio_int16 = audio_data
            
        # 保存为WAV文件
        with wave.open(filepath, 'w') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)   # 16位
            wav_file.setframerate(16000)  # 16kHz采样率
            wav_file.writeframes(audio_int16.tobytes())
            
        logger.info(f"💾 Audio saved to {filepath}")
    except Exception as e:
        logger.error(f"❌ Failed to save audio to {filename}: {e}")

# Global connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, Dict] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_sessions[user_id] = {
            "session_id": None,
            "npc_id": None,
            "conversation_history": [],
            "is_speaking": False,
            "audio_buffer": []
        }
        logger.info(f"User {user_id} connected")

    def disconnect(self, user_id: str):
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        logger.info(f"User {user_id} disconnected")

    async def send_message(self, user_id: str, message: dict):
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {user_id}: {e}")

manager = ConnectionManager()

# API Routes
@app.get("/")
async def root():
    return {"message": "Voice Chat API Server"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/process-audio")
async def process_audio_file(file: UploadFile = File(...), user_id: int = 1, npc_id: int = 1):
    """Process uploaded audio file - 完整流水线"""
    import time
    start_time = time.time()

    try:
        logger.info(f"📥 [TIMING] Received full audio processing request: {file.filename}")

        # Read audio file
        audio_data = await file.read()
        read_time = time.time()
        logger.info(f"⏱️ [TIMING] File read in {read_time - start_time:.2f}s. Size: {len(audio_data)} bytes")

        # Convert audio to numpy array with proper format handling
        try:
            audio_array = await convert_audio_to_numpy(audio_data, file.content_type)
            convert_time = time.time()
            logger.info(f"⏱️ [TIMING] Audio converted in {convert_time - read_time:.2f}s.")
        except Exception as audio_error:
            logger.error(f"❌ 音频格式转换失败: {audio_error}")
            return {"error": f"Audio format conversion failed: {str(audio_error)}"}

        # VAD processing (Temporarily disabled for debugging)
        # speech_segments = vad_service.detect_speech_segments(audio_array)
        # vad_time = time.time()
        # logger.info(f"⏱️ [TIMING] VAD processed in {vad_time - convert_time:.2f}s.")

        # if not speech_segments:
        #     return {"error": "No speech detected"}

        # # Extract speech audio
        # start_sample = int(speech_segments[0][0] * 16000)
        # end_sample = int(speech_segments[0][1] * 16000)
        # speech_audio = audio_array[start_sample:end_sample]

        # Since VAD is disabled, use the whole audio array
        speech_audio = audio_array
        vad_time = time.time()  # Reset timer for accurate ASR timing

        # 使用增强的ASR服务
        logger.info("🗣️ [TIMING] Calling enhanced_asr_service.transcribe()... (VAD is disabled)")
        transcription_result = enhanced_asr_service.transcribe(speech_audio)
        asr_time = time.time()
        logger.info(f"⏱️ [TIMING] ASR processed in {asr_time - vad_time:.2f}s.")
        logger.info(f"🔧 enhanced_asr_service返回: {transcription_result}")

        if not transcription_result.get("success"):
            logger.error(f"❌ enhanced_asr_service失败: {transcription_result}")
            return {"error": f"ASR failed: {transcription_result.get('error', 'Unknown error')}"}

        transcription_text = transcription_result["text"]
        logger.info(f"📝 ASR转录结果: '{transcription_text}'")

        # Get NPC and conversation history (with fallback)
        npc = await get_npc_by_id(npc_id)
        if not npc:
            # Use fallback NPC data
            fake_npc = {
                "id": npc_id,
                "name": "测试助手",
                "description": "用于测试的AI助手",
                "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。",
                "is_active": True
            }
            npc = fake_npc
            logger.info(f"使用假 NPC 数据进行音频处理，NPC ID: {npc_id}")

        get_npc_time = time.time()
        logger.info(f"⏱️ [TIMING] NPC data retrieved in {get_npc_time - asr_time:.2f}s.")

        # LLM processing - 使用ASR转录的文本
        logger.info("🤖 [TIMING] Calling llm_service.generate_response()...")
        response = await llm_service.generate_response(
            transcription_text,
            [],  # Empty history for this example
            npc["system_prompt"]
        )
        llm_time = time.time()
        logger.info(f"⏱️ [TIMING] LLM processed in {llm_time - get_npc_time:.2f}s.")

        if not response.get("success"):
            return {"error": "LLM processing failed"}

        # TTS processing
        logger.info("🔊 [TIMING] Calling tts_service.synthesize_speech()...")
        speak_content = response["speak_content"]
        tts_result = await tts_service.synthesize_speech(
            speak_content["text"],
            speak_content["emotion"],
            speak_content["speed"]
        )
        tts_time = time.time()
        logger.info(f"⏱️ [TIMING] TTS processed in {tts_time - llm_time:.2f}s.")

        if not tts_result["success"]:
            return {"error": "TTS processing failed"}

        total_time = time.time() - start_time
        logger.info(f"✅ [TIMING] Total processing time: {total_time:.2f}s.")

        # Return results
        return {
            "transcription": transcription_text,
            "asr_provider": transcription_result.get("provider", "unknown"),
            "asr_confidence": transcription_result.get("confidence", 0.0),
            "response_text": speak_content["text"],
            "emotion": speak_content["emotion"],
            "speed": speak_content["speed"],
            "audio_size": tts_result["size"]
        }

    except Exception as e:
        logger.error(f"Audio processing error: {e}")
        raise HTTPException(status_code=500, detail="Audio processing failed")

# WebSocket endpoint
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    logger.info(f"WebSocket connection established for user {user_id}")

    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)

            message_type = message.get("type")

            if message_type == "start_session":
                # Start new conversation session
                npc_id = message.get("npc_id", 1)
                logger.info(f"Starting session for user {user_id} with NPC {npc_id}")

                session_id = await create_conversation_session(int(user_id), npc_id)
                logger.info(f"Session creation result: {session_id}")

                if session_id:
                    manager.user_sessions[user_id]["session_id"] = session_id
                    manager.user_sessions[user_id]["npc_id"] = npc_id

                    logger.info(f"Session started successfully: {session_id}")
                    await manager.send_message(user_id, {
                        "type": "session_started",
                        "session_id": session_id,
                        "npc_id": npc_id
                    })
                else:
                    logger.error(f"Failed to create session for user {user_id}")
                    await manager.send_message(user_id, {
                        "type": "error",
                        "message": "Failed to start session"
                    })

            elif message_type == "audio_chunk":
                # Process audio chunk
                audio_data = base64.b64decode(message["data"])
                await process_audio_chunk(user_id, audio_data)

            elif message_type == "interrupt":
                # Handle user interruption
                manager.user_sessions[user_id]["is_speaking"] = False
                await manager.send_message(user_id, {
                    "type": "interrupted",
                    "message": "Assistant interrupted"
                })

            elif message_type == "end_session":
                # End conversation session
                session_id = manager.user_sessions[user_id].get("session_id")
                if session_id:
                    try:
                        supabase.table("conversation_sessions").update({
                            "ended_at": datetime.now().isoformat(),
                            "is_active": False
                        }).eq("id", session_id).execute()
                    except Exception as e:
                        logger.error(f"Error ending session: {e}")

                await manager.send_message(user_id, {
                    "type": "session_ended"
                })

    except WebSocketDisconnect:
        manager.disconnect(user_id)
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
        manager.disconnect(user_id)

async def process_audio_chunk(user_id: str, audio_data: bytes):
    """Process incoming audio chunk with enhanced debugging"""
    try:
        logger.info(f"🎤 [TRACE] Processing audio chunk for user {user_id}")
        logger.info(f"📊 Audio chunk size: {len(audio_data)} bytes")
        logger.info(f"📝 Audio chunk preview (first 20 bytes): {audio_data[:20]}")

        session = manager.user_sessions.get(user_id)
        if not session:
            logger.warning(f"⚠️ [TRACE] No session found for user {user_id}. Aborting.")
            return

        # 验证音频数据
        if not validate_audio_format(audio_data):
            logger.warning(f"❌ Invalid audio format for user {user_id}")
            return

        # 尝试多种音频格式解析
        audio_array = None
        parse_errors = []
        
        # 方法1: 尝试解析为int16格式
        try:
            if len(audio_data) % 2 == 0:
                audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
                logger.info(f"✅ Successfully parsed as int16, shape: {audio_array.shape}")
        except Exception as e:
            parse_errors.append(f"int16 parse failed: {e}")
            
        # 方法2: 如果方法1失败，尝试其他格式
        if audio_array is None:
            try:
                audio_uint8 = np.frombuffer(audio_data, dtype=np.uint8)
                audio_array = (audio_uint8.astype(np.float32) - 128.0) / 128.0
                logger.info(f"✅ Successfully parsed as uint8, shape: {audio_array.shape}")
            except Exception as e:
                parse_errors.append(f"uint8 parse failed: {e}")
                
        # 方法3: 尝试解析为float32格式
        if audio_array is None and len(audio_data) % 4 == 0:
            try:
                audio_array = np.frombuffer(audio_data, dtype=np.float32)
                logger.info(f"✅ Successfully parsed as float32, shape: {audio_array.shape}")
            except Exception as e:
                parse_errors.append(f"float32 parse failed: {e}")
                
        if audio_array is None:
            logger.error(f"❌ Failed to parse audio data for user {user_id}")
            for error in parse_errors:
                logger.error(f"   - {error}")
            return

        # Save raw audio chunk for debugging
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        chunk_filename = f"chunk_{user_id}_{timestamp}.wav"
        save_audio_to_wav(audio_array, chunk_filename)

        # Add to buffer
        session["audio_buffer"].extend(audio_array)
        logger.info(f"📊 [TRACE] Audio buffer size for user {user_id}: {len(session['audio_buffer'])} samples")

        # Skip VAD for now - directly process audio when buffer is large enough
        if len(session["audio_buffer"]) >= 16000:  # 1 second of audio
            buffer_array = np.array(session["audio_buffer"])

            logger.info(f"🚀 [TRACE] Buffer threshold reached for user {user_id}. Processing segment of size {len(buffer_array)}.")

            # Save complete speech segment for debugging
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            segment_filename = f"segment_{user_id}_{timestamp}.wav"
            save_audio_to_wav(buffer_array, segment_filename)

            # Process accumulated audio directly
            await process_speech_segment(user_id, buffer_array)
            session["audio_buffer"] = []
        elif len(session["audio_buffer"]) > 48000:  # 3 seconds max buffer
            logger.info(f"🔄 [TRACE] Buffer too large for user {user_id}, processing anyway.")
            buffer_array = np.array(session["audio_buffer"])

            # Save and process the buffer
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            segment_filename = f"segment_large_{user_id}_{timestamp}.wav"
            save_audio_to_wav(buffer_array, segment_filename)

            await process_speech_segment(user_id, buffer_array)
            session["audio_buffer"] = []

    except Exception as e:
        logger.error(f"Error processing audio chunk: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

async def process_speech_segment(user_id: str, audio_data: np.ndarray):
    """Process complete speech segment"""
    try:
        logger.info(f"🏁 [TRACE] Entered process_speech_segment for user {user_id}.")
        session = manager.user_sessions.get(user_id)
        if not session or session["is_speaking"]:
            logger.warning(f"⚠️ [TRACE] Skipping speech segment for user {user_id} because session is missing or assistant is speaking.")
            return

        session["is_speaking"] = True

        # ASR processing
        logger.info(f"🗣️ [TRACE] >>> Calling enhanced_asr_service.transcribe for user {user_id}. Audio length: {len(audio_data)} samples.")
        transcription_result = enhanced_asr_service.transcribe(audio_data)
        logger.info(f"✅ [TRACE] <<< Returned from enhanced_asr_service.transcribe for user {user_id}. Result: {transcription_result}")

        if not transcription_result.get("success"):
            logger.error(f"❌ Enhanced ASR transcription failed: {transcription_result.get('error')}")
            session["is_speaking"] = False
            return

        transcription = transcription_result

        if not transcription.get("text") or transcription.get("confidence", 1.0) < 0.5:
            logger.warning(f"⚠️ [PIPELINE] ASR text is empty or confidence is too low. Text: '{transcription.get('text')}', Confidence: {transcription.get('confidence', 0.0)}. Aborting.")
            session["is_speaking"] = False
            return

        # Send transcription to client
        await manager.send_message(user_id, {
            "type": "transcription",
            "text": transcription["text"],
            "confidence": transcription["confidence"]
        })

        # Get NPC prompt
        npc_id = session.get("npc_id", 1)
        logger.info(f"ℹ️ [PIPELINE] Fetching NPC data for npc_id: {npc_id}")
        npc = await get_npc_by_id(npc_id)

        if not npc:
            logger.warning(f"⚠️ [PIPELINE] Failed to get NPC data for npc_id: {npc_id}. Using fallback data to continue.")
            # Use fallback NPC data to allow the pipeline to continue
            npc = {
                "id": npc_id,
                "name": "Fallback Assistant",
                "description": "A fallback assistant for testing.",
                "system_prompt": "You are a helpful assistant. Please respond clearly and concisely.",
                "is_active": True
            }

        logger.info(f"✅ [PIPELINE] Successfully fetched NPC: {npc.get('name')}")

        # LLM processing with streaming
        logger.info(f"🤖 [PIPELINE] Preparing to call LLM. Transcription: '{transcription['text']}'")
        async for chunk in llm_service.generate_response_stream(
            transcription["text"],
            session["conversation_history"],
            npc["system_prompt"]
        ):
            if chunk["type"] == "speak":
                # TTS processing
                async for audio_chunk in tts_service.synthesize_speech_stream(
                    chunk["text"],
                    chunk["emotion"],
                    chunk["speed"]
                ):
                    if audio_chunk and session["is_speaking"]:
                        # Send audio chunk to client
                        audio_b64 = base64.b64encode(audio_chunk).decode()
                        await manager.send_message(user_id, {
                            "type": "audio_chunk",
                            "data": audio_b64
                        })

                # Save messages to database
                session_id = session.get("session_id")
                if session_id:
                    await save_message(session_id, "user", transcription["text"])
                    await save_message(session_id, "assistant", chunk["text"],
                                     emotion=chunk["emotion"], speed=chunk["speed"])

                # Update conversation history
                session["conversation_history"].extend([
                    {"role": "user", "content": transcription["text"]},
                    {"role": "assistant", "content": chunk["text"]}
                ])

                break

        session["is_speaking"] = False

        await manager.send_message(user_id, {
            "type": "response_complete"
        })

    except Exception as e:
        logger.error(f"Error processing speech segment: {e}")
        session = manager.user_sessions.get(user_id)
        if session:
            session["is_speaking"] = False

# Test endpoint for audio transmission
@app.post("/test-audio")
async def test_audio_endpoint(audio_data: bytes):
    """Test endpoint to verify audio data transmission"""
    logger.info(f"🧪 Test audio endpoint received: {len(audio_data)} bytes")
    logger.info(f"📝 Data preview: {audio_data[:20]}")
    
    # Try to parse the audio data
    try:
        if len(audio_data) % 2 == 0:
            audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            logger.info(f"✅ Successfully parsed as int16, shape: {audio_array.shape}")
        else:
            audio_uint8 = np.frombuffer(audio_data, dtype=np.uint8)
            audio_array = (audio_uint8.astype(np.float32) - 128.0) / 128.0
            logger.info(f"✅ Successfully parsed as uint8, shape: {audio_array.shape}")
            
        # Save for debugging
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        test_filename = f"test_audio_{timestamp}.wav"
        save_audio_to_wav(audio_array, test_filename)
        
        return {
            "status": "received", 
            "size": len(audio_data),
            "parsed_shape": audio_array.shape if 'audio_array' in locals() else "failed to parse"
        }
    except Exception as e:
        logger.error(f"❌ Failed to parse test audio: {e}")
        return {"status": "received", "size": len(audio_data), "error": str(e)}

# Helper functions (simplified for this example)
async def create_conversation_session(user_id: int, npc_id: int):
    """Create a conversation session"""
    # Simplified implementation
    return f"session_{user_id}_{npc_id}_{int(datetime.now().timestamp())}"

async def get_npc_by_id(npc_id: int):
    """Get NPC by ID"""
    # Simplified implementation
    return {
        "id": npc_id,
        "name": "Test NPC",
        "description": "A test NPC for voice chat",
        "system_prompt": "You are a helpful assistant. Please respond clearly and concisely.",
        "is_active": True
    }

async def save_message(session_id: str, role: str, content: str, **kwargs):
    """Save message to database"""
    # Simplified implementation
    logger.info(f"💾 Saving message: {role} - {content}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
