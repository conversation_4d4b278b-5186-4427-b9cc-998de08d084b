import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

# 初始化Supabase客户端
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_KEY")  # 使用服务密钥

print("SUPABASE_URL:", supabase_url)
print("SUPABASE_KEY:", supabase_key)

if not supabase_url or not supabase_key:
    print("环境变量未设置")
    sys.exit(1)

try:
    supabase = create_client(supabase_url, supabase_key)
    print("Supabase客户端创建成功")
except Exception as e:
    print(f"创建Supabase客户端失败: {e}")
    sys.exit(1)

# 执行测试查询
print("正在测试Supabase连接...")
try:
    # 测试连接
    response = supabase.table("users").select("count", count="exact").limit(1).execute()
    print("✓ Supabase连接成功")
    print(f"Users表记录数: {response.count}")
    
    # 测试其他表
    response = supabase.table("npcs").select("count", count="exact").limit(1).execute()
    print(f"NPCs表记录数: {response.count}")
    
    response = supabase.table("conversation_sessions").select("count", count="exact").limit(1).execute()
    print(f"Conversation sessions表记录数: {response.count}")
    
except Exception as e:
    print(f"✗ Supabase连接失败: {e}")
    import traceback
    traceback.print_exc()
