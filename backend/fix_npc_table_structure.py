#!/usr/bin/env python3
"""
修复NPC表结构
添加缺少的is_active列和其他必要字段
"""

import asyncio
import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加backend目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NPCTableFixer:
    """NPC表结构修复器"""
    
    def __init__(self):
        self.supabase = None
        self.results = []
        
    async def initialize_supabase(self):
        """初始化Supabase连接"""
        logger.info("🔑 初始化Supabase连接...")
        
        try:
            supabase_url = os.getenv("SUPABASE_URL")
            # 使用Service Key
            service_key = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiYW5vbiIsInJlZiI6InNicC13bGp2ajh6bWkxcXFvdmhoIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NTMxOTAzNjQsImV4cCI6MjA2ODc2NjM2NH0.A3O1gtaZRdTnpe1T7wHJzYSlw9UCqACVFFM35ju1J5Y"
            
            if not supabase_url:
                logger.error("❌ SUPABASE_URL未设置")
                return False
                
            self.supabase = create_client(supabase_url, service_key)
            logger.info("✅ Supabase客户端初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ Supabase初始化失败: {e}")
            return False
    
    async def check_npc_table_structure(self):
        """检查NPC表结构"""
        logger.info("🔍 检查NPC表结构...")
        
        try:
            # 获取一条NPC记录来检查字段
            result = self.supabase.table("npcs").select("*").limit(1).execute()
            
            if result.data:
                current_fields = list(result.data[0].keys())
                logger.info(f"📋 当前NPC表字段: {current_fields}")
                
                # 期望的字段
                expected_fields = [
                    "id", "name", "description", "system_prompt", 
                    "avatar_url", "is_active", "created_at", "updated_at"
                ]
                
                missing_fields = [field for field in expected_fields if field not in current_fields]
                extra_fields = [field for field in current_fields if field not in expected_fields]
                
                self.results.append({
                    "check": "table_structure",
                    "current_fields": current_fields,
                    "missing_fields": missing_fields,
                    "extra_fields": extra_fields,
                    "status": "checked"
                })
                
                if missing_fields:
                    logger.warning(f"⚠️ 缺少字段: {missing_fields}")
                if extra_fields:
                    logger.info(f"ℹ️ 额外字段: {extra_fields}")
                
                return missing_fields, current_fields
            else:
                logger.warning("⚠️ NPC表为空，无法检查结构")
                return [], []
                
        except Exception as e:
            logger.error(f"❌ 检查表结构失败: {e}")
            return [], []
    
    async def add_missing_columns(self, missing_fields):
        """添加缺少的列"""
        logger.info("🔧 添加缺少的列...")
        
        # 注意：Supabase的Python客户端不支持直接执行DDL语句
        # 我们需要使用RPC或者手动在Supabase控制台执行
        
        sql_commands = []
        
        for field in missing_fields:
            if field == "is_active":
                sql_commands.append("ALTER TABLE npcs ADD COLUMN is_active BOOLEAN DEFAULT true;")
            elif field == "avatar_url":
                sql_commands.append("ALTER TABLE npcs ADD COLUMN avatar_url TEXT;")
            elif field == "created_at":
                sql_commands.append("ALTER TABLE npcs ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();")
            elif field == "updated_at":
                sql_commands.append("ALTER TABLE npcs ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();")
            elif field == "description":
                sql_commands.append("ALTER TABLE npcs ADD COLUMN description TEXT;")
        
        if sql_commands:
            logger.info("📝 需要执行的SQL命令:")
            for i, cmd in enumerate(sql_commands, 1):
                logger.info(f"  {i}. {cmd}")
            
            # 保存SQL命令到文件
            with open("fix_npc_table.sql", "w") as f:
                f.write("-- 修复NPC表结构的SQL命令\n")
                f.write("-- 请在Supabase SQL编辑器中执行这些命令\n\n")
                for cmd in sql_commands:
                    f.write(cmd + "\n")
            
            logger.info("📄 SQL命令已保存到 fix_npc_table.sql")
            logger.info("⚠️ 请在Supabase控制台的SQL编辑器中手动执行这些命令")
            
            self.results.append({
                "action": "generate_sql",
                "sql_commands": sql_commands,
                "file": "fix_npc_table.sql",
                "status": "generated"
            })
        else:
            logger.info("✅ 没有需要添加的列")
    
    async def update_existing_npcs(self):
        """更新现有NPC记录"""
        logger.info("🔄 更新现有NPC记录...")
        
        try:
            # 获取所有NPC
            result = self.supabase.table("npcs").select("*").execute()
            
            updated_count = 0
            
            for npc in result.data:
                updates = {}
                
                # 如果没有is_active字段或为None，设置为True
                if npc.get("is_active") is None:
                    updates["is_active"] = True
                
                # 如果没有description，添加默认描述
                if not npc.get("description"):
                    updates["description"] = f"NPC角色: {npc.get('name', '未知')}"
                
                # 如果有更新需要执行
                if updates:
                    try:
                        self.supabase.table("npcs").update(updates).eq("id", npc["id"]).execute()
                        updated_count += 1
                        logger.info(f"✅ 更新NPC {npc.get('name', npc['id'])}: {updates}")
                    except Exception as e:
                        logger.error(f"❌ 更新NPC {npc['id']} 失败: {e}")
            
            logger.info(f"📊 共更新了 {updated_count} 个NPC记录")
            
            self.results.append({
                "action": "update_npcs",
                "updated_count": updated_count,
                "total_npcs": len(result.data),
                "status": "completed"
            })
            
        except Exception as e:
            logger.error(f"❌ 更新NPC记录失败: {e}")
    
    async def create_default_npcs(self):
        """创建默认NPC（如果需要）"""
        logger.info("🎭 检查是否需要创建默认NPC...")
        
        try:
            # 检查是否有活跃的NPC
            result = self.supabase.table("npcs").select("*").execute()
            
            active_npcs = [npc for npc in result.data if npc.get("is_active", True)]
            
            if not active_npcs:
                logger.info("📝 创建默认NPC...")
                
                default_npcs = [
                    {
                        "name": "默认助手",
                        "description": "通用AI助手，用于日常对话",
                        "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                        "is_active": True
                    },
                    {
                        "name": "朋友",
                        "description": "亲密朋友角色，轻松聊天",
                        "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                        "is_active": True
                    }
                ]
                
                created_count = 0
                for npc_data in default_npcs:
                    try:
                        result = self.supabase.table("npcs").insert(npc_data).execute()
                        if result.data:
                            created_count += 1
                            logger.info(f"✅ 创建NPC: {npc_data['name']}")
                    except Exception as e:
                        logger.error(f"❌ 创建NPC {npc_data['name']} 失败: {e}")
                
                logger.info(f"📊 共创建了 {created_count} 个默认NPC")
                
                self.results.append({
                    "action": "create_default_npcs",
                    "created_count": created_count,
                    "status": "completed"
                })
            else:
                logger.info(f"✅ 已有 {len(active_npcs)} 个活跃NPC，无需创建默认NPC")
                
        except Exception as e:
            logger.error(f"❌ 创建默认NPC失败: {e}")
    
    async def verify_fix(self):
        """验证修复结果"""
        logger.info("✅ 验证修复结果...")
        
        try:
            # 重新检查表结构
            result = self.supabase.table("npcs").select("*").limit(1).execute()
            
            if result.data:
                current_fields = list(result.data[0].keys())
                logger.info(f"📋 修复后的字段: {current_fields}")
                
                # 检查关键字段
                has_is_active = "is_active" in current_fields
                has_description = "description" in current_fields
                
                logger.info(f"✅ is_active字段: {'存在' if has_is_active else '缺失'}")
                logger.info(f"✅ description字段: {'存在' if has_description else '缺失'}")
                
                # 检查活跃NPC数量
                active_npcs_result = self.supabase.table("npcs").select("*").execute()
                active_count = len([npc for npc in active_npcs_result.data if npc.get("is_active", True)])
                
                logger.info(f"📊 活跃NPC数量: {active_count}")
                
                self.results.append({
                    "verification": "table_check",
                    "has_is_active": has_is_active,
                    "has_description": has_description,
                    "active_npc_count": active_count,
                    "status": "verified"
                })
                
                return has_is_active and active_count > 0
            else:
                logger.warning("⚠️ NPC表仍为空")
                return False
                
        except Exception as e:
            logger.error(f"❌ 验证失败: {e}")
            return False
    
    async def run_fix(self):
        """运行修复流程"""
        logger.info("🚀 开始NPC表结构修复...")
        
        # 初始化连接
        if not await self.initialize_supabase():
            logger.error("❌ 无法连接到Supabase")
            return False
        
        # 检查表结构
        missing_fields, current_fields = await self.check_npc_table_structure()
        
        # 如果有缺少的字段，生成SQL命令
        if missing_fields:
            await self.add_missing_columns(missing_fields)
            logger.warning("⚠️ 请先在Supabase控制台执行生成的SQL命令，然后重新运行此脚本")
            return False
        
        # 更新现有NPC
        await self.update_existing_npcs()
        
        # 创建默认NPC（如果需要）
        await self.create_default_npcs()
        
        # 验证修复结果
        success = await self.verify_fix()
        
        # 保存结果
        report = {
            "timestamp": datetime.now().isoformat(),
            "success": success,
            "results": self.results
        }
        
        with open("npc_table_fix_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("📄 修复报告已保存到 npc_table_fix_report.json")
        
        if success:
            logger.info("🎉 NPC表结构修复完成！")
        else:
            logger.error("💥 修复过程中遇到问题，请检查报告")
        
        return success

async def main():
    """主函数"""
    fixer = NPCTableFixer()
    success = await fixer.run_fix()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)