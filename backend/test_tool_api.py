#!/usr/bin/env python3
"""
工具API端点测试脚本
用于测试工具调用相关的API端点
"""
import requests
import json
import time
import sys

BASE_URL = "http://localhost:8000"

def test_api_endpoint(endpoint, method="GET", data=None, description=""):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    print(f"\n🔍 测试: {description}")
    print(f"   URL: {method} {url}")
    
    try:
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        else:
            print(f"   ❌ 不支持的HTTP方法: {method}")
            return False
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ 成功")
                
                # 显示关键信息
                if "tools" in result:
                    print(f"   📊 工具数量: {result.get('total', len(result['tools']))}")
                elif "ranked_tools" in result:
                    print(f"   📊 排序工具数量: {result.get('total', len(result['ranked_tools']))}")
                elif "response" in result:
                    print(f"   💬 响应长度: {len(str(result['response']))}")
                elif "statistics" in result:
                    stats = result['statistics']
                    print(f"   📊 总工具数: {stats.get('total_tools', 0)}")
                
                return True
            except json.JSONDecodeError:
                print(f"   ⚠️ 响应不是有效的JSON")
                print(f"   响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"   ❌ 失败")
            try:
                error = response.json()
                print(f"   错误: {error.get('detail', '未知错误')}")
            except:
                print(f"   错误内容: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ 连接失败 - 请确保服务器正在运行")
        return False
    except requests.exceptions.Timeout:
        print(f"   ❌ 请求超时")
        return False
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False

def wait_for_server(max_attempts=30):
    """等待服务器启动"""
    print("⏳ 等待服务器启动...")
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器已启动")
                return True
        except:
            pass
        
        print(f"   尝试 {attempt + 1}/{max_attempts}...")
        time.sleep(2)
    
    print("❌ 服务器启动超时")
    return False

def main():
    """主测试函数"""
    print("🚀 工具API端点测试")
    print("=" * 50)
    
    # 等待服务器启动
    if not wait_for_server():
        print("\n💡 请在另一个终端中运行: cd backend && python main.py")
        return 1
    
    # 测试用例
    test_cases = [
        {
            "endpoint": "/health",
            "method": "GET",
            "description": "健康检查"
        },
        {
            "endpoint": "/api/tools/enumerate",
            "method": "GET",
            "description": "枚举所有工具"
        },
        {
            "endpoint": "/api/tools/statistics",
            "method": "GET",
            "description": "获取工具统计信息"
        },
        {
            "endpoint": "/api/tools/categories",
            "method": "GET",
            "description": "获取工具类别"
        },
        {
            "endpoint": "/api/tools/rank",
            "method": "POST",
            "data": {
                "query": "我想了解最新的科技新闻",
                "top_k": 3
            },
            "description": "工具相关性排序"
        },
        {
            "endpoint": "/api/tools/top-relevant",
            "method": "GET",
            "description": "获取最相关工具"
        },
        {
            "endpoint": "/api/tools/execute",
            "method": "POST",
            "data": {
                "tool_name": "fetch_news",
                "parameters": {
                    "category": "technology",
                    "limit": 2
                }
            },
            "description": "执行工具"
        },
        {
            "endpoint": "/api/tools/enhanced-chat",
            "method": "POST",
            "data": {
                "user_input": "帮我搜索最新的AI技术发展",
                "use_tools": True
            },
            "description": "增强对话（带工具调用）"
        },
        {
            "endpoint": "/api/tools/health",
            "method": "GET",
            "description": "工具服务健康检查"
        }
    ]
    
    # 执行测试
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        # 为GET请求添加查询参数
        if test_case["method"] == "GET" and test_case["endpoint"] == "/api/tools/top-relevant":
            test_case["endpoint"] += "?query=搜索新闻"
        
        success = test_api_endpoint(
            test_case["endpoint"],
            test_case["method"],
            test_case.get("data"),
            test_case["description"]
        )
        
        if success:
            passed += 1
        
        time.sleep(0.5)  # 避免请求过快
    
    # 测试总结
    print(f"\n{'=' * 50}")
    print(f"测试总结")
    print(f"{'=' * 50}")
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有API端点测试通过！")
        return 0
    else:
        print(f"\n⚠️ {total - passed} 个测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)