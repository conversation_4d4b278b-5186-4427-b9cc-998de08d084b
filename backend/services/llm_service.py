import asyncio
import json
import re
import requests
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
import aiohttp
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SpeakContent:
    emotion: str
    speed: float
    text: str

class LLMService:
    def __init__(self, api_key: str, endpoint: str):
        self.api_key = api_key
        self.endpoint = endpoint
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def extract_speak_content(self, response_text: str) -> Optional[SpeakContent]:
        """Extract SPEAK content from LLM response"""
        try:
            # Use regex to find SPEAK tags
            speak_pattern = r'<SPEAK><emotion>(.*?)</emotion><speed>(.*?)</speed><text>(.*?)</text></SPEAK>'
            match = re.search(speak_pattern, response_text, re.DOTALL)
            
            if match:
                emotion = match.group(1).strip()
                speed = float(match.group(2).strip())
                text = match.group(3).strip()
                
                return SpeakContent(emotion=emotion, speed=speed, text=text)
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting SPEAK content: {e}")
            return None
    
    def build_conversation_messages(self, conversation_history: List[Dict[str, str]], 
                                  system_prompt: str) -> List[Dict[str, str]]:
        """Build conversation messages for the API"""
        messages = [{"role": "system", "content": system_prompt}]
        
        for msg in conversation_history:
            messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        
        return messages
    
    async def stream_chat_completion(self, 
                                   messages: List[Dict[str, str]], 
                                   model: str = "doubao-seed-1-6-250615",
                                   temperature: float = 0.7,
                                   max_tokens: int = 2000,
                                   tools: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """Stream chat completion from Volcano Engine API"""
        try:
            payload = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": True
            }
            
            if tools:
                payload["tools"] = tools
                payload["tool_choice"] = "auto"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.endpoint,
                    headers=self.headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"API request failed: {response.status} - {error_text}")
                        return
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data = line[6:]  # Remove 'data: ' prefix
                            
                            if data == '[DONE]':
                                break
                            
                            try:
                                chunk = json.loads(data)
                                if 'choices' in chunk and len(chunk['choices']) > 0:
                                    delta = chunk['choices'][0].get('delta', {})
                                    content = delta.get('content', '')
                                    
                                    if content:
                                        yield content
                                        
                            except json.JSONDecodeError:
                                continue
                                
        except Exception as e:
            logger.error(f"Error in stream_chat_completion: {e}")
            yield f"Error: {str(e)}"
    
    async def get_chat_completion(self, 
                                messages: List[Dict[str, str]], 
                                model: str = "doubao-seed-1-6-250615",
                                temperature: float = 0.7,
                                max_tokens: int = 2000,
                                tools: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Get complete chat response (non-streaming)"""
        try:
            payload = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }
            
            if tools:
                payload["tools"] = tools
                payload["tool_choice"] = "auto"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.endpoint,
                    headers=self.headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"API request failed: {response.status} - {error_text}")
                        return {"error": f"API request failed: {response.status}"}
                    
                    result = await response.json()
                    return result
                    
        except Exception as e:
            logger.error(f"Error in get_chat_completion: {e}")
            return {"error": str(e)}
    
    def get_default_tools(self) -> List[Dict]:
        """Get default tool definitions for function calling"""
        return [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get current weather information for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "The city and state/country, e.g. San Francisco, CA"
                            },
                            "unit": {
                                "type": "string",
                                "enum": ["celsius", "fahrenheit"],
                                "description": "Temperature unit"
                            }
                        },
                        "required": ["location"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_web",
                    "description": "Search the web for current information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query"
                            },
                            "num_results": {
                                "type": "integer",
                                "description": "Number of results to return",
                                "default": 5
                            }
                        },
                        "required": ["query"]
                    }
                }
            }
        ]
    
    async def process_tool_calls(self, tool_calls: List[Dict]) -> List[Dict]:
        """Process tool calls and return results"""
        results = []
        
        for tool_call in tool_calls:
            try:
                function_name = tool_call["function"]["name"]
                arguments = json.loads(tool_call["function"]["arguments"])
                
                # Mock tool implementations
                if function_name == "get_weather":
                    result = {
                        "location": arguments.get("location"),
                        "temperature": "22°C",
                        "condition": "Sunny",
                        "humidity": "65%"
                    }
                elif function_name == "search_web":
                    result = {
                        "query": arguments.get("query"),
                        "results": [
                            {"title": "Sample Result 1", "url": "https://example.com/1"},
                            {"title": "Sample Result 2", "url": "https://example.com/2"}
                        ]
                    }
                else:
                    result = {"error": f"Unknown function: {function_name}"}
                
                results.append({
                    "tool_call_id": tool_call["id"],
                    "role": "tool",
                    "name": function_name,
                    "content": json.dumps(result)
                })
                
            except Exception as e:
                logger.error(f"Error processing tool call: {e}")
                results.append({
                    "tool_call_id": tool_call.get("id", "unknown"),
                    "role": "tool",
                    "name": tool_call.get("function", {}).get("name", "unknown"),
                    "content": json.dumps({"error": str(e)})
                })
        
        return results
    
    async def generate_response_stream(self, 
                                     user_input: str,
                                     conversation_history: List[Dict[str, str]],
                                     system_prompt: str,
                                     use_tools: bool = True) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate streaming response with SPEAK content extraction"""
        try:
            # Build messages
            messages = self.build_conversation_messages(conversation_history, system_prompt)
            messages.append({"role": "user", "content": user_input})
            
            # Get tools if enabled
            tools = self.get_default_tools() if use_tools else None
            
            # Collect full response for processing
            full_response = ""
            
            # Stream the response
            async for chunk in self.stream_chat_completion(messages, tools=tools):
                full_response += chunk
                
                # Yield chunk for real-time streaming
                yield {
                    "type": "chunk",
                    "content": chunk,
                    "full_response": full_response
                }
            
            # Extract SPEAK content from full response
            speak_content = self.extract_speak_content(full_response)
            
            if speak_content:
                yield {
                    "type": "speak",
                    "emotion": speak_content.emotion,
                    "speed": speak_content.speed,
                    "text": speak_content.text,
                    "full_response": full_response
                }
            else:
                # Fallback if no SPEAK tags found
                yield {
                    "type": "speak",
                    "emotion": "neutral",
                    "speed": 1.0,
                    "text": full_response,
                    "full_response": full_response
                }
            
            # Final completion signal
            yield {
                "type": "complete",
                "full_response": full_response
            }
            
        except Exception as e:
            logger.error(f"Error in generate_response_stream: {e}")
            yield {
                "type": "error",
                "error": str(e)
            }
    
    def _generate_mock_response(self, user_input: str, system_prompt: str = None) -> Dict[str, Any]:
        """Generate mock response for testing when API is unavailable"""
        # Enhanced mock responses for common test cases
        mock_responses = {
            "Hello, this is a test message!": "Hello! I received your test message. This is a mock response from the AI assistant. Everything is working correctly!",
            "你好": "你好！很高兴见到你！我是你的AI助手，有什么可以帮助你的吗？",
            "1+1等于几": "1+1等于2。这是一个简单的数学问题。",
            "天气怎么样": "今天天气不错，阳光明媚，适合外出活动。",
            "介绍一下自己": "我是一个AI助手，可以帮助你回答问题、进行对话和提供各种信息。",
            "测试": "测试成功！系统运行正常。"
        }

        # Check for exact matches first
        if user_input in mock_responses:
            response_text = mock_responses[user_input]
        # Check for partial matches (case insensitive)
        else:
            user_lower = user_input.lower()
            found_response = None
            for key, value in mock_responses.items():
                if key.lower() in user_lower or user_lower in key.lower():
                    found_response = value
                    break

            if found_response:
                response_text = found_response
            else:
                # Generate contextual response based on input
                if any(word in user_input.lower() for word in ['hello', 'hi', 'hey']):
                    response_text = "Hello! Nice to meet you. How can I help you today?"
                elif any(word in user_input.lower() for word in ['test', '测试']):
                    response_text = "Test successful! The system is working properly."
                elif any(word in user_input.lower() for word in ['谢谢', 'thank', 'thanks']):
                    response_text = "不客气！很高兴能帮助你。"
                elif '?' in user_input or '？' in user_input:
                    response_text = f"这是一个很好的问题：{user_input}。让我来为你解答。"
                else:
                    response_text = f"我收到了你的消息：{user_input}。这是一个模拟响应，用于测试系统功能。"

        # Format as SPEAK content
        formatted_response = f"<turn>\n<THINK>\n## 1. 意图分析: 用户询问：{user_input}\n## 2. 行动规划: 提供友好回答\n## 3. 工具选择与参数构建: 无需工具\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.0</speed><text>{response_text}</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.0</speed><text>{response_text}</text></SPEAK>\n<JUDGE>\n本轮表现：9/10分\n优点：回答友好准确\n缺点：使用模拟响应\n重大失误：无\n</JUDGE>\n</turn>"

        return {
            "success": True,
            "full_response": formatted_response,
            "speak_content": {
                "emotion": "friendly",
                "speed": 1.0,
                "text": response_text
            },
            "mock_mode": True,
            "context_used": False,  # Mock模式不使用上下文
            "session_id": None
        }

    async def generate_response(self,
                              user_input: str,
                              conversation_history: List[Dict[str, str]],
                              system_prompt: str,
                              use_tools: bool = True) -> Dict[str, Any]:
        """Generate complete response (non-streaming)"""
        try:
            logger.info(f"Generating response for: {user_input}")

            # Build messages
            messages = self.build_conversation_messages(conversation_history, system_prompt)
            messages.append({"role": "user", "content": user_input})

            # Get tools if enabled
            tools = self.get_default_tools() if use_tools else None

            # Try to get real API response first
            logger.info("Calling real Volcano Engine API...")
            response = await self.get_chat_completion(messages, tools=tools)

            if "error" in response:
                # If API fails, use mock response
                logger.warning(f"API failed, using mock response: {response['error']}")
                return self._generate_mock_response(user_input, system_prompt)

            # Extract content from real API response
            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]
                logger.info(f"Real API response received: {content[:100]}...")

                # Extract SPEAK content
                speak_content = self.extract_speak_content(content)

                return {
                    "success": True,
                    "full_response": content,
                    "speak_content": {
                        "emotion": speak_content.emotion if speak_content else "neutral",
                        "speed": speak_content.speed if speak_content else 1.0,
                        "text": speak_content.text if speak_content else content
                    },
                    "mock_mode": False,
                    "context_used": False,  # 基础LLM服务不使用上下文
                    "session_id": None
                }
            else:
                # No content in response, use mock
                logger.warning("No content in API response, using mock response")
                return self._generate_mock_response(user_input, system_prompt)

        except Exception as e:
            logger.error(f"Error in generate_response: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            # Use mock response as fallback
            logger.warning("Using mock response as fallback due to exception")
            return self._generate_mock_response(user_input, system_prompt)