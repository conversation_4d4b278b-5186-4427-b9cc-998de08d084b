"""
MCP Service for handling MCP server interactions
"""
import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

from .mcp_client import mcp_client

logger = logging.getLogger(__name__)

class MCPService:
    def __init__(self):
        """Initialize MCP service"""
        self.servers = {}
        self.config_file = Path("backend/mcp_config/multi_server_config.json")
        self._load_existing_servers()
        self._sync_with_mcp_client()
        logger.info("MCP Service initialized")

    def _load_existing_servers(self):
        """Load existing servers from config file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    servers = config.get("servers", [])
                    for server in servers:
                        self.servers[server["name"]] = server
                logger.info(f"Loaded {len(self.servers)} existing MCP servers")
        except Exception as e:
            logger.warning(f"Failed to load existing servers: {e}")

    def _sync_with_mcp_client(self):
        """同步MCP客户端的配置到MCP服务"""
        try:
            from services.mcp_client import mcp_client

            # 将MCP客户端的配置同步到MCP服务
            for server_name, config in mcp_client.server_configs.items():
                if server_name not in self.servers:
                    server_data = {
                        "name": server_name,
                        "url": f"local://{config['command']}",
                        "registered_at": datetime.now().isoformat(),
                        "info": {
                            "source_config": config,
                            "command": config['command'],
                            "args": config['args'],
                            "env": config['env']
                        }
                    }
                    self.servers[server_name] = server_data

            logger.info(f"同步了 {len(mcp_client.server_configs)} 个MCP客户端配置")

        except Exception as e:
            logger.error(f"同步MCP客户端配置失败: {e}")

    def _save_servers_config(self):
        """Save current servers to config file"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            config = {
                "servers": list(self.servers.values()),
                "default_server": None
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            logger.info("Saved MCP servers configuration")
        except Exception as e:
            logger.error(f"Failed to save servers config: {e}")

    def register_server(self, server_name: str, server_url: str, server_info: Dict[str, Any] = None) -> bool:
        """Register a new MCP server"""
        try:
            if server_name in self.servers:
                logger.warning(f"Server {server_name} already registered")
                return False

            server_data = {
                "name": server_name,
                "url": server_url,
                "registered_at": datetime.now().isoformat(),
                "info": server_info or {}
            }

            self.servers[server_name] = server_data
            self._save_servers_config()
            logger.info(f"Registered MCP server: {server_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to register server {server_name}: {e}")
            return False

    def unregister_server(self, server_name: str) -> bool:
        """Unregister an MCP server"""
        try:
            if server_name not in self.servers:
                logger.warning(f"Server {server_name} not found")
                return False

            del self.servers[server_name]
            self._save_servers_config()
            logger.info(f"Unregistered MCP server: {server_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to unregister server {server_name}: {e}")
            return False

    def get_server_info(self, server_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific server"""
        return self.servers.get(server_name)

    def list_servers(self) -> List[Dict[str, Any]]:
        """Get list of registered MCP servers"""
        return list(self.servers.values())

    def initialize_config(self, config_data: Dict[str, Any]) -> bool:
        """Initialize MCP configuration"""
        try:
            # This could be used to set up user-specific MCP configurations
            logger.info("Initializing MCP configuration")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize config: {e}")
            return False

    def delete_config(self) -> bool:
        """Delete MCP configuration"""
        try:
            # This could be used to clean up user-specific MCP configurations
            logger.info("Deleting MCP configuration")
            return True
        except Exception as e:
            logger.error(f"Failed to delete config: {e}")
            return False

    def get_all_tools_for_reranker(self) -> List[Dict[str, Any]]:
        """Get all tools from all MCP servers for reranker"""
        tools = []

        # 不再添加内置工具，只使用真正的MCP工具

        # Get real MCP tools from all servers
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果已经在事件循环中，创建一个任务
                logger.warning("在事件循环中获取MCP工具，可能需要异步处理")
                # 暂时跳过MCP工具获取
            else:
                # 如果没有运行的事件循环，创建一个新的
                mcp_tools = asyncio.run(mcp_client.get_all_tools())
                tools.extend(mcp_tools)
        except Exception as e:
            logger.error(f"获取MCP工具失败: {e}")
            # 不再回退到mock实现

        logger.info(f"Retrieved {len(tools)} tools for reranker")
        return tools



    async def get_server_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """Get tools from specific MCP server"""
        if server_name not in self.servers:
            logger.warning(f"Server {server_name} not found")
            return []

        try:
            # 使用真正的MCP客户端获取工具
            tools = await mcp_client.get_server_tools(server_name)
            return tools
        except Exception as e:
            logger.error(f"获取服务器 {server_name} 工具失败: {e}")
            return []

    def execute_tool(self, tool_name: str, server_name: str = None, **parameters) -> Dict[str, Any]:
        """Execute MCP tool"""
        try:
            logger.info(f"Executing tool: {tool_name} with parameters: {parameters}")

            # 如果是内置工具，返回错误（不再支持内置工具）
            if server_name == "builtin" or server_name is None:
                return {
                    "success": False,
                    "tool_name": tool_name,
                    "server": server_name or "builtin",
                    "parameters": parameters,
                    "error": "内置工具已被移除，请使用真正的MCP服务器工具",
                    "timestamp": datetime.now().isoformat(),
                    "status": "error"
                }

            # 对于MCP服务器工具，使用真正的MCP客户端
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果已经在事件循环中，返回错误
                    logger.warning("在事件循环中执行MCP工具，需要使用异步版本")
                    return {
                        "success": False,
                        "tool_name": tool_name,
                        "server": server_name,
                        "parameters": parameters,
                        "error": "请使用异步版本的工具执行方法",
                        "timestamp": datetime.now().isoformat(),
                        "status": "error"
                    }
                else:
                    # 如果没有运行的事件循环，创建一个新的
                    result = asyncio.run(mcp_client.execute_tool(tool_name, server_name, **parameters))
                    return result
            except Exception as mcp_error:
                logger.error(f"MCP工具执行失败: {mcp_error}")
                return {
                    "success": False,
                    "tool_name": tool_name,
                    "server": server_name,
                    "parameters": parameters,
                    "error": str(mcp_error),
                    "timestamp": datetime.now().isoformat(),
                    "status": "error"
                }

        except Exception as e:
            logger.error(f"Failed to execute tool {tool_name}: {e}")
            return {
                "success": False,
                "tool_name": tool_name,
                "server": server_name or "builtin",
                "parameters": parameters,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            }



    async def get_all_tools_for_reranker_async(self) -> List[Dict[str, Any]]:
        """异步获取所有工具"""
        tools = []

        # Get real MCP tools from all servers
        try:
            mcp_tools = await mcp_client.get_all_tools()
            tools.extend(mcp_tools)
        except Exception as e:
            logger.error(f"获取MCP工具失败: {e}")
            # 不再回退到mock实现

        logger.info(f"Retrieved {len(tools)} tools for reranker (async)")
        return tools

    async def execute_tool_async(self, tool_name: str, server_name: str = None, **parameters) -> Dict[str, Any]:
        """异步执行MCP工具"""
        try:
            logger.info(f"Executing tool (async): {tool_name} with parameters: {parameters}")

            # 不再支持内置工具
            if server_name == "builtin" or server_name is None:
                return {
                    "success": False,
                    "tool_name": tool_name,
                    "server": server_name or "builtin",
                    "parameters": parameters,
                    "error": "内置工具已被移除，请使用真正的MCP服务器工具",
                    "timestamp": datetime.now().isoformat(),
                    "status": "error"
                }

            # 对于MCP服务器工具，使用真正的MCP客户端
            try:
                result = await mcp_client.execute_tool(tool_name, server_name, **parameters)
                return result
            except Exception as mcp_error:
                logger.error(f"MCP工具执行失败: {mcp_error}")
                return {
                    "success": False,
                    "tool_name": tool_name,
                    "server": server_name,
                    "parameters": parameters,
                    "error": str(mcp_error),
                    "timestamp": datetime.now().isoformat(),
                    "status": "error"
                }

        except Exception as e:
            logger.error(f"Failed to execute tool {tool_name}: {e}")
            return {
                "success": False,
                "tool_name": tool_name,
                "server": server_name or "builtin",
                "parameters": parameters,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            }

# Global MCP service instance
mcp_service = MCPService()
