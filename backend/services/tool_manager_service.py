"""
工具管理服务
负责工具的枚举、检索和管理
"""
import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
import asyncio
import importlib.util
import os

from services.mcp_service import mcp_service
from utils.reranker_service import get_reranker_service

logger = logging.getLogger(__name__)

class ToolManagerService:
    """
    工具管理服务类
    提供工具枚举、向量检索、重排序等功能
    """
    
    def __init__(self):
        """初始化工具管理服务"""
        self.mcp_service = mcp_service
        self.reranker_service = get_reranker_service()
        self._tool_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 300  # 5分钟缓存
        
        logger.info("工具管理服务初始化完成")
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self._cache_timestamp:
            return False
        
        current_time = datetime.now().timestamp()
        return (current_time - self._cache_timestamp) < self._cache_ttl
    
    def _update_cache(self, tools: List[Dict[str, Any]]):
        """更新工具缓存"""
        self._tool_cache = {tool['name']: tool for tool in tools}
        self._cache_timestamp = datetime.now().timestamp()
        logger.debug(f"工具缓存已更新，包含 {len(tools)} 个工具")
    
    def enumerate_all_tools(self, use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        枚举所有可用工具

        Args:
            use_cache (bool): 是否使用缓存

        Returns:
            List[Dict[str, Any]]: 工具列表
        """
        try:
            # 检查缓存
            logger.debug(f"检查缓存: use_cache={use_cache}, cache_valid={self._is_cache_valid()}, cache_size={len(self._tool_cache)}")
            if use_cache and self._is_cache_valid():
                logger.debug(f"使用缓存的工具列表，返回 {len(self._tool_cache)} 个工具")
                return list(self._tool_cache.values())

            # 检查是否在事件循环中，如果是则使用异步方法
            import asyncio
            try:
                loop = asyncio.get_running_loop()
                # 在事件循环中，使用异步方法
                logger.info("在事件循环中，使用异步方法获取工具")
                # 创建一个新的任务来获取工具
                task = asyncio.create_task(self.enumerate_all_tools_async(use_cache))
                # 由于我们在同步方法中，我们需要返回空列表并建议使用异步方法
                logger.warning("同步方法在事件循环中无法直接获取工具，请使用异步方法 enumerate_all_tools_async")
                return []
            except RuntimeError:
                # 没有运行的事件循环，可以使用同步方法
                logger.debug("没有运行的事件循环，使用同步方法")

            # 从MCP服务获取所有工具
            tools = self.mcp_service.get_all_tools_for_reranker()

            # 添加内置工具的详细信息
            enhanced_tools = []
            for tool in tools:
                enhanced_tool = self._enhance_tool_info(tool)
                enhanced_tools.append(enhanced_tool)

            # 更新缓存
            if use_cache:
                self._update_cache(enhanced_tools)

            logger.info(f"枚举到 {len(enhanced_tools)} 个工具")
            return enhanced_tools

        except Exception as e:
            logger.error(f"枚举工具时出错: {e}")
            return []

    async def enumerate_all_tools_async(self, use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        异步枚举所有可用工具

        Args:
            use_cache (bool): 是否使用缓存

        Returns:
            List[Dict[str, Any]]: 工具列表
        """
        try:
            # 检查缓存
            if use_cache and self._is_cache_valid():
                logger.debug("使用缓存的工具列表")
                return list(self._tool_cache.values())

            # 从MCP服务异步获取所有工具
            tools = await self.mcp_service.get_all_tools_for_reranker_async()

            # 添加内置工具的详细信息
            enhanced_tools = []
            for tool in tools:
                enhanced_tool = self._enhance_tool_info(tool)
                enhanced_tools.append(enhanced_tool)

            # 更新缓存
            if use_cache:
                self._update_cache(enhanced_tools)

            logger.info(f"异步枚举到 {len(enhanced_tools)} 个工具")
            return enhanced_tools

        except Exception as e:
            logger.error(f"异步枚举工具时出错: {e}")
            return []
    
    def _enhance_tool_info(self, tool: Dict[str, Any]) -> Dict[str, Any]:
        """
        增强工具信息，添加更详细的描述和参数
        
        Args:
            tool (Dict[str, Any]): 原始工具信息
            
        Returns:
            Dict[str, Any]: 增强后的工具信息
        """
        enhanced_tool = tool.copy()
        
        # 根据工具名称添加详细参数信息
        tool_name = tool.get('name', '')
        
        if tool_name == "get-tickets":
            enhanced_tool.update({
                "parameters": {
                    "from_station": {
                        "type": "string",
                        "description": "出发站点代码或名称",
                        "required": True
                    },
                    "to_station": {
                        "type": "string", 
                        "description": "到达站点代码或名称",
                        "required": True
                    },
                    "date": {
                        "type": "string",
                        "description": "出发日期，格式：YYYY-MM-DD",
                        "required": True
                    },
                    "train_type": {
                        "type": "string",
                        "description": "列车类型",
                        "enum": ["高铁", "动车", "普通列车", "全部"],
                        "default": "全部",
                        "required": False
                    }
                },
                "usage_examples": [
                    "查询北京到上海的火车票",
                    "BJP到SHH的高铁票"
                ]
            })
        
        elif tool_name == "search_and_summarize":
            enhanced_tool.update({
                "parameters": {
                    "query": {
                        "type": "string",
                        "description": "搜索查询关键词",
                        "required": True
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "最大结果数量",
                        "default": 5,
                        "required": False
                    }
                },
                "usage_examples": [
                    "搜索最新的AI技术发展",
                    "查找关于气候变化的信息"
                ]
            })
        
        elif tool_name == "fetch_news":
            enhanced_tool.update({
                "parameters": {
                    "category": {
                        "type": "string",
                        "description": "新闻类别",
                        "enum": ["technology", "science", "business", "sports", "entertainment"],
                        "default": "general",
                        "required": False
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回新闻数量",
                        "default": 5,
                        "required": False
                    }
                },
                "usage_examples": [
                    "获取科技新闻",
                    "查看最新体育资讯"
                ]
            })
        
        elif tool_name == "recall_current_activity":
            enhanced_tool.update({
                "parameters": {},
                "usage_examples": [
                    "我现在在做什么？",
                    "当前的活动状态是什么？"
                ]
            })
        
        elif tool_name == "recall_relevant_experiences":
            enhanced_tool.update({
                "parameters": {
                    "topic": {
                        "type": "string",
                        "description": "相关话题或关键词",
                        "required": True
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回经历数量",
                        "default": 3,
                        "required": False
                    }
                },
                "usage_examples": [
                    "回忆关于编程的经历",
                    "想起与朋友的对话"
                ]
            })

        # 为MCP工具添加参数信息
        elif tool_name == "query_train_schedule":
            enhanced_tool.update({
                "parameters": {
                    "departure": {
                        "type": "string",
                        "description": "出发城市或车站",
                        "required": True
                    },
                    "destination": {
                        "type": "string",
                        "description": "目的地城市或车站",
                        "required": True
                    },
                    "date": {
                        "type": "string",
                        "description": "出发日期，格式：YYYY-MM-DD",
                        "required": False
                    }
                },
                "usage_examples": [
                    "查询北京到上海的火车",
                    "北京南到杭州东的列车时刻"
                ]
            })

        elif tool_name == "search_bilibili_videos":
            enhanced_tool.update({
                "parameters": {
                    "keyword": {
                        "type": "string",
                        "description": "搜索关键词",
                        "required": True
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回视频数量",
                        "default": 10,
                        "required": False
                    }
                },
                "usage_examples": [
                    "搜索AI相关视频",
                    "查找编程教程"
                ]
            })

        elif tool_name == "get_cooking_recipe":
            enhanced_tool.update({
                "parameters": {
                    "dish_name": {
                        "type": "string",
                        "description": "菜品名称",
                        "required": True
                    }
                },
                "usage_examples": [
                    "红烧肉的做法",
                    "如何做宫保鸡丁"
                ]
            })

        # 添加通用字段
        enhanced_tool.setdefault("last_updated", datetime.now().isoformat())
        enhanced_tool.setdefault("version", "1.0")

        return enhanced_tool
    
    def get_tool_by_name(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """
        根据名称获取特定工具
        
        Args:
            tool_name (str): 工具名称
            
        Returns:
            Optional[Dict[str, Any]]: 工具信息，如果不存在则返回None
        """
        try:
            # 先尝试从缓存获取
            if self._is_cache_valid() and tool_name in self._tool_cache:
                return self._tool_cache[tool_name]
            
            # 从所有工具中查找
            all_tools = self.enumerate_all_tools()
            for tool in all_tools:
                if tool.get('name') == tool_name:
                    return tool
            
            logger.warning(f"未找到工具: {tool_name}")
            return None
            
        except Exception as e:
            logger.error(f"获取工具 {tool_name} 时出错: {e}")
            return None

    async def get_tool_by_name_async(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """
        根据名称获取特定工具（异步版本）

        Args:
            tool_name (str): 工具名称

        Returns:
            Optional[Dict[str, Any]]: 工具信息，如果不存在则返回None
        """
        try:
            # 先尝试从缓存获取
            if self._is_cache_valid() and tool_name in self._tool_cache:
                return self._tool_cache[tool_name]

            # 从所有工具中查找
            all_tools = await self.enumerate_all_tools_async()
            for tool in all_tools:
                if tool.get('name') == tool_name:
                    return tool

            logger.warning(f"未找到工具: {tool_name}")
            return None

        except Exception as e:
            logger.error(f"获取工具 {tool_name} 时出错: {e}")
            return None
    
    def search_tools_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        根据类别搜索工具
        
        Args:
            category (str): 工具类别
            
        Returns:
            List[Dict[str, Any]]: 匹配的工具列表
        """
        try:
            all_tools = self.enumerate_all_tools()
            matching_tools = [
                tool for tool in all_tools 
                if tool.get('category', '').lower() == category.lower()
            ]
            
            logger.info(f"在类别 '{category}' 中找到 {len(matching_tools)} 个工具")
            return matching_tools
            
        except Exception as e:
            logger.error(f"按类别搜索工具时出错: {e}")
            return []
    
    def rank_tools_by_relevance(self, user_query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        根据用户查询对工具进行相关性排序
        
        Args:
            user_query (str): 用户查询
            top_k (int): 返回前K个最相关的工具
            
        Returns:
            List[Dict[str, Any]]: 排序后的工具列表
        """
        try:
            # 获取所有工具
            all_tools = self.enumerate_all_tools()
            
            if not all_tools:
                logger.warning("没有可用的工具进行排序")
                return []
            
            # 准备工具描述用于重排序
            tool_descriptions = []
            for tool in all_tools:
                tool_descriptions.append({
                    'name': tool.get('name', ''),
                    'description': tool.get('description', '')
                })
            
            # 使用reranker服务进行排序
            print("tool_descriptions:",tool_descriptions)
            print("user_query:",user_query)
            ranked_tools = self.reranker_service.rank_tools_by_relevance(
                user_query, tool_descriptions
            )
            
            if ranked_tools is None:
                logger.warning("重排序服务不可用，返回原始工具列表")
                return all_tools[:top_k]
            
            # 将排序结果与原始工具信息合并
            result_tools = []
            for ranked_tool in ranked_tools[:top_k]:
                tool_name = ranked_tool.get('name')
                # 找到对应的完整工具信息
                for original_tool in all_tools:
                    if original_tool.get('name') == tool_name:
                        enhanced_tool = original_tool.copy()
                        enhanced_tool['relevance_score'] = ranked_tool.get('relevance_score', 0.0)
                        result_tools.append(enhanced_tool)
                        break
            
            logger.info(f"为查询 '{user_query}' 返回 {len(result_tools)} 个相关工具")
            return result_tools
            
        except Exception as e:
            logger.error(f"工具相关性排序时出错: {e}")
            # 返回前top_k个工具作为备用
            all_tools = self.enumerate_all_tools()
            return all_tools[:top_k]

    async def rank_tools_by_relevance_async(self, user_query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        根据用户查询对工具进行相关性排序（异步版本）

        Args:
            user_query (str): 用户查询
            top_k (int): 返回前K个最相关的工具

        Returns:
            List[Dict[str, Any]]: 排序后的工具列表
        """
        try:
            # 获取所有工具
            all_tools = await self.enumerate_all_tools_async()

            if not all_tools:
                logger.warning("没有可用的工具进行排序")
                return []

            # 准备工具描述用于重排序
            tool_descriptions = []
            for tool in all_tools:
                tool_descriptions.append({
                    'name': tool.get('name', ''),
                    'description': tool.get('description', '')
                })

            # 使用reranker服务进行排序
            ranked_tools = self.reranker_service.rank_tools_by_relevance(
                user_query, tool_descriptions
            )

            if ranked_tools is None:
                logger.warning("重排序服务不可用，返回原始工具列表")
                return all_tools[:top_k]

            # 将排序结果与原始工具信息合并
            result_tools = []
            for ranked_tool in ranked_tools[:top_k]:
                tool_name = ranked_tool.get('name')
                # 找到对应的完整工具信息
                for original_tool in all_tools:
                    if original_tool.get('name') == tool_name:
                        enhanced_tool = original_tool.copy()
                        enhanced_tool['relevance_score'] = ranked_tool.get('relevance_score', 0.0)
                        result_tools.append(enhanced_tool)
                        break

            logger.info(f"异步为查询 '{user_query}' 返回 {len(result_tools)} 个相关工具")
            return result_tools

        except Exception as e:
            logger.error(f"异步工具相关性排序时出错: {e}")
            # 返回前top_k个工具作为备用
            all_tools = await self.enumerate_all_tools_async()
            return all_tools[:top_k]
    
    def get_top_relevant_tool(self, user_query: str) -> Optional[Dict[str, Any]]:
        """
        获取与用户查询最相关的单个工具
        
        Args:
            user_query (str): 用户查询
            
        Returns:
            Optional[Dict[str, Any]]: 最相关的工具，如果没有则返回None
        """
        try:
            ranked_tools = self.rank_tools_by_relevance(user_query, top_k=1)
            
            if ranked_tools:
                return ranked_tools[0]
            
            return None
            
        except Exception as e:
            logger.error(f"获取最相关工具时出错: {e}")
            return None
    
    async def vector_search_tools(self, user_query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        使用向量检索搜索相关工具（未来扩展功能）
        
        Args:
            user_query (str): 用户查询
            top_k (int): 返回前K个最相关的工具
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        # TODO: 实现向量检索功能
        # 目前使用重排序作为替代方案
        logger.info("向量检索功能尚未实现，使用重排序替代")
        return self.rank_tools_by_relevance(user_query, top_k)
    
    def get_tool_statistics(self) -> Dict[str, Any]:
        """
        获取工具统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            all_tools = self.enumerate_all_tools()
            
            # 按类别统计
            category_stats = {}
            server_stats = {}
            
            for tool in all_tools:
                category = tool.get('category', 'unknown')
                server = tool.get('server', 'unknown')
                
                category_stats[category] = category_stats.get(category, 0) + 1
                server_stats[server] = server_stats.get(server, 0) + 1
            
            return {
                "total_tools": len(all_tools),
                "categories": category_stats,
                "servers": server_stats,
                "cache_status": {
                    "is_cached": self._is_cache_valid(),
                    "cache_timestamp": self._cache_timestamp,
                    "cache_ttl": self._cache_ttl
                },
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取工具统计信息时出错: {e}")
            return {
                "total_tools": 0,
                "error": str(e)
            }
    
    def refresh_tool_cache(self) -> bool:
        """
        刷新工具缓存
        
        Returns:
            bool: 是否成功刷新
        """
        try:
            logger.info("刷新工具缓存")
            # 清除现有缓存
            self._tool_cache = {}
            self._cache_timestamp = None
            # 重新获取工具并更新缓存
            self.enumerate_all_tools(use_cache=True)
            return True
        except Exception as e:
            logger.error(f"刷新工具缓存时出错: {e}")
            return False

# 创建全局实例
tool_manager_service = ToolManagerService()