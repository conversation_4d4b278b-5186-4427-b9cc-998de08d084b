"""
增强的LLM服务
集成工具调用功能和上下文感知能力
"""
import asyncio
import json
import re
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
import aiohttp
from dataclasses import dataclass
from datetime import datetime, timedelta

from services.llm_service import LLMService, SpeakContent
from services.tool_manager_service import tool_manager_service
from services.mcp_service import mcp_service

logger = logging.getLogger(__name__)

@dataclass
class ToolCall:
    """工具调用数据类"""
    name: str
    parameters: Dict[str, Any]
    relevance_score: float = 0.0

@dataclass
class ToolResult:
    """工具执行结果数据类"""
    tool_name: str
    success: bool
    result: Any
    error: Optional[str] = None

@dataclass
class ConversationTurn:
    """对话轮次数据类"""
    timestamp: datetime
    user_input: str
    assistant_response: str
    tools_used: List[str]
    context_summary: str = ""
    importance_score: float = 1.0

@dataclass
class ConversationMemory:
    """对话记忆数据类"""
    session_id: str
    turns: List[ConversationTurn]
    user_preferences: Dict[str, Any]
    context_summary: str
    last_updated: datetime
    max_turns: int = 20

    def add_turn(self, user_input: str, assistant_response: str, tools_used: List[str] = None):
        """添加对话轮次"""
        turn = ConversationTurn(
            timestamp=datetime.now(),
            user_input=user_input,
            assistant_response=assistant_response,
            tools_used=tools_used or [],
            importance_score=self._calculate_importance(user_input, assistant_response)
        )

        self.turns.append(turn)

        # 保持轮次数量在限制内
        if len(self.turns) > self.max_turns:
            # 保留重要的轮次
            self.turns = self._prune_turns()

        self.last_updated = datetime.now()

    def _calculate_importance(self, user_input: str, assistant_response: str) -> float:
        """计算对话轮次的重要性分数"""
        importance = 1.0

        # 包含用户偏好信息的对话更重要
        preference_keywords = ['喜欢', '不喜欢', '偏好', '习惯', '经常', '总是', '从不']
        for keyword in preference_keywords:
            if keyword in user_input:
                importance += 0.5

        # 包含工具调用的对话更重要
        if '<TOOL_CALL>' in assistant_response:
            importance += 0.3

        # 较长的对话可能包含更多信息
        if len(user_input) > 50:
            importance += 0.2

        return min(importance, 3.0)  # 最大重要性为3.0

    def _prune_turns(self) -> List[ConversationTurn]:
        """修剪对话轮次，保留重要的"""
        # 按重要性和时间排序
        sorted_turns = sorted(self.turns, key=lambda t: (t.importance_score, t.timestamp.timestamp()), reverse=True)

        # 保留最重要的轮次和最近的轮次
        important_turns = sorted_turns[:self.max_turns//2]  # 一半重要轮次
        recent_turns = self.turns[-(self.max_turns//2):]    # 一半最近轮次

        # 合并并去重
        kept_turns = list({turn.timestamp: turn for turn in important_turns + recent_turns}.values())

        # 按时间排序
        return sorted(kept_turns, key=lambda t: t.timestamp)

    def get_context_summary(self) -> str:
        """获取上下文摘要"""
        if not self.turns:
            return ""

        # 提取用户偏好
        preferences = []
        for turn in self.turns:
            if any(keyword in turn.user_input for keyword in ['喜欢', '偏好', '习惯']):
                preferences.append(turn.user_input)

        # 提取最近的重要信息
        recent_important = [turn for turn in self.turns[-5:] if turn.importance_score > 1.5]

        summary_parts = []
        if preferences:
            summary_parts.append(f"用户偏好: {'; '.join(preferences[:3])}")

        if recent_important:
            recent_topics = [turn.user_input[:30] + "..." if len(turn.user_input) > 30 else turn.user_input
                           for turn in recent_important]
            summary_parts.append(f"最近讨论: {'; '.join(recent_topics)}")

        return " | ".join(summary_parts)

    def is_expired(self, timeout_minutes: int = 60) -> bool:
        """检查记忆是否过期"""
        return (datetime.now() - self.last_updated) > timedelta(minutes=timeout_minutes)

class EnhancedLLMService(LLMService):
    """
    增强的LLM服务类
    集成工具调用、上下文感知和记忆功能
    """

    def __init__(self, api_key: str, endpoint: str, enable_context: bool = True):
        """初始化增强LLM服务"""
        super().__init__(api_key, endpoint)
        self.tool_manager = tool_manager_service
        self.mcp_service = mcp_service

        # 工具调用配置
        self.max_tools_per_query = 5
        self.tool_relevance_threshold = 0.1  # 降低阈值，让更多工具通过
        self.enable_tool_calling = True

        # 上下文和记忆配置
        self.enable_context = enable_context
        self.conversation_memories: Dict[str, ConversationMemory] = {}
        self.memory_cleanup_interval = 60  # 清理间隔（分钟）
        self.last_cleanup = datetime.now()
        self.max_context_length = 4000  # 最大上下文长度（字符）

        logger.info(f"增强LLM服务初始化完成 (上下文功能: {'启用' if enable_context else '禁用'})")

    def _cleanup_expired_memories(self):
        """清理过期的对话记忆"""
        if (datetime.now() - self.last_cleanup).total_seconds() < self.memory_cleanup_interval * 60:
            return

        expired_sessions = []
        for session_id, memory in self.conversation_memories.items():
            if memory.is_expired():
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            del self.conversation_memories[session_id]
            logger.info(f"🧹 清理过期记忆: {session_id}")

        self.last_cleanup = datetime.now()
        if expired_sessions:
            logger.info(f"🧹 清理了 {len(expired_sessions)} 个过期记忆")

    def get_or_create_memory(self, session_id: str) -> Optional[ConversationMemory]:
        """获取或创建对话记忆"""
        if not self.enable_context:
            return None

        self._cleanup_expired_memories()

        if session_id not in self.conversation_memories:
            self.conversation_memories[session_id] = ConversationMemory(
                session_id=session_id,
                turns=[],
                user_preferences={},
                context_summary="",
                last_updated=datetime.now()
            )
            logger.info(f"🆕 创建新的对话记忆: {session_id}")

        return self.conversation_memories[session_id]

    def add_conversation_turn(self, session_id: str, user_input: str, assistant_response: str, tools_used: List[str] = None):
        """添加对话轮次到记忆"""
        if not self.enable_context:
            return

        memory = self.get_or_create_memory(session_id)
        if memory:
            memory.add_turn(user_input, assistant_response, tools_used or [])
            logger.info(f"📝 会话 {session_id} 添加对话轮次，当前轮次数: {len(memory.turns)}")

    def get_context_enhanced_prompt(self, session_id: str, user_input: str, system_prompt: str) -> str:
        """获取增强的上下文提示"""
        if not self.enable_context:
            return system_prompt

        memory = self.get_or_create_memory(session_id)
        if not memory or not memory.turns:
            return system_prompt

        # 获取上下文摘要
        context_summary = memory.get_context_summary()

        # 获取最近的对话轮次
        recent_turns = memory.turns[-3:]  # 最近3轮

        # 构建增强提示
        enhanced_parts = [system_prompt]

        if context_summary:
            enhanced_parts.append(f"\n## 对话上下文\n{context_summary}")

        if recent_turns:
            enhanced_parts.append("\n## 最近对话")
            for i, turn in enumerate(recent_turns, 1):
                enhanced_parts.append(f"第{i}轮:")
                enhanced_parts.append(f"用户: {turn.user_input}")
                enhanced_parts.append(f"助手: {turn.assistant_response[:100]}...")
                if turn.tools_used:
                    enhanced_parts.append(f"使用工具: {', '.join(turn.tools_used)}")

        enhanced_parts.append(f"\n## 当前请求\n用户: {user_input}")
        enhanced_parts.append("\n请基于以上对话上下文，提供连贯且个性化的回复。")

        enhanced_prompt = "\n".join(enhanced_parts)

        # 检查长度限制
        if len(enhanced_prompt) > self.max_context_length:
            # 如果太长，只保留系统提示和最近一轮对话
            if recent_turns:
                last_turn = recent_turns[-1]
                enhanced_prompt = f"{system_prompt}\n\n## 上一轮对话\n用户: {last_turn.user_input}\n助手: {last_turn.assistant_response[:200]}...\n\n## 当前请求\n用户: {user_input}\n\n请基于上下文提供连贯的回复。"
            else:
                enhanced_prompt = system_prompt

        return enhanced_prompt

    def get_memory_stats(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        return {
            "total_sessions": len(self.conversation_memories),
            "context_enabled": self.enable_context,
            "sessions": {
                session_id: {
                    "turns_count": len(memory.turns),
                    "last_updated": memory.last_updated.isoformat(),
                    "context_summary": memory.context_summary,
                    "is_expired": memory.is_expired()
                }
                for session_id, memory in self.conversation_memories.items()
            }
        }

    def _should_use_tools(self, user_input: str, conversation_history: List[Dict[str, str]] = None) -> bool:
        """
        判断是否应该使用工具
        
        Args:
            user_input (str): 用户输入
            conversation_history (List[Dict[str, str]]): 对话历史
            
        Returns:
            bool: 是否应该使用工具
        """
        if not self.enable_tool_calling:
            return False
        
        # 检查用户输入中的关键词
        tool_keywords = [
            '搜索', '查找', '获取', '新闻', '天气', '时间', '计算',
            'search', 'find', 'get', 'news', 'weather', 'time', 'calculate',
            '帮我', '查询', '了解', '知道', '告诉我', '火车票', '高铁', '动车',
            '车票', 'BJP', 'SHH', '到', '的', '有哪些'
        ]
        
        user_lower = user_input.lower()
        for keyword in tool_keywords:
            if keyword in user_lower:
                return True
        
        # 检查是否包含问号（询问性质）
        if '?' in user_input or '？' in user_input:
            return True
        
        return False
    
    def _extract_tool_calls_from_response(self, response_text: str) -> List[ToolCall]:
        """
        从LLM响应中提取工具调用
        
        Args:
            response_text (str): LLM响应文本
            
        Returns:
            List[ToolCall]: 提取的工具调用列表
        """
        tool_calls = []
        
        try:
            # 查找工具调用模式
            tool_pattern = r'<TOOL_CALL>\s*name:\s*([^\n]+)\s*parameters:\s*({[^}]*})\s*</TOOL_CALL>'
            matches = re.findall(tool_pattern, response_text, re.DOTALL)
            
            for match in matches:
                tool_name = match[0].strip()
                try:
                    parameters = json.loads(match[1])
                    tool_calls.append(ToolCall(
                        name=tool_name,
                        parameters=parameters
                    ))
                except json.JSONDecodeError:
                    logger.warning(f"无法解析工具参数: {match[1]}")
            
        except Exception as e:
            logger.error(f"提取工具调用时出错: {e}")
        
        return tool_calls
    
    async def _execute_tool_call(self, tool_call: ToolCall) -> ToolResult:
        """
        执行单个工具调用

        Args:
            tool_call (ToolCall): 工具调用

        Returns:
            ToolResult: 工具执行结果
        """
        try:
            logger.info(f"执行工具: {tool_call.name}, 参数: {tool_call.parameters}")

            # 使用异步方法获取工具信息
            tool_info = await self.tool_manager.get_tool_by_name_async(tool_call.name)
            if not tool_info:
                return ToolResult(
                    tool_name=tool_call.name,
                    success=False,
                    result=None,
                    error=f"工具 {tool_call.name} 不存在"
                )

            # 获取正确的服务器名称
            server_name = tool_info.get('server')
            logger.info(f"工具 {tool_call.name} 来自服务器: {server_name}")

            # 使用异步方法执行工具
            execution_result = await self.mcp_service.execute_tool_async(
                tool_call.name,
                server_name,
                **tool_call.parameters
            )

            if execution_result.get('success', False):
                return ToolResult(
                    tool_name=tool_call.name,
                    success=True,
                    result=execution_result.get('result')
                )
            else:
                return ToolResult(
                    tool_name=tool_call.name,
                    success=False,
                    result=None,
                    error=execution_result.get('error', '工具执行失败')
                )

        except Exception as e:
            logger.error(f"执行工具 {tool_call.name} 时出错: {e}")
            return ToolResult(
                tool_name=tool_call.name,
                success=False,
                result=None,
                error=str(e)
            )
    
    async def _execute_multiple_tools(self, tool_calls: List[ToolCall]) -> List[ToolResult]:
        """
        并行执行多个工具调用
        
        Args:
            tool_calls (List[ToolCall]): 工具调用列表
            
        Returns:
            List[ToolResult]: 工具执行结果列表
        """
        if not tool_calls:
            return []
        
        # 并行执行所有工具调用
        tasks = [self._execute_tool_call(tool_call) for tool_call in tool_calls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        tool_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                tool_results.append(ToolResult(
                    tool_name=tool_calls[i].name,
                    success=False,
                    result=None,
                    error=str(result)
                ))
            else:
                tool_results.append(result)
        
        return tool_results
    
    def _build_tool_context(self, tool_results: List[ToolResult]) -> str:
        """
        构建工具执行结果的上下文
        
        Args:
            tool_results (List[ToolResult]): 工具执行结果列表
            
        Returns:
            str: 工具上下文字符串
        """
        if not tool_results:
            return ""
        
        context_parts = ["以下是工具执行结果："]
        
        for result in tool_results:
            if result.success:
                context_parts.append(f"工具 {result.tool_name} 执行成功：")
                context_parts.append(json.dumps(result.result, ensure_ascii=False, indent=2))
            else:
                context_parts.append(f"工具 {result.tool_name} 执行失败：{result.error}")
        
        context_parts.append("请基于以上工具结果回答用户问题。")
        
        return "\n".join(context_parts)
    
    async def _get_relevant_tools_for_llm(self, user_input: str) -> List[Dict[str, Any]]:
        """
        获取与用户输入相关的工具，用于LLM的function calling（异步版本）

        Args:
            user_input (str): 用户输入

        Returns:
            List[Dict[str, Any]]: 工具定义列表
        """
        try:
            # 获取相关工具（使用异步方法）
            relevant_tools = await self.tool_manager.rank_tools_by_relevance_async(
                user_input,
                top_k=self.max_tools_per_query
            )
            
            # 转换为OpenAI function calling格式
            function_definitions = []
            for tool in relevant_tools:
                # 使用前5个工具，不进行阈值过滤
                # if tool.get('relevance_score', 0) < self.tool_relevance_threshold:
                #     continue

                # 获取参数信息，优先使用inputSchema，然后是parameters
                input_schema = tool.get('inputSchema', {})
                parameters_info = tool.get('parameters', {})

                # 如果有inputSchema，使用它；否则使用parameters
                if input_schema and 'properties' in input_schema:
                    properties = input_schema.get('properties', {})
                    required = input_schema.get('required', [])
                else:
                    # 从parameters字段构建properties和required
                    properties = {}
                    required = []
                    for param_name, param_info in parameters_info.items():
                        if isinstance(param_info, dict):
                            properties[param_name] = {
                                "type": param_info.get('type', 'string'),
                                "description": param_info.get('description', '')
                            }
                            if param_info.get('required', False):
                                required.append(param_name)

                function_def = {
                    "type": "function",
                    "function": {
                        "name": tool.get('name', ''),
                        "description": tool.get('description', ''),
                        "parameters": {
                            "type": "object",
                            "properties": properties,
                            "required": required
                        }
                    }
                }

                function_definitions.append(function_def)
            
            logger.info(f"为用户输入选择了 {len(function_definitions)} 个相关工具")
            return function_definitions
            
        except Exception as e:
            logger.error(f"获取相关工具时出错: {e}")
            return []
    
    async def generate_response_with_tools(self,
                                         user_input: str,
                                         conversation_history: List[Dict[str, str]],
                                         system_prompt: str,
                                         session_id: str = None) -> Dict[str, Any]:
        """
        生成带工具调用的响应，支持上下文感知

        Args:
            user_input (str): 用户输入
            conversation_history (List[Dict[str, str]]): 对话历史
            system_prompt (str): 系统提示词
            session_id (str): 会话ID，用于上下文管理

        Returns:
            Dict[str, Any]: 响应结果
        """
        try:
            logger.info(f"🚀 生成带工具调用的响应: {user_input} (会话ID: {session_id})")

            # 获取或创建对话记忆
            memory = None
            enhanced_system_prompt = system_prompt
            if self.enable_context and session_id:
                memory = self.get_or_create_memory(session_id)
                enhanced_system_prompt = self.get_context_enhanced_prompt(session_id, user_input, system_prompt)
                logger.info(f"🧠 使用增强上下文提示，记忆轮次: {len(memory.turns) if memory else 0}")

            # 判断是否需要使用工具
            should_use_tools = self._should_use_tools(user_input, conversation_history)
            
            if not should_use_tools:
                logger.info("不需要使用工具，直接生成响应")
                result = await self.generate_response(user_input, conversation_history, enhanced_system_prompt, use_tools=False)

                # 更新对话记忆
                if memory and result.get('success'):
                    self.add_conversation_turn(session_id, user_input, result.get('full_response', ''), [])

                return result
            
            # 获取相关工具
            relevant_tools = await self._get_relevant_tools_for_llm(user_input)
            
            # 详细打印工具信息
            logger.info(f"🔧 为LLM准备的工具数量: {len(relevant_tools)}")
            if relevant_tools:
                logger.info("📋 工具详细信息 (OpenAI格式):")
                for i, tool in enumerate(relevant_tools):
                    logger.info(f"   {i+1}. 工具名称: {tool['function']['name']}")
                    logger.info(f"      工具描述: {tool['function']['description']}")
                    logger.info(f"      参数结构: {json.dumps(tool['function']['parameters'], ensure_ascii=False, indent=8)}")
            else:
                logger.warning("⚠️ 没有找到相关工具，直接生成响应")
                result = await self.generate_response(user_input, conversation_history, enhanced_system_prompt, use_tools=False)

                # 更新对话记忆
                if memory and result.get('success'):
                    self.add_conversation_turn(session_id, user_input, result.get('full_response', ''), [])

                return result

            # 构建消息（使用增强的系统提示）
            messages = self.build_conversation_messages(conversation_history, enhanced_system_prompt)
            messages.append({"role": "user", "content": user_input})
            
            logger.info(f"📤 调用LLM API，工具数量: {len(relevant_tools)}")
            # 调用LLM API with function calling
            print(f"relevant_tools:{relevant_tools}")
            response = await self.get_chat_completion(messages, tools=relevant_tools)
            print(f"response:{response}")



            if "error" in response:
                logger.warning(f"LLM API调用失败，使用备用方案: {response['error']}")
                return await self.generate_response(user_input, conversation_history, system_prompt, use_tools=False)
            
            # 检查是否有工具调用
            if "choices" in response and len(response["choices"]) > 0:
                choice = response["choices"][0]
                message = choice.get("message", {})
                
                # 处理工具调用
                tool_calls = message.get("tool_calls", [])
                if tool_calls:
                    logger.info(f"🎯 检测到 {len(tool_calls)} 个工具调用")
                    
                    # 打印工具调用详情
                    for i, tool_call in enumerate(tool_calls):
                        function_name = tool_call.get("function", {}).get("name", "unknown")
                        arguments = tool_call.get("function", {}).get("arguments", "{}")
                        logger.info(f"   {i+1}. 调用工具: {function_name}")
                        logger.info(f"      参数: {arguments}")
                    
                    # 执行工具调用
                    logger.info("🚀 开始执行工具调用...")
                    tool_results = await self._process_function_calls(tool_calls)
                    
                    # 打印工具执行结果
                    logger.info(f"📊 工具执行完成，结果数量: {len(tool_results)}")
                    for i, result in enumerate(tool_results):
                        logger.info(f"   {i+1}. 工具: {result.get('name', 'unknown')}")
                        logger.info(f"      执行状态: {'成功' if 'error' not in result.get('content', '') else '失败'}")
                        logger.info(f"      结果内容: {result.get('content', '')[:200]}...")
                    
                    # 将工具结果添加到对话中
                    messages.append(message)
                    for result in tool_results:
                        messages.append(result)
                    
                    # 再次调用LLM生成最终响应
                    final_response = await self.get_chat_completion(messages)
                    
                    if "choices" in final_response and len(final_response["choices"]) > 0:
                        final_content = final_response["choices"][0]["message"]["content"]
                        
                        # 提取SPEAK内容
                        speak_content = self.extract_speak_content(final_content)

                        # 更新对话记忆
                        tools_used = [tc["function"]["name"] for tc in tool_calls]
                        if memory:
                            self.add_conversation_turn(session_id, user_input, final_content, tools_used)

                        result = {
                            "success": True,
                            "full_response": final_content,
                            "speak_content": {
                                "emotion": speak_content.emotion if speak_content else "neutral",
                                "speed": speak_content.speed if speak_content else 1.0,
                                "text": speak_content.text if speak_content else final_content
                            },
                            "tool_calls_made": len(tool_calls),
                            "tools_used": tools_used,
                            "mock_mode": False,
                            "context_used": bool(memory),
                            "session_id": session_id
                        }

                        return result
                
                # 没有工具调用，直接返回响应
                content = message.get("content", "")
                speak_content = self.extract_speak_content(content)

                # 更新对话记忆
                if memory:
                    self.add_conversation_turn(session_id, user_input, content, [])

                return {
                    "success": True,
                    "full_response": content,
                    "speak_content": {
                        "emotion": speak_content.emotion if speak_content else "neutral",
                        "speed": speak_content.speed if speak_content else 1.0,
                        "text": speak_content.text if speak_content else content
                    },
                    "tool_calls_made": 0,
                    "tools_used": [],
                    "mock_mode": False,
                    "context_used": bool(memory),
                    "session_id": session_id
                }
            
            # 如果没有有效响应，使用备用方案
            logger.warning("LLM响应格式异常，使用备用方案")
            result = await self.generate_response(user_input, conversation_history, enhanced_system_prompt, use_tools=False)

            # 更新对话记忆
            if memory and result.get('success'):
                self.add_conversation_turn(session_id, user_input, result.get('full_response', ''), [])

            return result

        except Exception as e:
            logger.error(f"生成带工具调用的响应时出错: {e}")
            # 使用备用方案
            result = await self.generate_response(user_input, conversation_history, enhanced_system_prompt, use_tools=False)

            # 更新对话记忆
            if memory and result.get('success'):
                self.add_conversation_turn(session_id, user_input, result.get('full_response', ''), [])

            return result
    
    async def _process_function_calls(self, tool_calls: List[Dict]) -> List[Dict]:
        """
        处理函数调用并返回结果消息

        Args:
            tool_calls (List[Dict]): 工具调用列表

        Returns:
            List[Dict]: 工具结果消息列表
        """
        results = []

        for tool_call in tool_calls:
            try:
                function_name = tool_call["function"]["name"]
                arguments = json.loads(tool_call["function"]["arguments"])

                # 获取工具信息以确定正确的服务器
                tool_info = await self.tool_manager.get_tool_by_name_async(function_name)
                server_name = tool_info.get('server') if tool_info else None

                logger.info(f"执行工具 {function_name}，服务器: {server_name}，参数: {arguments}")

                # 使用异步方法执行工具
                execution_result = await self.mcp_service.execute_tool_async(
                    function_name,
                    server_name,
                    **arguments
                )

                # 构建结果消息
                result_content = json.dumps(execution_result, ensure_ascii=False)

                results.append({
                    "tool_call_id": tool_call["id"],
                    "role": "tool",
                    "name": function_name,
                    "content": result_content
                })

            except Exception as e:
                logger.error(f"处理工具调用时出错: {e}")
                results.append({
                    "tool_call_id": tool_call.get("id", "unknown"),
                    "role": "tool",
                    "name": tool_call.get("function", {}).get("name", "unknown"),
                    "content": json.dumps({"error": str(e)}, ensure_ascii=False)
                })

        return results
    
    async def generate_response_stream_with_tools(self,
                                                user_input: str,
                                                conversation_history: List[Dict[str, str]],
                                                system_prompt: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        生成带工具调用的流式响应
        
        Args:
            user_input (str): 用户输入
            conversation_history (List[Dict[str, str]]): 对话历史
            system_prompt (str): 系统提示词
            
        Yields:
            Dict[str, Any]: 流式响应数据
        """
        try:
            # 首先检查是否需要工具调用
            should_use_tools = self._should_use_tools(user_input, conversation_history)
            
            if not should_use_tools:
                # 不需要工具，直接流式响应
                async for chunk in self.generate_response_stream(user_input, conversation_history, system_prompt, use_tools=False):
                    yield chunk
                return
            
            # 需要工具调用，先获取完整响应
            response = await self.generate_response_with_tools(user_input, conversation_history, system_prompt)
            
            # 模拟流式输出
            if response.get("success"):
                full_text = response["speak_content"]["text"]
                
                # 分块发送
                chunk_size = 10
                for i in range(0, len(full_text), chunk_size):
                    chunk = full_text[i:i + chunk_size]
                    yield {
                        "type": "chunk",
                        "content": chunk,
                        "full_response": full_text[:i + len(chunk)]
                    }
                    await asyncio.sleep(0.05)  # 模拟延迟
                
                # 发送最终结果
                yield {
                    "type": "speak",
                    "emotion": response["speak_content"]["emotion"],
                    "speed": response["speak_content"]["speed"],
                    "text": response["speak_content"]["text"],
                    "full_response": response["full_response"],
                    "tool_calls_made": response.get("tool_calls_made", 0),
                    "tools_used": response.get("tools_used", [])
                }
                
                yield {
                    "type": "complete",
                    "full_response": response["full_response"]
                }
            else:
                yield {
                    "type": "error",
                    "error": "生成响应失败"
                }
                
        except Exception as e:
            logger.error(f"生成流式响应时出错: {e}")
            yield {
                "type": "error",
                "error": str(e)
            }
    
    def get_tool_usage_statistics(self) -> Dict[str, Any]:
        """
        获取工具使用统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.tool_manager.get_tool_statistics()
    
    def refresh_tools(self) -> bool:
        """
        刷新工具缓存
        
        Returns:
            bool: 是否成功刷新
        """
        return self.tool_manager.refresh_tool_cache()

# 创建全局实例
enhanced_llm_service = None

def get_enhanced_llm_service(api_key: str = None, endpoint: str = None, enable_context: bool = True) -> EnhancedLLMService:
    """获取增强LLM服务实例"""
    global enhanced_llm_service

    if enhanced_llm_service is None:
        import os
        api_key = api_key or os.getenv("VOLCANO_API_KEY")
        endpoint = endpoint or os.getenv("VOLCANO_ENDPOINT")
        enhanced_llm_service = EnhancedLLMService(api_key, endpoint, enable_context)

    return enhanced_llm_service