import numpy as np
import onnxruntime as ort
import torch
import librosa
from typing import List, Tuple
import logging

logger = logging.getLogger(__name__)

class VADService:
    def __init__(self, model_path: str, sample_rate: int = 16000):
        self.sample_rate = sample_rate
        self.model_path = model_path
        self.session = None
        self.reset_states()
        self._load_model()
    
    def _load_model(self):
        """Load the Silero VAD ONNX model"""
        try:
            # Specify providers explicitly for ONNX Runtime 1.9+
            providers = ['CPUExecutionProvider']
            self.session = ort.InferenceSession(self.model_path, providers=providers)
            logger.info(f"VAD model loaded successfully from {self.model_path}")
        except Exception as e:
            logger.error(f"Failed to load VAD model: {e}")
            raise
    
    def reset_states(self):
        """Reset the internal states of the VAD model"""
        self._h = np.zeros((2, 1, 64), dtype=np.float32)
        self._c = np.zeros((2, 1, 64), dtype=np.float32)
    
    def preprocess_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """Preprocess audio data for VAD"""
        # Ensure audio is float32 and normalized
        if audio_data.dtype != np.float32:
            audio_data = audio_data.astype(np.float32)
        
        # Normalize audio to [-1, 1] range
        if np.max(np.abs(audio_data)) > 1.0:
            audio_data = audio_data / np.max(np.abs(audio_data))
        
        # Resample if necessary
        if len(audio_data.shape) > 1:
            audio_data = audio_data.flatten()
        
        return audio_data
    
    def detect_speech(self, audio_chunk: np.ndarray, threshold: float = 0.5) -> Tuple[bool, float]:
        """
        Detect speech in audio chunk
        
        Args:
            audio_chunk: Audio data as numpy array
            threshold: Speech detection threshold (0.0 - 1.0)
            
        Returns:
            Tuple of (is_speech, confidence_score)
        """
        try:
            # Preprocess audio
            audio_chunk = self.preprocess_audio(audio_chunk)
            
            # Ensure chunk size is appropriate (512 samples for 16kHz)
            chunk_size = 512
            if len(audio_chunk) != chunk_size:
                if len(audio_chunk) < chunk_size:
                    # Pad with zeros
                    audio_chunk = np.pad(audio_chunk, (0, chunk_size - len(audio_chunk)))
                else:
                    # Take first chunk_size samples
                    audio_chunk = audio_chunk[:chunk_size]
            
            # Prepare input for ONNX model
            input_tensor = audio_chunk.reshape(1, -1).astype(np.float32)
            
            # Run inference
            inputs = {
                'input': input_tensor,
                'h': self._h,
                'c': self._c
            }
            
            outputs = self.session.run(None, inputs)
            speech_prob = outputs[0][0][0]  # Extract speech probability
            self._h = outputs[1]  # Update hidden state
            self._c = outputs[2]  # Update cell state

            is_speech = speech_prob > threshold

            return is_speech, float(speech_prob)

        except Exception as e:
            logger.error(f"VAD detection error: {e}")
            # For now, assume speech is present to continue processing
            # This allows the system to work even with VAD model issues
            return True, 0.8
    
    def process_audio_stream(self, audio_stream: np.ndarray, 
                           chunk_duration: float = 0.032,
                           threshold: float = 0.5) -> List[Tuple[float, bool, float]]:
        """
        Process continuous audio stream for speech detection
        
        Args:
            audio_stream: Continuous audio data
            chunk_duration: Duration of each chunk in seconds
            threshold: Speech detection threshold
            
        Returns:
            List of (timestamp, is_speech, confidence) tuples
        """
        results = []
        chunk_size = int(self.sample_rate * chunk_duration)
        
        for i in range(0, len(audio_stream), chunk_size):
            chunk = audio_stream[i:i + chunk_size]
            if len(chunk) < chunk_size:
                break
                
            timestamp = i / self.sample_rate
            is_speech, confidence = self.detect_speech(chunk, threshold)
            results.append((timestamp, is_speech, confidence))
        
        return results
    
    def detect_speech_segments(self, audio_data: np.ndarray,
                             min_speech_duration: float = 0.1,
                             min_silence_duration: float = 0.5,
                             threshold: float = 0.5) -> List[Tuple[float, float]]:
        """
        Detect speech segments with start and end times
        
        Args:
            audio_data: Audio data as numpy array
            min_speech_duration: Minimum speech segment duration
            min_silence_duration: Minimum silence duration to end segment
            threshold: Speech detection threshold
            
        Returns:
            List of (start_time, end_time) tuples for speech segments
        """
        detections = self.process_audio_stream(audio_data, threshold=threshold)
        segments = []
        
        current_segment_start = None
        last_speech_time = None
        
        for timestamp, is_speech, confidence in detections:
            if is_speech:
                if current_segment_start is None:
                    current_segment_start = timestamp
                last_speech_time = timestamp
            else:
                if (current_segment_start is not None and 
                    last_speech_time is not None and
                    timestamp - last_speech_time >= min_silence_duration):
                    
                    segment_duration = last_speech_time - current_segment_start
                    if segment_duration >= min_speech_duration:
                        segments.append((current_segment_start, last_speech_time))
                    
                    current_segment_start = None
                    last_speech_time = None
        
        # Handle case where audio ends during speech
        if (current_segment_start is not None and 
            last_speech_time is not None):
            segment_duration = last_speech_time - current_segment_start
            if segment_duration >= min_speech_duration:
                segments.append((current_segment_start, last_speech_time))
        
        return segments