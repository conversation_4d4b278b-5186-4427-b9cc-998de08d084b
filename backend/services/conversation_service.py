import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from supabase import Client

logger = logging.getLogger(__name__)

class ConversationService:
    def __init__(self, supabase_client: Client):
        self.supabase = supabase_client
    
    async def save_conversation_turn(
        self,
        session_id: str,
        messages: List[Dict[str, Any]]
    ) -> bool:
        """
        Save a complete conversation turn with support for 4 roles:
        - user: User input
        - assistant: AI response
        - developer: System/debug messages
        - tool: Tool call results
        """
        try:
            conversation_messages = []
            
            for message in messages:
                role = message.get('role')
                content = message.get('content', '')
                
                # Validate role
                if role not in ['user', 'assistant', 'developer', 'tool']:
                    logger.warning(f"Invalid role: {role}, skipping message")
                    continue
                
                message_data = {
                    'session_id': session_id,
                    'role': role,
                    'content': content,
                    'created_at': datetime.now().isoformat()
                }
                
                # Handle tool calls for assistant messages
                if role == 'assistant' and 'tool_calls' in message:
                    message_data['tool_calls'] = message['tool_calls']
                
                # Handle tool call responses
                if role == 'tool' and 'tool_call_id' in message:
                    message_data['tool_call_id'] = message['tool_call_id']
                
                # Handle audio-specific fields for assistant messages
                if role == 'assistant':
                    if 'emotion' in message:
                        message_data['emotion'] = message['emotion']
                    if 'speed' in message:
                        message_data['speed'] = float(message['speed'])
                    if 'audio_url' in message:
                        message_data['audio_url'] = message['audio_url']
                
                conversation_messages.append(message_data)
            
            # Batch insert messages
            if conversation_messages:
                result = self.supabase.table("conversation_messages").insert(conversation_messages).execute()
                return len(result.data) > 0
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving conversation turn: {e}")
            return False
    
    async def get_conversation_history(
        self,
        session_id: str,
        limit: int = 50,
        include_system: bool = False
    ) -> List[Dict[str, Any]]:
        """Get conversation history for a session"""
        try:
            query = self.supabase.table("conversation_messages").select("*").eq("session_id", session_id)
            
            if not include_system:
                query = query.in_("role", ["user", "assistant"])
            
            result = query.order("created_at", desc=False).limit(limit).execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    async def format_for_llm(
        self,
        session_id: str,
        system_prompt: str,
        limit: int = 20
    ) -> List[Dict[str, str]]:
        """Format conversation history for LLM input"""
        try:
            history = await self.get_conversation_history(session_id, limit, include_system=False)
            
            # Start with system message
            formatted_messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # Add conversation history
            for msg in history:
                role = msg['role']
                content = msg['content']
                
                # Only include user and assistant messages for LLM
                if role in ['user', 'assistant']:
                    formatted_messages.append({
                        "role": role,
                        "content": content
                    })
            
            return formatted_messages
            
        except Exception as e:
            logger.error(f"Error formatting conversation for LLM: {e}")
            return [{"role": "system", "content": system_prompt}]
    
    async def extract_speak_content(self, llm_response: str) -> Dict[str, Any]:
        """
        Extract SPEAK content from LLM response following the SFT training format:
        <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>content</text></SPEAK>
        """
        try:
            import re
            
            # Extract SPEAK block
            speak_pattern = r'<SPEAK><emotion>([^<]+)</emotion><speed>([^<]+)</speed><text>([^<]+)</text></SPEAK>'
            match = re.search(speak_pattern, llm_response)
            
            if match:
                emotion = match.group(1).strip()
                speed = float(match.group(2).strip())
                text = match.group(3).strip()
                
                return {
                    "success": True,
                    "emotion": emotion,
                    "speed": speed,
                    "text": text,
                    "raw_response": llm_response
                }
            else:
                # Fallback: use the entire response as text
                return {
                    "success": False,
                    "emotion": "neutral",
                    "speed": 1.0,
                    "text": llm_response.strip(),
                    "raw_response": llm_response
                }
                
        except Exception as e:
            logger.error(f"Error extracting SPEAK content: {e}")
            return {
                "success": False,
                "emotion": "neutral",
                "speed": 1.0,
                "text": "Sorry, I encountered an error processing the response.",
                "raw_response": llm_response
            }
    
    async def create_sft_training_entry(
        self,
        session_id: str,
        user_input: str,
        assistant_response: str,
        system_prompt: str,
        tool_calls: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """
        Create an entry in SFT training format based on your provided example
        """
        try:
            # Get recent conversation history for context
            history = await self.get_conversation_history(session_id, limit=10)
            
            messages = [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user", 
                    "content": user_input
                },
                {
                    "role": "assistant",
                    "content": assistant_response
                }
            ]
            
            # Add tool calls if present
            if tool_calls:
                messages[-1]["tool_calls"] = tool_calls
                
                # Add tool responses
                for tool_call in tool_calls:
                    messages.append({
                        "role": "tool",
                        "content": "Tool execution result",  # This would be the actual tool result
                        "tool_call_id": tool_call.get("id", "")
                    })
            
            return {
                "messages": messages,
                "timestamp": datetime.now().isoformat(),
                "session_id": session_id
            }
            
        except Exception as e:
            logger.error(f"Error creating SFT training entry: {e}")
            return {}
    
    async def log_developer_message(
        self,
        session_id: str,
        message: str,
        level: str = "info"
    ) -> bool:
        """Log developer/system messages for debugging"""
        try:
            result = self.supabase.table("conversation_messages").insert({
                "session_id": session_id,
                "role": "developer",
                "content": f"[{level.upper()}] {message}",
                "created_at": datetime.now().isoformat()
            }).execute()
            
            return len(result.data) > 0
            
        except Exception as e:
            logger.error(f"Error logging developer message: {e}")
            return False