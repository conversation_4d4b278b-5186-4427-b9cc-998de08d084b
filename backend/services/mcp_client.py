"""
简化的MCP客户端实现
用于连接和管理MCP服务器，不依赖复杂的MCP库
"""
import asyncio
import logging
import json
import subprocess
import os
import aiohttp
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)

class SimpleMCPClient:
    """简化的MCP客户端类，用于管理MCP服务器连接"""

    def __init__(self):
        """初始化MCP客户端"""
        self.active_processes: Dict[str, subprocess.Popen] = {}
        self.server_configs: Dict[str, Dict[str, Any]] = {}
        self._load_server_configs()
        logger.info("简化MCP客户端初始化完成")
    
    def _load_server_configs(self):
        """加载MCP服务器配置"""
        try:
            # 尝试不同的配置目录路径
            config_dir = Path("mcp_config")
            if not config_dir.exists():
                config_dir = Path("backend/mcp_config")

            if not config_dir.exists():
                logger.warning(f"MCP配置目录不存在: {config_dir}")
                return
            
            # 加载所有JSON配置文件
            for config_file in config_dir.glob("*.json"):
                if config_file.name == "multi_server_config.json":
                    continue  # 跳过多服务器配置文件
                
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 解析配置文件
                    mcp_servers = config.get("mcpServers", {})
                    for server_name, server_config in mcp_servers.items():
                        # 检查服务器类型
                        server_type = server_config.get("type", "local_command")

                        if server_type == "streamableHttp":
                            # HTTP类型的MCP服务器
                            self.server_configs[server_name] = {
                                "name": server_name,
                                "type": "streamableHttp",
                                "url": server_config.get("url"),
                                "headers": server_config.get("headers", {}),
                                "config_file": str(config_file)
                            }
                        else:
                            # 本地命令类型的MCP服务器
                            self.server_configs[server_name] = {
                                "name": server_name,
                                "type": "local_command",
                                "command": server_config.get("command"),
                                "args": server_config.get("args", []),
                                "env": server_config.get("env", {}),
                                "config_file": str(config_file)
                            }
                        logger.info(f"加载MCP服务器配置: {server_name} (类型: {server_type})")
                
                except Exception as e:
                    logger.error(f"加载配置文件 {config_file} 失败: {e}")
            
            logger.info(f"总共加载了 {len(self.server_configs)} 个MCP服务器配置")
            
        except Exception as e:
            logger.error(f"加载MCP服务器配置失败: {e}")
    
    async def start_server(self, server_name: str) -> bool:
        """启动MCP服务器"""
        try:
            if server_name not in self.server_configs:
                logger.error(f"未找到服务器配置: {server_name}")
                return False

            if server_name in self.active_processes:
                logger.info(f"服务器 {server_name} 已经在运行")
                return True

            config = self.server_configs[server_name]
            server_type = config.get("type", "local_command")

            if server_type == "streamableHttp":
                # HTTP类型的MCP服务器，测试连接
                logger.info(f"测试HTTP MCP服务器连接: {server_name}")
                url = config.get("url")
                headers = config.get("headers", {})

                if not url:
                    logger.error(f"HTTP MCP服务器 {server_name} 缺少URL配置")
                    return False

                # 测试连接 - 发送一个简单的MCP请求来测试
                try:
                    test_request = {
                        "jsonrpc": "2.0",
                        "id": 0,
                        "method": "tools/list",
                        "params": {}
                    }

                    async with aiohttp.ClientSession() as session:
                        async with session.post(url, json=test_request, headers=headers, timeout=10) as response:
                            if response.status == 200:
                                logger.info(f"HTTP MCP服务器 {server_name} 连接成功")
                                # 对于HTTP服务器，我们不需要启动进程，只需要标记为活跃
                                self.active_processes[server_name] = "http_active"
                                return True
                            else:
                                logger.error(f"HTTP MCP服务器 {server_name} 连接失败，状态码: {response.status}")
                                return False
                except Exception as http_error:
                    logger.error(f"HTTP MCP服务器 {server_name} 连接失败: {http_error}")
                    return False

            else:
                # 本地命令类型的MCP服务器
                if not config.get("command"):
                    logger.error(f"本地MCP服务器 {server_name} 缺少命令配置")
                    return False

                # 构建命令
                command = [config["command"]] + config.get("args", [])
                env = {**os.environ, **config.get("env", {})}

                # 启动服务器进程
                logger.info(f"启动MCP服务器: {server_name}, 命令: {' '.join(command)}")
                process = subprocess.Popen(
                    command,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    env=env,
                    text=True,
                    bufsize=0
                )

                # 保存进程
                self.active_processes[server_name] = process
                logger.info(f"MCP服务器 {server_name} 启动成功，PID: {process.pid}")
                return True

        except Exception as e:
            logger.error(f"启动MCP服务器 {server_name} 失败: {e}")
            return False
    
    async def stop_server(self, server_name: str) -> bool:
        """停止MCP服务器"""
        try:
            if server_name not in self.active_processes:
                logger.warning(f"服务器 {server_name} 未运行")
                return True

            process_or_marker = self.active_processes[server_name]

            if process_or_marker == "http_active":
                # HTTP类型的服务器，只需要从活跃列表中移除
                del self.active_processes[server_name]
                logger.info(f"HTTP MCP服务器 {server_name} 已断开连接")
                return True
            else:
                # 本地进程类型的服务器
                process = process_or_marker

                # 终止进程
                process.terminate()

                # 等待进程结束
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning(f"服务器 {server_name} 未能正常终止，强制杀死")
                    process.kill()
                    process.wait()

                # 移除进程
                del self.active_processes[server_name]
                logger.info(f"MCP服务器 {server_name} 已停止")
                return True

        except Exception as e:
            logger.error(f"停止MCP服务器 {server_name} 失败: {e}")
            return False
    
    async def get_server_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """获取服务器的工具列表"""
        try:
            # 确保服务器已启动
            if not await self.start_server(server_name):
                return []

            config = self.server_configs[server_name]
            server_type = config.get("type", "local_command")
            process_or_marker = self.active_processes[server_name]

            if server_type == "streamableHttp":
                # HTTP类型的MCP服务器
                return await self._get_http_server_tools(server_name, config)
            else:
                # 本地命令类型的MCP服务器
                return await self._get_local_server_tools(server_name, process_or_marker)

        except Exception as e:
            logger.error(f"获取服务器 {server_name} 工具列表失败: {e}")
            return []

    async def _get_http_server_tools(self, server_name: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取HTTP类型服务器的工具列表"""
        try:
            url = config.get("url")
            headers = config.get("headers", {})

            # 发送list_tools请求
            request_data = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/list",
                "params": {}
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=request_data, headers=headers, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"HTTP服务器 {server_name} 工具列表请求失败，状态码: {response.status}")
                        return []

                    response_data = await response.json()
                    return self._parse_tools_response(response_data, server_name)

        except Exception as e:
            logger.error(f"获取HTTP服务器 {server_name} 工具列表失败: {e}")
            return []

    async def _get_local_server_tools(self, server_name: str, process) -> List[Dict[str, Any]]:
        """获取本地命令类型服务器的工具列表"""
        try:
            # 发送list_tools请求
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/list",
                "params": {}
            }

            # 发送请求
            request_json = json.dumps(request) + "\n"
            process.stdin.write(request_json)
            process.stdin.flush()

            # 读取响应
            response_line = process.stdout.readline()
            if not response_line:
                logger.error(f"从服务器 {server_name} 未收到响应")
                return []

            response_data = json.loads(response_line.strip())
            return self._parse_tools_response(response_data, server_name)

        except Exception as e:
            logger.error(f"获取本地服务器 {server_name} 工具列表失败: {e}")
            return []

    def _parse_tools_response(self, response_data: Dict[str, Any], server_name: str) -> List[Dict[str, Any]]:
        """解析工具列表响应"""
        tools = []
        try:
            if "result" in response_data and "tools" in response_data["result"]:
                for tool in response_data["result"]["tools"]:
                    tool_dict = {
                        "name": tool.get("name", ""),
                        "description": tool.get("description", ""),
                        "server": server_name,
                        "category": "mcp",
                        "inputSchema": tool.get("inputSchema", {}),
                    }
                    tools.append(tool_dict)

            logger.info(f"从服务器 {server_name} 获取到 {len(tools)} 个工具")
            return tools
        except Exception as e:
            logger.error(f"解析服务器 {server_name} 工具列表失败: {e}")
            return []
    
    async def execute_tool(self, tool_name: str, server_name: str, **parameters) -> Dict[str, Any]:
        """执行MCP工具"""
        try:
            # 确保服务器已启动
            if not await self.start_server(server_name):
                return {
                    "success": False,
                    "error": f"无法启动服务器 {server_name}",
                    "timestamp": datetime.now().isoformat()
                }

            config = self.server_configs[server_name]
            server_type = config.get("type", "local_command")
            process_or_marker = self.active_processes[server_name]

            if server_type == "streamableHttp":
                # HTTP类型的MCP服务器
                return await self._execute_http_tool(tool_name, server_name, config, **parameters)
            else:
                # 本地命令类型的MCP服务器
                return await self._execute_local_tool(tool_name, server_name, process_or_marker, **parameters)

        except Exception as e:
            logger.error(f"执行工具 {tool_name} 失败: {e}")
            return {
                "success": False,
                "tool_name": tool_name,
                "server": server_name,
                "parameters": parameters,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            }

    async def _execute_http_tool(self, tool_name: str, server_name: str, config: Dict[str, Any], **parameters) -> Dict[str, Any]:
        """执行HTTP类型的MCP工具"""
        try:
            url = config.get("url")
            headers = config.get("headers", {})

            # 构建工具调用请求
            request_data = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": parameters
                }
            }

            logger.info(f"执行HTTP工具: {tool_name} on {server_name}, 参数: {parameters}")

            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=request_data, headers=headers, timeout=30) as response:
                    if response.status != 200:
                        return {
                            "success": False,
                            "error": f"HTTP请求失败，状态码: {response.status}",
                            "timestamp": datetime.now().isoformat()
                        }

                    response_data = await response.json()
                    return self._process_tool_response(response_data, tool_name, server_name, parameters)

        except Exception as e:
            logger.error(f"执行HTTP工具 {tool_name} 失败: {e}")
            return {
                "success": False,
                "tool_name": tool_name,
                "server": server_name,
                "parameters": parameters,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            }

    async def _execute_local_tool(self, tool_name: str, server_name: str, process, **parameters) -> Dict[str, Any]:
        """执行本地命令类型的MCP工具"""
        try:
            # 构建工具调用请求
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": parameters
                }
            }

            # 发送请求
            logger.info(f"执行本地工具: {tool_name} on {server_name}, 参数: {parameters}")
            request_json = json.dumps(request) + "\n"
            process.stdin.write(request_json)
            process.stdin.flush()

            # 读取响应
            response_line = process.stdout.readline()
            if not response_line:
                logger.error(f"从服务器 {server_name} 未收到响应")
                return {
                    "success": False,
                    "error": "未收到服务器响应",
                    "timestamp": datetime.now().isoformat()
                }

            response_data = json.loads(response_line.strip())
            return self._process_tool_response(response_data, tool_name, server_name, parameters)

        except Exception as e:
            logger.error(f"执行本地工具 {tool_name} 失败: {e}")
            return {
                "success": False,
                "tool_name": tool_name,
                "server": server_name,
                "parameters": parameters,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            }

    def _process_tool_response(self, response_data: Dict[str, Any], tool_name: str, server_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具响应数据"""
        try:
            # 处理结果
            if "result" in response_data:
                result_data = response_data["result"]
                content = ""
                if "content" in result_data:
                    for content_item in result_data["content"]:
                        if content_item.get("type") == "text":
                            content += content_item.get("text", "") + "\n"

                return {
                    "success": True,
                    "tool_name": tool_name,
                    "server": server_name,
                    "parameters": parameters,
                    "result": {
                        "content": content.strip(),
                        "raw_result": result_data
                    },
                    "timestamp": datetime.now().isoformat(),
                    "status": "success"
                }
            else:
                error_msg = response_data.get("error", {}).get("message", "未知错误")
                return {
                    "success": False,
                    "tool_name": tool_name,
                    "server": server_name,
                    "parameters": parameters,
                    "error": error_msg,
                    "timestamp": datetime.now().isoformat(),
                    "status": "error"
                }
        except Exception as e:
            logger.error(f"处理工具响应失败: {e}")
            return {
                "success": False,
                "tool_name": tool_name,
                "server": server_name,
                "parameters": parameters,
                "error": f"响应处理失败: {str(e)}",
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            }
    
    async def get_all_tools(self) -> List[Dict[str, Any]]:
        """获取所有服务器的工具列表"""
        all_tools = []
        
        for server_name in self.server_configs.keys():
            try:
                server_tools = await self.get_server_tools(server_name)
                all_tools.extend(server_tools)
            except Exception as e:
                logger.error(f"获取服务器 {server_name} 工具失败: {e}")
        
        logger.info(f"总共获取到 {len(all_tools)} 个MCP工具")
        return all_tools
    
    async def cleanup(self):
        """清理所有连接"""
        for server_name in list(self.active_processes.keys()):
            await self.stop_server(server_name)
        logger.info("简化MCP客户端清理完成")

# 创建全局实例
mcp_client = SimpleMCPClient()
