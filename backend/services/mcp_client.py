"""
简化的MCP客户端实现
用于连接和管理MCP服务器，不依赖复杂的MCP库
"""
import asyncio
import logging
import json
import subprocess
import os
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)

class SimpleMCPClient:
    """简化的MCP客户端类，用于管理MCP服务器连接"""

    def __init__(self):
        """初始化MCP客户端"""
        self.active_processes: Dict[str, subprocess.Popen] = {}
        self.server_configs: Dict[str, Dict[str, Any]] = {}
        self._load_server_configs()
        logger.info("简化MCP客户端初始化完成")
    
    def _load_server_configs(self):
        """加载MCP服务器配置"""
        try:
            # 尝试不同的配置目录路径
            config_dir = Path("mcp_config")
            if not config_dir.exists():
                config_dir = Path("backend/mcp_config")

            if not config_dir.exists():
                logger.warning(f"MCP配置目录不存在: {config_dir}")
                return
            
            # 加载所有JSON配置文件
            for config_file in config_dir.glob("*.json"):
                if config_file.name == "multi_server_config.json":
                    continue  # 跳过多服务器配置文件
                
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 解析配置文件
                    mcp_servers = config.get("mcpServers", {})
                    for server_name, server_config in mcp_servers.items():
                        self.server_configs[server_name] = {
                            "name": server_name,
                            "command": server_config.get("command"),
                            "args": server_config.get("args", []),
                            "env": server_config.get("env", {}),
                            "config_file": str(config_file)
                        }
                        logger.info(f"加载MCP服务器配置: {server_name}")
                
                except Exception as e:
                    logger.error(f"加载配置文件 {config_file} 失败: {e}")
            
            logger.info(f"总共加载了 {len(self.server_configs)} 个MCP服务器配置")
            
        except Exception as e:
            logger.error(f"加载MCP服务器配置失败: {e}")
    
    async def start_server(self, server_name: str) -> bool:
        """启动MCP服务器"""
        try:
            if server_name not in self.server_configs:
                logger.error(f"未找到服务器配置: {server_name}")
                return False

            if server_name in self.active_processes:
                logger.info(f"服务器 {server_name} 已经在运行")
                return True

            config = self.server_configs[server_name]

            # 构建命令
            command = [config["command"]] + config["args"]
            env = {**os.environ, **config["env"]}

            # 启动服务器进程
            logger.info(f"启动MCP服务器: {server_name}, 命令: {' '.join(command)}")
            process = subprocess.Popen(
                command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                text=True,
                bufsize=0
            )

            # 保存进程
            self.active_processes[server_name] = process

            logger.info(f"MCP服务器 {server_name} 启动成功，PID: {process.pid}")
            return True

        except Exception as e:
            logger.error(f"启动MCP服务器 {server_name} 失败: {e}")
            return False
    
    async def stop_server(self, server_name: str) -> bool:
        """停止MCP服务器"""
        try:
            if server_name not in self.active_processes:
                logger.warning(f"服务器 {server_name} 未运行")
                return True

            process = self.active_processes[server_name]

            # 终止进程
            process.terminate()

            # 等待进程结束
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                logger.warning(f"服务器 {server_name} 未能正常终止，强制杀死")
                process.kill()
                process.wait()

            # 移除进程
            del self.active_processes[server_name]

            logger.info(f"MCP服务器 {server_name} 已停止")
            return True

        except Exception as e:
            logger.error(f"停止MCP服务器 {server_name} 失败: {e}")
            return False
    
    async def get_server_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """获取服务器的工具列表"""
        try:
            # 确保服务器已启动
            if not await self.start_server(server_name):
                return []

            process = self.active_processes[server_name]

            # 发送list_tools请求
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/list",
                "params": {}
            }

            # 发送请求
            request_json = json.dumps(request) + "\n"
            process.stdin.write(request_json)
            process.stdin.flush()

            # 读取响应
            response_line = process.stdout.readline()
            if not response_line:
                logger.error(f"从服务器 {server_name} 未收到响应")
                return []

            response = json.loads(response_line.strip())

            # 解析工具列表
            tools = []
            if "result" in response and "tools" in response["result"]:
                for tool in response["result"]["tools"]:
                    tool_dict = {
                        "name": tool.get("name", ""),
                        "description": tool.get("description", ""),
                        "server": server_name,
                        "category": "mcp",
                        "inputSchema": tool.get("inputSchema", {}),
                    }
                    tools.append(tool_dict)

            logger.info(f"从服务器 {server_name} 获取到 {len(tools)} 个工具")
            return tools

        except Exception as e:
            logger.error(f"获取服务器 {server_name} 工具列表失败: {e}")
            return []
    
    async def execute_tool(self, tool_name: str, server_name: str, **parameters) -> Dict[str, Any]:
        """执行MCP工具"""
        try:
            # 确保服务器已启动
            if not await self.start_server(server_name):
                return {
                    "success": False,
                    "error": f"无法启动服务器 {server_name}",
                    "timestamp": datetime.now().isoformat()
                }

            process = self.active_processes[server_name]

            # 构建工具调用请求
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": parameters
                }
            }

            # 发送请求
            logger.info(f"执行工具: {tool_name} on {server_name}, 参数: {parameters}")
            request_json = json.dumps(request) + "\n"
            process.stdin.write(request_json)
            process.stdin.flush()

            # 读取响应
            response_line = process.stdout.readline()
            if not response_line:
                logger.error(f"从服务器 {server_name} 未收到响应")
                return {
                    "success": False,
                    "error": "未收到服务器响应",
                    "timestamp": datetime.now().isoformat()
                }

            response = json.loads(response_line.strip())

            # 处理结果
            if "result" in response:
                result_data = response["result"]
                content = ""
                if "content" in result_data:
                    for content_item in result_data["content"]:
                        if content_item.get("type") == "text":
                            content += content_item.get("text", "") + "\n"

                return {
                    "success": True,
                    "tool_name": tool_name,
                    "server": server_name,
                    "parameters": parameters,
                    "result": {
                        "content": content.strip(),
                        "raw_result": result_data
                    },
                    "timestamp": datetime.now().isoformat(),
                    "status": "success"
                }
            else:
                error_msg = response.get("error", {}).get("message", "未知错误")
                return {
                    "success": False,
                    "tool_name": tool_name,
                    "server": server_name,
                    "parameters": parameters,
                    "error": error_msg,
                    "timestamp": datetime.now().isoformat(),
                    "status": "error"
                }

        except Exception as e:
            logger.error(f"执行工具 {tool_name} 失败: {e}")
            return {
                "success": False,
                "tool_name": tool_name,
                "server": server_name,
                "parameters": parameters,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            }
    
    async def get_all_tools(self) -> List[Dict[str, Any]]:
        """获取所有服务器的工具列表"""
        all_tools = []
        
        for server_name in self.server_configs.keys():
            try:
                server_tools = await self.get_server_tools(server_name)
                all_tools.extend(server_tools)
            except Exception as e:
                logger.error(f"获取服务器 {server_name} 工具失败: {e}")
        
        logger.info(f"总共获取到 {len(all_tools)} 个MCP工具")
        return all_tools
    
    async def cleanup(self):
        """清理所有连接"""
        for server_name in list(self.active_processes.keys()):
            await self.stop_server(server_name)
        logger.info("简化MCP客户端清理完成")

# 创建全局实例
mcp_client = SimpleMCPClient()
