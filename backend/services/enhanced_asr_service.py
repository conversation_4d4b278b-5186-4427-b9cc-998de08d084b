#!/usr/bin/env python3
"""
增强的ASR服务
支持多个ASR提供商：Qwen2-Audio-7B (主要) + Gemini-2.5 (备用)
支持上下文感知的ASR转录，提高多轮对话的准确性
"""

import os
import logging
import numpy as np
import base64
import io
import wave
import asyncio
import concurrent.futures
import time
from typing import Dict, Any, Optional, List
import google.generativeai as genai
from openai import OpenAI
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class ConversationContext:
    """对话上下文管理类"""

    def __init__(self, session_id: str, max_history: int = 10):
        self.session_id = session_id
        self.max_history = max_history
        self.conversation_history: List[Dict[str, Any]] = []
        self.last_updated = datetime.now()

    def add_turn(self, user_input: str, asr_result: str, confidence: float = 0.0):
        """添加一轮对话"""
        turn = {
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "asr_result": asr_result,
            "confidence": confidence
        }

        self.conversation_history.append(turn)

        # 保持历史记录在限制范围内
        if len(self.conversation_history) > self.max_history:
            self.conversation_history = self.conversation_history[-self.max_history:]

        self.last_updated = datetime.now()
        logger.info(f"📝 会话 {self.session_id} 添加对话轮次，当前历史长度: {len(self.conversation_history)}")

    def get_context_prompt(self) -> str:
        """获取上下文提示"""
        if not self.conversation_history:
            return ""

        # 获取最近的几轮对话作为上下文
        recent_turns = self.conversation_history[-3:]  # 最近3轮
        context_parts = ["以下是最近的对话上下文："]

        for i, turn in enumerate(recent_turns, 1):
            context_parts.append(f"第{i}轮 - 用户说: {turn['asr_result']}")

        context_parts.append("请基于以上对话上下文，更准确地转录当前音频。")
        return "\n".join(context_parts)

    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """检查上下文是否过期"""
        return (datetime.now() - self.last_updated) > timedelta(minutes=timeout_minutes)


class EnhancedASRService:
    """增强的ASR服务，支持多个提供商，带超时和模型切换，支持上下文感知"""

    def __init__(self, qwen_timeout=5, gemini_timeout=10, gemini_first=True, enable_context=True):
        """初始化ASR服务"""
        self.qwen_timeout = qwen_timeout
        self.gemini_timeout = gemini_timeout
        self.gemini_first = gemini_first  # 是否优先使用Gemini
        self.enable_context = enable_context  # 是否启用上下文感知

        # 上下文管理
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        self.context_cleanup_interval = 60  # 清理间隔（分钟）
        self.last_cleanup = datetime.now()

        # 配置Qwen2-Audio-7B (主要ASR)
        self.qwen_client = OpenAI(
            base_url="http://172.16.1.151:20257/v1",
            api_key="EMPTY",
            timeout=qwen_timeout
        )

        # 配置Gemini (备用ASR)
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        self.gemini_models = [
            "gemini-2.0-flash-exp",
            "gemini-1.5-flash",
            "gemini-1.5-pro"
        ]
        self.current_gemini_model = 0

        if self.gemini_api_key:
            logger.info(f"✅ Gemini ASR备用服务已配置，可用模型: {self.gemini_models}")
        else:
            logger.warning("⚠️ GEMINI_API_KEY未配置，Gemini备用服务不可用")

        logger.info(f"🧠 ASR上下文感知功能: {'启用' if self.enable_context else '禁用'}")

    def _cleanup_expired_contexts(self):
        """清理过期的对话上下文"""
        if (datetime.now() - self.last_cleanup).total_seconds() < self.context_cleanup_interval * 60:
            return

        expired_sessions = []
        for session_id, context in self.conversation_contexts.items():
            if context.is_expired():
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            del self.conversation_contexts[session_id]
            logger.info(f"🧹 清理过期上下文: {session_id}")

        self.last_cleanup = datetime.now()
        if expired_sessions:
            logger.info(f"🧹 清理了 {len(expired_sessions)} 个过期上下文")

    def get_or_create_context(self, session_id: str) -> ConversationContext:
        """获取或创建对话上下文"""
        if not self.enable_context:
            return None

        self._cleanup_expired_contexts()

        if session_id not in self.conversation_contexts:
            self.conversation_contexts[session_id] = ConversationContext(session_id)
            logger.info(f"🆕 创建新的对话上下文: {session_id}")

        return self.conversation_contexts[session_id]

    def add_conversation_turn(self, session_id: str, user_input: str, asr_result: str, confidence: float = 0.0):
        """添加对话轮次到上下文"""
        if not self.enable_context:
            return

        context = self.get_or_create_context(session_id)
        if context:
            context.add_turn(user_input, asr_result, confidence)

    def get_context_stats(self) -> Dict[str, Any]:
        """获取上下文统计信息"""
        return {
            "total_sessions": len(self.conversation_contexts),
            "context_enabled": self.enable_context,
            "sessions": {
                session_id: {
                    "history_length": len(context.conversation_history),
                    "last_updated": context.last_updated.isoformat(),
                    "is_expired": context.is_expired()
                }
                for session_id, context in self.conversation_contexts.items()
            }
        }

    def _is_valid_audio(self, audio_data: np.ndarray) -> bool:
        """检测音频质量是否足够进行ASR"""
        try:
            # 计算音频统计信息
            rms = np.sqrt(np.mean(audio_data**2))
            max_amplitude = np.max(np.abs(audio_data))

            # 检测静音 - 降低阈值，允许更安静的音频
            if max_amplitude < 0.003:  # 进一步降低，从0.005到0.003
                logger.warning(f"⚠️ 音频音量过低: max={max_amplitude:.4f}")
                return False

            # 检测过载 - 大幅提高阈值，只有真正削波的音频才被拒绝
            # 0.999939这样的值是正常的，只有真正达到1.0才是过载
            if max_amplitude >= 1.0:  # 从0.99改为1.0，只拒绝真正过载的音频
                logger.warning(f"⚠️ 音频过载: max={max_amplitude:.4f}")
                return False

            # 检测RMS能量 - 进一步降低阈值，允许更低的能量
            if rms < 0.001:  # 从0.002降低到0.001
                logger.warning(f"⚠️ 音频能量过低: rms={rms:.4f}")
                return False

            logger.info(f"✅ 音频质量检查通过: rms={rms:.4f}, max={max_amplitude:.4f}")
            return True

        except Exception as e:
            logger.error(f"❌ 音频质量检测失败: {e}")
            return False
    
    def _is_valid_transcription(self, text: str) -> bool:
        """检测转录结果是否可能是幻觉"""
        if not text or not text.strip():
            return False
        
        # 常见的Gemini幻觉模式（基于实际遇到的问题）
        hallucination_patterns = [
            # 你实际遇到的幻觉内容
            "你好欢迎收听今天的节目",
            "今天天气真好",
            "我们一起去公园玩吧",
            "今天天氣真好",
            "我們一起去公園玩吧",
            
            # 其他常见幻觉模式
            "欢迎收听",
            "今天的节目", 
            "给大家分享",
            "如何用手机",
            "剪辑视频",
            "打开剪映",
            "好的请提供",
            "音频文件",
            "喂你好",
            "你好吗",
            "再见",
            "谢谢",
            "大家好",
            
            # 广播/教学常用语
            "首先打开",
            "接下来我们",
            "现在开始",
            "让我们一起",
            "带点什么去呢",
            "带些水果和零食",
            "那我们出发吧"
        ]
        
        text_lower = text.lower().replace(" ", "")
        for pattern in hallucination_patterns:
            if pattern.replace(" ", "") in text_lower:
                logger.warning(f"⚠️ 检测到可能的幻觉内容: '{text}' (匹配模式: '{pattern}')")
                return False
        
        # # 检测过于标准的句子结构
        # if len(text) > 20 and ("今天" in text or "我们" in text or "好的" in text):
        #     logger.warning(f"⚠️ 检测到可能的模板化内容: '{text}'")
        #     return False
        
        return True

    def transcribe(self, audio_data: np.ndarray, sample_rate: int = 16000, session_id: str = None) -> Dict[str, Any]:
        """
        转录音频数据，支持上下文感知

        Args:
            audio_data: 音频数据 (numpy array)
            sample_rate: 采样率
            session_id: 会话ID，用于上下文管理

        Returns:
            Dict包含转录结果
        """
        logger.info(f"🎯 开始ASR转录... (会话ID: {session_id}, 上下文: {'启用' if self.enable_context and session_id else '禁用'})")

        # 获取上下文信息
        context = None
        context_prompt = ""
        if self.enable_context and session_id:
            context = self.get_or_create_context(session_id)
            if context:
                context_prompt = context.get_context_prompt()
                if context_prompt:
                    logger.info(f"🧠 使用对话上下文，历史长度: {len(context.conversation_history)}")

        # # 音频质量检测
        # if not self._is_valid_audio(audio_data):
        #     logger.warning("⚠️ 音频质量不足，跳过ASR处理")
        #     return {
        #         'success': False,
        #         'text': '',
        #         'confidence': 0.0,
        #         'duration': len(audio_data) / sample_rate,
        #         'provider': 'none',
        #         'error': 'Audio quality too low for ASR'
        #     }
        
        # 执行ASR转录
        result = None
        if self.gemini_first:
            # Gemini优先模式
            logger.info("🚀 使用Gemini优先模式...")

            # 首先尝试Gemini
            gemini_result = self._try_gemini_asr(audio_data, sample_rate, context_prompt)
            if gemini_result and gemini_result.get('success'):
                logger.info("✅ Gemini ASR成功")
                result = gemini_result
            else:
                # 如果Gemini失败，尝试Qwen备用
                logger.warning("⚠️ Gemini ASR失败，尝试Qwen备用...")
                qwen_result = self._try_qwen_asr(audio_data, sample_rate, context_prompt)
                if qwen_result and qwen_result.get('success'):
                    logger.info("✅ Qwen ASR备用成功")
                    result = qwen_result
        else:
            # Qwen优先模式（原来的逻辑）
            logger.info("🚀 使用Qwen优先模式...")

            # 首先尝试Qwen2-Audio-7B
            qwen_result = self._try_qwen_asr(audio_data, sample_rate, context_prompt)
            if qwen_result and qwen_result.get('success'):
                logger.info("✅ Qwen2-Audio-7B ASR成功")
                result = qwen_result
            else:
                # 如果Qwen失败，尝试Gemini备用
                logger.warning("⚠️ Qwen2-Audio-7B ASR失败，尝试Gemini备用...")
                gemini_result = self._try_gemini_asr(audio_data, sample_rate, context_prompt)
                if gemini_result and gemini_result.get('success'):
                    logger.info("✅ Gemini ASR备用成功")
                    result = gemini_result

        # 处理结果并更新上下文
        if result and result.get('success'):
            # 更新对话上下文
            if context and session_id:
                transcribed_text = result.get('text', '')
                confidence = result.get('confidence', 0.0)
                context.add_turn("", transcribed_text, confidence)  # 用户输入为空，因为我们只有ASR结果
                logger.info(f"🧠 已更新会话 {session_id} 的上下文")

            # 添加上下文使用标记
            result['context_used'] = bool(context_prompt)
            result['session_id'] = session_id
            return result

        # 如果都失败，返回错误
        logger.error("❌ 所有ASR服务都失败")
        return {
            'success': False,
            'text': '',
            'confidence': 0.0,
            'duration': 0.0,
            'provider': 'none',
            'error': 'All ASR services failed',
            'context_used': bool(context_prompt),
            'session_id': session_id
        }
    
    def _try_qwen_asr(self, audio_data: np.ndarray, sample_rate: int, context_prompt: str = "") -> Optional[Dict[str, Any]]:
        """尝试使用Qwen2-Audio-7B进行ASR，支持上下文"""
        try:
            logger.info("📤 调用Qwen2-Audio-7B ASR...")
            start_time = time.time()

            # 转换音频格式
            audio_bytes = self._numpy_to_wav_bytes(audio_data, sample_rate)
            audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')

            # 使用线程池执行器来实现超时
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(self._qwen_api_call, audio_base64, context_prompt)
                try:
                    response = future.result(timeout=self.qwen_timeout)
                except concurrent.futures.TimeoutError:
                    logger.warning(f"⚠️ Qwen2-Audio-7B超时 ({self.qwen_timeout}秒)")
                    return None
            
            if response:
                transcribed_text = response.choices[0].message.content.strip()
                elapsed_time = time.time() - start_time
                logger.info(f"⏱️ Qwen2-Audio-7B耗时: {elapsed_time:.2f}秒")
                
                if transcribed_text:
                    return {
                        'success': True,
                        'text': transcribed_text,
                        'confidence': 0.9,  # Qwen通常有较高置信度
                        'duration': len(audio_data) / sample_rate,
                        'provider': 'Qwen2-Audio-7B',
                        'tokens': len(transcribed_text.split())
                    }
                else:
                    logger.warning("⚠️ Qwen2-Audio-7B返回空文本")
                    return None
            else:
                return None
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ Qwen2-Audio-7B ASR失败 ({elapsed_time:.2f}秒): {e}")
            return None
    
    def _qwen_api_call(self, audio_base64: str, context_prompt: str = ""):
        """分离的Qwen API调用，用于超时控制，支持上下文"""
        # 构建转录指令
        base_instruction = "请转录这段音频的内容，只返回转录文本，不要添加任何解释。"

        if context_prompt:
            instruction = f"{context_prompt}\n\n{base_instruction}"
            logger.info("🧠 Qwen ASR使用上下文提示")
        else:
            instruction = base_instruction

        return self.qwen_client.chat.completions.create(
            model="Qwen2-Audio-7B-Instruct",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "audio",
                            "audio": audio_base64
                        },
                        {
                            "type": "text",
                            "text": instruction
                        }
                    ]
                }
            ],
            temperature=0.1,
            max_tokens=1000
        )
    
    def _try_gemini_asr(self, audio_data: np.ndarray, sample_rate: int, context_prompt: str = "") -> Optional[Dict[str, Any]]:
        """尝试使用Gemini进行ASR，支持上下文"""
        try:
            if not self.gemini_api_key:
                logger.warning("⚠️ GEMINI_API_KEY未配置")
                return None

            logger.info("📤 调用Gemini ASR (新API)...")
            start_time = time.time()

            # 转换音频格式并保存为临时文件
            audio_bytes = self._numpy_to_wav_bytes(audio_data, sample_rate)

            # 使用线程池执行器来实现超时
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(self._gemini_api_call, audio_bytes, context_prompt)
                try:
                    result = future.result(timeout=self.gemini_timeout)
                    elapsed_time = time.time() - start_time
                    logger.info(f"⏱️ Gemini耗时: {elapsed_time:.2f}秒")
                    
                    if result:
                        # 验证转录结果是否为幻觉
                        # if not self._is_valid_transcription(result):
                        #     logger.warning(f"⚠️ Gemini返回疑似幻觉内容，拒绝结果: '{result}'")
                        #     return None
                        
                        logger.info(f"✅ Gemini转录结果验证通过: '{result}'")
                        return {
                            'success': True,
                            'text': result,
                            'confidence': 0.8,  # Gemini置信度稍低
                            'duration': len(audio_data) / sample_rate,
                            'provider': f'Gemini-{self.gemini_models[self.current_gemini_model]}',
                            'tokens': len(result.split())
                        }
                    else:
                        return None
                        
                except concurrent.futures.TimeoutError:
                    elapsed_time = time.time() - start_time
                    logger.warning(f"⚠️ Gemini超时 ({self.gemini_timeout}秒)")
                    
                    # 尝试切换到下一个模型
                    if self.current_gemini_model < len(self.gemini_models) - 1:
                        self.current_gemini_model += 1
                        logger.info(f"🔄 切换到Gemini模型: {self.gemini_models[self.current_gemini_model]}")
                        return self._try_gemini_asr(audio_data, sample_rate)  # 递归尝试下一个模型
                    else:
                        logger.error("❌ 所有Gemini模型都超时")
                        return None
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ Gemini ASR失败 ({elapsed_time:.2f}秒): {e}")
            return None
    
    def _gemini_api_call(self, audio_bytes: bytes, context_prompt: str = "") -> Optional[str]:
        """分离的Gemini API调用，用于超时控制，支持上下文"""
        try:
            from google import genai
            client = genai.Client(api_key=self.gemini_api_key)

            # 创建临时文件
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_bytes)
                temp_file_path = temp_file.name

            try:
                # 上传音频文件到Gemini
                logger.info("📤 上传音频到Gemini...")
                myfile = client.files.upload(file=temp_file_path)

                # 构建转录指令
                base_instruction = "请转录这段音频的内容，只返回转录的中文文本，不要添加任何解释或标点符号。"

                if context_prompt:
                    instruction = f"{context_prompt}\n\n{base_instruction}"
                    logger.info("🧠 Gemini ASR使用上下文提示")
                else:
                    instruction = base_instruction

                # 调用Gemini API进行转录
                current_model = self.gemini_models[self.current_gemini_model]
                logger.info(f"🎯 调用Gemini进行转录 (模型: {current_model})...")
                response = client.models.generate_content(
                    model=current_model,
                    contents=[instruction, myfile]
                )
                
                transcribed_text = response.text.strip()
                
                if transcribed_text:
                    return transcribed_text
                else:
                    logger.warning("⚠️ Gemini返回空文本")
                    return None
                    
            finally:
                # 清理临时文件
                import os
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"❌ Gemini API调用失败: {e}")
            return None
    
    def _numpy_to_wav_bytes(self, audio_data: np.ndarray, sample_rate: int) -> bytes:
        """将numpy音频数据转换为WAV字节"""
        # 确保音频数据在正确的范围内
        if audio_data.dtype != np.int16:
            # 转换为16位整数
            audio_data = (audio_data * 32767).astype(np.int16)
        
        # 创建WAV文件字节流
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        return wav_buffer.getvalue()
    
    def test_connection(self) -> Dict[str, Any]:
        """测试ASR服务连接"""
        results = {
            'qwen': False,
            'gemini': False,
            'available_services': []
        }
        
        # 测试Qwen连接
        try:
            models = self.qwen_client.models.list()
            results['qwen'] = True
            results['available_services'].append('Qwen2-Audio-7B')
            logger.info("✅ Qwen2-Audio-7B连接正常")
        except Exception as e:
            logger.error(f"❌ Qwen2-Audio-7B连接失败: {e}")
        
        # 测试Gemini连接
        try:
            if self.gemini_api_key:
                from google import genai
                client = genai.Client(api_key=self.gemini_api_key)
                # 简单测试 - 尝试生成内容
                test_response = client.models.generate_content(
                    model="gemini-2.0-flash-exp",
                    contents=["测试连接"]
                )
                if test_response and test_response.text:
                    results['gemini'] = True
                    results['available_services'].append('Gemini-2.0-Flash-Exp')
                    logger.info("✅ Gemini连接正常")
        except Exception as e:
            logger.error(f"❌ Gemini连接失败: {e}")
        
        return results