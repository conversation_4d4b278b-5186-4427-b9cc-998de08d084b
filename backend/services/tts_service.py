import asyncio
import websockets
import json
import base64
import logging
import aiohttp
from typing import Dict, Any, Optional, AsyncGenerator
import aiofiles
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class TTSService:
    def __init__(self, api_key: str, group_id: str):
        self.api_key = api_key
        self.group_id = group_id
        self.websocket_url = "wss://api.minimaxi.com/v1/t2a_pro"
        self.http_url = "https://api.minimaxi.com/v1/t2a_pro"
        self.voice_id = "male-qn-qingse"  # Default voice
        
    async def synthesize_speech_stream(self,
                                     text: str,
                                     emotion: str = "neutral",
                                     speed: float = 1.0,
                                     voice_id: Optional[str] = None) -> AsyncGenerator[bytes, None]:
        """
        Synthesize speech using MiniMax WebSocket API with streaming

        Args:
            text: Text to synthesize
            emotion: Emotion for TTS (neutral, happy, sad, etc.)
            speed: Speech speed (0.5 - 2.0)
            voice_id: Voice ID to use

        Yields:
            Audio data chunks as bytes
        """
        try:
            # Get complete audio data
            audio_data = await self._synthesize_complete_audio(text, emotion, speed, voice_id)

            if audio_data:
                # Yield the complete audio data
                yield audio_data
            else:
                # Yield empty bytes to indicate error
                yield b""

        except Exception as e:
            logger.error(f"TTS stream synthesis error: {e}")
            yield b""

    async def _synthesize_complete_audio(self, text: str, emotion: str, speed: float, voice_id: Optional[str]) -> bytes:
        """Internal method to get complete audio data"""
        try:
            # Try HTTP API first, then fallback to WebSocket
            logger.info("Trying HTTP API for TTS...")
            audio_data = await self._synthesize_http_api(text, emotion, speed, voice_id)

            if audio_data:
                return audio_data

            logger.info("HTTP API failed, trying WebSocket API...")
            return await self._synthesize_websocket_api(text, emotion, speed, voice_id)

        except Exception as e:
            logger.error(f"TTS complete audio synthesis error: {e}")
            return b""

    async def _synthesize_http_api(self, text: str, emotion: str, speed: float, voice_id: Optional[str]) -> bytes:
        """Try HTTP API for TTS synthesis"""
        try:
            current_voice_id = voice_id or self.voice_id

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # Try different model names
            models_to_try = ["speech-01", "t2a-pro", "tts-1", "speech-turbo"]

            async with aiohttp.ClientSession() as session:
                for model_name in models_to_try:
                    payload = {
                        "model": model_name,
                        "group_id": self.group_id,
                        "voice_id": current_voice_id,
                        "text": text,
                        "speed": speed,
                        "vol": 1.0,
                        "pitch": 0,
                        "audio_sample_rate": 16000,
                        "bitrate": 128000,
                        "format": "wav"
                    }

                    logger.info(f"Trying TTS model: {model_name}")

                    try:
                        async with session.post(self.http_url, headers=headers, json=payload) as response:
                            if response.status == 200:
                                response_data = await response.json()

                                # Check for base64 audio data first
                                if "audio" in response_data:
                                    # Decode base64 audio data
                                    audio_data = base64.b64decode(response_data["audio"])
                                    logger.info(f"HTTP TTS synthesis successful with model {model_name}: {len(audio_data)} bytes")
                                    return audio_data

                                # Check for audio file URL
                                elif "audio_file" in response_data and response_data["audio_file"]:
                                    audio_url = response_data["audio_file"]
                                    logger.info(f"Downloading audio from URL: {audio_url}")

                                    # Download the audio file
                                    async with session.get(audio_url) as audio_response:
                                        if audio_response.status == 200:
                                            audio_data = await audio_response.read()
                                            logger.info(f"HTTP TTS synthesis successful with model {model_name}: {len(audio_data)} bytes")
                                            return audio_data
                                        else:
                                            logger.warning(f"Failed to download audio from {audio_url}: {audio_response.status}")

                                else:
                                    logger.warning(f"No audio in response for model {model_name}: {response_data}")
                            else:
                                error_text = await response.text()
                                logger.warning(f"Model {model_name} failed with status {response.status}: {error_text}")
                    except Exception as e:
                        logger.warning(f"Model {model_name} request failed: {e}")
                        continue

                logger.error("All TTS models failed")
                return b""



        except Exception as e:
            logger.error(f"HTTP TTS synthesis error: {e}")
            return b""

    async def _synthesize_websocket_api(self, text: str, emotion: str, speed: float, voice_id: Optional[str]) -> bytes:
        """Fallback WebSocket API for TTS synthesis"""
        try:
            # Map emotions to MiniMax voice settings
            emotion_mapping = {
                "neutral": {"emotion": "neutral", "speed": speed},
                "happy": {"emotion": "cheerful", "speed": speed * 1.1},
                "sad": {"emotion": "sad", "speed": speed * 0.9},
                "excited": {"emotion": "excited", "speed": speed * 1.2},
                "calm": {"emotion": "calm", "speed": speed * 0.95},
                "friendly": {"emotion": "friendly", "speed": speed * 1.05}
            }

            voice_settings = emotion_mapping.get(emotion, emotion_mapping["neutral"])
            current_voice_id = voice_id or self.voice_id

            # WebSocket connection
            headers = {
                "Authorization": f"Bearer {self.api_key}"
            }

            # Use additional_headers for newer websockets versions
            try:
                async with websockets.connect(self.websocket_url, additional_headers=headers) as websocket:
                    audio_data = await self._handle_websocket_synthesis(websocket, text, current_voice_id, emotion, speed, None)
                    return audio_data
            except TypeError:
                # Fallback for older websockets versions
                async with websockets.connect(self.websocket_url, extra_headers=headers) as websocket:
                    audio_data = await self._handle_websocket_synthesis(websocket, text, current_voice_id, emotion, speed, None)
                    return audio_data

        except Exception as e:
            logger.error(f"WebSocket TTS synthesis error: {e}")
            return b""

    async def _handle_websocket_synthesis(self, websocket, text: str, voice_id: str, emotion: str, speed: float, output_file: Optional[str]):
        """Handle the websocket synthesis process"""
        try:
            # Get voice settings
            voice_settings = self.voice_configs.get(voice_id, self.voice_configs["default"])
            voice_settings["speed"] = speed
            voice_settings["emotion"] = emotion

            # Send synthesis request
            request_data = {
                "group_id": self.group_id,
                "voice_id": voice_id,
                "text": text,
                "speed": voice_settings["speed"],
                "vol": 1.0,
                "pitch": 0,
                "audio_sample_rate": 16000,
                "bitrate": 128000,
                "format": "wav",
                "use_p2s": True,  # Enable streaming
                "emotion": voice_settings.get("emotion", "neutral")
            }

            await websocket.send(json.dumps(request_data))
            logger.info(f"Sent TTS request for text: {text[:50]}...")

            # Collect audio data
            audio_chunks = []

            # Receive audio chunks
            async for message in websocket:
                try:
                    data = json.loads(message)

                    if data.get("status") == "success":
                        if "audio" in data:
                            # Decode base64 audio data
                            audio_data = base64.b64decode(data["audio"])
                            audio_chunks.append(audio_data)

                        if data.get("is_end", False):
                            logger.info("TTS synthesis completed")
                            break

                    elif data.get("status") == "error":
                        error_msg = data.get("message", "Unknown TTS error")
                        logger.error(f"TTS error: {error_msg}")
                        return b""

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse TTS response: {e}")
                    continue

            # Combine all audio chunks
            return b"".join(audio_chunks)

        except Exception as e:
            logger.error(f"TTS websocket synthesis error: {e}")
            return b""
    
    async def synthesize_speech(self, 
                              text: str,
                              emotion: str = "neutral",
                              speed: float = 1.0,
                              voice_id: Optional[str] = None,
                              output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Synthesize speech and optionally save to file
        
        Args:
            text: Text to synthesize
            emotion: Emotion for TTS
            speed: Speech speed
            voice_id: Voice ID to use
            output_file: Optional file path to save audio
            
        Returns:
            Dictionary with synthesis results
        """
        try:
            audio_chunks = []
            total_size = 0
            
            # Collect all audio chunks
            async for chunk in self.synthesize_speech_stream(text, emotion, speed, voice_id):
                if chunk:  # Skip empty chunks (errors)
                    audio_chunks.append(chunk)
                    total_size += len(chunk)
            
            if not audio_chunks:
                return {
                    "success": False,
                    "error": "No audio data received",
                    "audio_data": None,
                    "file_path": None
                }
            
            # Combine all chunks
            audio_data = b"".join(audio_chunks)
            
            # Save to file if requested
            file_path = None
            if output_file:
                try:
                    async with aiofiles.open(output_file, 'wb') as f:
                        await f.write(audio_data)
                    file_path = output_file
                    logger.info(f"Audio saved to {output_file}")
                except Exception as e:
                    logger.error(f"Failed to save audio file: {e}")
            
            return {
                "success": True,
                "audio_data": audio_data,
                "file_path": file_path,
                "size": total_size,
                "text": text,
                "emotion": emotion,
                "speed": speed,
                "voice_id": voice_id or self.voice_id
            }
            
        except Exception as e:
            logger.error(f"Error in synthesize_speech: {e}")
            return {
                "success": False,
                "error": str(e),
                "audio_data": None,
                "file_path": None
            }
    
    async def synthesize_batch(self, 
                             texts: list,
                             emotions: Optional[list] = None,
                             speeds: Optional[list] = None,
                             voice_ids: Optional[list] = None,
                             output_dir: Optional[str] = None) -> list:
        """
        Synthesize multiple texts in batch
        
        Args:
            texts: List of texts to synthesize
            emotions: List of emotions (optional)
            speeds: List of speeds (optional)
            voice_ids: List of voice IDs (optional)
            output_dir: Directory to save audio files (optional)
            
        Returns:
            List of synthesis results
        """
        results = []
        
        for i, text in enumerate(texts):
            emotion = emotions[i] if emotions and i < len(emotions) else "neutral"
            speed = speeds[i] if speeds and i < len(speeds) else 1.0
            voice_id = voice_ids[i] if voice_ids and i < len(voice_ids) else None
            
            output_file = None
            if output_dir:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"tts_{i}_{timestamp}.wav"
                output_file = os.path.join(output_dir, filename)
            
            result = await self.synthesize_speech(text, emotion, speed, voice_id, output_file)
            result["index"] = i
            results.append(result)
        
        return results
    
    def get_available_voices(self) -> Dict[str, str]:
        """Get available voice IDs and descriptions"""
        return {
            "male-qn-qingse": "Male - Clear and Natural",
            "female-shaonv": "Female - Young and Sweet",
            "male-qn-jingying": "Male - Professional",
            "female-qn-qingse": "Female - Clear and Natural",
            "presenter_male": "Male - Presenter Style",
            "presenter_female": "Female - Presenter Style",
            "audiobook_male_1": "Male - Audiobook Style 1",
            "audiobook_female_1": "Female - Audiobook Style 1"
        }
    
    def validate_parameters(self, emotion: str, speed: float) -> Dict[str, Any]:
        """Validate TTS parameters"""
        errors = []
        
        # Validate emotion
        valid_emotions = ["neutral", "happy", "sad", "excited", "calm", "friendly"]
        if emotion not in valid_emotions:
            errors.append(f"Invalid emotion: {emotion}. Valid options: {valid_emotions}")
        
        # Validate speed
        if not (0.5 <= speed <= 2.0):
            errors.append(f"Invalid speed: {speed}. Must be between 0.5 and 2.0")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test TTS service connection"""
        try:
            test_text = "Hello, this is a test."
            result = await self.synthesize_speech(test_text)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": "TTS service connection successful",
                    "audio_size": result["size"]
                }
            else:
                return {
                    "success": False,
                    "message": f"TTS test failed: {result.get('error', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"TTS connection test failed: {str(e)}"
            }
    
    async def get_synthesis_info(self, text: str) -> Dict[str, Any]:
        """Get estimated synthesis information without actually synthesizing"""
        try:
            # Estimate duration based on text length and average speaking rate
            # Average speaking rate: ~150 words per minute
            word_count = len(text.split())
            estimated_duration = (word_count / 150) * 60  # seconds
            
            # Estimate file size (rough approximation)
            # 16kHz, 16-bit WAV ≈ 32KB per second
            estimated_size = int(estimated_duration * 32 * 1024)
            
            return {
                "text_length": len(text),
                "word_count": word_count,
                "estimated_duration": estimated_duration,
                "estimated_size": estimated_size,
                "available_voices": list(self.get_available_voices().keys())
            }
            
        except Exception as e:
            logger.error(f"Error getting synthesis info: {e}")
            return {"error": str(e)}