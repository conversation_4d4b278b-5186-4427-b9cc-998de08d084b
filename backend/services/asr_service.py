import numpy as np
import onnxruntime as ort
import librosa
import torch
from typing import Optional, List, Dict, Any
import logging
import json
import os

logger = logging.getLogger(__name__)

class ASRService:
    def __init__(self, model_path: str, sample_rate: int = 16000):
        self.model_path = model_path
        self.sample_rate = sample_rate
        self.session = None
        self.tokenizer = None
        self._load_model()
    
    def _load_model(self):
        """Load FireRedASR ONNX model and tokenizer"""
        try:
            # Load ONNX model
            model_file = os.path.join(self.model_path, "model.onnx")
            self.session = ort.InferenceSession(model_file)
            
            # Load tokenizer configuration
            tokenizer_file = os.path.join(self.model_path, "tokenizer.json")
            if os.path.exists(tokenizer_file):
                with open(tokenizer_file, 'r', encoding='utf-8') as f:
                    self.tokenizer = json.load(f)
            
            logger.info(f"ASR model loaded successfully from {self.model_path}")
        except Exception as e:
            logger.error(f"Failed to load ASR model: {e}")
            raise
    
    def preprocess_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """Preprocess audio for ASR"""
        # Ensure audio is float32
        if audio_data.dtype != np.float32:
            audio_data = audio_data.astype(np.float32)
        
        # Normalize audio
        if np.max(np.abs(audio_data)) > 1.0:
            audio_data = audio_data / np.max(np.abs(audio_data))
        
        # Ensure mono audio
        if len(audio_data.shape) > 1:
            audio_data = audio_data.mean(axis=1)
        
        return audio_data
    
    def extract_features(self, audio_data: np.ndarray) -> np.ndarray:
        """Extract features from audio for ASR model"""
        try:
            # Apply pre-emphasis filter
            pre_emphasis = 0.97
            emphasized = np.append(audio_data[0], audio_data[1:] - pre_emphasis * audio_data[:-1])
            
            # Extract mel-spectrogram features
            n_fft = 512
            hop_length = 160
            n_mels = 80
            
            # Compute mel-spectrogram
            mel_spec = librosa.feature.melspectrogram(
                y=emphasized,
                sr=self.sample_rate,
                n_fft=n_fft,
                hop_length=hop_length,
                n_mels=n_mels,
                fmin=0,
                fmax=self.sample_rate // 2
            )
            
            # Convert to log scale
            log_mel_spec = librosa.power_to_db(mel_spec, ref=np.max)
            
            # Normalize features
            log_mel_spec = (log_mel_spec - np.mean(log_mel_spec)) / (np.std(log_mel_spec) + 1e-8)
            
            return log_mel_spec.T  # Transpose to (time, features)
            
        except Exception as e:
            logger.error(f"Feature extraction error: {e}")
            raise
    
    def decode_tokens(self, token_ids: List[int]) -> str:
        """Decode token IDs to text"""
        if not self.tokenizer:
            logger.warning("No tokenizer loaded, returning raw token IDs")
            return str(token_ids)
        
        try:
            # Simple token-to-text mapping (this would need to be adapted based on actual tokenizer format)
            vocab = self.tokenizer.get('vocab', {})
            id_to_token = {v: k for k, v in vocab.items()}
            
            tokens = [id_to_token.get(token_id, '<unk>') for token_id in token_ids]
            text = ''.join(tokens).replace('▁', ' ').strip()
            
            return text
            
        except Exception as e:
            logger.error(f"Token decoding error: {e}")
            return ""
    
    def transcribe(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """
        Transcribe audio to text
        
        Args:
            audio_data: Audio data as numpy array
            
        Returns:
            Dictionary with transcription results
        """
        try:
            # Preprocess audio
            audio_data = self.preprocess_audio(audio_data)
            
            # Extract features
            features = self.extract_features(audio_data)
            
            # Prepare input for ONNX model
            # Add batch dimension and ensure correct shape
            input_features = features[np.newaxis, :, :].astype(np.float32)
            
            # Create input length tensor
            input_length = np.array([features.shape[0]], dtype=np.int64)
            
            # Prepare inputs for the model
            inputs = {
                'input_features': input_features,
                'input_length': input_length
            }
            
            # Run inference
            outputs = self.session.run(None, inputs)
            
            # Extract predictions (assuming first output contains token probabilities)
            predictions = outputs[0]
            
            # Decode predictions to get token IDs
            token_ids = np.argmax(predictions, axis=-1).flatten().tolist()
            
            # Remove padding tokens and special tokens
            token_ids = [tid for tid in token_ids if tid > 0]
            
            # Decode to text
            transcription = self.decode_tokens(token_ids)
            
            # Calculate confidence score (simple average of max probabilities)
            confidence = float(np.mean(np.max(predictions, axis=-1)))
            
            return {
                'text': transcription,
                'confidence': confidence,
                'tokens': token_ids,
                'duration': len(audio_data) / self.sample_rate
            }
            
        except Exception as e:
            logger.error(f"ASR transcription error: {e}")
            return {
                'text': '',
                'confidence': 0.0,
                'tokens': [],
                'duration': 0.0,
                'error': str(e)
            }
    
    def transcribe_streaming(self, audio_chunks: List[np.ndarray]) -> List[Dict[str, Any]]:
        """
        Transcribe streaming audio chunks
        
        Args:
            audio_chunks: List of audio chunks
            
        Returns:
            List of transcription results for each chunk
        """
        results = []
        
        for i, chunk in enumerate(audio_chunks):
            try:
                result = self.transcribe(chunk)
                result['chunk_index'] = i
                result['timestamp'] = i * len(chunk) / self.sample_rate
                results.append(result)
            except Exception as e:
                logger.error(f"Error transcribing chunk {i}: {e}")
                results.append({
                    'text': '',
                    'confidence': 0.0,
                    'chunk_index': i,
                    'timestamp': i * len(chunk) / self.sample_rate,
                    'error': str(e)
                })
        
        return results
    
    def merge_transcriptions(self, transcriptions: List[Dict[str, Any]], 
                           confidence_threshold: float = 0.5) -> str:
        """
        Merge multiple transcription results into final text
        
        Args:
            transcriptions: List of transcription results
            confidence_threshold: Minimum confidence to include transcription
            
        Returns:
            Merged transcription text
        """
        valid_transcriptions = [
            t['text'] for t in transcriptions 
            if t.get('confidence', 0) >= confidence_threshold and t.get('text', '').strip()
        ]
        
        return ' '.join(valid_transcriptions).strip()
    
    def process_audio_file(self, audio_file_path: str, chunk_duration: float = 2.0) -> Dict[str, Any]:
        """
        Process an audio file and return transcription
        
        Args:
            audio_file_path: Path to audio file
            chunk_duration: Duration of each processing chunk in seconds
            
        Returns:
            Complete transcription result
        """
        try:
            # Load audio file
            audio_data, sr = librosa.load(audio_file_path, sr=self.sample_rate)
            
            # Split into chunks
            chunk_size = int(chunk_duration * self.sample_rate)
            chunks = [
                audio_data[i:i + chunk_size] 
                for i in range(0, len(audio_data), chunk_size)
            ]
            
            # Transcribe chunks
            chunk_results = self.transcribe_streaming(chunks)
            
            # Merge results
            final_text = self.merge_transcriptions(chunk_results)
            
            # Calculate overall confidence
            confidences = [r.get('confidence', 0) for r in chunk_results if r.get('confidence', 0) > 0]
            avg_confidence = np.mean(confidences) if confidences else 0.0
            
            return {
                'text': final_text,
                'confidence': float(avg_confidence),
                'duration': len(audio_data) / self.sample_rate,
                'chunks': chunk_results
            }
            
        except Exception as e:
            logger.error(f"Error processing audio file {audio_file_path}: {e}")
            return {
                'text': '',
                'confidence': 0.0,
                'duration': 0.0,
                'error': str(e)
            }