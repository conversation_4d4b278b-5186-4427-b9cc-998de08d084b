import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

# 初始化Supabase客户端
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_KEY")  # 使用服务密钥

print("SUPABASE_URL:", supabase_url)
print("SUPABASE_KEY:", supabase_key)

if not supabase_url or not supabase_key:
    print("环境变量未设置")
    sys.exit(1)

try:
    supabase = create_client(supabase_url, supabase_key)
    print("Supabase客户端创建成功")
except Exception as e:
    print(f"创建Supabase客户端失败: {e}")
    sys.exit(1)

def create_conversation_session(user_id: int, npc_id: int):
    """Create new conversation session"""
    try:
        result = supabase.table("conversation_sessions").insert({
            "user_id": user_id,
            "npc_id": npc_id,
            "is_active": True
        }).execute()
        print("创建会话结果:", result)
        return result.data[0]["id"] if result.data else None
    except Exception as e:
        print(f"创建会话时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

# 测试创建会话
print("正在测试创建会话...")
session_id = create_conversation_session(1, 1)
if session_id:
    print(f"✓ 会话创建成功，ID: {session_id}")
else:
    print("✗ 会话创建失败")
