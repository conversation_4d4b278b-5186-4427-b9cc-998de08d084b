#!/usr/bin/env python3
"""
修复数据库结构脚本 - 解决表名和字段名问题
"""
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def fix_database_structure():
    """修复数据库结构"""
    print("🔧 修复数据库结构...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    # 修复基础表结构
    print("\n🛠️  修复基础表结构...")
    
    # 1. 修复 users 表
    print("\n--- 修复 users 表 ---")
    try:
        # 检查是否存在 users 表
        result = supabase.table("users").select("*").limit(1).execute()
        print("✅ users 表已存在")
        
        # 尝试添加缺失的字段
        # 注意：Supabase Python 客户端不直接支持 ALTER TABLE 操作
        # 我们通过插入测试数据来检查字段是否存在
        test_user = {
            "username": "fix_test_user",
            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
            "email": "<EMAIL>",
            "nickname": "修复测试用户",
            "is_active": True
        }
        
        result = supabase.table("users").insert(test_user).execute()
        if result.data:
            print("✅ users 表结构正确")
            # 清理测试数据
            supabase.table("users").delete().eq("username", "fix_test_user").execute()
        else:
            print("❌ users 表结构有问题")
    except Exception as e:
        print(f"❌ users 表检查失败: {e}")
    
    # 2. 修复 npcs 表
    print("\n--- 修复 npcs 表 ---")
    try:
        # 检查是否存在 npcs 表
        result = supabase.table("npcs").select("*").limit(1).execute()
        print("✅ npcs 表已存在")
        
        # 尝试添加缺失的字段
        test_npc = {
            "name": "fix_test_npc",
            "description": "修复测试NPC",
            "system_prompt": "修复测试提示",
            "is_active": True
        }
        
        result = supabase.table("npcs").insert(test_npc).execute()
        if result.data:
            print("✅ npcs 表结构正确")
            # 清理测试数据
            supabase.table("npcs").delete().eq("name", "fix_test_npc").execute()
        else:
            print("❌ npcs 表结构有问题")
    except Exception as e:
        print(f"❌ npcs 表检查失败: {e}")
    
    # 3. 创建 0802 前缀表
    print("\n--- 创建 0802 前缀表 ---")
    prefixed_tables = [
        ("0802_users", "users"),
        ("0802_npcs", "npcs"),
        ("0802_conversation_sessions", "conversation_sessions"),
        ("0802_conversation_messages", "conversation_messages")
    ]
    
    for prefixed_name, base_name in prefixed_tables:
        try:
            print(f"\n创建表 '{prefixed_name}'...")
            
            # 通过插入测试数据的方式创建表
            if "users" in prefixed_name:
                test_data = {
                    "username": f"fix_0802_test_{base_name}",
                    "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                    "email": f"fix_0802_test_{base_name}@example.com",
                    "nickname": f"修复0802测试{base_name}",
                    "is_active": True
                }
            elif "npcs" in prefixed_name:
                test_data = {
                    "name": f"fix_0802_test_{base_name}",
                    "description": f"修复0802测试{base_name}",
                    "system_prompt": f"修复0802测试提示{base_name}",
                    "is_active": True
                }
            elif "conversation_sessions" in prefixed_name:
                # 先创建测试用户
                user_data = {
                    "username": f"fix_0802_session_user_{int(time.time())}",
                    "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                    "email": f"fix_0802_session_{int(time.time())}@example.com",
                    "nickname": "修复0802会话测试用户",
                    "is_active": True
                }
                user_result = supabase.table("0802_users").insert(user_data).execute()
                user_id = user_result.data[0]["id"] if user_result.data else 1
                
                # 先创建测试NPC
                npc_data = {
                    "name": f"fix_0802_session_npc_{int(time.time())}",
                    "description": "修复0802会话测试NPC",
                    "system_prompt": "修复0802会话测试提示",
                    "is_active": True
                }
                npc_result = supabase.table("0802_npcs").insert(npc_data).execute()
                npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                
                test_data = {
                    "user_id": user_id,
                    "npc_id": npc_id,
                    "is_active": True
                }
            elif "conversation_messages" in prefixed_name:
                # 先创建测试会话
                session_data = {
                    "user_id": 1,
                    "npc_id": 1,
                    "is_active": True
                }
                session_result = supabase.table("0802_conversation_sessions").insert(session_data).execute()
                session_id = session_result.data[0]["id"] if session_result.data else "test-session-id"
                
                test_data = {
                    "session_id": session_id,
                    "role": "user",
                    "content": f"修复0802测试消息{base_name}"
                }
            
            # 尝试插入数据来创建表
            result = supabase.table(prefixed_name).insert(test_data).execute()
            if result.data:
                print(f"✅ 表 '{prefixed_name}' 创建成功")
                # 清理测试数据
                if "users" in prefixed_name:
                    supabase.table(prefixed_name).delete().eq("username", f"fix_0802_test_{base_name}").execute()
                elif "npcs" in prefixed_name:
                    supabase.table(prefixed_name).delete().eq("name", f"fix_0802_test_{base_name}").execute()
                elif "conversation_sessions" in prefixed_name:
                    supabase.table(prefixed_name).delete().eq("user_id", user_id).execute()
                    supabase.table("0802_users").delete().eq("username", f"fix_0802_session_user_{int(time.time())}").execute()
                    supabase.table("0802_npcs").delete().eq("name", f"fix_0802_session_npc_{int(time.time())}").execute()
                elif "conversation_messages" in prefixed_name:
                    supabase.table(prefixed_name).delete().eq("session_id", session_id).execute()
                    supabase.table("0802_conversation_sessions").delete().eq("user_id", 1).execute()
            else:
                print(f"❌ 表 '{prefixed_name}' 创建失败")
                
        except Exception as e:
            print(f"❌ 表 '{prefixed_name}' 创建失败: {e}")
    
    # 插入默认数据
    print("\n--- 插入默认数据 ---")
    try:
        # 插入默认NPC数据
        default_npcs = [
            {
                "name": "默认助手",
                "description": "通用AI助手",
                "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。",
                "is_active": True
            },
            {
                "name": "朋友",
                "description": "亲密朋友角色",
                "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。",
                "is_active": True
            }
        ]
        
        for npc in default_npcs:
            try:
                result = supabase.table("npcs").insert(npc).execute()
                if result.data:
                    print(f"✅ 插入默认NPC: {npc['name']}")
            except Exception as e:
                print(f"⚠️  插入默认NPC '{npc['name']}' 失败: {e}")
                
        # 插入默认0802 NPC数据
        default_0802_npcs = [
            {
                "name": "0802默认助手",
                "description": "0802通用AI助手",
                "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。",
                "is_active": True
            },
            {
                "name": "0802朋友",
                "description": "0802亲密朋友角色",
                "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。",
                "is_active": True
            }
        ]
        
        for npc in default_0802_npcs:
            try:
                result = supabase.table("0802_npcs").insert(npc).execute()
                if result.data:
                    print(f"✅ 插入默认0802 NPC: {npc['name']}")
            except Exception as e:
                print(f"⚠️  插入默认0802 NPC '{npc['name']}' 失败: {e}")
                
    except Exception as e:
        print(f"❌ 插入默认数据失败: {e}")
    
    print("\n🎉 数据库结构修复完成！")
    return True

if __name__ == "__main__":
    import time
    print("🚀 开始修复数据库结构...")
    
    if fix_database_structure():
        print("\n✅ 数据库结构修复成功！")
        sys.exit(0)
    else:
        print("\n❌ 数据库结构修复失败")
        sys.exit(1)
