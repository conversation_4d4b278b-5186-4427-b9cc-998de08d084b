#!/usr/bin/env python3
"""
详细的数据库诊断脚本 - 明确识别表名和字段名问题
"""
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def detailed_db_diagnosis():
    """详细的数据库诊断"""
    print("🔍 详细的数据库诊断...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    # 检查每个表是否存在以及字段是否正确
    tables_to_check = {
        "users": ["id", "username", "password_hash", "email", "nickname", "is_active"],
        "npcs": ["id", "name", "description", "system_prompt", "is_active"],
        "conversation_sessions": ["id", "user_id", "npc_id", "started_at", "is_active"],
        "conversation_messages": ["id", "session_id", "role", "content"],
        "0802_users": ["id", "username", "password_hash", "email", "nickname", "is_active"],
        "0802_npcs": ["id", "name", "description", "system_prompt", "is_active"],
        "0802_conversation_sessions": ["id", "user_id", "npc_id", "started_at", "is_active"],
        "0802_conversation_messages": ["id", "session_id", "role", "content"]
    }
    
    # 检查表是否存在
    print("\n📊 表存在性检查:")
    existing_tables = []
    missing_tables = []
    
    for table_name in tables_to_check.keys():
        try:
            # 尝试查询表的一行数据
            result = supabase.table(table_name).select("*").limit(1).execute()
            print(f"✅ 表 '{table_name}' 存在")
            existing_tables.append(table_name)
        except Exception as e:
            print(f"❌ 表 '{table_name}' 不存在或无法访问: {e}")
            missing_tables.append(table_name)
    
    # 检查现有表的字段
    print("\n📋 字段检查:")
    for table_name in existing_tables:
        try:
            # 获取表结构信息
            result = supabase.table(table_name).select("*").limit(1).execute()
            if result.data:
                columns = list(result.data[0].keys()) if result.data else []
                expected_columns = tables_to_check[table_name]
                
                print(f"\n表 '{table_name}' 字段检查:")
                print(f"  实际字段: {columns}")
                print(f"  期望字段: {expected_columns}")
                
                # 检查缺失的字段
                missing_columns = [col for col in expected_columns if col not in columns]
                extra_columns = [col for col in columns if col not in expected_columns]
                
                if missing_columns:
                    print(f"  ❌ 缺失字段: {missing_columns}")
                else:
                    print(f"  ✅ 所有必需字段都存在")
                    
                if extra_columns:
                    print(f"  ℹ️  额外字段: {extra_columns}")
            else:
                print(f"表 '{table_name}' 无法获取字段信息")
        except Exception as e:
            print(f"❌ 检查表 '{table_name}' 字段时出错: {e}")
    
    # 尝试插入测试数据来验证表结构
    print("\n🧪 表结构验证测试:")
    
    # 测试 users 表
    if "users" in existing_tables:
        try:
            test_user = {
                "username": "diagnosis_test_user",
                "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                "email": "<EMAIL>",
                "nickname": "诊断测试用户",
                "is_active": True
            }
            
            result = supabase.table("users").insert(test_user).execute()
            if result.data:
                print("✅ users 表结构正确，可以插入数据")
                # 清理测试数据
                supabase.table("users").delete().eq("username", "diagnosis_test_user").execute()
            else:
                print("❌ users 表结构可能有问题，无法插入数据")
        except Exception as e:
            print(f"❌ users 表插入测试失败: {e}")
    
    # 测试 npcs 表
    if "npcs" in existing_tables:
        try:
            test_npc = {
                "name": "diagnosis_test_npc",
                "description": "诊断测试NPC",
                "system_prompt": "诊断测试提示",
                "is_active": True
            }
            
            result = supabase.table("npcs").insert(test_npc).execute()
            if result.data:
                print("✅ npcs 表结构正确，可以插入数据")
                # 清理测试数据
                supabase.table("npcs").delete().eq("name", "diagnosis_test_npc").execute()
            else:
                print("❌ npcs 表结构可能有问题，无法插入数据")
        except Exception as e:
            print(f"❌ npcs 表插入测试失败: {e}")
    
    # 尝试直接执行SQL查询来获取表信息
    print("\n🔍 直接SQL查询表信息:")
    try:
        # 查询所有表名
        result = supabase.rpc("execute_sql", {"query": "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"}).execute()
        if result.data:
            table_names = [row["table_name"] for row in result.data]
            print(f"✅ 数据库中的表: {table_names}")
        else:
            print("❌ 无法获取表信息")
    except Exception as e:
        print(f"❌ 查询表信息失败: {e}")
    
    # 总结
    print("\n📋 诊断总结:")
    print(f"  存在的表: {len(existing_tables)} 个")
    print(f"  缺失的表: {len(missing_tables)} 个")
    if missing_tables:
        print(f"    缺失的表列表: {missing_tables}")
    
    return True

if __name__ == "__main__":
    print("🚀 开始详细数据库诊断...")
    
    if detailed_db_diagnosis():
        print("\n✅ 数据库诊断完成！")
        sys.exit(0)
    else:
        print("\n❌ 数据库诊断失败")
        sys.exit(1)
