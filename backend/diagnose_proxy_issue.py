#!/usr/bin/env python3
"""
代理问题诊断脚本
专门检查网络连接、代理设置和Supabase认证问题
"""

import os
import sys
import json
import logging
import asyncio
import requests
from datetime import datetime
from pathlib import Path

# 添加backend目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProxyDiagnostic:
    """代理和网络诊断器"""
    
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "diagnostics": {},
            "recommendations": []
        }
        
    def check_environment_variables(self):
        """检查环境变量"""
        logger.info("🔍 检查环境变量...")
        
        required_vars = ["SUPABASE_URL", "SUPABASE_KEY"]
        missing_vars = []
        present_vars = {}
        
        for var in required_vars:
            value = os.getenv(var)
            if value:
                # 只显示前10个字符，保护敏感信息
                present_vars[var] = f"{value[:10]}..." if len(value) > 10 else value
            else:
                missing_vars.append(var)
        
        self.results["diagnostics"]["environment_variables"] = {
            "missing": missing_vars,
            "present": present_vars,
            "status": "success" if not missing_vars else "failed"
        }
        
        if missing_vars:
            logger.error(f"❌ 缺少环境变量: {missing_vars}")
            self.results["recommendations"].append(
                f"请设置缺少的环境变量: {', '.join(missing_vars)}"
            )
            return False
        else:
            logger.info("✅ 所有必需的环境变量都已设置")
            return True
    
    def check_proxy_settings(self):
        """检查代理设置"""
        logger.info("🌐 检查代理设置...")
        
        proxy_vars = ["HTTP_PROXY", "HTTPS_PROXY", "http_proxy", "https_proxy", "ALL_PROXY"]
        proxy_settings = {}
        
        for var in proxy_vars:
            value = os.getenv(var)
            if value:
                proxy_settings[var] = value
        
        self.results["diagnostics"]["proxy_settings"] = {
            "detected_proxies": proxy_settings,
            "has_proxy": len(proxy_settings) > 0
        }
        
        if proxy_settings:
            logger.info(f"🔗 检测到代理设置: {list(proxy_settings.keys())}")
            self.results["recommendations"].append(
                "检测到代理设置，如果连接失败，请尝试临时禁用代理或配置代理白名单"
            )
        else:
            logger.info("ℹ️ 未检测到代理设置")
        
        return True
    
    def test_basic_connectivity(self):
        """测试基本网络连接"""
        logger.info("🌍 测试基本网络连接...")
        
        test_urls = [
            "https://www.google.com",
            "https://httpbin.org/get",
            "https://api.github.com"
        ]
        
        connectivity_results = {}
        
        for url in test_urls:
            try:
                response = requests.get(url, timeout=10)
                connectivity_results[url] = {
                    "status": "success",
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds()
                }
                logger.info(f"✅ {url} - 状态码: {response.status_code}")
            except requests.exceptions.ProxyError as e:
                connectivity_results[url] = {
                    "status": "proxy_error",
                    "error": str(e)
                }
                logger.error(f"🚫 {url} - 代理错误: {e}")
                self.results["recommendations"].append(
                    f"代理连接失败 ({url})，请检查代理设置或尝试直连"
                )
            except requests.exceptions.Timeout as e:
                connectivity_results[url] = {
                    "status": "timeout",
                    "error": str(e)
                }
                logger.error(f"⏰ {url} - 超时: {e}")
            except Exception as e:
                connectivity_results[url] = {
                    "status": "error",
                    "error": str(e)
                }
                logger.error(f"❌ {url} - 错误: {e}")
        
        self.results["diagnostics"]["basic_connectivity"] = connectivity_results
        
        successful_connections = sum(1 for result in connectivity_results.values() 
                                   if result["status"] == "success")
        
        if successful_connections == 0:
            self.results["recommendations"].append(
                "所有基本网络连接测试都失败了，请检查网络连接或防火墙设置"
            )
            return False
        elif successful_connections < len(test_urls):
            self.results["recommendations"].append(
                "部分网络连接测试失败，可能存在网络或代理问题"
            )
        
        return successful_connections > 0
    
    def test_supabase_connectivity(self):
        """测试Supabase连接"""
        logger.info("🗄️ 测试Supabase连接...")
        
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")
        
        if not supabase_url or not supabase_key:
            logger.error("❌ Supabase环境变量未设置")
            return False
        
        # 测试基本URL连接
        try:
            response = requests.get(supabase_url, timeout=10)
            self.results["diagnostics"]["supabase_base_url"] = {
                "status": "success",
                "status_code": response.status_code,
                "accessible": True
            }
            logger.info(f"✅ Supabase基础URL可访问 - 状态码: {response.status_code}")
        except Exception as e:
            self.results["diagnostics"]["supabase_base_url"] = {
                "status": "failed",
                "error": str(e),
                "accessible": False
            }
            logger.error(f"❌ Supabase基础URL不可访问: {e}")
            self.results["recommendations"].append(
                "Supabase基础URL不可访问，请检查URL是否正确或网络连接"
            )
            return False
        
        # 测试REST API端点
        rest_url = f"{supabase_url}/rest/v1/"
        headers = {
            "apikey": supabase_key,
            "Authorization": f"Bearer {supabase_key}"
        }
        
        try:
            response = requests.get(rest_url, headers=headers, timeout=10)
            self.results["diagnostics"]["supabase_rest_api"] = {
                "status": "success" if response.status_code in [200, 404] else "failed",
                "status_code": response.status_code,
                "response_size": len(response.content)
            }
            
            if response.status_code == 401:
                logger.error("❌ Supabase认证失败 - API密钥可能无效")
                self.results["recommendations"].append(
                    "Supabase API密钥认证失败，请检查SUPABASE_KEY是否正确"
                )
                return False
            elif response.status_code in [200, 404]:
                logger.info("✅ Supabase REST API可访问")
                return True
            else:
                logger.warning(f"⚠️ Supabase REST API返回异常状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.results["diagnostics"]["supabase_rest_api"] = {
                "status": "failed",
                "error": str(e)
            }
            logger.error(f"❌ Supabase REST API连接失败: {e}")
            self.results["recommendations"].append(
                f"Supabase REST API连接失败: {str(e)}"
            )
            return False
    
    def test_supabase_table_access(self):
        """测试Supabase表访问"""
        logger.info("📋 测试Supabase表访问...")
        
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")
        
        if not supabase_url or not supabase_key:
            return False
        
        tables_to_test = ["npcs", "users", "conversation_sessions", "conversation_messages"]
        table_results = {}
        
        headers = {
            "apikey": supabase_key,
            "Authorization": f"Bearer {supabase_key}",
            "Content-Type": "application/json"
        }
        
        for table in tables_to_test:
            table_url = f"{supabase_url}/rest/v1/{table}?select=count&limit=1"
            
            try:
                response = requests.get(table_url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    table_results[table] = {
                        "status": "accessible",
                        "status_code": response.status_code
                    }
                    logger.info(f"✅ 表 '{table}' 可访问")
                elif response.status_code == 401:
                    table_results[table] = {
                        "status": "auth_failed",
                        "status_code": response.status_code
                    }
                    logger.error(f"❌ 表 '{table}' 认证失败")
                elif response.status_code == 404:
                    table_results[table] = {
                        "status": "not_found",
                        "status_code": response.status_code
                    }
                    logger.warning(f"⚠️ 表 '{table}' 不存在")
                else:
                    table_results[table] = {
                        "status": "error",
                        "status_code": response.status_code
                    }
                    logger.error(f"❌ 表 '{table}' 访问异常: {response.status_code}")
                    
            except Exception as e:
                table_results[table] = {
                    "status": "connection_failed",
                    "error": str(e)
                }
                logger.error(f"❌ 表 '{table}' 连接失败: {e}")
        
        self.results["diagnostics"]["supabase_tables"] = table_results
        
        # 分析结果
        accessible_tables = [table for table, result in table_results.items() 
                           if result["status"] == "accessible"]
        auth_failed_tables = [table for table, result in table_results.items() 
                            if result["status"] == "auth_failed"]
        
        if auth_failed_tables:
            self.results["recommendations"].append(
                f"以下表认证失败: {', '.join(auth_failed_tables)}，请检查API密钥权限"
            )
        
        if not accessible_tables:
            self.results["recommendations"].append(
                "没有可访问的表，请检查数据库配置和权限设置"
            )
            return False
        
        return len(accessible_tables) > 0
    
    def generate_proxy_bypass_commands(self):
        """生成代理绕过命令"""
        logger.info("🛠️ 生成代理绕过命令...")
        
        commands = {
            "临时禁用代理 (当前会话)": [
                "unset HTTP_PROXY",
                "unset HTTPS_PROXY", 
                "unset http_proxy",
                "unset https_proxy",
                "unset ALL_PROXY"
            ],
            "使用直连运行Python脚本": [
                "env -u HTTP_PROXY -u HTTPS_PROXY -u http_proxy -u https_proxy python test_npc_functions.py"
            ],
            "设置代理白名单 (如果使用代理)": [
                "export NO_PROXY=localhost,127.0.0.1,*************",
                "export no_proxy=localhost,127.0.0.1,*************"
            ]
        }
        
        self.results["diagnostics"]["proxy_bypass_commands"] = commands
        
        logger.info("📝 代理绕过命令已生成")
        return commands
    
    async def run_full_diagnosis(self):
        """运行完整诊断"""
        logger.info("🚀 开始代理和网络诊断...")
        
        # 运行所有诊断测试
        tests = [
            ("环境变量检查", self.check_environment_variables),
            ("代理设置检查", self.check_proxy_settings),
            ("基本网络连接", self.test_basic_connectivity),
            ("Supabase连接", self.test_supabase_connectivity),
            ("Supabase表访问", self.test_supabase_table_access),
            ("代理绕过命令", self.generate_proxy_bypass_commands)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 执行诊断: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    failed += 1
                    logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                failed += 1
                logger.error(f"❌ {test_name} - 异常: {e}")
        
        # 生成诊断总结
        self.results["summary"] = {
            "total_tests": len(tests),
            "passed": passed,
            "failed": failed,
            "success_rate": f"{(passed/len(tests)*100):.1f}%"
        }
        
        logger.info(f"\n{'='*60}")
        logger.info("📊 诊断总结")
        logger.info(f"{'='*60}")
        logger.info(f"总诊断项: {len(tests)}")
        logger.info(f"通过: {passed}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {(passed/len(tests)*100):.1f}%")
        
        # 输出建议
        if self.results["recommendations"]:
            logger.info(f"\n{'='*60}")
            logger.info("💡 建议和解决方案")
            logger.info(f"{'='*60}")
            for i, recommendation in enumerate(self.results["recommendations"], 1):
                logger.info(f"{i}. {recommendation}")
        
        # 保存诊断结果
        with open("proxy_diagnosis_report.json", "w", encoding="utf-8") as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        logger.info("📄 诊断报告已保存到 proxy_diagnosis_report.json")
        
        return failed == 0

async def main():
    """主函数"""
    diagnostic = ProxyDiagnostic()
    success = await diagnostic.run_full_diagnosis()
    
    if success:
        logger.info("🎉 所有诊断通过！")
        return 0
    else:
        logger.error("💥 发现问题，请查看建议和解决方案")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)