#!/usr/bin/env python3
"""
测试ASR上下文功能的API端点
"""

import os
import sys
import logging
import requests
import json
import time
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ASRContextAPITester:
    """ASR上下文API测试器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_asr_context_stats(self):
        """测试获取ASR上下文统计信息"""
        logger.info("🧪 测试ASR上下文统计API...")
        
        try:
            response = self.session.get(f"{self.base_url}/asr/context/stats")
            
            if response.status_code == 200:
                data = response.json()
                logger.info("✅ ASR上下文统计API调用成功")
                logger.info(f"📊 统计信息: {json.dumps(data, ensure_ascii=False, indent=2)}")
                return True
            else:
                logger.error(f"❌ API调用失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ API调用异常: {e}")
            return False
    
    def test_process_audio_with_context(self):
        """测试带上下文的音频处理API"""
        logger.info("🧪 测试带上下文的音频处理API...")
        
        # 创建一个简单的WAV文件用于测试
        import wave
        import numpy as np
        
        # 生成测试音频
        sample_rate = 16000
        duration = 2.0
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = (np.sin(2 * np.pi * 440 * t) * 0.3 * 32767).astype(np.int16)
        
        # 保存为临时WAV文件
        temp_wav = "temp_test_audio.wav"
        with wave.open(temp_wav, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        try:
            session_id = f"test_session_{int(time.time())}"
            
            # 第一次请求（无上下文）
            logger.info("📤 第一次音频处理请求（无上下文）...")
            with open(temp_wav, 'rb') as audio_file:
                files = {'file': ('test_audio.wav', audio_file, 'audio/wav')}
                data = {
                    'user_id': 1,
                    'npc_id': 1,
                    'session_id': session_id
                }
                
                response1 = self.session.post(
                    f"{self.base_url}/process-audio",
                    files=files,
                    data=data,
                    timeout=30
                )
            
            if response1.status_code == 200:
                result1 = response1.json()
                logger.info("✅ 第一次音频处理成功")
                logger.info(f"📝 转录结果: {result1.get('transcription', 'N/A')}")
                logger.info(f"🧠 使用上下文: {result1.get('context_used', False)}")
                logger.info(f"📊 历史长度: {result1.get('conversation_history_length', 0)}")
                
                # 第二次请求（应该有上下文）
                logger.info("📤 第二次音频处理请求（应该有上下文）...")
                with open(temp_wav, 'rb') as audio_file:
                    files = {'file': ('test_audio.wav', audio_file, 'audio/wav')}
                    data = {
                        'user_id': 1,
                        'npc_id': 1,
                        'session_id': session_id
                    }
                    
                    response2 = self.session.post(
                        f"{self.base_url}/process-audio",
                        files=files,
                        data=data,
                        timeout=30
                    )
                
                if response2.status_code == 200:
                    result2 = response2.json()
                    logger.info("✅ 第二次音频处理成功")
                    logger.info(f"📝 转录结果: {result2.get('transcription', 'N/A')}")
                    logger.info(f"🧠 使用上下文: {result2.get('context_used', False)}")
                    logger.info(f"📊 历史长度: {result2.get('conversation_history_length', 0)}")
                    
                    # 验证上下文使用情况
                    if result2.get('context_used') and result2.get('conversation_history_length', 0) > 0:
                        logger.info("🎉 上下文功能正常工作！")
                        return True
                    else:
                        logger.warning("⚠️ 第二次请求未使用上下文，可能是正常情况")
                        return True
                else:
                    logger.error(f"❌ 第二次音频处理失败: {response2.status_code} - {response2.text}")
                    return False
            else:
                logger.error(f"❌ 第一次音频处理失败: {response1.status_code} - {response1.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 音频处理测试异常: {e}")
            return False
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_wav)
            except:
                pass
    
    def test_session_context_info(self, session_id):
        """测试获取会话上下文信息"""
        logger.info(f"🧪 测试获取会话上下文信息: {session_id}")
        
        try:
            response = self.session.get(f"{self.base_url}/asr/context/sessions/{session_id}")
            
            if response.status_code == 200:
                data = response.json()
                logger.info("✅ 会话上下文信息获取成功")
                logger.info(f"📊 会话信息: {json.dumps(data, ensure_ascii=False, indent=2)}")
                return True
            elif response.status_code == 404:
                logger.info("ℹ️ 会话不存在（正常情况）")
                return True
            else:
                logger.error(f"❌ 获取会话信息失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 获取会话信息异常: {e}")
            return False
    
    def test_clear_session_context(self, session_id):
        """测试清除会话上下文"""
        logger.info(f"🧪 测试清除会话上下文: {session_id}")
        
        try:
            response = self.session.post(f"{self.base_url}/asr/context/sessions/{session_id}/clear")
            
            if response.status_code == 200:
                data = response.json()
                logger.info("✅ 会话上下文清除成功")
                logger.info(f"📝 响应: {data.get('message', 'N/A')}")
                return True
            elif response.status_code == 404:
                logger.info("ℹ️ 会话不存在（正常情况）")
                return True
            else:
                logger.error(f"❌ 清除会话上下文失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 清除会话上下文异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有API测试"""
        logger.info("🚀 开始ASR上下文API测试...")
        
        tests = [
            ("ASR上下文统计", self.test_asr_context_stats),
            ("带上下文的音频处理", self.test_process_audio_with_context),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n{'='*50}")
                logger.info(f"🧪 运行测试: {test_name}")
                logger.info(f"{'='*50}")
                
                result = test_func()
                if result:
                    logger.info(f"✅ 测试通过: {test_name}")
                    passed += 1
                else:
                    logger.error(f"❌ 测试失败: {test_name}")
                    failed += 1
                    
            except Exception as e:
                logger.error(f"❌ 测试异常: {test_name} - {e}")
                failed += 1
        
        # 额外测试：会话管理
        test_session_id = f"api_test_session_{int(time.time())}"
        
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 运行测试: 会话上下文管理")
        logger.info(f"{'='*50}")
        
        try:
            # 测试获取不存在的会话
            if self.test_session_context_info(test_session_id):
                passed += 1
            else:
                failed += 1
            
            # 测试清除不存在的会话
            if self.test_clear_session_context(test_session_id):
                passed += 1
            else:
                failed += 1
                
        except Exception as e:
            logger.error(f"❌ 会话管理测试异常: {e}")
            failed += 2
        
        # 输出测试总结
        logger.info(f"\n{'='*50}")
        logger.info(f"🎯 API测试总结")
        logger.info(f"{'='*50}")
        logger.info(f"✅ 通过: {passed}")
        logger.info(f"❌ 失败: {failed}")
        logger.info(f"📊 总计: {passed + failed}")
        
        if failed == 0:
            logger.info("🎉 所有API测试都通过了！")
        else:
            logger.warning(f"⚠️ 有 {failed} 个测试失败")
        
        return failed == 0


def main():
    """主函数"""
    logger.info("🎯 ASR上下文功能API测试")
    
    # 检查服务器是否运行
    base_url = "http://localhost:8000"
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            logger.info(f"✅ 服务器运行正常: {base_url}")
        else:
            logger.error(f"❌ 服务器响应异常: {response.status_code}")
            return 1
    except Exception as e:
        logger.error(f"❌ 无法连接到服务器 {base_url}: {e}")
        logger.info("💡 请确保后端服务器正在运行")
        return 1
    
    # 运行测试
    tester = ASRContextAPITester(base_url)
    success = tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有API测试完成，功能正常！")
        return 0
    else:
        logger.error("❌ 部分API测试失败，请检查日志")
        return 1


if __name__ == "__main__":
    exit(main())
