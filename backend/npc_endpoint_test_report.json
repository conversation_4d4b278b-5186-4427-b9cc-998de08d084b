{"timestamp": "2025-08-11T12:14:15.817685", "test_type": "endpoint_testing", "base_url": "http://localhost:8000", "summary": {"total_tests": 7, "passed": 7, "warnings": 0, "failed": 0, "success_rate": "100.0%"}, "test_results": [{"test": "get_npcs", "status": "success", "details": {"npc_count": 1, "data_source": "database", "first_npc": {"id": "6c0be9ce-55f6-4bb5-8728-08c31a422f6b", "name": "周可心", "description": "周可心，23岁，研究生。活泼开朗，善于倾听，对新技术充满好奇。来自德国柏林的研究生，专业是数字媒体设计，对AI和人机交互很感兴趣", "avatar_url": null, "is_active": true}}}, {"test": "get_npc_detail", "status": "success", "details": {"npc_id": "6c0be9ce-55f6-4bb5-8728-08c31a422f6b", "npc_name": "周可心", "has_description": true, "has_system_prompt": true, "has_persona_data": true}}, {"test": "get_npc_persona", "status": "success", "details": {"npc_id": "6c0be9ce-55f6-4bb5-8728-08c31a422f6b", "npc_name": "周可心", "persona_fields": ["age", "mbti", "name", "gender", "hobbies", "education", "background", "birth_date", "occupation", "birth_place", "personality", "current_city"], "system_prompt_length": 514}}, {"test": "create_chat_session", "status": "success", "details": {"session_id": 53, "npc_id": "6c0be9ce-55f6-4bb5-8728-08c31a422f6b", "user_id": 14, "npc_name": "周可心"}}, {"test": "add_message", "status": "success", "details": {"session_id": 53, "role": "user", "content": "你好，请介绍一下自己"}}, {"test": "get_session_context", "status": "success", "details": {"session_id": 53, "message_count": 1, "npc_name": "周可心", "has_npc_info": true}}, {"test": "cache_status", "status": "success", "details": {"cache_size": 1, "cache_ttl": 300}}]}