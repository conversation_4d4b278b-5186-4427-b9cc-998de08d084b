#!/usr/bin/env python3
"""
测试后端启动脚本 - 跳过需要torch的服务
"""
import asyncio
import os
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Real-time Voice Chat API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"]
)

@app.get("/")
async def root():
    return {"message": "Real-time Voice Chat API", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    services_status = {}
    
    # Check basic services
    try:
        from services.llm_service import LLMService
        services_status["llm"] = "configured"
    except Exception as e:
        services_status["llm"] = f"error: {str(e)}"
    
    try:
        from services.enhanced_llm_service import get_enhanced_llm_service
        services_status["enhanced_llm"] = "configured"
    except Exception as e:
        services_status["enhanced_llm"] = f"error: {str(e)}"
    
    try:
        from services.mcp_service import MCPService
        services_status["mcp"] = "configured"
    except Exception as e:
        services_status["mcp"] = f"error: {str(e)}"
    
    # Skip torch-dependent services for now
    services_status["vad"] = "skipped (torch dependency)"
    services_status["asr"] = "skipped (torch dependency)"
    
    return {
        "status": "healthy",
        "services": services_status,
        "version": "1.0.0"
    }

if __name__ == "__main__":
    logger.info("🚀 Starting test server...")
    uvicorn.run(app, host="0.0.0.0", port=8000)