import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Keys
BOCHA_API_KEY = os.getenv("BOCHA_API_KEY", "")  # Empty string as default if not found
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "")

# Database configurations
NEWS_MYSQL_CONFIG = {
    'host': os.getenv('NEWS_MYSQL_HOST', 'localhost'),
    'port': int(os.getenv('NEWS_MYSQL_PORT', 3306)),
    'user': os.getenv('NEWS_MYSQL_USER', 'root'),
    'password': os.getenv('NEWS_MYSQL_PASSWORD', ''),
    'database': os.getenv('NEWS_MYSQL_DATABASE', 'news_db')
}

CHAT_HISTORY_CONFIG = {
    'host': os.getenv('CHAT_HISTORY_HOST', 'localhost'),
    'port': int(os.getenv('CHAT_HISTORY_PORT', 3306)),
    'user': os.getenv('CHAT_HISTORY_USER', 'root'),
    'password': os.getenv('CHAT_HISTORY_PASSWORD', ''),
    'database': os.getenv('CHAT_HISTORY_DATABASE', 'chat_history_db')
}

# Elasticsearch configuration
ELASTICSEARCH_CONFIG = {
    'hosts': [os.getenv('ELASTICSEARCH_HOST', 'localhost:9200')],
    'http_auth': (os.getenv('ELASTICSEARCH_USER', ''), os.getenv('ELASTICSEARCH_PASSWORD', '')),
    'use_ssl': os.getenv('ELASTICSEARCH_USE_SSL', 'False').lower() == 'true',
    'verify_certs': os.getenv('ELASTICSEARCH_VERIFY_CERTS', 'True').lower() == 'true',
    'news_index': os.getenv('ELASTICSEARCH_NEWS_INDEX', 'news_index'),
    'npc_memory_index': os.getenv('ELASTICSEARCH_NPC_MEMORY_INDEX', 'npc_memory_index'),
    'npc_log_index': os.getenv('ELASTICSEARCH_NPC_LOG_INDEX', 'npc_log_index')
}

# Other configurations can be added here as needed
