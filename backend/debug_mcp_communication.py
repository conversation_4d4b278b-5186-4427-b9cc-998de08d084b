#!/usr/bin/env python3
"""
调试MCP服务器通信问题
"""

import subprocess
import json
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_mcp_server(command, args):
    """调试MCP服务器通信"""
    print(f"🔍 调试MCP服务器: {command} {' '.join(args)}")
    
    try:
        # 启动服务器进程
        full_command = [command] + args
        print(f"📝 完整命令: {' '.join(full_command)}")
        
        process = subprocess.Popen(
            full_command,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        print(f"✅ 进程启动成功，PID: {process.pid}")
        
        # 等待一下让服务器初始化
        time.sleep(2)
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            print(f"❌ 进程已退出，返回码: {process.returncode}")
            stderr_output = process.stderr.read()
            if stderr_output:
                print(f"错误输出: {stderr_output}")
            return
        
        # 发送初始化请求
        print("📤 发送初始化请求...")
        init_request = {
            "jsonrpc": "2.0",
            "id": 0,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "clientInfo": {
                    "name": "debug-client",
                    "version": "1.0.0"
                }
            }
        }
        
        init_json = json.dumps(init_request) + "\n"
        print(f"发送: {init_json.strip()}")
        process.stdin.write(init_json)
        process.stdin.flush()
        
        # 读取初始化响应
        print("📥 等待初始化响应...")
        init_response = process.stdout.readline()
        print(f"收到初始化响应: {repr(init_response)}")
        
        if init_response.strip():
            try:
                init_data = json.loads(init_response.strip())
                print(f"初始化响应解析成功: {init_data}")
            except json.JSONDecodeError as e:
                print(f"初始化响应JSON解析失败: {e}")
        
        # 发送工具列表请求
        print("📤 发送工具列表请求...")
        tools_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list",
            "params": {}
        }
        
        tools_json = json.dumps(tools_request) + "\n"
        print(f"发送: {tools_json.strip()}")
        process.stdin.write(tools_json)
        process.stdin.flush()
        
        # 读取工具列表响应
        print("📥 等待工具列表响应...")
        tools_response = process.stdout.readline()
        print(f"收到工具列表响应: {repr(tools_response)}")
        
        if tools_response.strip():
            try:
                tools_data = json.loads(tools_response.strip())
                print(f"工具列表响应解析成功: {tools_data}")
                
                if "result" in tools_data and "tools" in tools_data["result"]:
                    tools = tools_data["result"]["tools"]
                    print(f"✅ 找到 {len(tools)} 个工具:")
                    for tool in tools:
                        print(f"  - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
                else:
                    print("⚠️ 响应中没有找到工具列表")
                    
            except json.JSONDecodeError as e:
                print(f"工具列表响应JSON解析失败: {e}")
        else:
            print("❌ 工具列表响应为空")
        
        # 检查stderr是否有错误信息
        print("📋 检查错误输出...")
        process.stderr.settimeout = 1
        try:
            stderr_data = process.stderr.read()
            if stderr_data:
                print(f"错误输出: {stderr_data}")
        except:
            pass
        
        # 清理
        print("🧹 清理进程...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        
        print("✅ 调试完成")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🐛 MCP服务器通信调试")
    print("=" * 50)
    
    # 测试有问题的howtocook-mcp服务器
    print("\n🍳 测试 howtocook-mcp 服务器...")
    debug_mcp_server("npx", ["-y", "howtocook-mcp"])
    
    print("\n" + "=" * 50)
    print("🎯 调试建议:")
    print("1. 检查MCP服务器是否正确实现了MCP协议")
    print("2. 确认服务器是否需要初始化请求")
    print("3. 检查服务器的stdout/stderr输出")
    print("4. 验证JSON-RPC 2.0协议格式")

if __name__ == "__main__":
    main()
