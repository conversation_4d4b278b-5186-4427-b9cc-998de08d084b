import asyncio
import json
import logging
from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, Optional
import numpy as np
import base64
from datetime import datetime

logger = logging.getLogger(__name__)

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, Dict] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_sessions[user_id] = {
            "session_id": None,
            "npc_id": None,
            "conversation_history": [],
            "is_speaking": False,
            "audio_buffer": []
        }
        logger.info(f"✅ User {user_id} connected")
    
    def disconnect(self, user_id: str):
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        logger.info(f"🔌 User {user_id} disconnected")
    
    def is_connected(self, user_id: str) -> bool:
        return user_id in self.active_connections
    
    async def send_message(self, user_id: str, message: dict):
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(message))
                logger.info(f"📤 Message sent to {user_id}: {message.get('type', 'unknown')}")
            except Exception as e:
                logger.error(f"❌ Error sending message to {user_id}: {e}")
                self.disconnect(user_id)

# 全局连接管理器
manager = ConnectionManager()

async def websocket_endpoint_fixed(websocket: WebSocket, user_id: str):
    """修复的WebSocket端点，避免死循环问题"""
    logger.info(f"🔌 WebSocket connection attempt for user: {user_id}")
    
    try:
        # 连接WebSocket
        await manager.connect(websocket, user_id)
        
        # 发送连接成功消息
        await manager.send_message(user_id, {
            "type": "connected",
            "message": "WebSocket connected successfully",
            "user_id": user_id,
            "timestamp": datetime.now().isoformat()
        })
        
        # 主消息循环 - 修复死循环问题
        while manager.is_connected(user_id):
            try:
                # 设置合理的超时时间
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                logger.info(f"📥 Received message from {user_id}: {len(data)} chars")
                
                # 解析消息
                try:
                    message = json.loads(data)
                    await handle_websocket_message(user_id, message)
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON decode error from {user_id}: {e}")
                    await manager.send_message(user_id, {
                        "type": "error",
                        "message": "Invalid JSON format"
                    })
                
            except asyncio.TimeoutError:
                # 发送心跳检查连接状态
                try:
                    await manager.send_message(user_id, {
                        "type": "heartbeat",
                        "timestamp": datetime.now().isoformat()
                    })
                    logger.debug(f"💓 Heartbeat sent to {user_id}")
                except Exception:
                    # 如果心跳发送失败，说明连接已断开
                    logger.info(f"💔 Heartbeat failed for {user_id}, disconnecting")
                    break
                    
            except WebSocketDisconnect:
                logger.info(f"🔌 WebSocket disconnect for {user_id}")
                break
                
            except Exception as e:
                logger.error(f"❌ Unexpected error in WebSocket loop for {user_id}: {e}")
                # 发送错误消息但不断开连接
                try:
                    await manager.send_message(user_id, {
                        "type": "error",
                        "message": f"Server error: {str(e)}"
                    })
                except Exception:
                    # 如果发送错误消息也失败，则断开连接
                    break
    
    except WebSocketDisconnect:
        logger.info(f"🔌 WebSocket disconnected during setup for {user_id}")
    except Exception as e:
        logger.error(f"❌ WebSocket setup error for {user_id}: {e}")
    finally:
        # 确保清理连接
        manager.disconnect(user_id)
        logger.info(f"🧹 Cleanup completed for {user_id}")

async def handle_websocket_message(user_id: str, message: dict):
    """处理WebSocket消息"""
    message_type = message.get("type", "unknown")
    logger.info(f"🔄 Processing message type '{message_type}' from {user_id}")
    
    try:
        if message_type == "start_session":
            await handle_start_session(user_id, message)
        elif message_type == "audio_chunk":
            await handle_audio_chunk(user_id, message)
        elif message_type == "interrupt":
            await handle_interrupt(user_id, message)
        elif message_type == "end_session":
            await handle_end_session(user_id, message)
        elif message_type == "ping":
            await handle_ping(user_id, message)
        else:
            logger.warning(f"❓ Unknown message type '{message_type}' from {user_id}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            })
    
    except Exception as e:
        logger.error(f"❌ Error handling message '{message_type}' from {user_id}: {e}")
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Error processing {message_type}: {str(e)}"
        })

async def handle_start_session(user_id: str, message: dict):
    """处理开始会话"""
    npc_id = message.get("npc_id")
    if not npc_id:
        await manager.send_message(user_id, {
            "type": "error",
            "message": "Missing npc_id"
        })
        return
    
    # 生成会话ID
    session_id = f"session_{user_id}_{npc_id}_{int(datetime.now().timestamp())}"
    
    # 更新用户会话信息
    if user_id in manager.user_sessions:
        manager.user_sessions[user_id]["session_id"] = session_id
        manager.user_sessions[user_id]["npc_id"] = npc_id
    
    await manager.send_message(user_id, {
        "type": "session_started",
        "session_id": session_id,
        "npc_id": npc_id,
        "message": "Session started successfully"
    })
    
    logger.info(f"✅ Session started for {user_id}: {session_id}")

async def handle_audio_chunk(user_id: str, message: dict):
    """处理音频块"""
    try:
        audio_data_b64 = message.get("data")
        if not audio_data_b64:
            await manager.send_message(user_id, {
                "type": "error",
                "message": "Missing audio data"
            })
            return
        
        # 解码音频数据
        try:
            audio_data = base64.b64decode(audio_data_b64)
            logger.info(f"🎵 Audio chunk received from {user_id}: {len(audio_data)} bytes")
        except Exception as e:
            logger.error(f"❌ Failed to decode audio data from {user_id}: {e}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": "Invalid audio data encoding"
            })
            return
        
        # 添加到音频缓冲区
        if user_id in manager.user_sessions:
            buffer = manager.user_sessions[user_id]["audio_buffer"]
            buffer.append(audio_data)
            
            # 当缓冲区达到一定大小时处理音频
            total_size = sum(len(chunk) for chunk in buffer)
            if total_size >= 16000:  # 约1秒的音频数据
                await process_audio_buffer(user_id)
    
    except Exception as e:
        logger.error(f"❌ Error processing audio chunk from {user_id}: {e}")
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Audio processing error: {str(e)}"
        })

async def process_audio_buffer(user_id: str):
    """处理音频缓冲区"""
    try:
        if user_id not in manager.user_sessions:
            return
        
        buffer = manager.user_sessions[user_id]["audio_buffer"]
        if not buffer:
            return
        
        # 合并音频数据
        combined_audio = b''.join(buffer)
        buffer.clear()
        
        logger.info(f"🎤 Processing audio buffer for {user_id}: {len(combined_audio)} bytes")
        
        # 模拟ASR处理
        transcription = await simulate_asr_processing(combined_audio)
        
        # 发送转录结果
        await manager.send_message(user_id, {
            "type": "transcription",
            "text": transcription,
            "confidence": 0.95
        })
        
        # 模拟LLM处理
        response = await simulate_llm_processing(transcription)
        
        # 模拟TTS处理
        audio_response = await simulate_tts_processing(response)
        
        # 发送音频响应
        await manager.send_message(user_id, {
            "type": "audio_chunk",
            "data": base64.b64encode(audio_response).decode('utf-8')
        })
        
        # 发送完成信号
        await manager.send_message(user_id, {
            "type": "response_complete",
            "message": "Response processing completed"
        })
        
    except Exception as e:
        logger.error(f"❌ Error processing audio buffer for {user_id}: {e}")
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Audio buffer processing error: {str(e)}"
        })

async def simulate_asr_processing(audio_data: bytes) -> str:
    """模拟ASR处理"""
    await asyncio.sleep(0.5)  # 模拟处理时间
    return "这是模拟的语音识别结果"

async def simulate_llm_processing(text: str) -> str:
    """模拟LLM处理"""
    await asyncio.sleep(1.0)  # 模拟处理时间
    return f"我听到你说：{text}。这是我的回复。"

async def simulate_tts_processing(text: str) -> bytes:
    """模拟TTS处理"""
    await asyncio.sleep(0.5)  # 模拟处理时间
    # 生成模拟的音频数据
    audio_length = len(text) * 1000  # 根据文本长度生成音频
    return bytes([i % 256 for i in range(audio_length)])

async def handle_interrupt(user_id: str, message: dict):
    """处理中断"""
    logger.info(f"⏸️ Interrupt received from {user_id}")
    
    # 清空音频缓冲区
    if user_id in manager.user_sessions:
        manager.user_sessions[user_id]["audio_buffer"].clear()
    
    await manager.send_message(user_id, {
        "type": "interrupted",
        "message": "Processing interrupted"
    })

async def handle_end_session(user_id: str, message: dict):
    """处理结束会话"""
    logger.info(f"🔚 End session received from {user_id}")
    
    # 清理会话数据
    if user_id in manager.user_sessions:
        manager.user_sessions[user_id]["session_id"] = None
        manager.user_sessions[user_id]["npc_id"] = None
        manager.user_sessions[user_id]["audio_buffer"].clear()
    
    await manager.send_message(user_id, {
        "type": "session_ended",
        "message": "Session ended successfully"
    })

async def handle_ping(user_id: str, message: dict):
    """处理ping消息"""
    await manager.send_message(user_id, {
        "type": "pong",
        "timestamp": datetime.now().isoformat()
    })