-- 创建基础表的SQL脚本

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    nickname VARCHAR(100),
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- NPCs table for different conversation prompts
CREATE TABLE IF NOT EXISTS npcs (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    system_prompt TEXT NOT NULL,
    avatar_url TEXT,
    is_active <PERSON><PERSON><PERSON>EAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conversation sessions
CREATE TABLE IF NOT EXISTS conversation_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    npc_id INTEGER REFERENCES npcs(id) ON DELETE CASCADE,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- Conversation messages
CREATE TABLE IF NOT EXISTS conversation_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES conversation_sessions(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'developer', 'tool')),
    content TEXT NOT NULL,
    audio_url TEXT,
    emotion VARCHAR(50),
    speed DECIMAL(3,1),
    tool_calls JSONB,
    tool_call_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_conversation_sessions_user_id ON conversation_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_session_id ON conversation_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at ON conversation_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_role ON conversation_messages(role);

-- Insert default NPCs
INSERT INTO npcs (name, description, system_prompt, is_active) VALUES 
('默认助手', '通用AI助手', '你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>
<THINK>
## 1. 意图分析: [分析用户意图]
## 2. 行动规划: [规划回应策略]
## 3. 工具选择与参数构建: [如需要]
## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>
## 5. 最终输出序列: <SPEAK>
</THINK>
<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>
<JUDGE>
本轮表现：[评分]/10分
优点：[优点分析]
缺点：[缺点分析]
重大失误：[如有]
</JUDGE>
</turn>', true),
('朋友', '亲密朋友角色', '你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>
<THINK>
## 1. 意图分析: [分析用户意图]
## 2. 行动规划: [规划回应策略]
## 3. 工具选择与参数构建: [如需要]
## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>
## 5. 最终输出序列: <SPEAK>
</THINK>
<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>
<JUDGE>
本轮表现：[评分]/10分
优点：[优点分析]
缺点：[缺点分析]
重大失误：[如有]
</JUDGE>
</turn>', true)
ON CONFLICT DO NOTHING;

-- 创建以0802为前缀的表

-- 0802 Users table
CREATE TABLE IF NOT EXISTS "0802_users" (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    nickname VARCHAR(100),
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 0802 NPCs table for different conversation prompts
CREATE TABLE IF NOT EXISTS "0802_npcs" (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    system_prompt TEXT NOT NULL,
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 0802 Conversation sessions
CREATE TABLE IF NOT EXISTS "0802_conversation_sessions" (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id INTEGER REFERENCES "0802_users"(id) ON DELETE CASCADE,
    npc_id INTEGER REFERENCES "0802_npcs"(id) ON DELETE CASCADE,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- 0802 Conversation messages
CREATE TABLE IF NOT EXISTS "0802_conversation_messages" (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES "0802_conversation_sessions"(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'developer', 'tool')),
    content TEXT NOT NULL,
    audio_url TEXT,
    emotion VARCHAR(50),
    speed DECIMAL(3,1),
    tool_calls JSONB,
    tool_call_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for 0802 tables
CREATE INDEX IF NOT EXISTS idx_0802_users_username ON "0802_users"(username);
CREATE INDEX IF NOT EXISTS idx_0802_users_email ON "0802_users"(email);
CREATE INDEX IF NOT EXISTS idx_0802_conversation_sessions_user_id ON "0802_conversation_sessions"(user_id);
CREATE INDEX IF NOT EXISTS idx_0802_conversation_messages_session_id ON "0802_conversation_messages"(session_id);
CREATE INDEX IF NOT EXISTS idx_0802_conversation_messages_created_at ON "0802_conversation_messages"(created_at);
CREATE INDEX IF NOT EXISTS idx_0802_conversation_messages_role ON "0802_conversation_messages"(role);

-- Insert default 0802 NPCs
INSERT INTO "0802_npcs" (name, description, system_prompt, is_active) VALUES 
('0802默认助手', '0802通用AI助手', '你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>
<THINK>
## 1. 意图分析: [分析用户意图]
## 2. 行动规划: [规划回应策略]
## 3. 工具选择与参数构建: [如需要]
## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>
## 5. 最终输出序列: <SPEAK>
</THINK>
<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>
<JUDGE>
本轮表现：[评分]/10分
优点：[优点分析]
缺点：[缺点分析]
重大失误：[如有]
</JUDGE>
</turn>', true),
('0802朋友', '0802亲密朋友角色', '你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>
<THINK>
## 1. 意图分析: [分析用户意图]
## 2. 行动规划: [规划回应策略]
## 3. 工具选择与参数构建: [如需要]
## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>
## 5. 最终输出序列: <SPEAK>
</THINK>
<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>
<JUDGE>
本轮表现：[评分]/10分
优点：[优点分析]
缺点：[缺点分析]
重大失误：[如有]
</JUDGE>
</turn>', true)
ON CONFLICT DO NOTHING;
