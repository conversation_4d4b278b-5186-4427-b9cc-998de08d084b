#!/usr/bin/env python3
"""
启动后端服务并测试新的NPC功能
"""

import subprocess
import time
import requests
import logging
import sys
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_server_running(url="http://localhost:8000", timeout=30):
    """检查服务器是否运行"""
    logger.info(f"🔍 检查服务器状态: {url}")
    
    for i in range(timeout):
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 服务器正在运行")
                return True
        except requests.exceptions.RequestException:
            pass
        
        if i < timeout - 1:
            logger.info(f"⏳ 等待服务器启动... ({i+1}/{timeout})")
            time.sleep(1)
    
    logger.error("❌ 服务器启动超时")
    return False

def test_basic_endpoints():
    """测试基本端点"""
    logger.info("🧪 测试基本端点...")
    
    endpoints = [
        ("/", "根端点"),
        ("/health", "健康检查"),
        ("/npcs", "NPCs列表"),
        ("/debug/users", "用户调试"),
        ("/debug/sessions", "会话调试")
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=10)
            if response.status_code == 200:
                logger.info(f"✅ {description}: OK")
            else:
                logger.warning(f"⚠️ {description}: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"❌ {description}: {e}")

def run_npc_tests():
    """运行NPC端点测试"""
    logger.info("🎭 运行NPC端点测试...")
    
    try:
        # 运行端点测试脚本
        result = subprocess.run([
            sys.executable, "test_new_npc_endpoints.py"
        ], capture_output=True, text=True, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            logger.info("✅ NPC端点测试通过")
            logger.info("📄 详细结果请查看 npc_endpoint_test_report.json")
        else:
            logger.error("❌ NPC端点测试失败")
            logger.error(f"错误输出: {result.stderr}")
        
        # 显示测试输出
        if result.stdout:
            logger.info("📋 测试输出:")
            for line in result.stdout.split('\n')[-10:]:  # 显示最后10行
                if line.strip():
                    logger.info(f"   {line}")
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"❌ 运行测试脚本失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 启动NPC功能测试...")
    
    # 检查服务器是否已经运行
    if check_server_running():
        logger.info("✅ 服务器已在运行，开始测试")
    else:
        logger.info("🔧 服务器未运行，请手动启动:")
        logger.info("   cd backend && python main.py")
        logger.info("   然后重新运行此脚本")
        return 1
    
    # 测试基本端点
    test_basic_endpoints()
    
    # 等待一下让服务器稳定
    time.sleep(2)
    
    # 运行NPC测试
    success = run_npc_tests()
    
    if success:
        logger.info("🎉 所有测试完成！NPC功能正常")
        logger.info("📊 可以查看以下文件了解详细结果:")
        logger.info("   - npc_endpoint_test_report.json")
        return 0
    else:
        logger.error("💥 测试过程中发现问题")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)