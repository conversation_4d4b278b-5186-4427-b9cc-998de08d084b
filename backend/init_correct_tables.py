#!/usr/bin/env python3
"""
创建正确的数据库表（不带0802前缀）并插入测试用户
"""
import os
import sys
import bcrypt
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def init_correct_tables():
    """初始化正确的数据库表"""
    print("🔧 初始化正确的数据库表...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    try:
        # 1. 创建 users 表（如果不存在）
        print("\n📝 检查 users 表...")
        try:
            result = supabase.table("users").select("*").limit(1).execute()
            print("✅ users 表已存在")
        except Exception:
            print("📝 users 表不存在，尝试创建...")
            # 插入测试数据来创建表
            test_user = {
                "username": "init_test",
                "password_hash": "test_hash",
                "email": "<EMAIL>",
                "nickname": "初始化测试",
                "is_active": True
            }
            try:
                result = supabase.table("users").insert(test_user).execute()
                if result.data:
                    # 删除测试数据
                    supabase.table("users").delete().eq("username", "init_test").execute()
                    print("✅ users 表创建成功")
            except Exception as e:
                print(f"❌ 创建 users 表失败: {e}")
        
        # 2. 创建 npcs 表（如果不存在）
        print("\n📝 检查 npcs 表...")
        try:
            result = supabase.table("npcs").select("*").limit(1).execute()
            print("✅ npcs 表已存在")
        except Exception:
            print("📝 npcs 表不存在，尝试创建...")
            # 插入测试数据来创建表
            test_npc = {
                "name": "init_test_npc",
                "description": "初始化测试NPC",
                "system_prompt": "测试提示",
                "is_active": True
            }
            try:
                result = supabase.table("npcs").insert(test_npc).execute()
                if result.data:
                    # 删除测试数据
                    supabase.table("npcs").delete().eq("name", "init_test_npc").execute()
                    print("✅ npcs 表创建成功")
            except Exception as e:
                print(f"❌ 创建 npcs 表失败: {e}")
        
        # 3. 插入测试用户
        print("\n👤 插入测试用户...")
        
        # 检查 test_user 是否已存在
        result = supabase.table("users").select("*").eq("username", "test_user").execute()
        if result.data:
            print("✅ test_user 已存在")
        else:
            # 创建密码哈希
            password_hash = bcrypt.hashpw("test_password".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            test_user = {
                "username": "test_user",
                "password_hash": password_hash,
                "email": "<EMAIL>",
                "nickname": "测试用户",
                "is_active": True
            }
            
            try:
                result = supabase.table("users").insert(test_user).execute()
                if result.data:
                    print("✅ 测试用户创建成功")
                    print("   用户名: test_user")
                    print("   密码: test_password")
            except Exception as e:
                print(f"❌ 创建测试用户失败: {e}")
        
        # 4. 插入默认 NPCs
        print("\n🤖 插入默认 NPCs...")
        
        default_npcs = [
            {
                "name": "默认助手",
                "description": "通用AI助手，用于测试和日常对话",
                "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。对于数学问题，请直接给出答案。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                "is_active": True,
                "avatar_url": None
            },
            {
                "name": "朋友",
                "description": "亲密朋友角色，轻松聊天伙伴",
                "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                "is_active": True,
                "avatar_url": None
            }
        ]
        
        for npc in default_npcs:
            # 检查是否已存在
            result = supabase.table("npcs").select("*").eq("name", npc["name"]).execute()
            if result.data:
                print(f"✅ NPC '{npc['name']}' 已存在")
            else:
                try:
                    result = supabase.table("npcs").insert(npc).execute()
                    if result.data:
                        print(f"✅ 创建 NPC: {npc['name']}")
                except Exception as e:
                    print(f"❌ 创建 NPC '{npc['name']}' 失败: {e}")
        
        print("\n🎉 数据库初始化完成！")
        print("\n📋 登录信息:")
        print("   用户名: test_user")
        print("   密码: test_password")
        print("   或者可以使用前端的 Guest 登录功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化过程中出错: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始初始化正确的数据库表...")
    
    if init_correct_tables():
        print("\n✅ 初始化成功！")
        sys.exit(0)
    else:
        print("\n❌ 初始化失败")
        sys.exit(1)