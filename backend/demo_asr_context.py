#!/usr/bin/env python3
"""
ASR上下文功能演示脚本
展示增强ASR服务的上下文感知能力
"""

import os
import sys
import logging
import numpy as np
import asyncio
import json
import time
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.enhanced_asr_service import EnhancedASRService
from services.llm_service import LLMService
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ASRContextDemo:
    """ASR上下文功能演示"""
    
    def __init__(self):
        """初始化演示"""
        logger.info("🎯 初始化ASR上下文功能演示...")
        
        # 初始化增强ASR服务
        self.asr_service = EnhancedASRService(
            qwen_timeout=10,
            gemini_timeout=15,
            gemini_first=True,
            enable_context=True
        )
        
        # 初始化LLM服务
        volcano_api_key = os.getenv("VOLCANO_API_KEY")
        volcano_endpoint = os.getenv("VOLCANO_ENDPOINT")
        
        if volcano_api_key and volcano_endpoint:
            self.llm_service = LLMService(volcano_api_key, volcano_endpoint)
            self.llm_available = True
            logger.info("✅ LLM服务初始化成功")
        else:
            logger.warning("⚠️ LLM服务环境变量未设置，将使用模拟模式")
            self.llm_service = LLMService("mock_key", "mock_endpoint")
            self.llm_available = False
        
        # 演示场景
        self.demo_scenarios = [
            {
                "name": "餐厅订餐场景",
                "description": "模拟用户在餐厅订餐的多轮对话",
                "system_prompt": "你是一个智能餐厅助手，帮助用户点餐。请根据对话历史提供个性化服务。",
                "conversations": [
                    "我想点一份意大利面",
                    "要加培根吗",
                    "还要一杯可乐",
                    "总共多少钱",
                    "可以外送吗"
                ]
            },
            {
                "name": "技术支持场景",
                "description": "模拟用户寻求技术支持的对话",
                "system_prompt": "你是一个技术支持专家，帮助用户解决技术问题。请基于之前的对话提供连贯的帮助。",
                "conversations": [
                    "我的电脑开不了机",
                    "电源灯是亮的",
                    "按了开机键没反应",
                    "风扇有转动的声音",
                    "显示器也是黑屏"
                ]
            },
            {
                "name": "旅行规划场景",
                "description": "模拟用户规划旅行的对话",
                "system_prompt": "你是一个旅行规划助手，帮助用户制定旅行计划。请根据用户的偏好和之前的讨论提供建议。",
                "conversations": [
                    "我想去日本旅游",
                    "大概一周时间",
                    "预算在一万左右",
                    "喜欢历史文化景点",
                    "什么时候去比较好"
                ]
            }
        ]
    
    def generate_mock_audio(self, text: str, sample_rate: int = 16000) -> np.ndarray:
        """生成模拟音频数据"""
        # 根据文本长度生成相应长度的音频
        duration = max(1.0, len(text) * 0.15)  # 每个字符0.15秒
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # 生成复合频率信号模拟语音
        frequencies = [150, 300, 600, 1200]  # 模拟人声频率范围
        audio = np.zeros_like(t)
        
        for i, freq in enumerate(frequencies):
            amplitude = 0.1 / (i + 1)  # 递减幅度
            audio += amplitude * np.sin(2 * np.pi * freq * t)
        
        # 添加一些随机噪声模拟真实语音
        noise = np.random.normal(0, 0.01, len(audio))
        audio += noise
        
        return audio.astype(np.float32)
    
    async def demonstrate_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """演示单个场景"""
        logger.info(f"\n{'='*60}")
        logger.info(f"🎬 演示场景: {scenario['name']}")
        logger.info(f"📝 场景描述: {scenario['description']}")
        logger.info(f"{'='*60}")
        
        session_id = f"demo_{scenario['name'].replace(' ', '_').lower()}"
        system_prompt = scenario['system_prompt']
        results = []
        
        for i, user_text in enumerate(scenario['conversations']):
            logger.info(f"\n--- 第 {i+1} 轮对话 ---")
            logger.info(f"👤 用户: {user_text}")
            
            # 生成模拟音频
            audio_data = self.generate_mock_audio(user_text)
            
            # ASR转录（带上下文）
            start_time = time.time()
            asr_result = self.asr_service.transcribe(
                audio_data=audio_data,
                sample_rate=16000,
                session_id=session_id
            )
            asr_time = time.time() - start_time
            
            # 记录ASR结果
            transcribed_text = asr_result.get('text', user_text)  # 如果ASR失败，使用原文本
            context_used = asr_result.get('context_used', False)
            
            logger.info(f"🎤 ASR转录: {transcribed_text}")
            logger.info(f"🧠 使用上下文: {'是' if context_used else '否'}")
            logger.info(f"⏱️ ASR耗时: {asr_time:.2f}秒")
            
            # 获取对话历史用于LLM
            conversation_history = []
            context = self.asr_service.conversation_contexts.get(session_id)
            if context and context.conversation_history:
                for turn in context.conversation_history[-3:]:  # 最近3轮
                    conversation_history.extend([
                        {"role": "user", "content": turn["asr_result"]},
                        {"role": "assistant", "content": f"关于'{turn['asr_result']}'的回复"}
                    ])
            
            # LLM生成回复
            start_time = time.time()
            llm_response = await self.llm_service.generate_response(
                user_input=transcribed_text,
                conversation_history=conversation_history,
                system_prompt=system_prompt
            )
            llm_time = time.time() - start_time
            
            if llm_response.get('success'):
                assistant_text = llm_response['speak_content']['text']
                logger.info(f"🤖 助手回复: {assistant_text}")
            else:
                assistant_text = f"关于'{transcribed_text}'的智能回复"
                logger.info(f"🤖 助手回复 (模拟): {assistant_text}")
            
            logger.info(f"⏱️ LLM耗时: {llm_time:.2f}秒")
            
            # 记录结果
            turn_result = {
                "turn": i + 1,
                "user_input": user_text,
                "asr_result": transcribed_text,
                "context_used": context_used,
                "assistant_response": assistant_text,
                "timing": {
                    "asr": asr_time,
                    "llm": llm_time
                }
            }
            results.append(turn_result)
            
            # 短暂延迟模拟真实对话节奏
            await asyncio.sleep(0.5)
        
        # 获取最终上下文统计
        context_stats = self.asr_service.get_context_stats()
        session_stats = context_stats['sessions'].get(session_id, {})
        
        scenario_summary = {
            "scenario_name": scenario['name'],
            "total_turns": len(results),
            "context_usage_rate": sum(1 for r in results[1:] if r['context_used']) / max(1, len(results) - 1),
            "final_history_length": session_stats.get('history_length', 0),
            "average_asr_time": sum(r['timing']['asr'] for r in results) / len(results),
            "average_llm_time": sum(r['timing']['llm'] for r in results) / len(results),
            "results": results
        }
        
        logger.info(f"\n📊 场景总结:")
        logger.info(f"总轮次: {scenario_summary['total_turns']}")
        logger.info(f"上下文使用率: {scenario_summary['context_usage_rate']:.1%}")
        logger.info(f"最终历史长度: {scenario_summary['final_history_length']}")
        logger.info(f"平均ASR耗时: {scenario_summary['average_asr_time']:.2f}秒")
        logger.info(f"平均LLM耗时: {scenario_summary['average_llm_time']:.2f}秒")
        
        return scenario_summary
    
    async def run_demo(self):
        """运行完整演示"""
        logger.info("🚀 开始ASR上下文功能演示...")
        
        # 检查环境变量
        if not os.getenv("GEMINI_API_KEY"):
            logger.warning("⚠️ GEMINI_API_KEY未设置，ASR可能无法正常工作")
        
        # 显示初始状态
        initial_stats = self.asr_service.get_context_stats()
        logger.info(f"📊 初始状态: {json.dumps(initial_stats, ensure_ascii=False, indent=2)}")
        
        # 运行所有演示场景
        all_results = []
        for scenario in self.demo_scenarios:
            try:
                result = await self.demonstrate_scenario(scenario)
                all_results.append(result)
            except Exception as e:
                logger.error(f"❌ 场景 '{scenario['name']}' 演示失败: {e}")
        
        # 显示最终统计
        final_stats = self.asr_service.get_context_stats()
        logger.info(f"\n{'='*60}")
        logger.info(f"🎯 演示完成 - 最终统计")
        logger.info(f"{'='*60}")
        logger.info(f"📊 最终状态: {json.dumps(final_stats, ensure_ascii=False, indent=2)}")
        
        # 生成演示报告
        demo_report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_scenarios": len(self.demo_scenarios),
            "successful_scenarios": len(all_results),
            "initial_stats": initial_stats,
            "final_stats": final_stats,
            "scenario_results": all_results
        }
        
        # 保存演示报告
        report_file = f"asr_context_demo_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(demo_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 演示报告已保存: {report_file}")
        
        # 显示总结
        if all_results:
            avg_context_usage = sum(r['context_usage_rate'] for r in all_results) / len(all_results)
            total_turns = sum(r['total_turns'] for r in all_results)
            
            logger.info(f"\n🎉 演示总结:")
            logger.info(f"✅ 成功场景: {len(all_results)}/{len(self.demo_scenarios)}")
            logger.info(f"📊 总对话轮次: {total_turns}")
            logger.info(f"🧠 平均上下文使用率: {avg_context_usage:.1%}")
            logger.info(f"💾 最终会话数: {final_stats['total_sessions']}")
        
        return demo_report


async def main():
    """主函数"""
    logger.info("🎯 ASR上下文功能演示程序")
    
    try:
        demo = ASRContextDemo()
        report = await demo.run_demo()
        
        logger.info("🎉 演示完成！")
        return 0
        
    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断演示")
        return 1
    except Exception as e:
        logger.error(f"❌ 演示失败: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
