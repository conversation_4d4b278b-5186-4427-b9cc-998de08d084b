"""
任务处理模块 - 处理与 Supabase 相关的异步任务
"""
import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from supabase import Client
import json

logger = logging.getLogger(__name__)

class TaskManager:
    """任务管理器，处理各种异步任务"""
    
    def __init__(self, supabase_client: Client):
        self.supabase = supabase_client
        self.task_queue = asyncio.Queue()
        self.running_tasks = {}
        self.task_results = {}
        
    async def create_conversation_session(self, user_id: int, npc_id: int) -> Dict[str, Any]:
        """创建对话会话任务"""
        task_id = str(uuid.uuid4())
        logger.info(f"🚀 开始创建对话会话任务 {task_id}: user_id={user_id}, npc_id={npc_id}")
        
        try:
            # 首先验证用户和NPC是否存在
            user_check = await self._check_user_exists(user_id)
            npc_check = await self._check_npc_exists(npc_id)
            
            logger.info(f"📋 用户检查结果: {user_check}")
            logger.info(f"📋 NPC检查结果: {npc_check}")
            
            # 创建会话数据
            session_data = {
                "user_id": user_id,
                "npc_id": npc_id,
                "is_active": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            # 尝试插入到数据库
            try:
                result = self.supabase.table("conversation_sessions").insert(session_data).execute()
                
                if result.data:
                    session_id = result.data[0]["id"]
                    logger.info(f"✅ 成功创建数据库会话: {session_id}")
                    
                    return {
                        "success": True,
                        "session_id": session_id,
                        "task_id": task_id,
                        "source": "database",
                        "data": result.data[0]
                    }
                else:
                    raise Exception("数据库返回空结果")
                    
            except Exception as db_error:
                logger.warning(f"⚠️ 数据库创建会话失败: {db_error}")
                
                # 降级到内存存储
                fallback_session_id = f"mem_{task_id}"
                fallback_data = {
                    **session_data,
                    "id": fallback_session_id
                }
                
                logger.info(f"🔄 使用内存降级方案: {fallback_session_id}")
                
                return {
                    "success": True,
                    "session_id": fallback_session_id,
                    "task_id": task_id,
                    "source": "memory_fallback",
                    "data": fallback_data,
                    "warning": f"数据库不可用: {str(db_error)}"
                }
                
        except Exception as e:
            logger.error(f"❌ 创建对话会话任务失败 {task_id}: {e}")
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e)
            }
    
    async def _check_user_exists(self, user_id: int) -> Dict[str, Any]:
        """检查用户是否存在"""
        try:
            result = self.supabase.table("users").select("id, username, nickname").eq("id", user_id).execute()
            
            if result.data:
                return {
                    "exists": True,
                    "source": "database",
                    "data": result.data[0]
                }
            else:
                # 检查内存用户存储
                from main import SIMPLE_USERS
                for username, user_data in SIMPLE_USERS.items():
                    if user_data["id"] == user_id:
                        return {
                            "exists": True,
                            "source": "memory",
                            "data": user_data
                        }
                
                return {
                    "exists": False,
                    "source": "not_found"
                }
                
        except Exception as e:
            logger.warning(f"用户检查失败: {e}")
            # 降级到内存检查
            try:
                from main import SIMPLE_USERS
                for username, user_data in SIMPLE_USERS.items():
                    if user_data["id"] == user_id:
                        return {
                            "exists": True,
                            "source": "memory_fallback",
                            "data": user_data,
                            "warning": f"数据库检查失败: {str(e)}"
                        }
                
                return {
                    "exists": False,
                    "source": "memory_fallback",
                    "warning": f"数据库检查失败: {str(e)}"
                }
            except Exception as mem_e:
                return {
                    "exists": False,
                    "source": "error",
                    "error": f"数据库和内存检查都失败: {str(e)}, {str(mem_e)}"
                }
    
    async def _check_npc_exists(self, npc_id: int) -> Dict[str, Any]:
        """检查NPC是否存在"""
        try:
            result = self.supabase.table("npcs").select("id, name, description, system_prompt").eq("id", npc_id).execute()
            
            if result.data:
                return {
                    "exists": True,
                    "source": "database",
                    "data": result.data[0]
                }
            else:
                # 使用默认NPC数据
                default_npc = self._get_default_npc(npc_id)
                return {
                    "exists": True,
                    "source": "default",
                    "data": default_npc
                }
                
        except Exception as e:
            logger.warning(f"NPC检查失败: {e}")
            # 降级到默认NPC
            default_npc = self._get_default_npc(npc_id)
            return {
                "exists": True,
                "source": "default_fallback",
                "data": default_npc,
                "warning": f"数据库检查失败: {str(e)}"
            }
    
    def _get_default_npc(self, npc_id: int) -> Dict[str, Any]:
        """获取默认NPC数据"""
        default_npcs = {
            1: {
                "id": 1,
                "name": "默认助手",
                "description": "通用AI助手，用于测试和基本对话",
                "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。保持对话轻松愉快，并尽力帮助用户解决问题。",
                "is_active": True
            },
            2: {
                "id": 2,
                "name": "朋友",
                "description": "亲密朋友角色，轻松聊天",
                "system_prompt": "你是用户的好朋友，用轻松、亲切的语调与用户聊天。可以开玩笑，分享有趣的话题，让对话充满友谊的温暖。",
                "is_active": True
            }
        }
        
        return default_npcs.get(npc_id, default_npcs[1])
    
    async def save_conversation_message(self, session_id: str, role: str, content: str, 
                                      audio_url: Optional[str] = None, 
                                      emotion: Optional[str] = None, 
                                      speed: Optional[float] = None) -> Dict[str, Any]:
        """保存对话消息任务"""
        task_id = str(uuid.uuid4())
        logger.info(f"💾 开始保存消息任务 {task_id}: session_id={session_id}, role={role}")
        
        try:
            message_data = {
                "session_id": session_id,
                "role": role,
                "content": content,
                "audio_url": audio_url,
                "emotion": emotion,
                "speed": speed,
                "created_at": datetime.now().isoformat()
            }
            
            # 检查是否是内存会话
            if session_id.startswith("mem_"):
                logger.info(f"🔄 内存会话，跳过数据库保存: {session_id}")
                return {
                    "success": True,
                    "task_id": task_id,
                    "source": "memory_session",
                    "message": "内存会话，消息未持久化存储"
                }
            
            # 尝试保存到数据库
            try:
                result = self.supabase.table("conversation_messages").insert(message_data).execute()
                
                if result.data:
                    logger.info(f"✅ 成功保存消息到数据库")
                    return {
                        "success": True,
                        "task_id": task_id,
                        "source": "database",
                        "data": result.data[0]
                    }
                else:
                    raise Exception("数据库返回空结果")
                    
            except Exception as db_error:
                logger.warning(f"⚠️ 数据库保存消息失败: {db_error}")
                return {
                    "success": True,
                    "task_id": task_id,
                    "source": "skipped",
                    "warning": f"数据库不可用，消息未保存: {str(db_error)}"
                }
                
        except Exception as e:
            logger.error(f"❌ 保存消息任务失败 {task_id}: {e}")
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e)
            }
    
    async def end_conversation_session(self, session_id: str) -> Dict[str, Any]:
        """结束对话会话任务"""
        task_