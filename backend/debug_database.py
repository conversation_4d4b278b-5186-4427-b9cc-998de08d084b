#!/usr/bin/env python3
"""
调试数据库连接和表结构
"""
import os
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def debug_database():
    """调试数据库连接和表结构"""
    print("🔧 调试数据库连接和表结构...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    # 检查表是否存在
    tables_to_check = ["users", "npcs", "conversation_sessions", "conversation_messages", 
                      "0802_users", "0802_npcs", "0802_conversation_sessions", "0802_conversation_messages"]
    
    for table_name in tables_to_check:
        try:
            # 尝试查询表
            result = supabase.table(table_name).select("*").limit(1).execute()
            print(f"✅ 表 {table_name} 存在")
        except Exception as e:
            print(f"❌ 表 {table_name} 不存在或无法访问: {e}")
    
    # 尝试创建一个测试用户来验证表结构
    try:
        test_user = {
            "username": "debug_test_user",
            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
            "email": "<EMAIL>",
            "nickname": "调试测试用户",
            "is_active": True
        }
        
        result = supabase.table("users").insert(test_user).execute()
        if result.data:
            print("✅ users 表结构正确")
            # 删除测试数据
            supabase.table("users").delete().eq("username", "debug_test_user").execute()
        else:
            print("❌ users 表结构可能有问题")
    except Exception as e:
        print(f"❌ users 表结构验证失败: {e}")
    
    # 尝试创建一个测试NPC来验证表结构
    try:
        test_npc = {
            "name": "debug_test_npc",
            "description": "调试测试NPC",
            "system_prompt": "调试测试提示",
            "is_active": True
        }
        
        result = supabase.table("npcs").insert(test_npc).execute()
        if result.data:
            print("✅ npcs 表结构正确")
            # 删除测试数据
            supabase.table("npcs").delete().eq("name", "debug_test_npc").execute()
        else:
            print("❌ npcs 表结构可能有问题")
    except Exception as e:
        print(f"❌ npcs 表结构验证失败: {e}")
    
    return True

if __name__ == "__main__":
    debug_database()
