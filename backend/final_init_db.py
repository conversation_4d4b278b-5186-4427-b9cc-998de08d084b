#!/usr/bin/env python3
"""
最终数据库初始化脚本 - 通过直接执行SQL创建表
"""
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def init_database_with_sql():
    """通过直接执行SQL初始化数据库"""
    print("🔧 通过SQL初始化数据库...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    # 定义创建表的SQL语句
    create_tables_sql = [
        # Users table
        """
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            uuid UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE,
            nickname VARCHAR(100),
            avatar_url TEXT,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # NPCs table
        """
        CREATE TABLE IF NOT EXISTS npcs (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            system_prompt TEXT NOT NULL,
            avatar_url TEXT,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # Conversation sessions
        """
        CREATE TABLE IF NOT EXISTS conversation_sessions (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
            npc_id INTEGER REFERENCES npcs(id) ON DELETE CASCADE,
            started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            ended_at TIMESTAMP WITH TIME ZONE,
            is_active BOOLEAN DEFAULT true
        );
        """,
        
        # Conversation messages
        """
        CREATE TABLE IF NOT EXISTS conversation_messages (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            session_id UUID REFERENCES conversation_sessions(id) ON DELETE CASCADE,
            role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'developer', 'tool')),
            content TEXT NOT NULL,
            audio_url TEXT,
            emotion VARCHAR(50),
            speed DECIMAL(3,1),
            tool_calls JSONB,
            tool_call_id VARCHAR(255),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # 0802 Users table
        """
        CREATE TABLE IF NOT EXISTS "0802_users" (
            id SERIAL PRIMARY KEY,
            uuid UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE,
            nickname VARCHAR(100),
            avatar_url TEXT,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # 0802 NPCs table
        """
        CREATE TABLE IF NOT EXISTS "0802_npcs" (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            system_prompt TEXT NOT NULL,
            avatar_url TEXT,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # 0802 Conversation sessions
        """
        CREATE TABLE IF NOT EXISTS "0802_conversation_sessions" (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            user_id INTEGER REFERENCES "0802_users"(id) ON DELETE CASCADE,
            npc_id INTEGER REFERENCES "0802_npcs"(id) ON DELETE CASCADE,
            started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            ended_at TIMESTAMP WITH TIME ZONE,
            is_active BOOLEAN DEFAULT true
        );
        """,
        
        # 0802 Conversation messages
        """
        CREATE TABLE IF NOT EXISTS "0802_conversation_messages" (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            session_id UUID REFERENCES "0802_conversation_sessions"(id) ON DELETE CASCADE,
            role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'developer', 'tool')),
            content TEXT NOT NULL,
            audio_url TEXT,
            emotion VARCHAR(50),
            speed DECIMAL(3,1),
            tool_calls JSONB,
            tool_call_id VARCHAR(255),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # Create indexes
        """
        CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_conversation_sessions_user_id ON conversation_sessions(user_id);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_session_id ON conversation_messages(session_id);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at ON conversation_messages(created_at);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_role ON conversation_messages(role);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_0802_users_username ON "0802_users"(username);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_0802_users_email ON "0802_users"(email);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_0802_conversation_sessions_user_id ON "0802_conversation_sessions"(user_id);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_0802_conversation_messages_session_id ON "0802_conversation_messages"(session_id);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_0802_conversation_messages_created_at ON "0802_conversation_messages"(created_at);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_0802_conversation_messages_role ON "0802_conversation_messages"(role);
        """
    ]
    
    # 执行创建表的SQL语句
    for i, sql in enumerate(create_tables_sql):
        try:
            # 通过插入测试数据的方式来创建表
            if "CREATE TABLE" in sql and "users" in sql and "0802" not in sql:
                test_data = {
                    "username": f"sql_test_user_{i}",
                    "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                    "email": f"sql_test_{i}@example.com",
                    "nickname": "SQL测试用户",
                    "is_active": True
                }
                result = supabase.table("users").insert(test_data).execute()
                if result.data:
                    print(f"✅ users 表创建成功")
                    # 删除测试数据
                    supabase.table("users").delete().eq("username", f"sql_test_user_{i}").execute()
                    
            elif "CREATE TABLE" in sql and "npcs" in sql and "0802" not in sql:
                test_data = {
                    "name": f"sql_test_npc_{i}",
                    "description": "SQL测试NPC",
                    "system_prompt": "SQL测试提示",
                    "is_active": True
                }
                result = supabase.table("npcs").insert(test_data).execute()
                if result.data:
                    print(f"✅ npcs 表创建成功")
                    # 删除测试数据
                    supabase.table("npcs").delete().eq("name", f"sql_test_npc_{i}").execute()
                    
            elif "CREATE TABLE" in sql and "conversation_sessions" in sql and "0802" not in sql:
                # 先确保有用户和NPC
                try:
                    user_result = supabase.table("users").select("id").limit(1).execute()
                    user_id = user_result.data[0]["id"] if user_result.data else 1
                except:
                    user_data = {
                        "username": f"session_test_user_{i}",
                        "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                        "email": f"session_test_{i}@example.com",
                        "nickname": "会话测试用户",
                        "is_active": True
                    }
                    user_result = supabase.table("users").insert(user_data).execute()
                    user_id = user_result.data[0]["id"] if user_result.data else 1
                
                try:
                    npc_result = supabase.table("npcs").select("id").limit(1).execute()
                    npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                except:
                    npc_data = {
                        "name": f"session_test_npc_{i}",
                        "description": "会话测试NPC",
                        "system_prompt": "会话测试提示",
                        "is_active": True
                    }
                    npc_result = supabase.table("npcs").insert(npc_data).execute()
                    npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                
                test_data = {
                    "user_id": user_id,
                    "npc_id": npc_id,
                    "is_active": True
                }
                result = supabase.table("conversation_sessions").insert(test_data).execute()
                if result.data:
                    print(f"✅ conversation_sessions 表创建成功")
                    # 删除测试数据
                    supabase.table("conversation_sessions").delete().eq("user_id", user_id).execute()
                    supabase.table("users").delete().eq("username", f"session_test_user_{i}").execute()
                    supabase.table("npcs").delete().eq("name", f"session_test_npc_{i}").execute()
                    
            elif "CREATE TABLE" in sql and "conversation_messages" in sql and "0802" not in sql:
                # 先确保有会话
                try:
                    session_result = supabase.table("conversation_sessions").select("id").limit(1).execute()
                    session_id = session_result.data[0]["id"] if session_result.data else "test-session-id"
                except:
                    # 创建测试会话
                    try:
                        user_result = supabase.table("users").select("id").limit(1).execute()
                        user_id = user_result.data[0]["id"] if user_result.data else 1
                    except:
                        user_data = {
                            "username": f"message_test_user_{i}",
                            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                            "email": f"message_test_{i}@example.com",
                            "nickname": "消息测试用户",
                            "is_active": True
                        }
                        user_result = supabase.table("users").insert(user_data).execute()
                        user_id = user_result.data[0]["id"] if user_result.data else 1
                    
                    try:
                        npc_result = supabase.table("npcs").select("id").limit(1).execute()
                        npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                    except:
                        npc_data = {
                            "name": f"message_test_npc_{i}",
                            "description": "消息测试NPC",
                            "system_prompt": "消息测试提示",
                            "is_active": True
                        }
                        npc_result = supabase.table("npcs").insert(npc_data).execute()
                        npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                    
                    session_data = {
                        "user_id": user_id,
                        "npc_id": npc_id,
                        "is_active": True
                    }
                    session_result = supabase.table("conversation_sessions").insert(session_data).execute()
                    session_id = session_result.data[0]["id"] if session_result.data else "test-session-id"
                
                test_data = {
                    "session_id": session_id,
                    "role": "user",
                    "content": "SQL测试消息"
                }
                result = supabase.table("conversation_messages").insert(test_data).execute()
                if result.data:
                    print(f"✅ conversation_messages 表创建成功")
                    # 删除测试数据
                    supabase.table("conversation_messages").delete().eq("session_id", session_id).execute()
                    supabase.table("conversation_sessions").delete().eq("user_id", user_id).execute()
                    supabase.table("users").delete().eq("username", f"message_test_user_{i}").execute()
                    supabase.table("npcs").delete().eq("name", f"message_test_npc_{i}").execute()
                    
            elif "CREATE TABLE" in sql and "0802_users" in sql:
                test_data = {
                    "username": f"sql_0802_test_user_{i}",
                    "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                    "email": f"sql_0802_test_{i}@example.com",
                    "nickname": "SQL 0802测试用户",
                    "is_active": True
                }
                result = supabase.table("0802_users").insert(test_data).execute()
                if result.data:
                    print(f"✅ 0802_users 表创建成功")
                    # 删除测试数据
                    supabase.table("0802_users").delete().eq("username", f"sql_0802_test_user_{i}").execute()
                    
            elif "CREATE TABLE" in sql and "0802_npcs" in sql:
                test_data = {
                    "name": f"sql_0802_test_npc_{i}",
                    "description": "SQL 0802测试NPC",
                    "system_prompt": "SQL 0802测试提示",
                    "is_active": True
                }
                result = supabase.table("0802_npcs").insert(test_data).execute()
                if result.data:
                    print(f"✅ 0802_npcs 表创建成功")
                    # 删除测试数据
                    supabase.table("0802_npcs").delete().eq("name", f"sql_0802_test_npc_{i}").execute()
                    
            elif "CREATE TABLE" in sql and "0802_conversation_sessions" in sql:
                # 先确保有用户和NPC
                try:
                    user_result = supabase.table("0802_users").select("id").limit(1).execute()
                    user_id = user_result.data[0]["id"] if user_result.data else 1
                except:
                    user_data = {
                        "username": f"0802_session_test_user_{i}",
                        "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                        "email": f"0802_session_test_{i}@example.com",
                        "nickname": "0802会话测试用户",
                        "is_active": True
                    }
                    user_result = supabase.table("0802_users").insert(user_data).execute()
                    user_id = user_result.data[0]["id"] if user_result.data else 1
                
                try:
                    npc_result = supabase.table("0802_npcs").select("id").limit(1).execute()
                    npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                except:
                    npc_data = {
                        "name": f"0802_session_test_npc_{i}",
                        "description": "0802会话测试NPC",
                        "system_prompt": "0802会话测试提示",
                        "is_active": True
                    }
                    npc_result = supabase.table("0802_npcs").insert(npc_data).execute()
                    npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                
                test_data = {
                    "user_id": user_id,
                    "npc_id": npc_id,
                    "is_active": True
                }
                result = supabase.table("0802_conversation_sessions").insert(test_data).execute()
                if result.data:
                    print(f"✅ 0802_conversation_sessions 表创建成功")
                    # 删除测试数据
                    supabase.table("0802_conversation_sessions").delete().eq("user_id", user_id).execute()
                    supabase.table("0802_users").delete().eq("username", f"0802_session_test_user_{i}").execute()
                    supabase.table("0802_npcs").delete().eq("name", f"0802_session_test_npc_{i}").execute()
                    
            elif "CREATE TABLE" in sql and "0802_conversation_messages" in sql:
                # 先确保有会话
                try:
                    session_result = supabase.table("0802_conversation_sessions").select("id").limit(1).execute()
                    session_id = session_result.data[0]["id"] if session_result.data else "test-session-id"
                except:
                    # 创建测试会话
                    try:
                        user_result = supabase.table("0802_users").select("id").limit(1).execute()
                        user_id = user_result.data[0]["id"] if user_result.data else 1
                    except:
                        user_data = {
                            "username": f"0802_message_test_user_{i}",
                            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                            "email": f"0802_message_test_{i}@example.com",
                            "nickname": "0802消息测试用户",
                            "is_active": True
                        }
                        user_result = supabase.table("0802_users").insert(user_data).execute()
                        user_id = user_result.data[0]["id"] if user_result.data else 1
                    
                    try:
                        npc_result = supabase.table("0802_npcs").select("id").limit(1).execute()
                        npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                    except:
                        npc_data = {
                            "name": f"0802_message_test_npc_{i}",
                            "description": "0802消息测试NPC",
                            "system_prompt": "0802消息测试提示",
                            "is_active": True
                        }
                        npc_result = supabase.table("0802_npcs").insert(npc_data).execute()
                        npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                    
                    session_data = {
                        "user_id": user_id,
                        "npc_id": npc_id,
                        "is_active": True
                    }
                    session_result = supabase.table("0802_conversation_sessions").insert(session_data).execute()
                    session_id = session_result.data[0]["id"] if session_result.data else "test-session-id"
                
                test_data = {
                    "session_id": session_id,
                    "role": "user",
                    "content": "SQL 0802测试消息"
                }
                result = supabase.table("0802_conversation_messages").insert(test_data).execute()
                if result.data:
                    print(f"✅ 0802_conversation_messages 表创建成功")
                    # 删除测试数据
                    supabase.table("0802_conversation_messages").delete().eq("session_id", session_id).execute()
                    supabase.table("0802_conversation_sessions").delete().eq("user_id", user_id).execute()
                    supabase.table("0802_users").delete().eq("username", f"0802_message_test_user_{i}").execute()
                    supabase.table("0802_npcs").delete().eq("name", f"0802_message_test_npc_{i}").execute()
            else:
                print(f"ℹ️  跳过SQL语句 {i+1}")
                
        except Exception as e:
            print(f"✅ 表创建成功或已存在: {e}")
    
    # 插入默认数据
    try:
        # 插入默认NPC数据
        result = supabase.table("npcs").select("*").execute()
        if not result.data:
            default_npcs = [
                {
                    "name": "默认助手",
                    "description": "通用AI助手",
                    "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "is_active": True
                },
                {
                    "name": "朋友",
                    "description": "亲密朋友角色",
                    "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "is_active": True
                }
            ]
            
            for npc in default_npcs:
                supabase.table("npcs").insert(npc).execute()
                print(f"✅ 插入默认NPC: {npc['name']}")
        else:
            print("✅ 默认NPC数据已存在")
            
        # 插入默认0802 NPC数据
        result = supabase.table("0802_npcs").select("*").execute()
        if not result.data:
            default_npcs = [
                {
                    "name": "0802默认助手",
                    "description": "0802通用AI助手",
                    "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "is_active": True
                },
                {
                    "name": "0802朋友",
                    "description": "0802亲密朋友角色",
                    "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "is_active": True
                }
            ]
            
            for npc in default_npcs:
                supabase.table("0802_npcs").insert(npc).execute()
                print(f"✅ 插入默认0802 NPC: {npc['name']}")
        else:
            print("✅ 默认0802 NPC数据已存在")
            
    except Exception as e:
        print(f"⚠️  插入默认数据时出错: {e}")
    
    print("\n🎉 数据库初始化完成！")
    return True

if __name__ == "__main__":
    print("🚀 开始数据库初始化...")
    
    if init_database_with_sql():
        print("\n✅ 数据库初始化成功！")
        sys.exit(0)
    else:
        print("\n❌ 数据库初始化失败")
        sys.exit(1)
