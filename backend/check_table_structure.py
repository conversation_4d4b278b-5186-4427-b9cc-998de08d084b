#!/usr/bin/env python3
"""
检查表结构脚本 - 明确识别表名和字段名问题
"""
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def check_table_structure():
    """检查表结构"""
    print("🔍 检查表结构...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    # 检查每个表的结构
    tables_to_check = {
        "users": ["id", "username", "password_hash", "email", "nickname", "is_active"],
        "npcs": ["id", "name", "description", "system_prompt", "is_active"],
        "conversation_sessions": ["id", "user_id", "npc_id", "started_at", "is_active"],
        "conversation_messages": ["id", "session_id", "role", "content"]
    }
    
    print("\n📊 表结构详细检查:")
    
    for table_name, expected_columns in tables_to_check.items():
        print(f"\n--- 检查表 '{table_name}' ---")
        try:
            # 尝试插入测试数据来检查表结构
            if table_name == "users":
                test_data = {
                    "username": f"structure_test_{table_name}",
                    "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                    "email": f"structure_test_{table_name}@example.com",
                    "nickname": f"结构测试{table_name}",
                    "is_active": True
                }
            elif table_name == "npcs":
                test_data = {
                    "name": f"structure_test_{table_name}",
                    "description": f"结构测试{table_name}",
                    "system_prompt": f"结构测试提示{table_name}",
                    "is_active": True
                }
            elif table_name == "conversation_sessions":
                # 先创建测试用户
                user_data = {
                    "username": f"session_test_user_{int(time.time())}",
                    "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
                    "email": f"session_test_{int(time.time())}@example.com",
                    "nickname": "会话测试用户",
                    "is_active": True
                }
                user_result = supabase.table("users").insert(user_data).execute()
                user_id = user_result.data[0]["id"] if user_result.data else 1
                
                # 先创建测试NPC
                npc_data = {
                    "name": f"session_test_npc_{int(time.time())}",
                    "description": "会话测试NPC",
                    "system_prompt": "会话测试提示",
                    "is_active": True
                }
                npc_result = supabase.table("npcs").insert(npc_data).execute()
                npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                
                test_data = {
                    "user_id": user_id,
                    "npc_id": npc_id,
                    "is_active": True
                }
            elif table_name == "conversation_messages":
                # 先创建测试会话
                session_data = {
                    "user_id": 1,
                    "npc_id": 1,
                    "is_active": True
                }
                session_result = supabase.table("conversation_sessions").insert(session_data).execute()
                session_id = session_result.data[0]["id"] if session_result.data else "test-session-id"
                
                test_data = {
                    "session_id": session_id,
                    "role": "user",
                    "content": f"结构测试消息{table_name}"
                }
            
            # 尝试插入数据
            result = supabase.table(table_name).insert(test_data).execute()
            if result.data:
                print(f"✅ 表 '{table_name}' 结构正确，可以插入数据")
                # 清理测试数据
                if table_name == "users":
                    supabase.table(table_name).delete().eq("username", f"structure_test_{table_name}").execute()
                elif table_name == "npcs":
                    supabase.table(table_name).delete().eq("name", f"structure_test_{table_name}").execute()
                elif table_name == "conversation_sessions":
                    supabase.table(table_name).delete().eq("user_id", user_id).execute()
                    supabase.table("users").delete().eq("username", f"session_test_user_{int(time.time())}").execute()
                    supabase.table("npcs").delete().eq("name", f"session_test_npc_{int(time.time())}").execute()
                elif table_name == "conversation_messages":
                    supabase.table(table_name).delete().eq("session_id", session_id).execute()
                    supabase.table("conversation_sessions").delete().eq("user_id", 1).execute()
            else:
                print(f"❌ 表 '{table_name}' 插入数据失败")
                
        except Exception as e:
            print(f"❌ 表 '{table_name}' 检查失败: {e}")
            # 打印详细的错误信息
            if hasattr(e, 'response') and hasattr(e.response, 'json'):
                try:
                    error_details = e.response.json()
                    print(f"   错误详情: {error_details}")
                except:
                    print(f"   错误详情: {str(e)}")
    
    # 检查0802前缀的表
    print("\n--- 检查0802前缀表 ---")
    prefixed_tables = ["0802_users", "0802_npcs", "0802_conversation_sessions", "0802_conversation_messages"]
    
    for table_name in prefixed_tables:
        try:
            # 尝试查询表
            result = supabase.table(table_name).select("*").limit(1).execute()
            print(f"✅ 表 '{table_name}' 存在")
        except Exception as e:
            print(f"❌ 表 '{table_name}' 不存在: {e}")
    
    return True

if __name__ == "__main__":
    import time
    print("🚀 开始表结构检查...")
    
    if check_table_structure():
        print("\n✅ 表结构检查完成！")
        sys.exit(0)
    else:
        print("\n❌ 表结构检查失败")
        sys.exit(1)
