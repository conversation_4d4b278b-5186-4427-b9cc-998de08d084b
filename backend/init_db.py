#!/usr/bin/env python3
"""
数据库初始化脚本 - 创建测试所需的假数据和0802前缀的表
"""
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def init_database():
    """初始化数据库，创建测试数据"""
    print("🔧 初始化数据库...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    # 创建以0802为前缀的表（通过插入数据的方式触发表创建）
    try:
        print("\n📝 创建0802前缀的表...")
        
        # 创建0802_users表
        test_user = {
            "username": "0802_test_user",
            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
            "email": "<EMAIL>",
            "nickname": "0802测试用户",
            "is_active": True
        }
        
        result = supabase.table("0802_users").insert(test_user).execute()
        if result.data:
            print("✅ 0802_users 表创建成功")
            # 删除测试数据
            supabase.table("0802_users").delete().eq("username", "0802_test_user").execute()
        else:
            print("⚠️ 0802_users 表可能已存在")
    except Exception as e:
        print(f"✅ 0802_users 表已存在或创建成功: {e}")
    
    # 创建0802_npcs表
    try:
        test_npc = {
            "name": "0802_test_npc",
            "description": "0802测试NPC",
            "system_prompt": "0802测试提示",
            "is_active": True
        }
        
        result = supabase.table("0802_npcs").insert(test_npc).execute()
        if result.data:
            print("✅ 0802_npcs 表创建成功")
            # 删除测试数据
            supabase.table("0802_npcs").delete().eq("name", "0802_test_npc").execute()
        else:
            print("⚠️ 0802_npcs 表可能已存在")
    except Exception as e:
        print(f"✅ 0802_npcs 表已存在或创建成功: {e}")
    
    # 创建0802_conversation_sessions表
    try:
        # 先创建一个用户和NPC用于外键引用
        user_data = {
            "username": "0802_session_test_user",
            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
            "email": "<EMAIL>",
            "nickname": "0802会话测试用户",
            "is_active": True
        }
        user_result = supabase.table("0802_users").insert(user_data).execute()
        user_id = user_result.data[0]["id"] if user_result.data else 1
        
        npc_data = {
            "name": "0802_session_test_npc",
            "description": "0802会话测试NPC",
            "system_prompt": "0802测试提示",
            "is_active": True
        }
        npc_result = supabase.table("0802_npcs").insert(npc_data).execute()
        npc_id = npc_result.data[0]["id"] if npc_result.data else 1
        
        # 创建会话表
        test_session = {
            "user_id": user_id,
            "npc_id": npc_id,
            "is_active": True
        }
        
        result = supabase.table("0802_conversation_sessions").insert(test_session).execute()
        if result.data:
            print("✅ 0802_conversation_sessions 表创建成功")
            # 删除测试数据
            supabase.table("0802_conversation_sessions").delete().eq("user_id", user_id).execute()
            supabase.table("0802_users").delete().eq("username", "0802_session_test_user").execute()
            supabase.table("0802_npcs").delete().eq("name", "0802_session_test_npc").execute()
        else:
            print("⚠️ 0802_conversation_sessions 表可能已存在")
    except Exception as e:
        print(f"✅ 0802_conversation_sessions 表已存在或创建成功: {e}")
    
    # 创建0802_conversation_messages表
    try:
        # 先创建一个用户、NPC和会话用于外键引用
        user_data = {
            "username": "0802_message_test_user",
            "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",
            "email": "<EMAIL>",
            "nickname": "0802消息测试用户",
            "is_active": True
        }
        user_result = supabase.table("0802_users").insert(user_data).execute()
        user_id = user_result.data[0]["id"] if user_result.data else 1
        
        npc_data = {
            "name": "0802_message_test_npc",
            "description": "0802消息测试NPC",
            "system_prompt": "0802测试提示",
            "is_active": True
        }
        npc_result = supabase.table("0802_npcs").insert(npc_data).execute()
        npc_id = npc_result.data[0]["id"] if npc_result.data else 1
        
        session_data = {
            "user_id": user_id,
            "npc_id": npc_id,
            "is_active": True
        }
        session_result = supabase.table("0802_conversation_sessions").insert(session_data).execute()
        session_id = session_result.data[0]["id"] if session_result.data else "test-session-id"
        
        # 创建消息表
        test_message = {
            "session_id": session_id,
            "role": "user",
            "content": "0802测试消息"
        }
        
        result = supabase.table("0802_conversation_messages").insert(test_message).execute()
        if result.data:
            print("✅ 0802_conversation_messages 表创建成功")
            # 删除测试数据
            supabase.table("0802_conversation_messages").delete().eq("session_id", session_id).execute()
            supabase.table("0802_conversation_sessions").delete().eq("user_id", user_id).execute()
            supabase.table("0802_users").delete().eq("username", "0802_message_test_user").execute()
            supabase.table("0802_npcs").delete().eq("name", "0802_message_test_npc").execute()
        else:
            print("⚠️ 0802_conversation_messages 表可能已存在")
    except Exception as e:
        print(f"✅ 0802_conversation_messages 表已存在或创建成功: {e}")
    
    # 检查并创建测试 NPC 数据
    try:
        print("\n📝 检查 NPC 数据...")
        
        # 先检查是否已有数据
        result = supabase.table("npcs").select("*").execute()
        
        if result.data:
            print(f"✅ 找到 {len(result.data)} 个现有 NPC")
            for npc in result.data:
                print(f"   - ID: {npc['id']}, 名称: {npc['name']}")
        else:
            print("📝 创建测试 NPC 数据...")
            
            # 创建测试 NPC
            test_npcs = [
                {
                    "name": "默认助手",
                    "description": "通用AI助手，用于测试",
                    "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "avatar_url": None,
                    "is_active": True
                },
                {
                    "name": "朋友",
                    "description": "亲密朋友角色，轻松聊天",
                    "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "avatar_url": None,
                    "is_active": True
                }
            ]
            
            for npc_data in test_npcs:
                result = supabase.table("npcs").insert(npc_data).execute()
                if result.data:
                    print(f"✅ 创建 NPC: {npc_data['name']} (ID: {result.data[0]['id']})")
                else:
                    print(f"⚠️ 创建 NPC 失败: {npc_data['name']}")
    
    except Exception as e:
        print(f"❌ NPC 数据操作失败: {e}")
        return False
    
    # 检查并创建测试用户数据
    try:
        print("\n👤 检查用户数据...")
        
        result = supabase.table("users").select("*").execute()
        
        if result.data:
            print(f"✅ 找到 {len(result.data)} 个现有用户")
        else:
            print("📝 创建测试用户数据...")
            
            # 创建测试用户
            test_user = {
                "username": "test_user",
                "password_hash": "$2b$12$dummy.hash.for.testing.purposes.only",  # 假的密码哈希
                "email": "<EMAIL>",
                "nickname": "测试用户",
                "is_active": True
            }
            
            result = supabase.table("users").insert(test_user).execute()
            if result.data:
                print(f"✅ 创建测试用户: {test_user['username']} (ID: {result.data[0]['id']})")
            else:
                print("⚠️ 创建测试用户失败")
    
    except Exception as e:
        print(f"❌ 用户数据操作失败: {e}")
        return False
    
    # 插入默认0802 NPC数据
    try:
        result = supabase.table("0802_npcs").select("*").execute()
        if not result.data:
            default_npcs = [
                {
                    "name": "0802默认助手",
                    "description": "0802通用AI助手",
                    "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "is_active": True
                },
                {
                    "name": "0802朋友",
                    "description": "0802亲密朋友角色",
                    "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                    "is_active": True
                }
            ]
            
            for npc in default_npcs:
                supabase.table("0802_npcs").insert(npc).execute()
                print(f"✅ 插入默认0802 NPC: {npc['name']}")
        else:
            print("✅ 默认0802 NPC数据已存在")
    except Exception as e:
        print(f"⚠️ 插入默认0802 NPC数据时出错: {e}")
    
    print("\n🎉 数据库初始化完成！")
    return True

def test_database_connection():
    """测试数据库连接和数据访问"""
    print("\n🔍 测试数据库连接...")
    
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # 测试 NPC 查询
        npc_result = supabase.table("npcs").select("*").eq("id", 1).execute()
        if npc_result.data:
            npc = npc_result.data[0]
            print(f"✅ 成功获取 NPC ID=1: {npc['name']}")
            return True
        else:
            print("❌ 无法获取 NPC ID=1")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始数据库初始化...")
    
    if init_database():
        if test_database_connection():
            print("\n✅ 所有测试通过！数据库已准备就绪。")
            sys.exit(0)
        else:
            print("\n❌ 数据库连接测试失败")
            sys.exit(1)
    else:
        print("\n❌ 数据库初始化失败")
        sys.exit(1)
