#!/usr/bin/env python3
"""
数据库表初始化脚本 - 创建0802前缀的表（直接方法）
"""
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# 加载环境变量
load_dotenv()

def init_tables():
    """初始化数据库表"""
    print("🔧 初始化数据库表...")
    
    # 初始化 Supabase 客户端
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ 缺少 Supabase 配置，请检查 .env 文件")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        print(f"✅ 连接到 Supabase: {supabase_url}")
    except Exception as e:
        print(f"❌ 无法连接到 Supabase: {e}")
        return False
    
    # 创建以0802为前缀的表
    try:
        print("\n📝 创建0802前缀的表...")
        
        # 检查表是否已存在
        try:
            result = supabase.table("0802_users").select("*").limit(1).execute()
            print("✅ 0802_users 表已存在")
        except Exception:
            print("📝 0802_users 表不存在，尝试创建...")
            # 如果表不存在，插入一条测试数据然后删除
            try:
                test_data = {
                    "username": "test_user",
                    "password_hash": "test_hash",
                    "email": "<EMAIL>",
                    "nickname": "测试用户"
                }
                result = supabase.table("0802_users").insert(test_data).execute()
                if result.data:
                    # 删除测试数据
                    supabase.table("0802_users").delete().eq("username", "test_user").execute()
                    print("✅ 0802_users 表创建成功")
            except Exception as e:
                print(f"❌ 创建 0802_users 表时出错: {e}")
        
        # 检查 NPCs 表是否已存在
        try:
            result = supabase.table("0802_npcs").select("*").limit(1).execute()
            print("✅ 0802_npcs 表已存在")
        except Exception:
            print("📝 0802_npcs 表不存在，尝试创建...")
            # 如果表不存在，插入一条测试数据然后删除
            try:
                test_data = {
                    "name": "test_npc",
                    "description": "测试NPC",
                    "system_prompt": "测试提示"
                }
                result = supabase.table("0802_npcs").insert(test_data).execute()
                if result.data:
                    # 删除测试数据
                    supabase.table("0802_npcs").delete().eq("name", "test_npc").execute()
                    print("✅ 0802_npcs 表创建成功")
            except Exception as e:
                print(f"❌ 创建 0802_npcs 表时出错: {e}")
        
        # 检查会话表是否已存在
        try:
            result = supabase.table("0802_conversation_sessions").select("*").limit(1).execute()
            print("✅ 0802_conversation_sessions 表已存在")
        except Exception:
            print("📝 0802_conversation_sessions 表不存在，尝试创建...")
            # 如果表不存在，插入一条测试数据然后删除
            try:
                # 先创建一个用户和NPC用于外键引用
                user_data = {
                    "username": "session_test_user",
                    "password_hash": "test_hash",
                    "email": "<EMAIL>",
                    "nickname": "会话测试用户"
                }
                user_result = supabase.table("0802_users").insert(user_data).execute()
                user_id = user_result.data[0]["id"] if user_result.data else 1
                
                npc_data = {
                    "name": "session_test_npc",
                    "description": "会话测试NPC",
                    "system_prompt": "测试提示"
                }
                npc_result = supabase.table("0802_npcs").insert(npc_data).execute()
                npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                
                # 创建会话表
                test_data = {
                    "user_id": user_id,
                    "npc_id": npc_id,
                    "is_active": True
                }
                result = supabase.table("0802_conversation_sessions").insert(test_data).execute()
                if result.data:
                    # 删除测试数据
                    supabase.table("0802_conversation_sessions").delete().eq("user_id", user_id).execute()
                    supabase.table("0802_users").delete().eq("username", "session_test_user").execute()
                    supabase.table("0802_npcs").delete().eq("name", "session_test_npc").execute()
                    print("✅ 0802_conversation_sessions 表创建成功")
            except Exception as e:
                print(f"❌ 创建 0802_conversation_sessions 表时出错: {e}")
        
        # 检查消息表是否已存在
        try:
            result = supabase.table("0802_conversation_messages").select("*").limit(1).execute()
            print("✅ 0802_conversation_messages 表已存在")
        except Exception:
            print("📝 0802_conversation_messages 表不存在，尝试创建...")
            # 如果表不存在，插入一条测试数据然后删除
            try:
                # 先创建一个用户、NPC和会话用于外键引用
                user_data = {
                    "username": "message_test_user",
                    "password_hash": "test_hash",
                    "email": "<EMAIL>",
                    "nickname": "消息测试用户"
                }
                user_result = supabase.table("0802_users").insert(user_data).execute()
                user_id = user_result.data[0]["id"] if user_result.data else 1
                
                npc_data = {
                    "name": "message_test_npc",
                    "description": "消息测试NPC",
                    "system_prompt": "测试提示"
                }
                npc_result = supabase.table("0802_npcs").insert(npc_data).execute()
                npc_id = npc_result.data[0]["id"] if npc_result.data else 1
                
                session_data = {
                    "user_id": user_id,
                    "npc_id": npc_id,
                    "is_active": True
                }
                session_result = supabase.table("0802_conversation_sessions").insert(session_data).execute()
                session_id = session_result.data[0]["id"] if session_result.data else "test-session-id"
                
                # 创建消息表
                test_data = {
                    "session_id": session_id,
                    "role": "user",
                    "content": "测试消息"
                }
                result = supabase.table("0802_conversation_messages").insert(test_data).execute()
                if result.data:
                    # 删除测试数据
                    supabase.table("0802_conversation_messages").delete().eq("session_id", session_id).execute()
                    supabase.table("0802_conversation_sessions").delete().eq("user_id", user_id).execute()
                    supabase.table("0802_users").delete().eq("username", "message_test_user").execute()
                    supabase.table("0802_npcs").delete().eq("name", "message_test_npc").execute()
                    print("✅ 0802_conversation_messages 表创建成功")
            except Exception as e:
                print(f"❌ 创建 0802_conversation_messages 表时出错: {e}")
        
        # 插入默认NPC数据
        try:
            result = supabase.table("0802_npcs").select("*").execute()
            if not result.data:
                default_npcs = [
                    {
                        "name": "默认助手",
                        "description": "通用AI助手",
                        "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                        "is_active": True
                    },
                    {
                        "name": "朋友",
                        "description": "亲密朋友角色",
                        "system_prompt": "你是用户的好朋友，用轻松友好的语调聊天。回复格式必须为：<turn>\n<THINK>\n## 1. 意图分析: [分析用户意图]\n## 2. 行动规划: [规划回应策略]\n## 3. 工具选择与参数构建: [如需要]\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.1</speed><text>[回复内容]</text></SPEAK>\n<JUDGE>\n本轮表现：[评分]/10分\n优点：[优点分析]\n缺点：[缺点分析]\n重大失误：[如有]\n</JUDGE>\n</turn>",
                        "is_active": True
                    }
                ]
                
                for npc in default_npcs:
                    supabase.table("0802_npcs").insert(npc).execute()
                    print(f"✅ 插入默认NPC: {npc['name']}")
            else:
                print("✅ 默认NPC数据已存在")
        except Exception as e:
            print(f"⚠️ 插入默认NPC数据时出错: {e}")
        
        print("\n🎉 数据库表初始化完成！")
        return True
        
    except Exception as e:
        print(f"❌ 创建表时出错: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始数据库表初始化...")
    
    if init_tables():
        print("\n✅ 所有表创建成功！")
        sys.exit(0)
    else:
        print("\n❌ 表初始化失败")
        sys.exit(1)
