#!/usr/bin/env python3
"""
模拟HTTP MCP服务器，用于测试HTTP类型的MCP客户端
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import uvicorn
import json

app = FastAPI(title="Mock HTTP MCP Server", version="1.0.0")

class MCPRequest(BaseModel):
    jsonrpc: str = "2.0"
    id: int
    method: str
    params: Optional[Dict[str, Any]] = {}

class MCPResponse(BaseModel):
    jsonrpc: str = "2.0"
    id: int
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None

# 模拟工具定义
MOCK_TOOLS = [
    {
        "name": "get_weather",
        "description": "获取指定城市的天气信息",
        "inputSchema": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string",
                    "description": "城市名称"
                }
            },
            "required": ["city"]
        }
    },
    {
        "name": "search_location",
        "description": "搜索地理位置信息",
        "inputSchema": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "搜索关键词"
                }
            },
            "required": ["query"]
        }
    }
]

@app.get("/")
async def root():
    """根路径，返回服务信息"""
    return {"message": "Mock HTTP MCP Server", "version": "1.0.0"}

@app.post("/mcp")
async def handle_mcp_request(request: MCPRequest):
    """处理MCP请求"""
    try:
        if request.method == "tools/list":
            # 返回工具列表
            return MCPResponse(
                id=request.id,
                result={
                    "tools": MOCK_TOOLS
                }
            )
        
        elif request.method == "tools/call":
            # 处理工具调用
            tool_name = request.params.get("name")
            arguments = request.params.get("arguments", {})
            
            if tool_name == "get_weather":
                city = arguments.get("city", "北京")
                return MCPResponse(
                    id=request.id,
                    result={
                        "content": [
                            {
                                "type": "text",
                                "text": f"{city}的天气：晴天，温度25°C，湿度60%，风速5km/h"
                            }
                        ]
                    }
                )
            
            elif tool_name == "search_location":
                query = arguments.get("query", "")
                return MCPResponse(
                    id=request.id,
                    result={
                        "content": [
                            {
                                "type": "text",
                                "text": f"搜索'{query}'的结果：找到3个相关位置 - {query}市中心、{query}火车站、{query}机场"
                            }
                        ]
                    }
                )
            
            else:
                return MCPResponse(
                    id=request.id,
                    error={
                        "code": -32601,
                        "message": f"未知工具: {tool_name}"
                    }
                )
        
        else:
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32601,
                    "message": f"未知方法: {request.method}"
                }
            )
    
    except Exception as e:
        return MCPResponse(
            id=request.id,
            error={
                "code": -32603,
                "message": f"内部错误: {str(e)}"
            }
        )

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "mock-http-mcp"}

if __name__ == "__main__":
    print("🚀 启动模拟HTTP MCP服务器...")
    print("📍 服务地址: http://localhost:8003")
    print("🔧 MCP端点: http://localhost:8003/mcp")
    uvicorn.run(app, host="0.0.0.0", port=8003)
