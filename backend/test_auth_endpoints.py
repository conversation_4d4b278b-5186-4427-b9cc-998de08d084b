#!/usr/bin/env python3
"""
测试认证端点（注册和登录）
"""
import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:8000"

def test_register_endpoint():
    """测试注册端点"""
    print("1. 测试注册端点 (/auth/register) ...")
    try:
        # 测试数据
        test_data = {
            "username": f"test_user_{int(time.time())}",
            "password": "test_password_123",
            "email": f"test_{int(time.time())}@example.com",
            "nickname": "测试用户"
        }
        
        response = requests.post(f"{BASE_URL}/auth/register", params=test_data)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
            print("   ✅ 注册端点测试通过")
            return True
        elif response.status_code == 422:
            print("   ⚠️  注册端点返回422（参数验证错误）")
            print(f"   响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
            # 尝试另一种方式
            response = requests.post(f"{BASE_URL}/auth/register", data=test_data)
            if response.status_code == 200:
                print("   ✅ 使用表单数据注册成功")
                return True
            else:
                print("   ❌ 注册端点测试失败")
                return False
        else:
            print("   ❌ 注册端点测试失败")
            return False
    except Exception as e:
        print(f"   ❌ 注册端点测试失败: {e}")
        return False

def test_login_endpoint():
    """测试登录端点"""
    print("\n2. 测试登录端点 (/auth/login) ...")
    try:
        # 测试数据
        test_data = {
            "username": "test_user",
            "password": "test_password"
        }
        
        response = requests.post(f"{BASE_URL}/auth/login", params=test_data)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 登录端点测试通过")
            return True
        elif response.status_code == 401:
            print("   ⚠️  登录端点返回401（用户名或密码错误），但端点本身工作正常")
            return True
        elif response.status_code == 422:
            print("   ⚠️  登录端点返回422（参数验证错误）")
            print(f"   响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
            # 尝试另一种方式
            response = requests.post(f"{BASE_URL}/auth/login", data=test_data)
            if response.status_code == 200 or response.status_code == 401:
                print("   ✅ 使用表单数据测试成功")
                return True
            else:
                print("   ❌ 登录端点测试失败")
                return False
        else:
            print("   ❌ 登录端点测试失败")
            return False
    except Exception as e:
        print(f"   ❌ 登录端点测试失败: {e}")
        return False

def test_register_and_login_flow():
    """测试注册和登录流程"""
    print("\n3. 测试注册和登录完整流程 ...")
    try:
        # 生成唯一用户名
        username = f"flow_test_user_{int(time.time())}"
        password = "flow_test_password_123"
        
        # 注册用户
        print("   正在注册用户...")
        register_data = {
            "username": username,
            "password": password,
            "email": f"{username}@example.com",
            "nickname": "流程测试用户"
        }
        
        register_response = requests.post(f"{BASE_URL}/auth/register", params=register_data)
        print(f"   注册状态码: {register_response.status_code}")
        
        if register_response.status_code == 200:
            print("   ✅ 用户注册成功")
            registered_user = register_response.json()["user"]
            
            # 登录用户
            print("   正在登录用户...")
            login_data = {
                "username": username,
                "password": password
            }
            
            login_response = requests.post(f"{BASE_URL}/auth/login", params=login_data)
            print(f"   登录状态码: {login_response.status_code}")
            
            if login_response.status_code == 200:
                print("   ✅ 用户登录成功")
                logged_in_user = login_response.json()["user"]
                
                # 验证用户信息一致性
                if (registered_user["id"] == logged_in_user["id"] and 
                    registered_user["username"] == logged_in_user["username"]):
                    print("   ✅ 用户信息一致性验证通过")
                    return True
                else:
                    print("   ❌ 用户信息不一致")
                    return False
            else:
                print("   ❌ 用户登录失败")
                return False
        else:
            print("   ❌ 用户注册失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 注册和登录流程测试失败: {e}")
        return False

def run_auth_tests():
    """运行认证测试"""
    print("🚀 开始测试认证端点...")
    print("=" * 50)
    print("🔍 测试认证端点...")
    print(f"后端地址: {BASE_URL}")
    
    test_results = []
    
    # 运行各个测试
    test_results.append(test_register_endpoint())
    test_results.append(test_login_endpoint())
    test_results.append(test_register_and_login_flow())
    
    # 统计结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"📊 认证测试结果: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有认证端点测试通过！")
        return True
    else:
        print("⚠️  部分认证端点测试失败。")
        return False

if __name__ == "__main__":
    success = run_auth_tests()
    exit(0 if success else 1)
