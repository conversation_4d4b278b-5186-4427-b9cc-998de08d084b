#!/usr/bin/env python3
"""
调试会话相关端点
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def test_session_endpoints():
    """测试会话端点"""
    
    # 1. 首先获取一个NPC
    logger.info("1. 获取NPCs列表...")
    response = requests.get(f"{BASE_URL}/npcs")
    if response.status_code != 200:
        logger.error(f"获取NPCs失败: {response.status_code}")
        return
    
    npcs = response.json().get("npcs", [])
    if not npcs:
        logger.error("没有可用的NPCs")
        return
    
    npc_id = npcs[0]["id"]
    logger.info(f"使用NPC: {npc_id} ({npcs[0]['name']})")
    
    # 2. 创建会话
    logger.info("2. 创建会话...")
    response = requests.post(f"{BASE_URL}/npcs/{npc_id}/chat", params={"user_id": 14})
    if response.status_code != 200:
        logger.error(f"创建会话失败: {response.status_code} - {response.text}")
        return
    
    session_data = response.json()
    session_id = session_data["session_id"]
    logger.info(f"会话创建成功: {session_id}")
    
    # 3. 测试添加消息 - 使用JSON body而不是query params
    logger.info("3. 添加消息 (使用JSON body)...")
    message_data = {
        "role": "user",
        "content": "你好，这是一条测试消息",
        "emotion": "neutral",
        "speed": 1.0
    }
    
    # 尝试不同的方式发送消息
    logger.info("3a. 尝试使用query parameters...")
    response = requests.post(
        f"{BASE_URL}/sessions/{session_id}/messages",
        params=message_data
    )
    logger.info(f"Query params方式: {response.status_code}")
    if response.status_code == 200:
        logger.info(f"响应: {response.json()}")
    else:
        logger.error(f"错误: {response.text}")
    
    # 4. 检查会话上下文
    logger.info("4. 获取会话上下文...")
    response = requests.get(f"{BASE_URL}/sessions/{session_id}/context")
    if response.status_code == 200:
        context = response.json()
        logger.info(f"会话上下文获取成功:")
        logger.info(f"  会话ID: {context['session_id']}")
        logger.info(f"  NPC: {context['npc']['name']}")
        logger.info(f"  消息数: {context['message_count']}")
        logger.info(f"  消息列表: {len(context['messages'])} 条")
        
        # 显示消息内容
        for i, msg in enumerate(context['messages']):
            logger.info(f"    {i+1}. {msg.get('role', 'unknown')}: {msg.get('content', '')[:50]}...")
    else:
        logger.error(f"获取会话上下文失败: {response.status_code} - {response.text}")
    
    # 5. 再添加一条消息测试
    logger.info("5. 添加第二条消息...")
    message_data2 = {
        "role": "assistant", 
        "content": "你好！我是周可心，很高兴认识你！",
        "emotion": "friendly",
        "speed": 1.1
    }
    
    response = requests.post(
        f"{BASE_URL}/sessions/{session_id}/messages",
        params=message_data2
    )
    
    if response.status_code == 200:
        logger.info("第二条消息添加成功")
    else:
        logger.error(f"第二条消息添加失败: {response.status_code} - {response.text}")
    
    # 6. 再次检查会话上下文
    logger.info("6. 再次获取会话上下文...")
    response = requests.get(f"{BASE_URL}/sessions/{session_id}/context")
    if response.status_code == 200:
        context = response.json()
        logger.info(f"更新后的消息数: {context['message_count']}")
        for i, msg in enumerate(context['messages']):
            logger.info(f"    {i+1}. {msg.get('role', 'unknown')}: {msg.get('content', '')[:50]}...")
    
    # 7. 直接查询数据库中的消息（通过API）
    logger.info("7. 检查数据库中的消息...")
    try:
        # 这里我们可以通过检查服务器日志来了解数据库操作
        logger.info("请检查服务器日志以了解数据库操作详情")
    except Exception as e:
        logger.error(f"检查数据库失败: {e}")

if __name__ == "__main__":
    logger.info("🚀 开始调试会话端点...")
    test_session_endpoints()
    logger.info("✅ 调试完成")