# Deployment Guide - Real-time Voice Chat App

This guide covers deploying the real-time voice chat application to production environments.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │  Backend API    │    │   Supabase DB   │
│   (Frontend)    │◄──►│   (FastAPI)     │◄──►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   App Stores    │    │   Cloud Server  │    │  External APIs  │
│  (iOS/Android)  │    │  (AWS/GCP/etc)  │    │ (Volcano/MiniMax)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Prerequisites

- Python 3.8+
- Flutter 3.0+
- Docker (optional)
- Cloud server (AWS EC2, GCP, etc.)
- Domain name (optional)

## Backend Deployment

### Option 1: Docker Deployment (Recommended)

1. **Create Dockerfile**:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY backend/ .

# Create models directory
RUN mkdir -p models

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

2. **Create docker-compose.yml**:

```yaml
version: '3.8'

services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - VOLCANO_API_KEY=${VOLCANO_API_KEY}
      - VOLCANO_ENDPOINT=${VOLCANO_ENDPOINT}
      - MINIMAX_API_KEY=${MINIMAX_API_KEY}
      - MINIMAX_GROUP_ID=${MINIMAX_GROUP_ID}
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
    restart: unless-stopped
```

3. **Deploy with Docker**:

```bash
# Build and run
docker-compose up -d

# Check logs
docker-compose logs -f backend
```

### Option 2: Direct Server Deployment

1. **Setup server environment**:

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv nginx -y

# Create application directory
sudo mkdir -p /opt/voice-chat-app
sudo chown $USER:$USER /opt/voice-chat-app
cd /opt/voice-chat-app

# Clone or upload your code
git clone <your-repo> .
# or upload files via scp/rsync
```

2. **Setup Python environment**:

```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r backend/requirements.txt

# Install additional system dependencies
sudo apt install libsndfile1 ffmpeg -y
```

3. **Setup systemd service**:

Create `/etc/systemd/system/voice-chat-app.service`:

```ini
[Unit]
Description=Voice Chat App Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/voice-chat-app/backend
Environment=PATH=/opt/voice-chat-app/venv/bin
ExecStart=/opt/voice-chat-app/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable voice-chat-app
sudo systemctl start voice-chat-app
sudo systemctl status voice-chat-app
```

4. **Setup Nginx reverse proxy**:

Create `/etc/nginx/sites-available/voice-chat-app`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/voice-chat-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Frontend Deployment

### Mobile App Deployment

1. **Build for Android**:

```bash
cd frontend

# Build APK
flutter build apk --release

# Build App Bundle (for Google Play)
flutter build appbundle --release
```

2. **Build for iOS**:

```bash
# Build for iOS (requires macOS and Xcode)
flutter build ios --release

# Create IPA file
flutter build ipa --release
```

3. **Deploy to App Stores**:

- **Google Play Store**: Upload the `.aab` file
- **Apple App Store**: Upload the `.ipa` file via Xcode or Transporter

### Web Deployment (Optional)

```bash
# Build for web
flutter build web --release

# Deploy to hosting service (Netlify, Vercel, etc.)
# Upload the build/web directory
```

## Environment Configuration

### Production Environment Variables

Create production `.env` file:

```bash
# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# Volcano Engine
VOLCANO_API_KEY=your-volcano-api-key
VOLCANO_ENDPOINT=https://ark.cn-beijing.volces.com/api/v3/chat/completions

# MiniMax TTS
MINIMAX_API_KEY=your-minimax-api-key
MINIMAX_GROUP_ID=your-group-id

# WeChat (if using)
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# Model paths
SILERO_VAD_MODEL_PATH=/opt/voice-chat-app/models/silero_vad.onnx
FIRERED_ASR_MODEL_PATH=/opt/voice-chat-app/models/firered_asr

# Security
SECRET_KEY=your-secret-key-here
```

### Flutter Configuration

Update `frontend/lib/config/app_config.dart` for production:

```dart
class AppConfig {
  // Production backend URL
  static const String backendUrl = 'https://your-domain.com';
  static const String websocketUrl = 'wss://your-domain.com/ws';
  
  // Other configurations...
}
```

## SSL/HTTPS Setup

### Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Logging

### Setup Application Monitoring

1. **Install monitoring tools**:

```bash
# Install Prometheus and Grafana (optional)
sudo apt install prometheus grafana -y
```

2. **Setup log rotation**:

Create `/etc/logrotate.d/voice-chat-app`:

```
/opt/voice-chat-app/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload voice-chat-app
    endscript
}
```

### Health Checks

Add health check endpoint to your FastAPI app:

```python
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }
```

## Performance Optimization

### Backend Optimization

1. **Use Gunicorn with multiple workers**:

```bash
# Install Gunicorn
pip install gunicorn

# Run with multiple workers
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

2. **Setup Redis for caching** (optional):

```bash
# Install Redis
sudo apt install redis-server -y

# Configure Redis in your app
pip install redis
```

### Database Optimization

1. **Supabase Performance**:
   - Enable connection pooling
   - Add database indexes
   - Use read replicas for heavy queries

2. **Connection Pooling**:

```python
# In your FastAPI app
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=0,
)
```

## Security Considerations

### Backend Security

1. **API Rate Limiting**:

```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.get("/api/endpoint")
@limiter.limit("10/minute")
async def endpoint(request: Request):
    return {"message": "Hello"}
```

2. **CORS Configuration**:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://your-domain.com"],  # Specific origins in production
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
```

3. **Input Validation**:

```python
from pydantic import BaseModel, validator

class AudioRequest(BaseModel):
    audio_data: str
    
    @validator('audio_data')
    def validate_audio_data(cls, v):
        # Add validation logic
        return v
```

### Mobile App Security

1. **API Key Protection**:
   - Use environment-specific configurations
   - Implement certificate pinning
   - Use secure storage for sensitive data

2. **Network Security**:
   - Always use HTTPS/WSS
   - Implement request signing
   - Add network timeout configurations

## Troubleshooting

### Common Issues

1. **WebSocket Connection Issues**:
   - Check firewall settings
   - Verify proxy configuration
   - Test WebSocket endpoint directly

2. **Audio Processing Issues**:
   - Verify model files are present
   - Check audio format compatibility
   - Monitor memory usage

3. **API Rate Limits**:
   - Implement exponential backoff
   - Monitor API usage
   - Cache responses when possible

### Debugging Commands

```bash
# Check service status
sudo systemctl status voice-chat-app

# View logs
sudo journalctl -u voice-chat-app -f

# Test API endpoints
curl -X GET https://your-domain.com/health

# Test WebSocket
wscat -c wss://your-domain.com/ws/test-user-id
```

## Scaling Considerations

### Horizontal Scaling

1. **Load Balancer Setup**:
   - Use Nginx or cloud load balancers
   - Implement sticky sessions for WebSocket
   - Health check configuration

2. **Database Scaling**:
   - Use Supabase read replicas
   - Implement database sharding
   - Cache frequently accessed data

3. **CDN Integration**:
   - Use CloudFlare or AWS CloudFront
   - Cache static assets
   - Optimize audio delivery

This deployment guide provides a comprehensive approach to deploying your real-time voice chat application in production environments.