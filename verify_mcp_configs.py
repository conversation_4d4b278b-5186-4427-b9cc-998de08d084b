#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证所有MCP配置的脚本
"""

import sys
import os
import asyncio
import logging
import json
from pathlib import Path

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def verify_mcp_configs():
    """验证所有MCP配置"""
    try:
        from services.mcp_client import mcp_client
        from services.mcp_service import mcp_service
        from services.tool_manager_service import tool_manager_service
        
        logger.info("开始验证所有MCP配置...")
        
        # 1. 检查配置文件加载
        logger.info("1. 检查MCP配置文件...")
        config_dir = Path("backend/mcp_config")
        config_files = list(config_dir.glob("*.json"))
        logger.info(f"找到 {len(config_files)} 个配置文件:")
        for config_file in config_files:
            logger.info(f"  - {config_file.name}")
        
        # 2. 检查MCP客户端配置
        logger.info("2. 检查MCP客户端配置...")
        logger.info(f"MCP客户端配置了 {len(mcp_client.server_configs)} 个服务器:")
        for server_name, config in mcp_client.server_configs.items():
            logger.info(f"  - {server_name}: {config['command']} {' '.join(config['args'])}")
        
        # 3. 验证每个服务器配置
        logger.info("3. 验证每个服务器配置...")
        total_tools = 0
        working_servers = 0
        failed_servers = 0
        
        for server_name in mcp_client.server_configs.keys():
            try:
                logger.info(f"验证服务器: {server_name}")
                
                # 尝试启动服务器
                success = await mcp_client.start_server(server_name)
                if success:
                    logger.info(f"  ✓ 服务器 {server_name} 启动成功")
                    
                    # 尝试获取工具列表
                    tools = await mcp_client.get_server_tools(server_name)
                    if tools:
                        logger.info(f"  ✓ 获取到 {len(tools)} 个工具")
                        total_tools += len(tools)
                        working_servers += 1
                        
                        # 显示前3个工具
                        for i, tool in enumerate(tools[:3]):
                            logger.info(f"    - {tool['name']}: {tool['description'][:50]}...")
                        
                        if len(tools) > 3:
                            logger.info(f"    ... 还有 {len(tools) - 3} 个工具")
                    else:
                        logger.warning(f"  ⚠ 服务器 {server_name} 没有返回工具")
                        failed_servers += 1
                    
                    # 停止服务器
                    await mcp_client.stop_server(server_name)
                else:
                    logger.error(f"  ✗ 服务器 {server_name} 启动失败")
                    failed_servers += 1
                    
            except Exception as e:
                logger.error(f"  ✗ 验证服务器 {server_name} 时出错: {e}")
                failed_servers += 1
        
        # 4. 测试工具管理服务
        logger.info("4. 测试工具管理服务...")
        try:
            tools = await tool_manager_service.enumerate_all_tools_async(use_cache=False)
            logger.info(f"工具管理服务获取到 {len(tools)} 个工具")
            
            # 按服务器分组显示
            server_tools = {}
            for tool in tools:
                server = tool.get('server', 'unknown')
                if server not in server_tools:
                    server_tools[server] = []
                server_tools[server].append(tool)
            
            for server, tools_list in server_tools.items():
                logger.info(f"  {server}: {len(tools_list)} 个工具")
        
        except Exception as e:
            logger.error(f"测试工具管理服务失败: {e}")
        
        # 5. 总结
        logger.info("5. 验证总结...")
        logger.info(f"总配置服务器数: {len(mcp_client.server_configs)}")
        logger.info(f"正常工作服务器数: {working_servers}")
        logger.info(f"失败服务器数: {failed_servers}")
        logger.info(f"总工具数: {total_tools}")
        
        if failed_servers == 0:
            logger.info("✓ 所有MCP服务器配置验证通过！")
        else:
            logger.warning(f"⚠ 有 {failed_servers} 个服务器配置存在问题")
        
        # 6. 清理
        logger.info("6. 清理资源...")
        await mcp_client.cleanup()
        
        logger.info("MCP配置验证完成")
        
    except Exception as e:
        logger.error(f"验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    logger.info("启动MCP配置验证")
    asyncio.run(verify_mcp_configs())

if __name__ == "__main__":
    main()
