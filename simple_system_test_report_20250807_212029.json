{"simple_test_summary": {"total_tests": 9, "passed": 3, "failed": 6, "success_rate": "33.3%"}, "test_results": [{"test_name": "Backend Root", "success": true, "details": "Message: Simple Voice Chat API", "error": null, "timestamp": "2025-08-07T21:20:29.181295"}, {"test_name": "Backend Health", "success": true, "details": "Checked 0 services", "error": null, "timestamp": "2025-08-07T21:20:29.184495"}, {"test_name": "Authentication", "success": false, "details": "HTTP 404: {\"detail\":\"Not Found\"}", "error": null, "timestamp": "2025-08-07T21:20:29.187320"}, {"test_name": "NPC Endpoints", "success": false, "details": "HTTP 404", "error": null, "timestamp": "2025-08-07T21:20:29.189682"}, {"test_name": "MCP Servers", "success": false, "details": "HTTP 404", "error": null, "timestamp": "2025-08-07T21:20:29.192461"}, {"test_name": "LLM Endpoint", "success": true, "details": "Q: '' -> A: '...' (DB: unknown)", "error": null, "timestamp": "2025-08-07T21:20:29.207117"}, {"test_name": "Debug Users", "success": false, "details": "HTTP 404", "error": null, "timestamp": "2025-08-07T21:20:29.209503"}, {"test_name": "Debug Sessions", "success": false, "details": "HTTP 404", "error": null, "timestamp": "2025-08-07T21:20:29.211761"}, {"test_name": "TTS Service", "success": false, "details": "", "error": "HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10d952250>: Failed to establish a new connection: [Errno 61] Connection refused'))", "timestamp": "2025-08-07T21:20:29.213465"}], "generated_at": "2025-08-07T21:20:29.213508"}