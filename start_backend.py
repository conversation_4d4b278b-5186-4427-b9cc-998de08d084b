#!/usr/bin/env python3
"""
启动后端服务的便捷脚本
"""
import os
import sys
import subprocess

def main():
    """启动后端服务"""
    print("🚀 启动语音聊天后端服务...")
    
    # 切换到 backend 目录
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    
    if not os.path.exists(backend_dir):
        print("❌ backend 目录不存在")
        return 1
    
    try:
        # 启动服务
        print("📡 服务将在 http://localhost:8000 启动")
        print("💡 使用 Ctrl+C 停止服务")
        print("=" * 50)
        
        os.chdir(backend_dir)
        subprocess.run([sys.executable, "main.py"])
        
    except KeyboardInterrupt:
        print("\n⏹️ 服务已停止")
        return 0
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
