#!/usr/bin/env python3
"""
独立的ASR测试脚本
用于测试保存的音频文件，诊断ASR问题
"""

import os
import sys
import numpy as np
import librosa
import logging
from pathlib import Path

# 添加backend目录到路径
sys.path.append('backend')

from services.multimodal_asr_service import MultimodalASRService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_audio_file(file_path: str, target_sr: int = 16000):
    """加载音频文件"""
    try:
        logger.info(f"Loading audio file: {file_path}")
        
        # 使用librosa加载音频
        audio, sr = librosa.load(file_path, sr=target_sr)
        logger.info(f"Audio loaded: shape={audio.shape}, sr={sr}, duration={len(audio)/sr:.2f}s")
        
        # 检查音频统计信息
        logger.info(f"Audio stats: min={audio.min():.4f}, max={audio.max():.4f}, mean={audio.mean():.4f}, std={audio.std():.4f}")
        
        return audio, sr
    except Exception as e:
        logger.error(f"Error loading audio file: {e}")
        return None, None

def test_asr_service(audio_file_path: str):
    """测试ASR服务"""
    logger.info("=" * 60)
    logger.info("ASR Service Test")
    logger.info("=" * 60)
    
    # 1. 加载音频文件
    audio, sr = load_audio_file(audio_file_path)
    if audio is None:
        logger.error("Failed to load audio file")
        return
    
    # 2. 初始化ASR服务
    try:
        logger.info("Initializing ASR service...")
        asr_service = MultimodalASRService(
            model="Qwen2-Audio-7B-Instruct",
            api_key="EMPTY",
            api_base="http://************:20257/v1"
        )
        logger.info("ASR service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize ASR service: {e}")
        return
    
    # 3. 测试ASR转录
    try:
        logger.info("Starting ASR transcription...")
        logger.info(f"Input audio: shape={audio.shape}, dtype={audio.dtype}")
        
        # 调用ASR服务
        result = asr_service.transcribe(audio)
        
        logger.info("ASR transcription completed")
        logger.info(f"Result: {result}")
        
        if result:
            logger.info(f"✅ Transcribed text: '{result.get('text', '')}'")
            logger.info(f"✅ Confidence: {result.get('confidence', 0.0):.3f}")
            logger.info(f"✅ Duration: {result.get('duration', 0.0):.3f}s")
            logger.info(f"✅ Tokens: {len(result.get('tokens', []))}")
        else:
            logger.error("❌ ASR returned empty result")
            
    except Exception as e:
        logger.error(f"❌ ASR transcription failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

def test_openai_client():
    """测试OpenAI客户端连接"""
    logger.info("=" * 60)
    logger.info("OpenAI Client Connection Test")
    logger.info("=" * 60)
    
    try:
        from openai import OpenAI
        
        # 测试连接到本地服务器
        client = OpenAI(
            base_url="http://************:20257/v1",
            api_key="dummy-key"
        )
        
        logger.info("Testing connection to local ASR server...")
        
        # 尝试获取模型列表
        models = client.models.list()
        logger.info(f"✅ Connected successfully. Available models: {[m.id for m in models.data]}")
        
    except Exception as e:
        logger.error(f"❌ Failed to connect to local ASR server: {e}")
        logger.error("This might be the root cause of ASR issues")

def main():
    """主函数"""
    # 检查音频文件
    # audio_file = "backend/audio_recordings/chunk_test-user_20250805_232853_058.wav"
    audio_file = "/Users/<USER>/LocalRepo/20250729t163822/开拓路-3.m4a"

    if not os.path.exists(audio_file):
        logger.error(f"Audio file not found: {audio_file}")
        
        # 列出可用的音频文件
        audio_dir = "backend/audio_recordings"
        if os.path.exists(audio_dir):
            files = list(Path(audio_dir).glob("*.wav"))
            if files:
                logger.info("Available audio files:")
                for f in files:
                    logger.info(f"  - {f}")
                audio_file = str(files[0])  # 使用第一个文件
                logger.info(f"Using: {audio_file}")
            else:
                logger.error("No audio files found in audio_recordings directory")
                return
        else:
            logger.error("audio_recordings directory not found")
            return
    
    # 1. 测试OpenAI客户端连接
    test_openai_client()
    
    print()
    
    # 2. 测试ASR服务
    test_asr_service(audio_file)

if __name__ == "__main__":
    main()
