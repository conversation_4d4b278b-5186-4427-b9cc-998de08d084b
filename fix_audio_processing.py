#!/usr/bin/env python3
"""
修复音频处理问题的脚本
"""

import asyncio
import websockets
import json
import base64
import numpy as np
import requests
import time
import logging
import wave
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioProcessingFixer:
    def __init__(self, backend_url="http://localhost:8000", websocket_url="ws://localhost:8000"):
        self.backend_url = backend_url
        self.websocket_url = websocket_url
        self.user_id = "test_user_1"
        self.npc_id = 1
        self.session_id = None
        
    def generate_test_audio_data(self, duration_ms=1000, sample_rate=16000):
        """生成测试音频数据（模拟语音）"""
        samples = int(duration_ms * sample_rate / 1000)
        # 生成简单的正弦波作为测试音频
        t = np.linspace(0, duration_ms/1000, samples)
        frequency = 440  # A4音符
        audio = np.sin(2 * np.pi * frequency * t) * 0.5
        # 转换为16位整数
        audio_int16 = (audio * 32767).astype(np.int16)
        return audio_int16.tobytes()
    
    def save_audio_to_wav(self, audio_data: bytes, filename: str, sample_rate: int = 16000):
        """保存音频数据到WAV文件用于调试"""
        try:
            # 确保audio_recordings目录存在
            audio_dir = Path("audio_recordings")
            audio_dir.mkdir(exist_ok=True)
            
            # 完整的文件路径
            file_path = audio_dir / filename
            
            # 将字节数据转换为numpy数组
            if len(audio_data) % 2 == 0:
                audio_array = np.frombuffer(audio_data, dtype=np.int16)
            else:
                # 如果是奇数字节，尝试作为uint8处理
                audio_uint8 = np.frombuffer(audio_data, dtype=np.uint8)
                audio_array = (audio_uint8.astype(np.int16) - 128) * 256
            
            # 保存为WAV文件
            with wave.open(str(file_path), 'wb') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)   # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_array.tobytes())
            
            logger.info(f"📁 音频已保存: {filename} ({len(audio_data)} 字节)")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"❌ 保存音频失败 {filename}: {e}")
            return None
    
    async def test_audio_formats(self):
        """测试不同的音频格式"""
        logger.info("🔍 测试不同的音频格式...")
        
        # 生成测试音频
        audio_data = self.generate_test_audio_data(duration_ms=2000)  # 2秒音频
        logger.info(f"🎵 生成测试音频: {len(audio_data)} 字节")
        
        # 保存原始音频用于参考
        self.save_audio_to_wav(audio_data, "test_original.wav")
        
        # 测试1: 直接发送原始音频数据
        logger.info("🧪 测试1: 直接发送原始音频数据...")
        test1_result = await self.send_audio_test("test1_direct", audio_data)
        
        # 测试2: 发送base64编码的音频数据
        logger.info("🧪 测试2: 发送base64编码的音频数据...")
        base64_audio = base64.b64encode(audio_data).decode('utf-8')
        test2_result = await self.send_audio_test("test2_base64", base64_audio)
        
        # 测试3: 发送分块音频数据
        logger.info("🧪 测试3: 发送分块音频数据...")
        chunk_size = 1024
        chunks = [audio_data[i:i+chunk_size] for i in range(0, len(audio_data), chunk_size)]
        test3_result = await self.send_chunked_audio_test("test3_chunked", chunks)
        
        return {
            "test1_direct": test1_result,
            "test2_base64": test2_result,
            "test3_chunked": test3_result
        }
    
    async def send_audio_test(self, test_name: str, audio_data):
        """发送音频测试"""
        try:
            # 用户登录
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params={"username": "test_user", "password": "test_password"},
                timeout=10
            )
            
            if response.status_code != 200:
                logger.error(f"❌ 登录失败: {response.status_code}")
                return False
            
            user_data = response.json()
            user_id = user_data.get('user', {}).get('id', 1)
            logger.info(f"✅ 用户登录成功，ID: {user_id}")
            
            # WebSocket连接
            uri = f"{self.websocket_url}/ws/{user_id}"
            logger.info(f"🔌 连接WebSocket: {uri}")
            
            async with websockets.connect(uri) as websocket:
                logger.info("✅ WebSocket连接成功")
                
                # 启动会话
                start_msg = {"type": "start_session", "npc_id": self.npc_id}
                await websocket.send(json.dumps(start_msg))
                logger.info("📤 发送会话启动请求")
                
                # 等待会话启动响应
                response = await asyncio.wait_for(websocket.recv(), timeout=10)
                response_data = json.loads(response)
                
                if response_data.get("type") == "session_started":
                    self.session_id = response_data.get("session_id")
                    logger.info(f"✅ 会话启动成功，ID: {self.session_id}")
                else:
                    logger.error(f"❌ 会话启动失败: {response_data}")
                    return False
                
                # 发送音频数据
                logger.info(f"🎵 发送测试音频数据 ({test_name})...")
                
                if isinstance(audio_data, str):
                    # Base64字符串
                    audio_message = {
                        "type": "audio_chunk",
                        "data": audio_data
                    }
                else:
                    # 字节数据，转换为base64
                    base64_audio = base64.b64encode(audio_data).decode('utf-8')
                    audio_message = {
                        "type": "audio_chunk",
                        "data": base64_audio
                    }
                
                await websocket.send(json.dumps(audio_message))
                logger.info(f"📤 发送音频数据 ({len(str(audio_data))} 字符)")
                
                # 等待处理结果
                logger.info("⏳ 等待处理结果...")
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                        response_data = json.loads(response)
                        logger.info(f"📥 收到响应: {response_data.get('type', 'unknown')}")
                        
                        if response_data.get("type") == "transcription":
                            logger.info(f"🎯 转录结果: {response_data.get('text', 'N/A')}")
                            return True
                        elif response_data.get("type") == "response_complete":
                            logger.info("✅ 响应完成")
                            return True
                        elif response_data.get("type") == "error":
                            logger.error(f"❌ 错误: {response_data.get('message', 'Unknown error')}")
                            return False
                            
                except asyncio.TimeoutError:
                    logger.warning("⚠️ 等待响应超时")
                    return False
                
        except Exception as e:
            logger.error(f"❌ 音频测试异常 ({test_name}): {e}")
            return False
    
    async def send_chunked_audio_test(self, test_name: str, chunks):
        """发送分块音频测试"""
        try:
            # 用户登录
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params={"username": "test_user", "password": "test_password"},
                timeout=10
            )
            
            if response.status_code != 200:
                logger.error(f"❌ 登录失败: {response.status_code}")
                return False
            
            user_data = response.json()
            user_id = user_data.get('user', {}).get('id', 1)
            logger.info(f"✅ 用户登录成功，ID: {user_id}")
            
            # WebSocket连接
            uri = f"{self.websocket_url}/ws/{user_id}"
            logger.info(f"🔌 连接WebSocket: {uri}")
            
            async with websockets.connect(uri) as websocket:
                logger.info("✅ WebSocket连接成功")
                
                # 启动会话
                start_msg = {"type": "start_session", "npc_id": self.npc_id}
                await websocket.send(json.dumps(start_msg))
                logger.info("📤 发送会话启动请求")
                
                # 等待会话启动响应
                response = await asyncio.wait_for(websocket.recv(), timeout=10)
                response_data = json.loads(response)
                
                if response_data.get("type") == "session_started":
                    self.session_id = response_data.get("session_id")
                    logger.info(f"✅ 会话启动成功，ID: {self.session_id}")
                else:
                    logger.error(f"❌ 会话启动失败: {response_data}")
                    return False
                
                # 发送音频块
                logger.info(f"🎵 发送分块音频数据 ({test_name})...")
                logger.info(f"📦 将音频分为 {len(chunks)} 个块进行流式传输")
                
                for i, chunk in enumerate(chunks):
                    # 编码为base64
                    base64_chunk = base64.b64encode(chunk).decode('utf-8')
                    
                    # 发送音频块
                    audio_message = {
                        "type": "audio_chunk",
                        "data": base64_chunk
                    }
                    
                    await websocket.send(json.dumps(audio_message))
                    logger.info(f"📤 发送音频块 {i+1}/{len(chunks)} ({len(chunk)} 字节)")
                    
                    # 模拟实时流的延迟
                    await asyncio.sleep(0.1)
                
                # 等待处理结果
                logger.info("⏳ 等待处理结果...")
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                        response_data = json.loads(response)
                        logger.info(f"📥 收到响应: {response_data.get('type', 'unknown')}")
                        
                        if response_data.get("type") == "transcription":
                            logger.info(f"🎯 转录结果: {response_data.get('text', 'N/A')}")
                            return True
                        elif response_data.get("type") == "response_complete":
                            logger.info("✅ 响应完成")
                            return True
                        elif response_data.get("type") == "error":
                            logger.error(f"❌ 错误: {response_data.get('message', 'Unknown error')}")
                            return False
                            
                except asyncio.TimeoutError:
                    logger.warning("⚠️ 等待响应超时")
                    return False
                
        except Exception as e:
            logger.error(f"❌ 分块音频测试异常 ({test_name}): {e}")
            return False
    
    async def fix_websocket_communication(self):
        """修复WebSocket通信问题"""
        logger.info("🔧 修复WebSocket通信问题...")
        
        try:
            # 用户登录
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params={"username": "test_user", "password": "test_password"},
                timeout=10
            )
            
            if response.status_code != 200:
                logger.error(f"❌ 登录失败: {response.status_code}")
                return False
            
            user_data = response.json()
            user_id = user_data.get('user', {}).get('id', 1)
            logger.info(f"✅ 用户登录成功，ID: {user_id}")
            
            # WebSocket连接
            uri = f"{self.websocket_url}/ws/{user_id}"
            logger.info(f"🔌 连接WebSocket: {uri}")
            
            async with websockets.connect(uri) as websocket:
                logger.info("✅ WebSocket连接成功")
                
                # 启动会话
                start_msg = {"type": "start_session", "npc_id": self.npc_id}
                await websocket.send(json.dumps(start_msg))
                logger.info("📤 发送会话启动请求")
                
                # 等待会话启动响应
                response = await asyncio.wait_for(websocket.recv(), timeout=10)
                response_data = json.loads(response)
                
                if response_data.get("type") == "session_started":
                    self.session_id = response_data.get("session_id")
                    logger.info(f"✅ 会话启动成功，ID: {self.session_id}")
                else:
                    logger.error(f"❌ 会话启动失败: {response_data}")
                    return False
                
                # 发送测试消息
                test_messages = [
                    {"type": "ping", "data": "test"},
                    {"type": "audio_chunk", "data": "test_audio_data"},
                    {"type": "custom", "data": "custom_message"}
                ]
                
                for msg in test_messages:
                    await websocket.send(json.dumps(msg))
                    logger.info(f"📤 发送测试消息: {msg['type']}")
                    
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        response_data = json.loads(response)
                        logger.info(f"📥 收到响应: {response_data.get('type', 'unknown')}")
                    except asyncio.TimeoutError:
                        logger.warning(f"⚠️ 等待 {msg['type']} 响应超时")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ WebSocket通信修复异常: {e}")
            return False

async def main():
    """主函数"""
    fixer = AudioProcessingFixer()
    
    logger.info("🚀 开始修复音频处理问题...")
    
    # 测试不同的音频格式
    test_results = await fixer.test_audio_formats()
    logger.info(f"📊 音频格式测试结果: {test_results}")
    
    # 修复WebSocket通信
    websocket_fixed = await fixer.fix_websocket_communication()
    logger.info(f"🔧 WebSocket通信修复结果: {websocket_fixed}")
    
    logger.info("✅ 音频处理问题修复完成！")

if __name__ == "__main__":
    asyncio.run(main())
