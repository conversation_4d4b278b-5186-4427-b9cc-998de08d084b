# Quick Start Guide - Real-time Voice Chat App

This guide will help you get the voice chat app running with your specific Supabase configuration.

## 🚀 Your Configuration

**Supabase URL**: `http://*************:8000`
**Frontend & Backend**: Configured to use your Supabase instance

## 📋 Prerequisites

1. **Python 3.8+** installed
2. **Flutter 3.0+** installed
3. **API Keys** for external services

## 🛠️ Setup Steps

### 1. Install Backend Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Download Required Models

```bash
# Run the setup script
python ../setup.py
```

**Manual Model Downloads:**

1. **Silero VAD Model** (auto-downloaded by setup script):
   - URL: https://github.com/snakers4/silero-vad/raw/master/files/silero_vad.onnx
   - Save to: `backend/models/silero_vad.onnx`

2. **FireRedASR Model** (download with git):
   ```bash
   git clone https://huggingface.co/FireRedTeam/FireRedASR-AED-L backend/models/firered_asr
   ```
   - Alternative: https://www.modelscope.cn/models/manyeyes/fireredasr-aed-large-zh-en-onnx-offline-20250124
   - Contains: model files, tokenizer, config, and other artifacts

### 3. Configure API Keys

Edit `backend/.env` and add your API keys:

```env
# Your Supabase is already configured ✅
SUPABASE_URL=http://*************:8000
SUPABASE_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

# Add these API keys:
VOLCANO_API_KEY=your_volcano_api_key_here
VOLCANO_ENDPOINT=https://ark.cn-beijing.volces.com/api/v3/chat/completions

MINIMAX_API_KEY=your_minimax_api_key_here
MINIMAX_GROUP_ID=your_minimax_group_id_here

# Optional WeChat login:
WECHAT_APP_ID=your_wechat_app_id_here
WECHAT_APP_SECRET=your_wechat_app_secret_here
```

### 4. Setup Database

Run the SQL migration in your Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste the content from `supabase/migrations/001_initial_schema.sql`
4. Execute the SQL script

### 5. Start Backend Server

```bash
cd backend
python main.py
```

The server will start on `http://localhost:8000`

### 6. Setup Flutter Frontend

```bash
cd frontend
flutter pub get
```

### 7. Run Flutter App

```bash
flutter run
```

## 🧪 Testing the Setup

### Test Backend API

```bash
# Health check
curl http://localhost:8000/health

# Get NPCs
curl http://localhost:8000/npcs

# Test WebSocket (using wscat if installed)
wscat -c ws://localhost:8000/ws/test-user
```

### Test Frontend

1. Launch the Flutter app
2. Use "Continue as Guest" for testing
3. Select an NPC
4. Test voice chat functionality

## 🔧 API Keys You Need

### 1. Volcano Engine (火山方舟)
- **Purpose**: LLM streaming responses
- **Get it from**: https://www.volcengine.com/
- **Documentation**: https://www.volcengine.com/docs/82379/1569618

### 2. MiniMax TTS
- **Purpose**: Text-to-speech conversion
- **Get it from**: https://platform.minimaxi.com/
- **Documentation**: https://platform.minimaxi.com/document/同步语音合成

### 3. WeChat Login (Optional)
- **Purpose**: User authentication
- **Get it from**: WeChat Open Platform
- **Note**: Can use guest login for testing

## 📱 Mobile App Testing

### Android
```bash
flutter build apk --debug
flutter install
```

### iOS (requires macOS)
```bash
flutter build ios --debug
```

## 🐛 Troubleshooting

### Common Issues

1. **Models not found**:
   - Ensure models are downloaded to correct paths
   - Check file permissions

2. **API connection errors**:
   - Verify API keys are correct
   - Check network connectivity
   - Ensure Supabase instance is running

3. **WebSocket connection fails**:
   - Check if backend server is running
   - Verify WebSocket URL in Flutter config

4. **Audio recording issues**:
   - Grant microphone permissions
   - Test on physical device (not simulator)

### Debug Commands

```bash
# Check backend logs
cd backend && python main.py

# Check Flutter logs
flutter logs

# Test API endpoints
curl -X GET http://localhost:8000/health
```

## 🎯 Next Steps

1. **Add your API keys** to `backend/.env`
2. **Download FireRedASR model** manually
3. **Run the database migration** in Supabase
4. **Test the complete flow**: Login → NPC Selection → Voice Chat

## 📞 Voice Chat Flow

1. **User speaks** → VAD detects speech
2. **Audio processed** → ASR transcribes to text
3. **LLM generates response** → Volcano Engine API
4. **Text converted to speech** → MiniMax TTS
5. **Audio played** → User hears response
6. **Interruption supported** → User can interrupt anytime

Your app is now configured and ready to run! 🎉