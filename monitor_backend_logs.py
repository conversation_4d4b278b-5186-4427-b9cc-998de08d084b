#!/usr/bin/env python3
"""
Monitor backend logs in real-time
实时监控后端日志
"""

import subprocess
import sys
import os
from datetime import datetime

def monitor_logs():
    """监控日志文件"""
    print("🔍 开始监控后端日志...")
    print("按 Ctrl+C 停止监控")
    print("="*50)
    
    # 查找可能的日志文件
    log_files = [
        "backend/app.log",
        "backend/server.log",
        "app.log",
        "server.log",
        "uvicorn.log"
    ]
    
    # 检查是否存在日志文件
    existing_logs = [f for f in log_files if os.path.exists(f)]
    
    if existing_logs:
        print(f"📄 找到日志文件: {existing_logs}")
        # 监控最新的日志文件
        latest_log = max(existing_logs, key=os.path.getmtime)
        print(f"📋 监控日志文件: {latest_log}")
        
        try:
            # 使用tail命令实时监控日志
            process = subprocess.Popen(['tail', '-f', latest_log], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE,
                                     universal_newlines=True)
            
            while True:
                output = process.stdout.readline()
                if output:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] {output.strip()}")
                
        except KeyboardInterrupt:
            print("\n👋 停止日志监控")
            process.terminate()
            return
        except Exception as e:
            print(f"❌ 监控日志时出错: {e}")
    
    else:
        print("⚠️ 未找到日志文件，尝试监控标准输出...")
        print("请在另一个终端中运行后端服务以查看输出")

if __name__ == "__main__":
    monitor_logs()
