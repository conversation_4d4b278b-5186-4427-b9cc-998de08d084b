{"simple_test_summary": {"total_tests": 7, "passed": 0, "failed": 7, "success_rate": "0.0%"}, "test_results": [{"test_name": "Backend Basic", "success": false, "details": "", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10646cfd0>: Failed to establish a new connection: [Errno 61] Connection refused'))", "timestamp": "2025-08-07T21:13:46.495912"}, {"test_name": "Authentication", "success": false, "details": "", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/login?username=test_user&password=test_password (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1064d4b20>: Failed to establish a new connection: [Errno 61] Connection refused'))", "timestamp": "2025-08-07T21:13:46.497210"}, {"test_name": "NPC Endpoints", "success": false, "details": "", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /npcs (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1064e2550>: Failed to establish a new connection: [Errno 61] Connection refused'))", "timestamp": "2025-08-07T21:13:46.498559"}, {"test_name": "MCP Endpoints", "success": false, "details": "", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /mcp/servers (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1064e2ee0>: Failed to establish a new connection: [Errno 61] Connection refused'))", "timestamp": "2025-08-07T21:13:46.499966"}, {"test_name": "LLM Endpoint", "success": false, "details": "", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /test (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1064ed880>: Failed to establish a new connection: [Errno 61] Connection refused'))", "timestamp": "2025-08-07T21:13:46.501603"}, {"test_name": "Debug Endpoints", "success": false, "details": "", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /debug/users (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1064e2250>: Failed to establish a new connection: [Errno 61] Connection refused'))", "timestamp": "2025-08-07T21:13:46.502604"}, {"test_name": "TTS Service", "success": false, "details": "", "error": "HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1011f6130>: Failed to establish a new connection: [Errno 61] Connection refused'))", "timestamp": "2025-08-07T21:13:46.505123"}], "generated_at": "2025-08-07T21:13:46.505153"}