#!/usr/bin/env python3
"""
快速语音测试启动器
一键启动完整的语音流水线测试
"""
import subprocess
import sys
import time
import webbrowser
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("🎤 快速语音流水线测试")
    print("=" * 50)
    print("选择测试方式:")
    print("1. 启动完整测试环境 (后端服务 + 网页测试)")
    print("2. 运行自动化测试脚本 (各阶段独立测试)")
    print("3. 仅启动后端服务")
    print("4. 退出")
    print("=" * 50)
    
    while True:
        try:
            choice = input("请选择 (1-4): ").strip()
            
            if choice == '1':
                logger.info("🚀 启动完整测试环境...")
                subprocess.run([sys.executable, "start_full_voice_test.py"])
                break
                
            elif choice == '2':
                logger.info("🧪 运行自动化测试脚本...")
                subprocess.run([sys.executable, "test_voice_pipeline_stages.py"])
                break
                
            elif choice == '3':
                logger.info("📡 仅启动后端服务...")
                try:
                    subprocess.run([
                        sys.executable, "-m", "uvicorn", "backend.main:app",
                        "--host", "0.0.0.0",
                        "--port", "8000",
                        "--reload"
                    ])
                except KeyboardInterrupt:
                    logger.info("🛑 后端服务已停止")
                break
                
            elif choice == '4':
                logger.info("👋 退出测试")
                break
                
            else:
                print("❌ 无效选择，请输入 1-4")
                continue
                
        except KeyboardInterrupt:
            logger.info("\n🛑 用户中断")
            break
        except Exception as e:
            logger.error(f"❌ 执行错误: {e}")
            break

if __name__ == "__main__":
    main()