#!/usr/bin/env python3
"""
MCP工具集成测试脚本
专门测试MCP工具的集成和调用功能
"""

import sys
import json
import requests
from datetime import datetime
from pathlib import Path

# 添加backend目录到路径
sys.path.append('backend')

class MCPToolsTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.test_results = []
        
    def log_test(self, test_name, success, details="", error=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": str(error) if error else None,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {details}")
        if error:
            print(f"   Error: {error}")
    
    def test_mcp_service_direct(self):
        """直接测试MCP服务"""
        try:
            from services.mcp_service import mcp_service
            
            # 测试服务器列表
            servers = mcp_service.list_servers()
            self.log_test(
                "Direct MCP Service - Server List", 
                True, 
                f"Found {len(servers)} servers"
            )
            
            # 测试工具列表
            tools = mcp_service.get_all_tools_for_reranker()
            categories = {}
            for tool in tools:
                category = tool.get("category", "unknown")
                if category not in categories:
                    categories[category] = []
                categories[category].append(tool["name"])
            
            self.log_test(
                "Direct MCP Service - Tools List", 
                True, 
                f"Found {len(tools)} tools in {len(categories)} categories"
            )
            
            # 测试每个类别的工具
            for category, tool_names in categories.items():
                print(f"  📂 {category.upper()}: {', '.join(tool_names)}")
            
            return True, tools
            
        except Exception as e:
            self.log_test("Direct MCP Service", False, error=e)
            return False, []
    
    def test_builtin_tools(self):
        """测试内置工具"""
        try:
            from services.mcp_service import mcp_service
            
            builtin_tools = [
                ("search_and_summarize", {"query": "人工智能最新发展"}),
                ("fetch_news", {"category": "technology", "limit": 3}),
                ("recall_current_activity", {}),
                ("recall_relevant_experiences", {"topic": "AI对话"})
            ]
            
            for tool_name, params in builtin_tools:
                try:
                    result = mcp_service.execute_tool(tool_name, **params)
                    success = result.get("success", False)
                    
                    if success:
                        result_data = result.get("result", {})
                        if isinstance(result_data, dict):
                            # 提取关键信息
                            key_info = []
                            if "summary" in result_data:
                                key_info.append(f"summary: {result_data['summary'][:50]}...")
                            if "news" in result_data:
                                key_info.append(f"news: {len(result_data['news'])} items")
                            if "current_activity" in result_data:
                                key_info.append(f"activity: {result_data['current_activity']}")
                            if "experiences" in result_data:
                                key_info.append(f"experiences: {len(result_data['experiences'])} items")
                            
                            details = f"Tool executed successfully. {', '.join(key_info)}"
                        else:
                            details = f"Tool executed successfully. Result: {str(result_data)[:100]}..."
                    else:
                        details = f"Tool execution failed: {result.get('error', 'Unknown error')}"
                    
                    self.log_test(f"Builtin Tool - {tool_name}", success, details)
                    
                except Exception as e:
                    self.log_test(f"Builtin Tool - {tool_name}", False, error=e)
            
            return True
            
        except Exception as e:
            self.log_test("Builtin Tools Test", False, error=e)
            return False
    
    def test_mcp_server_tools(self):
        """测试MCP服务器工具"""
        try:
            from services.mcp_service import mcp_service
            
            servers = mcp_service.list_servers()
            
            for server in servers:
                server_name = server["name"]
                
                # 获取服务器工具
                tools = mcp_service.get_server_tools(server_name)
                
                self.log_test(
                    f"MCP Server Tools - {server_name}", 
                    True, 
                    f"Found {len(tools)} tools"
                )
                
                # 测试每个工具
                for tool in tools:
                    tool_name = tool["name"]
                    
                    try:
                        # 根据工具类型准备测试参数
                        test_params = self._get_test_params_for_tool(tool_name)
                        
                        result = mcp_service.execute_tool(tool_name, server_name, **test_params)
                        success = result.get("success", False)
                        
                        if success:
                            result_data = result.get("result", {})
                            details = f"Executed with params: {test_params}. Result keys: {list(result_data.keys()) if isinstance(result_data, dict) else 'non-dict result'}"
                        else:
                            details = f"Execution failed: {result.get('error', 'Unknown error')}"
                        
                        self.log_test(f"Server Tool - {tool_name}", success, details)
                        
                    except Exception as e:
                        self.log_test(f"Server Tool - {tool_name}", False, error=e)
            
            return True
            
        except Exception as e:
            self.log_test("MCP Server Tools Test", False, error=e)
            return False
    
    def _get_test_params_for_tool(self, tool_name):
        """根据工具名称获取测试参数"""
        if "train" in tool_name.lower() or "12306" in tool_name.lower():
            return {"departure": "北京", "destination": "上海"}
        elif "bilibili" in tool_name.lower() or "video" in tool_name.lower():
            return {"keyword": "AI技术"}
        elif "arxiv" in tool_name.lower() or "paper" in tool_name.lower():
            return {"query": "machine learning", "limit": 5}
        elif "location" in tool_name.lower() or "amap" in tool_name.lower():
            return {"address": "北京市朝阳区"}
        elif "cook" in tool_name.lower() or "recipe" in tool_name.lower():
            return {"dish": "宫保鸡丁"}
        else:
            return {"query": "测试查询", "limit": 3}
    
    def test_api_endpoints(self):
        """测试MCP相关的API端点"""
        try:
            # 测试服务器列表API
            response = requests.get(f"{self.backend_url}/mcp/servers", timeout=10)
            if response.status_code == 200:
                data = response.json()
                servers = data.get("servers", [])
                self.log_test(
                    "API - MCP Servers", 
                    True, 
                    f"Retrieved {len(servers)} servers via API"
                )
            else:
                self.log_test("API - MCP Servers", False, f"HTTP {response.status_code}")
            
            # 测试工具列表API
            response = requests.get(f"{self.backend_url}/mcp/tools", timeout=10)
            if response.status_code == 200:
                data = response.json()
                tools = data.get("tools", [])
                
                # 统计工具类别
                categories = {}
                for tool in tools:
                    category = tool.get("category", "unknown")
                    if category not in categories:
                        categories[category] = 0
                    categories[category] += 1
                
                self.log_test(
                    "API - MCP Tools", 
                    True, 
                    f"Retrieved {len(tools)} tools. Categories: {dict(categories)}"
                )
            else:
                self.log_test("API - MCP Tools", False, f"HTTP {response.status_code}")
            
            # 测试工具执行API
            test_cases = [
                ("search_and_summarize", {"query": "AI发展趋势"}),
                ("fetch_news", {"category": "technology", "limit": 3}),
                ("query_train_schedule", {"departure": "北京", "destination": "上海"})
            ]
            
            for tool_name, params in test_cases:
                try:
                    response = requests.post(
                        f"{self.backend_url}/mcp/tools/execute",
                        params={"tool_name": tool_name, "parameters": json.dumps(params)},
                        timeout=15
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        success = result.get("success", False)
                        
                        self.log_test(
                            f"API - Execute {tool_name}", 
                            success, 
                            f"Tool executed via API. Status: {result.get('status', 'unknown')}"
                        )
                    else:
                        self.log_test(f"API - Execute {tool_name}", False, f"HTTP {response.status_code}")
                        
                except Exception as e:
                    self.log_test(f"API - Execute {tool_name}", False, error=e)
            
            return True
            
        except Exception as e:
            self.log_test("API Endpoints Test", False, error=e)
            return False
    
    def test_tool_categories_coverage(self):
        """测试工具类别覆盖度"""
        try:
            from services.mcp_service import mcp_service
            
            tools = mcp_service.get_all_tools_for_reranker()
            
            # 期望的工具类别
            expected_categories = {
                "search", "news", "memory", "travel", "entertainment", 
                "research", "location", "cooking", "general"
            }
            
            # 实际的工具类别
            actual_categories = set()
            category_tools = {}
            
            for tool in tools:
                category = tool.get("category", "unknown")
                actual_categories.add(category)
                
                if category not in category_tools:
                    category_tools[category] = []
                category_tools[category].append(tool["name"])
            
            # 检查覆盖度
            covered_categories = expected_categories.intersection(actual_categories)
            missing_categories = expected_categories - actual_categories
            extra_categories = actual_categories - expected_categories
            
            coverage_rate = len(covered_categories) / len(expected_categories) * 100
            
            details = f"Coverage: {coverage_rate:.1f}% ({len(covered_categories)}/{len(expected_categories)})"
            if missing_categories:
                details += f". Missing: {missing_categories}"
            if extra_categories:
                details += f". Extra: {extra_categories}"
            
            self.log_test(
                "Tool Categories Coverage", 
                coverage_rate >= 80,  # 80%以上覆盖率算成功
                details
            )
            
            # 详细输出每个类别的工具
            print("  📊 Tool Categories Detail:")
            for category, tool_list in sorted(category_tools.items()):
                print(f"    {category}: {len(tool_list)} tools - {', '.join(tool_list)}")
            
            return True
            
        except Exception as e:
            self.log_test("Tool Categories Coverage", False, error=e)
            return False
    
    def generate_mcp_report(self):
        """生成MCP测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        report = {
            "mcp_test_summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
            },
            "test_results": self.test_results,
            "generated_at": datetime.now().isoformat()
        }
        
        # 保存报告
        report_file = f"mcp_tools_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n{'='*60}")
        print("🔧 MCP TOOLS TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {report['mcp_test_summary']['success_rate']}")
        print(f"Report saved to: {report_file}")
        
        return report

def main():
    """主测试函数"""
    print("🔧 Starting MCP Tools Integration Test")
    print("="*60)
    
    tester = MCPToolsTester()
    
    # 1. 直接测试MCP服务
    print("\n1. Direct MCP Service Test")
    success, tools = tester.test_mcp_service_direct()
    
    # 2. 测试内置工具
    print("\n2. Builtin Tools Test")
    tester.test_builtin_tools()
    
    # 3. 测试MCP服务器工具
    print("\n3. MCP Server Tools Test")
    tester.test_mcp_server_tools()
    
    # 4. 测试API端点
    print("\n4. API Endpoints Test")
    tester.test_api_endpoints()
    
    # 5. 测试工具类别覆盖度
    print("\n5. Tool Categories Coverage Test")
    tester.test_tool_categories_coverage()
    
    # 生成测试报告
    print("\n6. Generating MCP Test Report")
    report = tester.generate_mcp_report()
    
    return report

if __name__ == "__main__":
    main()
