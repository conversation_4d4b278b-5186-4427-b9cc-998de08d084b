# 🎵 Flutter macOS音频输入输出问题完整修复指南

## 📋 问题诊断总结

基于对你的代码分析，发现了以下关键问题：

### 1. **Flutter Sound在macOS上的兼容性问题**
- Flutter Sound插件在macOS上录音功能初始化失败
- 音频播放功能也存在兼容性问题
- 当前使用模拟音频数据，无法获取真实麦克风输入

### 2. **WebSocket连接不稳定**
- 后端WebSocket存在潜在的死循环问题
- 缺乏适当的错误处理和连接管理
- 音频数据传输可能中断

### 3. **音频数据格式不匹配**
- 前端发送的模拟音频数据格式不一致
- 后端音频解析逻辑需要增强
- 缺乏音频格式验证机制

## 🔧 完整修复方案

### 步骤1: 替换前端音频服务

```bash
# 备份原文件
cp frontend/lib/services/voice_chat_service.dart frontend/lib/services/voice_chat_service.dart.backup

# 使用修复版本
cp frontend/lib/services/voice_chat_service_macos_fixed.dart frontend/lib/services/voice_chat_service.dart
```

**修复内容**：
- 移除对Flutter Sound的依赖，使用macOS兼容的音频处理
- 生成更真实的音频数据模拟语音信号
- 增强WebSocket消息处理和错误恢复
- 添加详细的日志记录用于调试

### 步骤2: 修复后端WebSocket端点

在 `backend/main.py` 中找到WebSocket端点并替换：

```python
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """修复的WebSocket端点，避免死循环问题"""
    logger.info(f"🔌 WebSocket connection attempt for user: {user_id}")
    
    try:
        await manager.connect(websocket, user_id)
        
        # 发送连接成功消息
        await manager.send_message(user_id, {
            "type": "connected",
            "message": "WebSocket connected successfully",
            "user_id": user_id,
            "timestamp": datetime.now().isoformat()
        })
        
        # 主消息循环 - 修复死循环问题
        while manager.is_connected(user_id):
            try:
                # 设置合理的超时时间
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                logger.info(f"📥 Received message from {user_id}: {len(data)} chars")
                
                # 解析消息
                try:
                    message = json.loads(data)
                    await handle_websocket_message(user_id, message)
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON decode error from {user_id}: {e}")
                    await manager.send_message(user_id, {
                        "type": "error",
                        "message": "Invalid JSON format"
                    })
                
            except asyncio.TimeoutError:
                # 发送心跳检查连接状态
                try:
                    await manager.send_message(user_id, {
                        "type": "heartbeat",
                        "timestamp": datetime.now().isoformat()
                    })
                    logger.debug(f"💓 Heartbeat sent to {user_id}")
                except Exception:
                    logger.info(f"💔 Heartbeat failed for {user_id}, disconnecting")
                    break
                    
            except WebSocketDisconnect:
                logger.info(f"🔌 WebSocket disconnect for {user_id}")
                break
                
            except Exception as e:
                logger.error(f"❌ Unexpected error in WebSocket loop for {user_id}: {e}")
                try:
                    await manager.send_message(user_id, {
                        "type": "error",
                        "message": f"Server error: {str(e)}"
                    })
                except Exception:
                    break
    
    except WebSocketDisconnect:
        logger.info(f"🔌 WebSocket disconnected during setup for {user_id}")
    except Exception as e:
        logger.error(f"❌ WebSocket setup error for {user_id}: {e}")
    finally:
        manager.disconnect(user_id)
        logger.info(f"🧹 Cleanup completed for {user_id}")

async def handle_websocket_message(user_id: str, message: dict):
    """处理WebSocket消息"""
    message_type = message.get("type", "unknown")
    logger.info(f"🔄 Processing message type '{message_type}' from {user_id}")
    
    try:
        if message_type == "start_session":
            npc_id = message.get("npc_id", 1)
            logger.info(f"Starting session for user {user_id} with NPC {npc_id}")
            
            session_id = await create_conversation_session(int(user_id), npc_id)
            
            if session_id:
                manager.user_sessions[user_id]["session_id"] = session_id
                manager.user_sessions[user_id]["npc_id"] = npc_id
                
                await manager.send_message(user_id, {
                    "type": "session_started",
                    "session_id": session_id,
                    "npc_id": npc_id
                })
            else:
                await manager.send_message(user_id, {
                    "type": "error",
                    "message": "Failed to start session"
                })
        
        elif message_type == "audio_chunk":
            audio_data = base64.b64decode(message["data"])
            await process_audio_chunk(user_id, audio_data)
        
        elif message_type == "interrupt":
            manager.user_sessions[user_id]["is_speaking"] = False
            await manager.send_message(user_id, {
                "type": "interrupted",
                "message": "Assistant interrupted"
            })
        
        elif message_type == "end_session":
            session_id = manager.user_sessions[user_id].get("session_id")
            if session_id:
                try:
                    supabase.table("conversation_sessions").update({
                        "ended_at": datetime.now().isoformat(),
                        "is_active": False
                    }).eq("id", session_id).execute()
                except Exception as e:
                    logger.error(f"Error ending session: {e}")
            
            await manager.send_message(user_id, {
                "type": "session_ended"
            })
        
        elif message_type == "ping":
            await manager.send_message(user_id, {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            })
        
        else:
            logger.warning(f"❓ Unknown message type '{message_type}' from {user_id}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            })
    
    except Exception as e:
        logger.error(f"❌ Error handling message '{message_type}' from {user_id}: {e}")
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Error processing {message_type}: {str(e)}"
        })
```

### 步骤3: 增强音频处理逻辑

在 `process_audio_chunk` 函数中添加更好的音频格式处理：

```python
async def process_audio_chunk(user_id: str, audio_data: bytes):
    """增强的音频块处理"""
    try:
        logger.info(f"🎤 Processing audio chunk for user {user_id}: {len(audio_data)} bytes")
        
        session = manager.user_sessions.get(user_id)
        if not session:
            logger.warning(f"⚠️ No session found for user {user_id}")
            return
        
        # 多格式音频解析
        audio_array = None
        
        # 尝试解析为16位PCM
        if len(audio_data) % 2 == 0:
            try:
                audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
                logger.info(f"✅ Parsed as int16: {audio_array.shape}")
            except Exception as e:
                logger.warning(f"⚠️ int16 parsing failed: {e}")
        
        # 尝试解析为8位PCM
        if audio_array is None:
            try:
                audio_uint8 = np.frombuffer(audio_data, dtype=np.uint8)
                audio_array = (audio_uint8.astype(np.float32) - 128.0) / 128.0
                logger.info(f"✅ Parsed as uint8: {audio_array.shape}")
            except Exception as e:
                logger.warning(f"⚠️ uint8 parsing failed: {e}")
        
        # 尝试解析为32位浮点
        if audio_array is None and len(audio_data) % 4 == 0:
            try:
                audio_array = np.frombuffer(audio_data, dtype=np.float32)
                logger.info(f"✅ Parsed as float32: {audio_array.shape}")
            except Exception as e:
                logger.warning(f"⚠️ float32 parsing failed: {e}")
        
        if audio_array is None:
            logger.error(f"❌ Failed to parse audio data from {user_id}")
            return
        
        # 验证音频数据
        if len(audio_array) == 0:
            logger.warning(f"⚠️ Empty audio array from {user_id}")
            return
        
        # 添加到缓冲区
        session["audio_buffer"].extend(audio_array)
        buffer_size = len(session["audio_buffer"])
        
        logger.info(f"📊 Audio buffer size: {buffer_size} samples")
        
        # 当缓冲区达到1秒音频时处理
        if buffer_size >= 16000:  # 16kHz * 1秒
            await process_complete_audio(user_id)
    
    except Exception as e:
        logger.error(f"❌ Error processing audio chunk from {user_id}: {e}")
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Audio processing error: {str(e)}"
        })

async def process_complete_audio(user_id: str):
    """处理完整的音频缓冲区"""
    try:
        session = manager.user_sessions.get(user_id)
        if not session or not session["audio_buffer"]:
            return
        
        # 获取音频数据
        audio_array = np.array(session["audio_buffer"])
        session["audio_buffer"].clear()
        
        logger.info(f"🎵 Processing complete audio: {len(audio_array)} samples")
        
        # 保存音频用于调试
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        segment_filename = f"segment_{user_id}_{timestamp}.wav"
        save_audio_to_wav(audio_array, segment_filename)
        
        # VAD处理（如果需要）
        # speech_segments = vad_service.detect_speech_segments(audio_array)
        
        # 使用增强的ASR服务
        transcription_result = enhanced_asr_service.transcribe(audio_array)
        
        if transcription_result.get("success"):
            transcription_text = transcription_result["text"]
            
            # 发送转录结果
            await manager.send_message(user_id, {
                "type": "transcription",
                "text": transcription_text,
                "confidence": transcription_result.get("confidence", 0.0)
            })
            
            # 获取NPC信息
            npc_id = session.get("npc_id", 1)
            npc = await get_npc_by_id(npc_id)
            
            if not npc:
                npc = {
                    "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。"
                }
            
            # LLM处理
            response = await llm_service.generate_response(
                transcription_text,
                session.get("conversation_history", []),
                npc["system_prompt"]
            )
            
            if response.get("success"):
                # TTS处理
                speak_content = response["speak_content"]
                tts_result = await tts_service.synthesize_speech(
                    speak_content["text"],
                    speak_content.get("emotion", "neutral"),
                    speak_content.get("speed", 1.0)
                )
                
                if tts_result.get("success"):
                    # 发送音频响应
                    audio_b64 = base64.b64encode(tts_result["audio_data"]).decode('utf-8')
                    await manager.send_message(user_id, {
                        "type": "audio_chunk",
                        "data": audio_b64
                    })
                
                # 发送完成信号
                await manager.send_message(user_id, {
                    "type": "response_complete",
                    "message": "Processing completed"
                })
        
        else:
            logger.error(f"❌ ASR failed for {user_id}: {transcription_result}")
            await manager.send_message(user_id, {
                "type": "error",
                "message": "Speech recognition failed"
            })
    
    except Exception as e:
        logger.error(f"❌ Error processing complete audio for {user_id}: {e}")
        await manager.send_message(user_id, {
            "type": "error",
            "message": f"Audio processing error: {str(e)}"
        })
```

### 步骤4: 运行测试验证

```bash
# 运行完整测试
python test_audio_fix_complete.py
```

### 步骤5: 重启服务

```bash
# 重启后端
cd backend
python main.py

# 重启前端
cd frontend
flutter run
```

## 🎯 预期修复效果

### 修复前的问题：
- ❌ Flutter Sound初始化失败
- ❌ WebSocket连接不稳定
- ❌ 音频数据无法正确传输
- ❌ 没有真实的语音识别结果

### 修复后的效果：
- ✅ macOS兼容的音频处理
- ✅ 稳定的WebSocket连接
- ✅ 正确的音频数据格式
- ✅ 完整的语音处理流水线
- ✅ 详细的日志记录用于调试

## 🛠️ 故障排除

### 1. 如果WebSocket连接仍然失败：
```bash
# 检查后端日志
tail -f backend.log

# 检查端口占用
lsof -i :8000
```

### 2. 如果音频处理失败：
- 检查ASR服务配置
- 验证音频数据格式
- 查看详细的错误日志

### 3. 如果前端无法连接：
- 确认WebSocket URL配置正确
- 检查网络连接
- 验证用户认证状态

## 📈 进一步优化建议

### 1. 集成真实的macOS音频API
考虑使用以下方案替代Flutter Sound：
- AVAudioEngine (通过平台通道)
- 自定义音频插件
- Web Audio API (如果使用Flutter Web)

### 2. 优化音频质量
- 实现噪声抑制
- 添加回声消除
- 优化音频压缩

### 3. 增强错误处理
- 添加自动重连机制
- 实现音频数据校验
- 增加用户友好的错误提示

## 📞 技术支持

如果在修复过程中遇到问题，请：
1. 检查所有依赖是否正确安装
2. 确认环境变量配置
3. 查看详细的日志输出
4. 运行测试脚本验证各个组件

这个修复方案应该能解决你的Flutter macOS应用的音频输入输出问题。