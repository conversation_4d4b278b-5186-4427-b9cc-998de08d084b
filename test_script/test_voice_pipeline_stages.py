#!/usr/bin/env python3
"""
语音流水线各阶段独立测试
测试录音→ASR→LLM→TTS→播放的每个环节
"""
import asyncio
import websockets
import json
import base64
import requests
import wave
import numpy as np
import logging
from pathlib import Path
import time
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoicePipelineTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.websocket_url = "ws://localhost:8000"
        self.audio_dir = Path("backend/audio_recordings")
        self.test_results = {
            'test_time': datetime.now().isoformat(),
            'stages': {},
            'performance': {},
            'issues': []
        }
        
    def log_stage_result(self, stage, status, details=None, error=None, duration=None):
        """记录阶段测试结果"""
        result = {
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'details': details or {},
            'error': error,
            'duration': duration
        }
        self.test_results['stages'][stage] = result
        
        if status == 'PASS':
            logger.info(f"✅ {stage}: 通过 ({duration:.2f}s)" if duration else f"✅ {stage}: 通过")
        elif status == 'FAIL':
            logger.error(f"❌ {stage}: 失败 - {error}")
            self.test_results['issues'].append({
                'stage': stage,
                'error': error,
                'details': details
            })
        elif status == 'WARN':
            logger.warning(f"⚠️ {stage}: 警告 - {error}")
    
    def test_backend_health(self):
        """测试后端健康状态"""
        logger.info("🏥 测试后端健康状态...")
        start_time = time.time()
        
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=10)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                health_data = response.json()
                services = health_data.get('services', {})
                
                # 检查各个服务状态
                service_issues = []
                for service, status in services.items():
                    if "error" in str(status).lower():
                        service_issues.append(f"{service}: {status}")
                
                if service_issues:
                    self.log_stage_result('backend_health', 'WARN', 
                                        details=health_data,
                                        error=f"部分服务异常: {'; '.join(service_issues)}",
                                        duration=duration)
                else:
                    self.log_stage_result('backend_health', 'PASS', 
                                        details=health_data,
                                        duration=duration)
                return True
            else:
                self.log_stage_result('backend_health', 'FAIL',
                                    details={'status_code': response.status_code},
                                    error=f"HTTP状态码: {response.status_code}",
                                    duration=duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_stage_result('backend_health', 'FAIL',
                                error=str(e),
                                duration=duration)
            return False
    
    def test_user_authentication(self):
        """测试用户认证"""
        logger.info("🔐 测试用户认证...")
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params={"username": "test_user", "password": "test_password"},
                timeout=10
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                auth_data = response.json()
                user_id = auth_data.get('user', {}).get('id')
                
                self.log_stage_result('authentication', 'PASS',
                                    details=auth_data,
                                    duration=duration)
                return user_id
            else:
                self.log_stage_result('authentication', 'FAIL',
                                    details={'status_code': response.status_code, 'response': response.text},
                                    error=f"认证失败: {response.status_code}",
                                    duration=duration)
                return None
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_stage_result('authentication', 'FAIL',
                                error=str(e),
                                duration=duration)
            return None
    
    async def test_websocket_connection(self, user_id):
        """测试WebSocket连接"""
        logger.info("🔌 测试WebSocket连接...")
        start_time = time.time()
        
        try:
            uri = f"{self.websocket_url}/ws/{user_id}"
            async with websockets.connect(uri, timeout=10) as websocket:
                duration = time.time() - start_time
                
                # 测试会话启动
                session_id = await self.test_session_start(websocket)
                
                if session_id:
                    self.log_stage_result('websocket_connection', 'PASS',
                                        details={'session_id': session_id},
                                        duration=duration)
                    return websocket, session_id
                else:
                    self.log_stage_result('websocket_connection', 'FAIL',
                                        error="会话启动失败",
                                        duration=duration)
                    return None, None
                    
        except Exception as e:
            duration = time.time() - start_time
            self.log_stage_result('websocket_connection', 'FAIL',
                                error=str(e),
                                duration=duration)
            return None, None
    
    async def test_session_start(self, websocket):
        """测试会话启动"""
        logger.info("🚀 测试会话启动...")
        start_time = time.time()
        
        try:
            # 发送启动会话消息
            start_message = {"type": "start_session", "npc_id": 1}
            await websocket.send(json.dumps(start_message))
            
            # 等待响应
            response = await asyncio.wait_for(websocket.recv(), timeout=10)
            response_data = json.loads(response)
            duration = time.time() - start_time
            
            if response_data.get("type") == "session_started":
                session_id = response_data.get("session_id")
                self.log_stage_result('session_start', 'PASS',
                                    details=response_data,
                                    duration=duration)
                return session_id
            else:
                self.log_stage_result('session_start', 'FAIL',
                                    details=response_data,
                                    error=f"意外响应: {response_data.get('type')}",
                                    duration=duration)
                return None
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_stage_result('session_start', 'FAIL',
                                error=str(e),
                                duration=duration)
            return None
    
    def generate_test_audio(self, duration=2.0, frequency=440, sample_rate=16000):
        """生成测试音频数据"""
        logger.info(f"🎵 生成测试音频: {duration}秒, {frequency}Hz, {sample_rate}Hz采样率")
        
        # 生成正弦波
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = (np.sin(2 * np.pi * frequency * t) * 32767).astype(np.int16)
        
        # 保存为WAV文件用于测试
        test_audio_path = Path("test_generated_audio.wav")
        with wave.open(str(test_audio_path), 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        logger.info(f"📁 测试音频已保存: {test_audio_path}")
        return audio_data.tobytes(), test_audio_path
    
    async def test_asr_stage(self, websocket):
        """测试ASR语音识别阶段"""
        logger.info("🎯 测试ASR语音识别阶段...")
        start_time = time.time()
        
        try:
            # 使用现有音频文件或生成测试音频
            audio_files = list(self.audio_dir.glob("*.wav")) if self.audio_dir.exists() else []
            
            if audio_files:
                # 使用现有音频文件
                test_file = audio_files[0]
                logger.info(f"📁 使用现有音频文件: {test_file.name}")
                with open(test_file, 'rb') as f:
                    audio_data = f.read()
            else:
                # 生成测试音频
                audio_data, test_audio_path = self.generate_test_audio()
                logger.info("🎵 使用生成的测试音频")
            
            # 转换为base64
            base64_audio = base64.b64encode(audio_data).decode('utf-8')
            
            # 发送音频数据
            audio_message = {
                "type": "audio_chunk",
                "data": base64_audio,
                "metadata": {
                    "test_stage": "asr",
                    "audio_size": len(audio_data)
                }
            }
            
            await websocket.send(json.dumps(audio_message))
            logger.info(f"📤 发送音频数据: {len(audio_data)} bytes")
            
            # 等待ASR响应
            transcription_received = False
            timeout_count = 0
            max_timeout = 30
            
            while timeout_count < max_timeout and not transcription_received:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1)
                    response_data = json.loads(response)
                    
                    if response_data.get('type') == 'transcription':
                        duration = time.time() - start_time
                        text = response_data.get('text', '')
                        confidence = response_data.get('confidence', 0)
                        
                        self.log_stage_result('asr_recognition', 'PASS',
                                            details={
                                                'text': text,
                                                'confidence': confidence,
                                                'audio_size': len(audio_data)
                                            },
                                            duration=duration)
                        
                        logger.info(f"🎯 ASR识别结果: '{text}' (置信度: {confidence:.2f})")
                        transcription_received = True
                        return text, confidence
                        
                    elif response_data.get('type') == 'error':
                        duration = time.time() - start_time
                        error_msg = response_data.get('message', '未知错误')
                        self.log_stage_result('asr_recognition', 'FAIL',
                                            details=response_data,
                                            error=error_msg,
                                            duration=duration)
                        return None, None
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 5 == 0:
                        logger.info(f"⏰ 等待ASR响应... ({timeout_count}s)")
                    continue
            
            if not transcription_received:
                duration = time.time() - start_time
                self.log_stage_result('asr_recognition', 'FAIL',
                                    error="ASR响应超时",
                                    duration=duration)
                return None, None
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_stage_result('asr_recognition', 'FAIL',
                                error=str(e),
                                duration=duration)
            return None, None
    
    async def test_llm_stage(self, websocket, transcription_text):
        """测试LLM对话生成阶段"""
        logger.info("🧠 测试LLM对话生成阶段...")
        start_time = time.time()
        
        try:
            # 等待LLM响应
            llm_response_received = False
            timeout_count = 0
            max_timeout = 30
            
            while timeout_count < max_timeout and not llm_response_received:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1)
                    response_data = json.loads(response)
                    
                    if response_data.get('type') in ['response', 'llm_response']:
                        duration = time.time() - start_time
                        response_text = response_data.get('text', '')
                        
                        self.log_stage_result('llm_generation', 'PASS',
                                            details={
                                                'input_text': transcription_text,
                                                'response_text': response_text,
                                                'response_length': len(response_text)
                                            },
                                            duration=duration)
                        
                        logger.info(f"🧠 LLM响应: '{response_text}'")
                        llm_response_received = True
                        return response_text
                        
                    elif response_data.get('type') == 'error':
                        duration = time.time() - start_time
                        error_msg = response_data.get('message', '未知错误')
                        self.log_stage_result('llm_generation', 'FAIL',
                                            details=response_data,
                                            error=error_msg,
                                            duration=duration)
                        return None
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 5 == 0:
                        logger.info(f"⏰ 等待LLM响应... ({timeout_count}s)")
                    continue
            
            if not llm_response_received:
                duration = time.time() - start_time
                self.log_stage_result('llm_generation', 'FAIL',
                                    error="LLM响应超时",
                                    duration=duration)
                return None
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_stage_result('llm_generation', 'FAIL',
                                error=str(e),
                                duration=duration)
            return None
    
    async def test_tts_stage(self, websocket, llm_response_text):
        """测试TTS语音合成阶段"""
        logger.info("🔊 测试TTS语音合成阶段...")
        start_time = time.time()
        
        try:
            # 等待TTS音频响应
            tts_response_received = False
            timeout_count = 0
            max_timeout = 30
            audio_chunks = []
            
            while timeout_count < max_timeout and not tts_response_received:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1)
                    response_data = json.loads(response)
                    
                    if response_data.get('type') == 'audio_chunk':
                        audio_data_b64 = response_data.get('data', '')
                        if audio_data_b64:
                            audio_chunks.append(audio_data_b64)
                            logger.info(f"🔊 收到TTS音频块: {len(audio_data_b64)} base64字符")
                        
                    elif response_data.get('type') == 'response_complete':
                        duration = time.time() - start_time
                        
                        if audio_chunks:
                            # 保存音频用于验证
                            try:
                                combined_audio = ''.join(audio_chunks)
                                audio_bytes = base64.b64decode(combined_audio)
                                
                                tts_audio_path = Path("test_tts_output.wav")
                                with open(tts_audio_path, 'wb') as f:
                                    f.write(audio_bytes)
                                
                                self.log_stage_result('tts_synthesis', 'PASS',
                                                    details={
                                                        'input_text': llm_response_text,
                                                        'audio_chunks': len(audio_chunks),
                                                        'total_audio_size': len(audio_bytes),
                                                        'output_file': str(tts_audio_path)
                                                    },
                                                    duration=duration)
                                
                                logger.info(f"🔊 TTS合成完成: {len(audio_bytes)} bytes, 保存到 {tts_audio_path}")
                                tts_response_received = True
                                return audio_bytes, tts_audio_path
                                
                            except Exception as decode_error:
                                self.log_stage_result('tts_synthesis', 'FAIL',
                                                    error=f"音频解码失败: {decode_error}",
                                                    duration=duration)
                                return None, None
                        else:
                            self.log_stage_result('tts_synthesis', 'FAIL',
                                                error="没有收到音频数据",
                                                duration=duration)
                            return None, None
                        
                    elif response_data.get('type') == 'error':
                        duration = time.time() - start_time
                        error_msg = response_data.get('message', '未知错误')
                        self.log_stage_result('tts_synthesis', 'FAIL',
                                            details=response_data,
                                            error=error_msg,
                                            duration=duration)
                        return None, None
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 5 == 0:
                        logger.info(f"⏰ 等待TTS响应... ({timeout_count}s)")
                    continue
            
            if not tts_response_received:
                duration = time.time() - start_time
                self.log_stage_result('tts_synthesis', 'FAIL',
                                    error="TTS响应超时",
                                    duration=duration)
                return None, None
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_stage_result('tts_synthesis', 'FAIL',
                                error=str(e),
                                duration=duration)
            return None, None
    
    def test_audio_playback(self, audio_data, audio_file_path):
        """测试音频播放能力"""
        logger.info("▶️ 测试音频播放能力...")
        start_time = time.time()
        
        try:
            if not audio_data or not audio_file_path or not Path(audio_file_path).exists():
                self.log_stage_result('audio_playback', 'FAIL',
                                    error="音频文件不存在或数据为空")
                return False
            
            # 验证音频文件格式
            try:
                with wave.open(str(audio_file_path), 'rb') as wav_file:
                    frames = wav_file.getnframes()
                    sample_rate = wav_file.getframerate()
                    channels = wav_file.getnchannels()
                    sample_width = wav_file.getsampwidth()
                    duration = frames / sample_rate
                    
                    duration_test = time.time() - start_time
                    
                    self.log_stage_result('audio_playback', 'PASS',
                                        details={
                                            'file_path': str(audio_file_path),
                                            'file_size': len(audio_data),
                                            'duration': f"{duration:.2f}秒",
                                            'sample_rate': f"{sample_rate}Hz",
                                            'channels': channels,
                                            'sample_width': f"{sample_width * 8}bit",
                                            'frames': frames
                                        },
                                        duration=duration_test)
                    
                    logger.info(f"▶️ 音频文件验证成功: {duration:.2f}秒, {sample_rate}Hz, {channels}声道")
                    return True
                    
            except Exception as wav_error:
                duration_test = time.time() - start_time
                self.log_stage_result('audio_playback', 'FAIL',
                                    error=f"音频文件格式验证失败: {wav_error}",
                                    duration=duration_test)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_stage_result('audio_playback', 'FAIL',
                                error=str(e),
                                duration=duration)
            return False
    
    async def run_full_pipeline_test(self):
        """运行完整流水线测试"""
        logger.info("🚀 开始完整语音流水线测试...")
        logger.info("=" * 60)
        
        total_start_time = time.time()
        
        # 1. 测试后端健康状态
        if not self.test_backend_health():
            logger.error("❌ 后端健康检查失败，停止测试")
            return False
        
        # 2. 测试用户认证
        user_id = self.test_user_authentication()
        if not user_id:
            logger.error("❌ 用户认证失败，停止测试")
            return False
        
        # 3. 测试WebSocket连接和会话启动
        websocket, session_id = await self.test_websocket_connection(user_id)
        if not websocket or not session_id:
            logger.error("❌ WebSocket连接失败，停止测试")
            return False
        
        try:
            # 4. 测试ASR阶段
            transcription_text, confidence = await self.test_asr_stage(websocket)
            if not transcription_text:
                logger.error("❌ ASR测试失败，停止后续测试")
                return False
            
            # 5. 测试LLM阶段
            llm_response_text = await self.test_llm_stage(websocket, transcription_text)
            if not llm_response_text:
                logger.error("❌ LLM测试失败，停止后续测试")
                return False
            
            # 6. 测试TTS阶段
            audio_data, audio_file_path = await self.test_tts_stage(websocket, llm_response_text)
            if not audio_data:
                logger.error("❌ TTS测试失败，停止后续测试")
                return False
            
            # 7. 测试音频播放
            playback_success = self.test_audio_playback(audio_data, audio_file_path)
            
            # 计算总体性能
            total_duration = time.time() - total_start_time
            self.test_results['performance']['total_duration'] = total_duration
            
            logger.info("=" * 60)
            logger.info("📊 完整流水线测试结果:")
            logger.info(f"   总耗时: {total_duration:.2f}秒")
            
            # 统计各阶段结果
            passed_stages = 0
            total_stages = 0
            
            for stage, result in self.test_results['stages'].items():
                total_stages += 1
                if result['status'] == 'PASS':
                    passed_stages += 1
                    duration = result.get('duration', 0)
                    logger.info(f"   ✅ {stage}: 通过 ({duration:.2f}s)")
                else:
                    logger.info(f"   ❌ {stage}: {result['status']} - {result.get('error', 'N/A')}")
            
            success_rate = (passed_stages / total_stages * 100) if total_stages > 0 else 0
            logger.info(f"   成功率: {success_rate:.1f}% ({passed_stages}/{total_stages})")
            
            logger.info("=" * 60)
            
            return success_rate >= 80  # 80%以上成功率认为测试通过
            
        finally:
            # 关闭WebSocket连接
            if websocket:
                await websocket.close()
    
    def save_test_report(self):
        """保存测试报告"""
        report_file = f"voice_pipeline_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 测试报告已保存: {report_file}")
        return report_file

async def main():
    """主函数"""
    logger.info("🎤 语音流水线各阶段独立测试")
    logger.info("🎯 目标: 验证录音→ASR→LLM→TTS→播放完整链路")
    logger.info("=" * 60)
    
    tester = VoicePipelineTester()
    
    try:
        success = await tester.run_full_pipeline_test()
        
        # 保存测试报告
        report_file = tester.save_test_report()
        
        if success:
            logger.info("🎉 语音流水线测试全部通过！")
            logger.info("💡 系统已准备就绪，可以进行实际使用")
        else:
            logger.error("❌ 语音流水线测试失败！")
            logger.info("🔧 请检查失败的阶段并修复问题")
        
        logger.info(f"📄 详细报告: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        tester.save_test_report()

if __name__ == "__main__":
    asyncio.run(main())