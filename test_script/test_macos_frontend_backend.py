#!/usr/bin/env python3
"""
macOS前端后端连通性测试
测试Flutter macOS应用与后端的语音流连通性
"""
import asyncio
import websockets
import json
import base64
import requests
import wave
import logging
import time
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MacOSFrontendBackendTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.websocket_url = "ws://localhost:8000"
        self.audio_dir = Path("backend/audio_recordings")
        
    def test_backend_connectivity(self):
        """测试后端连通性"""
        logger.info("🔍 测试后端连通性...")
        
        try:
            # 测试基本连接
            response = requests.get(f"{self.backend_url}/", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 后端基本连接正常")
                logger.info(f"   响应: {response.json()}")
            else:
                logger.error(f"❌ 后端连接异常: {response.status_code}")
                return False
                
            # 测试健康检查
            response = requests.get(f"{self.backend_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                logger.info("✅ 后端健康检查通过")
                
                services = health_data.get('services', {})
                for service, status in services.items():
                    logger.info(f"   {service}: {status}")
                    
                return True
            else:
                logger.error(f"❌ 后端健康检查失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 后端连通性测试异常: {e}")
            return False
    
    def test_auth_endpoints(self):
        """测试认证端点"""
        logger.info("🔐 测试认证端点...")
        
        try:
            # 测试登录
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params={
                    "username": "test_user",
                    "password": "test_password"
                },
                timeout=10
            )
            
            if response.status_code == 200:
                auth_data = response.json()
                logger.info("✅ 用户认证成功")
                user_info = auth_data.get('user', {})
                logger.info(f"   用户ID: {user_info.get('id')}")
                logger.info(f"   用户名: {user_info.get('username')}")
                logger.info(f"   昵称: {user_info.get('nickname')}")
                return auth_data
            else:
                logger.error(f"❌ 用户认证失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 认证端点测试异常: {e}")
            return None
    
    def test_npc_endpoints(self):
        """测试NPC端点"""
        logger.info("👥 测试NPC端点...")
        
        try:
            response = requests.get(f"{self.backend_url}/npcs", timeout=10)
            if response.status_code == 200:
                npcs_data = response.json()
                npcs = npcs_data.get('npcs', [])
                logger.info(f"✅ 获取到 {len(npcs)} 个NPC")
                
                for npc in npcs:
                    logger.info(f"   - {npc.get('name')} (ID: {npc.get('id')})")
                
                return npcs
            else:
                logger.error(f"❌ 获取NPC列表失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"❌ NPC端点测试异常: {e}")
            return []
    
    async def test_websocket_connection(self, user_id):
        """测试WebSocket连接"""
        logger.info("🔌 测试WebSocket连接...")
        
        try:
            uri = f"{self.websocket_url}/ws/{user_id}"
            logger.info(f"   连接URI: {uri}")
            
            async with websockets.connect(uri, timeout=10) as websocket:
                logger.info("✅ WebSocket连接成功")
                
                # 测试会话启动
                session_id = await self.test_session_management(websocket)
                if not session_id:
                    return False
                
                # 测试语音流
                success = await self.test_voice_streaming(websocket)
                
                return success
                
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
            return False
    
    async def test_session_management(self, websocket):
        """测试会话管理"""
        logger.info("🚀 测试会话管理...")
        
        try:
            # 发送启动会话消息
            start_message = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_message))
            logger.info("📤 发送会话启动请求")
            
            # 等待响应
            response = await asyncio.wait_for(websocket.recv(), timeout=15)
            response_data = json.loads(response)
            
            logger.info(f"📥 收到响应类型: {response_data.get('type')}")
            
            if response_data.get("type") == "session_started":
                session_id = response_data.get("session_id")
                npc_id = response_data.get("npc_id")
                logger.info(f"✅ 会话启动成功")
                logger.info(f"   会话ID: {session_id}")
                logger.info(f"   NPC ID: {npc_id}")
                return session_id
            else:
                logger.error(f"❌ 会话启动失败: {response_data}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 会话管理测试异常: {e}")
            return None
    
    async def test_voice_streaming(self, websocket):
        """测试语音流传输"""
        logger.info("🎵 测试语音流传输...")
        
        # 获取音频文件
        audio_files = list(self.audio_dir.glob("*.wav"))
        if not audio_files:
            logger.error("❌ 没有可用的音频文件")
            return False
        
        test_file = audio_files[0]
        logger.info(f"📁 使用音频文件: {test_file.name}")
        
        try:
            # 读取音频数据
            with wave.open(str(test_file), 'rb') as wav_file:
                audio_data = wav_file.readframes(wav_file.getnframes())
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
            
            logger.info(f"📊 音频信息:")
            logger.info(f"   采样率: {sample_rate} Hz")
            logger.info(f"   声道数: {channels}")
            logger.info(f"   位深: {sample_width * 8} bit")
            logger.info(f"   数据长度: {len(audio_data)} bytes")
            
            # 分块发送音频
            chunk_size = 1024
            chunks = [audio_data[i:i + chunk_size] for i in range(0, len(audio_data), chunk_size)]
            logger.info(f"🔪 音频分为 {len(chunks)} 个块")
            
            responses_received = []
            transcription_received = False
            audio_response_received = False
            
            # 发送音频块
            for i, chunk in enumerate(chunks):
                # 编码为base64
                base64_chunk = base64.b64encode(chunk).decode('utf-8')
                
                # 发送音频块
                audio_message = {
                    "type": "audio_chunk",
                    "data": base64_chunk
                }
                
                await websocket.send(json.dumps(audio_message))
                logger.info(f"📤 发送音频块 {i+1}/{len(chunks)} ({len(chunk)} bytes)")
                
                # 检查响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=0.5)
                    response_data = json.loads(response)
                    responses_received.append(response_data)
                    
                    msg_type = response_data.get('type')
                    if msg_type == 'transcription':
                        text = response_data.get('text', '')
                        confidence = response_data.get('confidence', 0)
                        logger.info(f"🎯 转录结果: '{text}' (置信度: {confidence:.2f})")
                        transcription_received = True
                    elif msg_type == 'audio_chunk':
                        logger.info("🔊 收到AI音频响应")
                        audio_response_received = True
                    elif msg_type == 'response_complete':
                        logger.info("✅ AI响应完成")
                        break
                    else:
                        logger.info(f"📥 收到响应: {msg_type}")
                        
                except asyncio.TimeoutError:
                    # 没有立即响应，继续发送
                    pass
                
                # 模拟实时流的延迟
                await asyncio.sleep(0.1)
            
            # 等待最终响应
            logger.info("⏳ 等待最终处理结果...")
            try:
                timeout_count = 0
                max_timeout = 30  # 30秒超时
                
                while timeout_count < max_timeout:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=1)
                        response_data = json.loads(response)
                        responses_received.append(response_data)
                        
                        msg_type = response_data.get('type')
                        logger.info(f"📥 最终响应: {msg_type}")
                        
                        if msg_type == 'response_complete':
                            logger.info("✅ 语音处理流程完成")
                            break
                        elif msg_type == 'transcription':
                            text = response_data.get('text', '')
                            logger.info(f"🎯 最终转录: '{text}'")
                            transcription_received = True
                        elif msg_type == 'audio_chunk':
                            logger.info("🔊 收到AI音频块")
                            audio_response_received = True
                        elif msg_type == 'error':
                            error_msg = response_data.get('message', '未知错误')
                            logger.error(f"❌ 处理错误: {error_msg}")
                            break
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        if timeout_count % 5 == 0:
                            logger.info(f"⏰ 等待响应中... ({timeout_count}s)")
                        continue
                        
            except Exception as e:
                logger.error(f"❌ 等待最终响应异常: {e}")
            
            # 统计结果
            logger.info("=" * 50)
            logger.info("📊 语音流测试结果:")
            logger.info(f"   发送音频块数: {len(chunks)}")
            logger.info(f"   收到响应数: {len(responses_received)}")
            logger.info(f"   收到转录结果: {'✅' if transcription_received else '❌'}")
            logger.info(f"   收到音频响应: {'✅' if audio_response_received else '❌'}")
            
            # 统计响应类型
            response_types = {}
            for resp in responses_received:
                resp_type = resp.get('type', 'unknown')
                response_types[resp_type] = response_types.get(resp_type, 0) + 1
            
            for resp_type, count in response_types.items():
                logger.info(f"   {resp_type}: {count} 次")
            
            logger.info("=" * 50)
            
            # 判断测试是否成功
            success = transcription_received or audio_response_received
            if success:
                logger.info("✅ 语音流测试成功")
            else:
                logger.warning("⚠️ 语音流测试部分成功或失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 语音流传输异常: {e}")
            return False
    
    def test_audio_file_upload(self):
        """测试音频文件上传API"""
        logger.info("📤 测试音频文件上传API...")
        
        audio_files = list(self.audio_dir.glob("*.wav"))
        if not audio_files:
            logger.error("❌ 没有可用的音频文件")
            return False
        
        test_file = audio_files[0]
        logger.info(f"📁 使用音频文件: {test_file.name}")
        
        try:
            with open(test_file, 'rb') as f:
                files = {'file': (test_file.name, f, 'audio/wav')}
                data = {'user_id': 1, 'npc_id': 1}
                
                response = requests.post(
                    f"{self.backend_url}/process-audio",
                    files=files,
                    data=data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info("✅ 音频文件上传处理成功")
                    logger.info(f"   转录结果: {result.get('transcription', 'N/A')}")
                    logger.info(f"   响应文本: {result.get('response_text', 'N/A')}")
                    logger.info(f"   情感: {result.get('emotion', 'N/A')}")
                    logger.info(f"   语速: {result.get('speed', 'N/A')}")
                    logger.info(f"   音频大小: {result.get('audio_size', 'N/A')} bytes")
                    return True
                else:
                    logger.error(f"❌ 音频文件上传失败: {response.status_code} - {response.text}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 音频文件上传异常: {e}")
            return False
    
    async def run_complete_test(self):
        """运行完整测试"""
        logger.info("🚀 开始macOS前端后端连通性测试...")
        logger.info("=" * 60)
        
        # 1. 测试后端连通性
        if not self.test_backend_connectivity():
            logger.error("❌ 后端连通性测试失败，停止测试")
            return False
        
        # 2. 测试认证端点
        auth_data = self.test_auth_endpoints()
        if not auth_data:
            logger.error("❌ 认证端点测试失败，停止测试")
            return False
        
        user_id = auth_data.get('user', {}).get('id', 1)
        
        # 3. 测试NPC端点
        npcs = self.test_npc_endpoints()
        if not npcs:
            logger.warning("⚠️ NPC端点测试失败，但继续其他测试")
        
        # 4. 测试音频文件上传API
        if not self.test_audio_file_upload():
            logger.warning("⚠️ 音频文件上传API测试失败，但继续其他测试")
        
        # 5. 测试WebSocket连接和语音流
        if not await self.test_websocket_connection(user_id):
            logger.error("❌ WebSocket和语音流测试失败")
            return False
        
        logger.info("=" * 60)
        logger.info("🎉 macOS前端后端连通性测试完成！")
        return True

async def main():
    """主函数"""
    logger.info("🍎 macOS前端后端连通性测试开始...")
    
    tester = MacOSFrontendBackendTester()
    
    # 检查音频文件
    if not tester.audio_dir.exists():
        logger.error(f"❌ 音频目录不存在: {tester.audio_dir}")
        return
    
    audio_files = list(tester.audio_dir.glob("*.wav"))
    if not audio_files:
        logger.error("❌ 没有找到音频文件")
        return
    
    logger.info(f"📁 找到 {len(audio_files)} 个音频文件用于测试")
    
    # 运行测试
    success = await tester.run_complete_test()
    
    if success:
        logger.info("✅ 所有测试通过！前端后端连通性正常！")
        logger.info("💡 现在可以在Flutter macOS应用中测试语音功能了")
    else:
        logger.error("❌ 测试失败！请检查后端服务和配置")

if __name__ == "__main__":
    asyncio.run(main())