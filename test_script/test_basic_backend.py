#!/usr/bin/env python3
import requests

print("Testing basic backend connectivity...")

# 测试根路径
try:
    response = requests.get("http://localhost:8000/", timeout=5)
    print(f"Root endpoint: {response.status_code} - {response.text}")
except Exception as e:
    print(f"Root endpoint error: {e}")

# 测试健康检查
try:
    response = requests.get("http://localhost:8000/health", timeout=5)
    print(f"Health endpoint: {response.status_code}")
except Exception as e:
    print(f"Health endpoint error: {e}")

# 测试登录端点
try:
    response = requests.post(
        "http://localhost:8000/auth/login?username=test_user&password=test_password",
        timeout=5
    )
    print(f"Login endpoint: {response.status_code} - {response.text}")
except Exception as e:
    print(f"Login endpoint error: {e}")