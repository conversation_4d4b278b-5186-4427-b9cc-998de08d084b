#!/usr/bin/env python3
"""
最终ASR修复测试
验证音频质量检测和幻觉内容过滤
"""
import asyncio
import logging
import numpy as np
import sys
sys.path.append('backend')

from services.enhanced_asr_service import EnhancedASRService

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_asr_fix():
    """测试ASR修复效果"""
    logger.info("🔧 ASR修复效果测试")
    logger.info("🎯 验证音频质量检测和幻觉内容过滤")
    logger.info("=" * 60)
    
    # 创建ASR服务
    asr_service = EnhancedASRService()
    
    test_cases = [
        {
            'name': '静音音频',
            'audio': np.zeros(16000, dtype=np.float32),
            'expected': '应该被音频质量检测拦截'
        },
        {
            'name': '极低音量噪音',
            'audio': np.random.normal(0, 0.001, 16000).astype(np.float32),
            'expected': '应该被音频质量检测拦截'
        },
        {
            'name': '正常音量噪音',
            'audio': np.random.normal(0, 0.1, 16000).astype(np.float32),
            'expected': '可能通过质量检测，但结果可能被幻觉检测拦截'
        },
        {
            'name': '正弦波信号',
            'audio': (np.sin(2 * np.pi * 440 * np.linspace(0, 1, 16000, False)) * 0.5).astype(np.float32),
            'expected': '可能通过质量检测，但结果可能被幻觉检测拦截'
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n{i}️⃣ 测试: {test_case['name']}")
        logger.info(f"   预期: {test_case['expected']}")
        
        try:
            result = asr_service.transcribe(test_case['audio'])
            
            success = result.get('success', False)
            text = result.get('text', '')
            confidence = result.get('confidence', 0)
            error = result.get('error', '')
            
            logger.info(f"   结果: success={success}")
            if text:
                logger.info(f"   文本: '{text}'")
            if error:
                logger.info(f"   错误: {error}")
            logger.info(f"   置信度: {confidence}")
            
            # 判断是否符合预期
            if not success and not text:
                status = "✅ 正确拦截"
            elif success and text and len(text.strip()) > 0:
                status = "⚠️ 通过了检测"
            else:
                status = "❓ 未知状态"
            
            logger.info(f"   状态: {status}")
            
            results.append({
                'name': test_case['name'],
                'success': success,
                'text': text,
                'confidence': confidence,
                'error': error,
                'status': status
            })
            
        except Exception as e:
            logger.error(f"   ❌ 测试失败: {e}")
            results.append({
                'name': test_case['name'],
                'success': False,
                'text': '',
                'confidence': 0,
                'error': str(e),
                'status': "❌ 异常"
            })
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("📊 测试结果总结")
    logger.info("=" * 60)
    
    blocked_count = sum(1 for r in results if not r['success'])
    passed_count = sum(1 for r in results if r['success'] and r['text'])
    
    logger.info(f"总测试数: {len(results)}")
    logger.info(f"被正确拦截: {blocked_count}")
    logger.info(f"通过检测: {passed_count}")
    
    for result in results:
        logger.info(f"\n📋 {result['name']}")
        logger.info(f"   状态: {result['status']}")
        if result['text']:
            logger.info(f"   输出: '{result['text']}'")
    
    logger.info("\n" + "=" * 60)
    if blocked_count >= 2:  # 至少静音和低音量应该被拦截
        logger.info("🎉 修复效果良好！音频质量检测工作正常")
    else:
        logger.warning("⚠️ 修复效果有限，需要进一步调整")
    
    logger.info("💡 现在可以测试真实的语音输入了")

async def main():
    """主函数"""
    await test_asr_fix()

if __name__ == "__main__":
    asyncio.run(main())