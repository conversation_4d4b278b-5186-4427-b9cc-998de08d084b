#!/usr/bin/env python3
"""
启动简化语音测试
"""

import webbrowser
import subprocess
import time
import sys

def main():
    print("🚀 启动简化语音测试")
    print("=" * 40)
    
    # 确保Web服务器运行
    print("🌐 启动Web服务器...")
    subprocess.Popen([sys.executable, '-m', 'http.server', '8002'], 
                    stdout=subprocess.DEVNULL, 
                    stderr=subprocess.DEVNULL)
    
    time.sleep(2)
    
    # 打开简化测试页面
    url = "http://localhost:8002/simple_voice_test.html"
    print(f"🌐 打开测试页面: {url}")
    webbrowser.open(url)
    
    print("\n📋 使用说明:")
    print("1. 页面会自动测试服务连接")
    print("2. 点击'开始录音'按钮")
    print("3. 说话后点击'停止录音'")
    print("4. 查看ASR识别和LLM回复")
    print("5. 听取TTS语音播放")
    
    print("\n🔧 如果服务未运行，请在其他终端执行:")
    print("终端1: cd backend && python main.py")
    print("终端2: python start_real_voice_test.py")

if __name__ == "__main__":
    main()