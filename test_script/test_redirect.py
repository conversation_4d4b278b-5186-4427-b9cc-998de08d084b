#!/usr/bin/env python3
import requests

print("Testing redirect behavior...")

# 测试不跟随重定向
try:
    response = requests.post(
        "http://localhost:8000/auth/login",
        params={"username": "test_user", "password": "test_password"},
        allow_redirects=False,  # 不跟随重定向
        timeout=5
    )
    print(f"Without redirect: Status {response.status_code}")
    if 300 <= response.status_code < 400:
        print(f"Redirect location: {response.headers.get('Location', 'None')}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {e}")

print("\n" + "="*50 + "\n")

# 测试跟随重定向
try:
    response = requests.post(
        "http://localhost:8000/auth/login",
        params={"username": "test_user", "password": "test_password"},
        allow_redirects=True,  # 跟随重定向
        timeout=5
    )
    print(f"With redirect: Status {response.status_code}")
    print(f"Final URL: {response.url}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {e}")