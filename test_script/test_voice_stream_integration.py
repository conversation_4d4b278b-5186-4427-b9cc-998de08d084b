#!/usr/bin/env python3
"""
综合测试后端和前端的语音流联通性
测试语音输入/输出的完整流程
"""
import asyncio
import websockets
import json
import base64
import numpy as np
import requests
import time
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VoiceStreamTester:
    def __init__(self, backend_url="http://localhost:8000", websocket_url="ws://localhost:8000"):
        self.backend_url = backend_url
        self.websocket_url = websocket_url
        self.user_id = "test_user_1"
        self.npc_id = 1
        self.session_id = None
        
    def generate_test_audio_data(self, duration_ms=1000, sample_rate=16000):
        """生成测试音频数据（模拟语音）"""
        samples = int(duration_ms * sample_rate / 1000)
        # 生成简单的正弦波作为测试音频
        t = np.linspace(0, duration_ms/1000, samples)
        frequency = 440  # A4音符
        audio = np.sin(2 * np.pi * frequency * t) * 0.5
        # 转换为16位整数
        audio_int16 = (audio * 32767).astype(np.int16)
        return audio_int16.tobytes()
    
    async def test_backend_health(self):
        """测试后端健康状态"""
        logger.info("🏥 测试后端健康状态...")
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                logger.info("✅ 后端健康检查通过")
                logger.info(f"   服务状态: {health_data.get('services', {})}")
                return True
            else:
                logger.error(f"❌ 后端健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 后端健康检查异常: {e}")
            return False
    
    async def test_auth_system(self):
        """测试认证系统"""
        logger.info("🔐 测试认证系统...")
        try:
            # 测试登录
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params={
                    "username": "test_user",
                    "password": "test_password"
                },
                timeout=10
            )
            
            if response.status_code == 200:
                auth_data = response.json()
                logger.info("✅ 用户认证成功")
                logger.info(f"   用户信息: {auth_data.get('user', {})}")
                return True
            else:
                logger.error(f"❌ 用户认证失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            logger.error(f"❌ 认证系统异常: {e}")
            return False
    
    async def test_websocket_connection(self):
        """测试WebSocket连接"""
        logger.info("🔌 测试WebSocket连接...")
        try:
            uri = f"{self.websocket_url}/ws/{self.user_id}"
            async with websockets.connect(uri, timeout=10) as websocket:
                logger.info("✅ WebSocket连接成功")
                
                # 发送会话开始消息
                start_message = {
                    "type": "start_session",
                    "npc_id": self.npc_id
                }
                await websocket.send(json.dumps(start_message))
                logger.info("📤 发送会话开始消息")
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    logger.info(f"📥 收到响应: {response_data}")
                    
                    if response_data.get("type") == "session_started":
                        self.session_id = response_data.get("session_id")
                        logger.info(f"✅ 会话创建成功，会话ID: {self.session_id}")
                        return websocket
                    else:
                        logger.error(f"❌ 会话创建失败: {response_data}")
                        return None
                except asyncio.TimeoutError:
                    logger.error("❌ 等待会话响应超时")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
            return None
    
    async def test_audio_streaming(self, websocket):
        """测试音频流传输"""
        logger.info("🎵 测试音频流传输...")
        
        try:
            # 生成测试音频数据
            audio_data = self.generate_test_audio_data(duration_ms=2000)  # 2秒音频
            logger.info(f"📊 生成测试音频数据: {len(audio_data)} 字节")
            
            # 分块发送音频数据（模拟实时流）
            chunk_size = 1024  # 每块1KB
            chunks = [audio_data[i:i+chunk_size] for i in range(0, len(audio_data), chunk_size)]
            
            logger.info(f"📦 将音频分为 {len(chunks)} 个块进行流式传输")
            
            for i, chunk in enumerate(chunks):
                # 编码为base64
                base64_chunk = base64.b64encode(chunk).decode('utf-8')
                
                # 发送音频块
                audio_message = {
                    "type": "audio_chunk",
                    "data": base64_chunk
                }
                
                await websocket.send(json.dumps(audio_message))
                logger.info(f"📤 发送音频块 {i+1}/{len(chunks)} ({len(chunk)} 字节)")
                
                # 模拟实时流的延迟
                await asyncio.sleep(0.1)
                
                # 检查是否有响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=0.1)
                    response_data = json.loads(response)
                    logger.info(f"📥 收到实时响应: {response_data.get('type', 'unknown')}")
                    
                    if response_data.get("type") == "transcription":
                        logger.info(f"🎯 转录结果: {response_data.get('text', 'N/A')}")
                    elif response_data.get("type") == "audio_chunk":
                        logger.info("🔊 收到TTS音频块")
                    elif response_data.get("type") == "response_complete":
                        logger.info("✅ 响应完成")
                        break
                        
                except asyncio.TimeoutError:
                    # 没有立即响应，继续发送
                    continue
            
            # 等待最终响应
            logger.info("⏳ 等待处理完成...")
            try:
                while True:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    response_data = json.loads(response)
                    logger.info(f"📥 最终响应: {response_data}")
                    
                    if response_data.get("type") == "response_complete":
                        logger.info("✅ 音频流处理完成")
                        return True
                    elif response_data.get("type") == "error":
                        logger.error(f"❌ 处理错误: {response_data.get('message', 'Unknown error')}")
                        return False
                        
            except asyncio.TimeoutError:
                logger.error("❌ 等待最终响应超时")
                return False
                
        except Exception as e:
            logger.error(f"❌ 音频流传输异常: {e}")
            return False
    
    async def test_interrupt_functionality(self, websocket):
        """测试中断功能"""
        logger.info("⏸️ 测试中断功能...")
        
        try:
            # 发送中断消息
            interrupt_message = {"type": "interrupt"}
            await websocket.send(json.dumps(interrupt_message))
            logger.info("📤 发送中断消息")
            
            # 等待中断响应
            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            response_data = json.loads(response)
            
            if response_data.get("type") == "interrupted":
                logger.info("✅ 中断功能正常")
                return True
            else:
                logger.error(f"❌ 中断响应异常: {response_data}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 中断功能测试异常: {e}")
            return False
    
    async def test_session_end(self, websocket):
        """测试会话结束"""
        logger.info("🔚 测试会话结束...")
        
        try:
            # 发送会话结束消息
            end_message = {"type": "end_session"}
            await websocket.send(json.dumps(end_message))
            logger.info("📤 发送会话结束消息")
            
            # 等待结束响应
            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            response_data = json.loads(response)
            
            if response_data.get("type") == "session_ended":
                logger.info("✅ 会话结束正常")
                return True
            else:
                logger.error(f"❌ 会话结束响应异常: {response_data}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 会话结束测试异常: {e}")
            return False
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🚀 开始语音流综合测试...")
        logger.info("=" * 60)
        
        test_results = {}
        
        # 1. 测试后端健康状态
        test_results["backend_health"] = await self.test_backend_health()
        
        # 2. 测试认证系统
        test_results["auth_system"] = await self.test_auth_system()
        
        # 3. 测试WebSocket连接和会话创建
        websocket = await self.test_websocket_connection()
        test_results["websocket_connection"] = websocket is not None
        
        if websocket:
            try:
                # 4. 测试音频流传输
                test_results["audio_streaming"] = await self.test_audio_streaming(websocket)
                
                # 5. 测试中断功能
                test_results["interrupt_functionality"] = await self.test_interrupt_functionality(websocket)
                
                # 6. 测试会话结束
                test_results["session_end"] = await self.test_session_end(websocket)
                
            finally:
                await websocket.close()
        else:
            test_results["audio_streaming"] = False
            test_results["interrupt_functionality"] = False
            test_results["session_end"] = False
        
        # 输出测试结果
        logger.info("=" * 60)
        logger.info("📊 测试结果汇总:")
        logger.info("=" * 60)
        
        all_passed = True
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"   {test_name.replace('_', ' ').title()}: {status}")
            if not result:
                all_passed = False
        
        logger.info("=" * 60)
        if all_passed:
            logger.info("🎉 所有测试通过！语音流系统运行正常")
        else:
            logger.info("⚠️ 部分测试失败，请检查相关组件")
        
        return test_results

async def main():
    """主函数"""
    tester = VoiceStreamTester()
    results = await tester.run_comprehensive_test()
    
    # 返回退出码
    all_passed = all(results.values())
    exit(0 if all_passed else 1)

if __name__ == "__main__":
    asyncio.run(main())