#!/usr/bin/env python3
"""
测试完整的语音管道 - 前后端集成测试
"""
import asyncio
import websockets
import json
import base64
import numpy as np
import requests
import time
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FullVoicePipelineTester:
    def __init__(self, backend_url="http://localhost:8000", websocket_url="ws://localhost:8000"):
        self.backend_url = backend_url
        self.websocket_url = websocket_url
        self.user_id = None
        self.session_id = None
        
    def generate_test_audio_data(self, duration_ms=1000, sample_rate=16000):
        """生成测试音频数据（模拟语音）"""
        samples = int(duration_ms * sample_rate / 1000)
        # 生成简单的正弦波作为测试音频
        t = np.linspace(0, duration_ms/1000, samples)
        frequency = 440  # A4音符
        audio = np.sin(2 * np.pi * frequency * t) * 0.5
        # 转换为16位整数
        audio_int16 = (audio * 32767).astype(np.int16)
        return audio_int16.tobytes()
    
    async def login_user(self, username="test_user", password="test_password"):
        """用户登录"""
        try:
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params={"username": username, "password": password},
                timeout=10
            )
            
            if response.status_code != 200:
                logger.error(f"❌ 登录失败: {response.status_code}")
                return False
            
            user_data = response.json()
            self.user_id = user_data.get('user', {}).get('id', 1)
            logger.info(f"✅ 用户登录成功，ID: {self.user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 登录异常: {e}")
            return False
    
    async def connect_websocket(self):
        """连接WebSocket"""
        if not self.user_id:
            logger.error("❌ 用户未登录")
            return None
            
        try:
            uri = f"{self.websocket_url}/ws/{self.user_id}"
            logger.info(f"🔌 连接WebSocket: {uri}")
            
            websocket = await websockets.connect(uri)
            logger.info("✅ WebSocket连接成功")
            return websocket
            
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
            return None
    
    async def start_session(self, websocket, npc_id=1):
        """启动会话"""
        try:
            start_msg = {"type": "start_session", "npc_id": npc_id}
            await websocket.send(json.dumps(start_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动响应
            response = await asyncio.wait_for(websocket.recv(), timeout=10)
            response_data = json.loads(response)
            
            if response_data.get("type") == "session_started":
                self.session_id = response_data.get("session_id")
                logger.info(f"✅ 会话启动成功，ID: {self.session_id}")
                return True
            else:
                logger.error(f"❌ 会话启动失败: {response_data}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 启动会话异常: {e}")
            return False
    
    async def send_audio_chunks(self, websocket, audio_data, chunk_size=1024):
        """发送音频块"""
        try:
            # 将音频数据分块
            chunks = [audio_data[i:i+chunk_size] for i in range(0, len(audio_data), chunk_size)]
            logger.info(f"📦 将音频分为 {len(chunks)} 个块进行流式传输")
            
            for i, chunk in enumerate(chunks):
                # 编码为base64
                base64_chunk = base64.b64encode(chunk).decode('utf-8')
                
                # 发送音频块
                audio_message = {
                    "type": "audio_chunk",
                    "data": base64_chunk
                }
                
                await websocket.send(json.dumps(audio_message))
                logger.info(f"📤 发送音频块 {i+1}/{len(chunks)} ({len(chunk)} 字节)")
                
                # 模拟实时流的延迟
                await asyncio.sleep(0.05)  # 50ms延迟
                
            return True
            
        except Exception as e:
            logger.error(f"❌ 发送音频块异常: {e}")
            return False
    
    async def wait_for_response(self, websocket, timeout=30):
        """等待处理结果"""
        logger.info("⏳ 等待处理结果...")
        results = {
            "transcription": None,
            "response_complete": False,
            "audio_chunks": [],
            "errors": []
        }
        
        try:
            while True:
                response = await asyncio.wait_for(websocket.recv(), timeout=timeout)
                response_data = json.loads(response)
                message_type = response_data.get("type", "unknown")
                
                logger.info(f"📥 收到响应: {message_type}")
                
                if message_type == "transcription":
                    results["transcription"] = response_data.get("text", "")
                    logger.info(f"🎯 转录结果: {results['transcription']}")
                elif message_type == "audio_chunk":
                    results["audio_chunks"].append(response_data.get("data", ""))
                    logger.info(f"🎵 收到音频块: {len(response_data.get('data', ''))} 字符")
                elif message_type == "response_complete":
                    results["response_complete"] = True
                    logger.info("✅ 响应完成")
                    break
                elif message_type == "error":
                    error_msg = response_data.get("message", "Unknown error")
                    results["errors"].append(error_msg)
                    logger.error(f"❌ 错误: {error_msg}")
                    break
                    
        except asyncio.TimeoutError:
            logger.warning("⚠️ 等待响应超时")
        except Exception as e:
            logger.error(f"❌ 等待响应异常: {e}")
            
        return results
    
    async def end_session(self, websocket):
        """结束会话"""
        try:
            end_msg = {"type": "end_session"}
            await websocket.send(json.dumps(end_msg))
            logger.info("📤 发送结束会话请求")
            
            # 等待结束响应
            try:
                end_response = await asyncio.wait_for(websocket.recv(), timeout=5)
                end_data = json.loads(end_response)
                if end_data.get("type") == "session_ended":
                    logger.info("✅ 会话正常结束")
                else:
                    logger.warning(f"⚠️ 意外的结束响应: {end_data}")
            except asyncio.TimeoutError:
                logger.warning("⚠️ 等待结束响应超时")
                
        except Exception as e:
            logger.error(f"❌ 结束会话异常: {e}")
    
    async def test_full_pipeline(self):
        """测试完整的语音管道"""
        logger.info("🚀 开始测试完整的语音管道...")
        
        # 1. 用户登录
        if not await self.login_user():
            return False
        
        # 2. WebSocket连接
        websocket = await self.connect_websocket()
        if not websocket:
            return False
        
        try:
            # 3. 启动会话
            if not await self.start_session(websocket):
                return False
            
            # 4. 生成测试音频
            logger.info("🎵 生成测试音频数据...")
            audio_data = self.generate_test_audio_data(duration_ms=3000)  # 3秒音频
            logger.info(f"✅ 生成音频数据: {len(audio_data)} 字节")
            
            # 5. 发送音频数据
            if not await self.send_audio_chunks(websocket, audio_data):
                return False
            
            # 6. 等待处理结果
            results = await self.wait_for_response(websocket)
            
            # 7. 结束会话
            await self.end_session(websocket)
            
            # 8. 关闭WebSocket
            await websocket.close()
            logger.info("🔌 WebSocket连接已关闭")
            
            # 9. 验证结果
            success = (
                results["transcription"] is not None and 
                len(results["transcription"]) > 0 and
                results["response_complete"]
            )
            
            if success:
                logger.info("🎉 完整语音管道测试成功！")
                logger.info(f"📊 测试结果:")
                logger.info(f"   - 转录文本: '{results['transcription']}'")
                logger.info(f"   - 音频块数量: {len(results['audio_chunks'])}")
                logger.info(f"   - 错误数量: {len(results['errors'])}")
            else:
                logger.error("❌ 完整语音管道测试失败！")
                logger.info(f"📊 测试结果:")
                logger.info(f"   - 转录文本: {results['transcription']}")
                logger.info(f"   - 音频块数量: {len(results['audio_chunks'])}")
                logger.info(f"   - 错误: {results['errors']}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生异常: {e}")
            try:
                await websocket.close()
            except:
                pass
            return False
    
    async def test_multiple_rounds(self, rounds=3):
        """测试多轮对话"""
        logger.info(f"🔄 开始测试多轮对话 ({rounds} 轮)...")
        
        success_count = 0
        
        for round_num in range(rounds):
            logger.info(f"🎮 第 {round_num + 1} 轮对话测试...")
            
            # 重新登录和连接
            if not await self.login_user():
                continue
            
            websocket = await self.connect_websocket()
            if not websocket:
                continue
            
            try:
                # 启动会话
                if not await self.start_session(websocket):
                    await websocket.close()
                    continue
                
                # 生成不同的测试音频
                durations = [2000, 3000, 1500]  # 不同时长
                audio_data = self.generate_test_audio_data(duration_ms=durations[round_num % len(durations)])
                
                # 发送音频
                if not await self.send_audio_chunks(websocket, audio_data):
                    await websocket.close()
                    continue
                
                # 等待响应
                results = await self.wait_for_response(websocket, timeout=20)
                
                # 结束会话
                await self.end_session(websocket)
                await websocket.close()
                
                # 检查结果
                if results["transcription"] and results["response_complete"]:
                    success_count += 1
                    logger.info(f"✅ 第 {round_num + 1} 轮测试成功")
                else:
                    logger.error(f"❌ 第 {round_num + 1} 轮测试失败")
                    
            except Exception as e:
                logger.error(f"❌ 第 {round_num + 1} 轮测试异常: {e}")
                try:
                    await websocket.close()
                except:
                    pass
        
        logger.info(f"📊 多轮对话测试结果: {success_count}/{rounds} 轮成功")
        return success_count == rounds

async def main():
    """主函数"""
    tester = FullVoicePipelineTester()
    
    logger.info("🚀 开始完整的语音管道集成测试...")
    
    # 测试单轮对话
    single_round_success = await tester.test_full_pipeline()
    
    # 测试多轮对话
    multi_round_success = await tester.test_multiple_rounds(3)
    
    if single_round_success and multi_round_success:
        logger.info("🎉 所有测试都成功完成！语音管道工作正常。")
    else:
        logger.error("❌ 部分测试失败。需要进一步检查。")
    
    logger.info("✅ 集成测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
