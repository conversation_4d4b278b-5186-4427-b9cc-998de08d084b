#!/usr/bin/env python3
"""
测试ASR绕过功能
验证跳过VAD和ASR服务后的完整流程
"""
import asyncio
import websockets
import json
import base64
import requests
import numpy as np
import wave
import logging
from pathlib import Path
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_asr_bypass():
    """测试ASR绕过功能"""
    logger.info("🚀 开始测试ASR绕过功能...")
    
    # 1. 检查后端健康状态
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            logger.info("✅ 后端健康检查通过")
            logger.info(f"   服务状态: {health_data.get('services', {})}")
        else:
            logger.error(f"❌ 后端健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 后端连接失败: {e}")
        return False
    
    # 2. 用户登录
    try:
        response = requests.post(
            "http://localhost:8000/auth/login",
            params={"username": "test_user", "password": "test_password"},
            timeout=10
        )
        if response.status_code != 200:
            logger.error(f"❌ 登录失败: {response.status_code}")
            return False
        
        user_data = response.json()
        user_id = user_data.get('user', {}).get('id', 1)
        logger.info(f"✅ 用户登录成功，ID: {user_id}")
    except Exception as e:
        logger.error(f"❌ 登录异常: {e}")
        return False
    
    # 3. WebSocket连接测试
    try:
        uri = f"ws://localhost:8000/ws/{user_id}"
        logger.info(f"🔌 连接WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 启动会话
            start_msg = {"type": "start_session", "npc_id": 1}
            await websocket.send(json.dumps(start_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动确认
            response = await asyncio.wait_for(websocket.recv(), timeout=10)
            response_data = json.loads(response)
            
            if response_data.get("type") == "session_started":
                session_id = response_data.get("session_id")
                logger.info(f"✅ 会话启动成功，ID: {session_id}")
            else:
                logger.error(f"❌ 会话启动失败: {response_data}")
                return False
            
            # 4. 生成测试音频
            logger.info("🎵 生成测试音频...")
            sample_rate = 16000
            duration = 2.0
            frequency = 440
            
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = (np.sin(2 * np.pi * frequency * t) * 32767).astype(np.int16)
            audio_bytes = audio_data.tobytes()
            
            logger.info(f"📊 测试音频: {len(audio_bytes)} bytes, {duration}秒, {frequency}Hz")
            
            # 5. 发送音频数据
            base64_audio = base64.b64encode(audio_bytes).decode('utf-8')
            
            audio_msg = {
                "type": "audio_chunk",
                "data": base64_audio,
                "metadata": {
                    "test": "asr_bypass",
                    "size": len(audio_bytes)
                }
            }
            
            await websocket.send(json.dumps(audio_msg))
            logger.info(f"📤 发送音频数据: {len(audio_bytes)} bytes")
            
            # 6. 等待完整的处理响应
            responses_received = []
            transcription_received = False
            llm_response_received = False
            tts_audio_received = False
            
            logger.info("⏳ 等待完整的ASR -> LLM -> TTS处理...")
            
            timeout_count = 0
            max_timeout = 30
            
            while timeout_count < max_timeout:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1)
                    response_data = json.loads(response)
                    responses_received.append(response_data)
                    
                    msg_type = response_data.get('type')
                    logger.info(f"📥 收到响应: {msg_type}")
                    
                    if msg_type == 'transcription':
                        text = response_data.get('text', '')
                        confidence = response_data.get('confidence', 0)
                        fallback = response_data.get('fallback', False)
                        logger.info(f"🎯 ASR转录结果: '{text}' (置信度: {confidence:.2f}, fallback: {fallback})")
                        transcription_received = True
                        
                    elif msg_type == 'response' or msg_type == 'llm_response':
                        text = response_data.get('text', '')
                        logger.info(f"🧠 LLM响应: '{text}'")
                        llm_response_received = True
                        
                    elif msg_type == 'audio_chunk':
                        audio_data_b64 = response_data.get('data', '')
                        logger.info(f"🔊 收到TTS音频响应: {len(audio_data_b64)} chars base64")
                        tts_audio_received = True
                        
                        # 保存音频响应用于验证
                        try:
                            audio_bytes = base64.b64decode(audio_data_b64)
                            response_audio_path = Path("test_asr_bypass_tts_output.wav")
                            with open(response_audio_path, 'wb') as f:
                                f.write(audio_bytes)
                            logger.info(f"💾 TTS音频已保存: {response_audio_path} ({len(audio_bytes)} bytes)")
                        except Exception as e:
                            logger.warning(f"⚠️ 保存TTS音频失败: {e}")
                        
                    elif msg_type == 'response_complete':
                        logger.info("✅ 完整响应处理完成")
                        break
                        
                    elif msg_type == 'error':
                        error_msg = response_data.get('message', '未知错误')
                        logger.error(f"❌ 处理错误: {error_msg}")
                        break
                    
                    else:
                        logger.info(f"📥 其他响应: {msg_type}")
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 5 == 0:
                        logger.info(f"⏰ 等待处理中... ({timeout_count}s)")
                    continue
            
            # 7. 统计和分析结果
            logger.info("=" * 60)
            logger.info("📊 ASR绕过测试结果:")
            logger.info(f"   发送音频大小: {len(audio_bytes)} bytes")
            logger.info(f"   收到响应数: {len(responses_received)}")
            logger.info(f"   处理时间: {timeout_count} 秒")
            
            # 检查各个环节
            logger.info("\n🔍 各环节验证:")
            logger.info(f"   ASR (语音识别): {'✅ 成功' if transcription_received else '❌ 失败'}")
            logger.info(f"   LLM (对话生成): {'✅ 成功' if llm_response_received else '❌ 失败'}")
            logger.info(f"   TTS (语音合成): {'✅ 成功' if tts_audio_received else '❌ 失败'}")
            
            # 统计响应类型
            response_types = {}
            for resp in responses_received:
                resp_type = resp.get('type', 'unknown')
                response_types[resp_type] = response_types.get(resp_type, 0) + 1
            
            logger.info("\n📈 响应类型统计:")
            for resp_type, count in response_types.items():
                logger.info(f"   {resp_type}: {count} 次")
            
            logger.info("=" * 60)
            
            # 判断测试是否成功
            success = transcription_received and llm_response_received and tts_audio_received
            
            if success:
                logger.info("🎉 ASR绕过测试全部成功！完整流程正常工作")
            else:
                logger.warning("⚠️ ASR绕过测试部分成功，某些环节可能有问题")
            
            return success
            
    except Exception as e:
        logger.error(f"❌ WebSocket测试异常: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🧪 ASR绕过功能测试")
    logger.info("🎯 目标: 验证跳过VAD和ASR服务后的完整流程")
    logger.info("=" * 60)
    
    success = await test_asr_bypass()
    
    logger.info("=" * 60)
    if success:
        logger.info("🎉 ASR绕过测试成功！")
        logger.info("💡 系统可以使用fallback ASR响应正常工作")
        logger.info("🔧 可以继续调试真实的ASR服务连接")
    else:
        logger.error("❌ ASR绕过测试失败！")
        logger.info("🔧 请检查后端服务和配置")

if __name__ == "__main__":
    asyncio.run(main())