#!/usr/bin/env python3
"""
完整语音聊天链路调试脚本
排查 ASR 转录和 LLM 响应问题
"""

import asyncio
import json
import base64
import numpy as np
import wave
import tempfile
import os
from datetime import datetime
import requests

# 导入后端服务
import sys
sys.path.append('backend')

from services.enhanced_asr_service import EnhancedASRService
from services.llm_service import LLMService
from services.tts_service import TTSService

async def test_asr_service():
    """测试 ASR 服务"""
    print("\n🔍 测试 ASR 服务...")
    
    # 创建测试音频数据（模拟录音）
    sample_rate = 16000
    duration = 3  # 3秒
    frequency = 440  # A4 音符
    
    # 生成正弦波测试音频
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(frequency * 2 * np.pi * t).astype(np.float32) * 0.3
    
    print(f"📊 测试音频: {len(audio_data)} 样本, {duration}秒, {sample_rate}Hz")
    
    # 保存测试音频为 WAV 文件
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
        with wave.open(temp_file.name, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            # 转换为 int16
            audio_int16 = (audio_data * 32767).astype(np.int16)
            wav_file.writeframes(audio_int16.tobytes())
        
        print(f"📁 测试音频文件: {temp_file.name}")
        
        # 测试增强 ASR 服务
        try:
            enhanced_asr = EnhancedASRService(
                qwen_timeout=5,
                gemini_timeout=10,
                gemini_first=True
            )
            
            # 测试连接
            connection_result = enhanced_asr.test_connection()
            print(f"🔌 ASR 服务连接状态: {connection_result}")
            
            # 测试转录
            transcription_result = enhanced_asr.transcribe(audio_data)
            print(f"📝 ASR 转录结果: {transcription_result}")
            
            if transcription_result.get("success"):
                print(f"✅ ASR 成功: '{transcription_result['text']}'")
                print(f"   置信度: {transcription_result.get('confidence', 'N/A')}")
                print(f"   提供商: {transcription_result.get('provider', 'N/A')}")
            else:
                print(f"❌ ASR 失败: {transcription_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ ASR 服务测试失败: {e}")
        
        # 清理临时文件
        try:
            os.unlink(temp_file.name)
        except:
            pass

async def test_llm_service():
    """测试 LLM 服务"""
    print("\n🔍 测试 LLM 服务...")
    
    # 测试问题列表
    test_questions = [
        "你好",
        "123加321等于多少",
        "123加321用四进制等于多少",
        "今天天气怎么样",
        "请简单介绍一下自己"
    ]
    
    try:
        llm_service = LLMService(
            os.getenv("VOLCANO_API_KEY"), 
            os.getenv("VOLCANO_ENDPOINT")
        )
        
        system_prompt = "你是一个友善的AI助手，请用自然的语调回复用户。对于数学问题，请详细计算并给出答案。"
        
        for question in test_questions:
            print(f"\n📤 测试问题: '{question}'")
            
            try:
                response = await llm_service.generate_response(
                    question,
                    [],  # 空的对话历史
                    system_prompt
                )
                
                if response.get("success"):
                    speak_content = response["speak_content"]
                    response_text = speak_content["text"]
                    emotion = speak_content.get("emotion", "neutral")
                    
                    print(f"✅ LLM 响应成功:")
                    print(f"   文本: '{response_text[:100]}{'...' if len(response_text) > 100 else ''}'")
                    print(f"   情感: {emotion}")
                    print(f"   完整长度: {len(response_text)} 字符")
                    
                    # 检查异常响应
                    if response_text.strip() == "..." or len(response_text.strip()) < 3:
                        print(f"⚠️ 检测到异常响应: '{response_text}'")
                        print(f"   原始响应: {response}")
                    
                else:
                    print(f"❌ LLM 响应失败: {response}")
                    
            except Exception as e:
                print(f"❌ LLM 请求异常: {e}")
                
    except Exception as e:
        print(f"❌ LLM 服务初始化失败: {e}")

async def test_tts_service():
    """测试 TTS 服务"""
    print("\n🔍 测试 TTS 服务...")
    
    test_texts = [
        "你好，我是AI助手",
        "123加321等于444",
        "...",  # 测试异常文本
        "",     # 测试空文本
        "这是一个很长的测试文本，用来检查TTS服务是否能够正确处理较长的文本内容，包括标点符号和数字123。"
    ]
    
    try:
        tts_service = TTSService(
            os.getenv("MINIMAX_API_KEY"), 
            os.getenv("MINIMAX_GROUP_ID")
        )
        
        for text in test_texts:
            print(f"\n📤 测试文本: '{text}'")
            
            try:
                tts_result = await tts_service.synthesize_speech(
                    text,
                    "neutral",
                    1.0
                )
                
                if tts_result.get("success"):
                    audio_data = tts_result["audio_data"]
                    print(f"✅ TTS 合成成功: {len(audio_data)} 字节")
                    
                    # 保存测试音频
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"tts_test_{timestamp}_{len(text)}.wav"
                    with open(filename, 'wb') as f:
                        f.write(audio_data)
                    print(f"📁 音频已保存: {filename}")
                    
                else:
                    print(f"❌ TTS 合成失败: {tts_result}")
                    
            except Exception as e:
                print(f"❌ TTS 请求异常: {e}")
                
    except Exception as e:
        print(f"❌ TTS 服务初始化失败: {e}")

async def test_backend_api():
    """测试后端 API 端点"""
    print("\n🔍 测试后端 API...")
    
    base_url = "http://localhost:8000"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ 后端健康检查通过")
            print(f"   服务状态: {health_data.get('services', {})}")
        else:
            print(f"❌ 后端健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
    
    # 测试 ASR 端点
    try:
        # 创建测试音频文件
        sample_rate = 16000
        duration = 2
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(440 * 2 * np.pi * t).astype(np.float32) * 0.3
        
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            with wave.open(temp_file.name, 'wb') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(sample_rate)
                audio_int16 = (audio_data * 32767).astype(np.int16)
                wav_file.writeframes(audio_int16.tobytes())
            
            # 测试 ASR API
            with open(temp_file.name, 'rb') as f:
                files = {'file': ('test.wav', f, 'audio/wav')}
                response = requests.post(f"{base_url}/api/asr/transcribe", files=files, timeout=30)
                
                if response.status_code == 200:
                    asr_result = response.json()
                    print("✅ ASR API 测试通过")
                    print(f"   转录结果: {asr_result}")
                else:
                    print(f"❌ ASR API 测试失败: {response.status_code}")
                    print(f"   响应: {response.text}")
            
            # 清理临时文件
            try:
                os.unlink(temp_file.name)
            except:
                pass
                
    except Exception as e:
        print(f"❌ ASR API 测试异常: {e}")

def analyze_audio_data(audio_data_b64: str):
    """分析音频数据"""
    print("\n🔍 分析音频数据...")
    
    try:
        # 解码 base64 音频数据
        audio_bytes = base64.b64decode(audio_data_b64)
        print(f"📊 音频数据大小: {len(audio_bytes)} 字节")
        
        # 尝试解析为不同格式
        formats_to_try = [
            ("int16", np.int16, 32768.0),
            ("uint8", np.uint8, 128.0),
            ("float32", np.float32, 1.0)
        ]
        
        for format_name, dtype, scale in formats_to_try:
            try:
                if format_name == "uint8":
                    audio_array = np.frombuffer(audio_bytes, dtype=dtype).astype(np.float32)
                    audio_array = (audio_array - 128.0) / 128.0
                else:
                    audio_array = np.frombuffer(audio_bytes, dtype=dtype).astype(np.float32) / scale
                
                print(f"✅ {format_name} 解析成功:")
                print(f"   样本数: {len(audio_array)}")
                print(f"   时长: {len(audio_array) / 16000:.2f} 秒 (假设16kHz)")
                print(f"   幅度范围: [{audio_array.min():.3f}, {audio_array.max():.3f}]")
                print(f"   RMS: {np.sqrt(np.mean(audio_array**2)):.3f}")
                
                # 检查是否为静音
                if np.max(np.abs(audio_array)) < 0.01:
                    print("⚠️ 检测到静音或极低音量")
                
                break
                
            except Exception as e:
                print(f"❌ {format_name} 解析失败: {e}")
        
    except Exception as e:
        print(f"❌ 音频数据分析失败: {e}")

async def main():
    """主测试函数"""
    print("🔍 开始完整语音聊天链路调试")
    print("=" * 60)
    
    # 检查环境变量
    print("\n🔍 检查环境变量...")
    env_vars = [
        "VOLCANO_API_KEY",
        "VOLCANO_ENDPOINT", 
        "MINIMAX_API_KEY",
        "MINIMAX_GROUP_ID"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * (len(value) - 4) + value[-4:] if len(value) > 4 else '***'}")
        else:
            print(f"❌ {var}: 未设置")
    
    # 运行各项测试
    await test_backend_api()
    await test_asr_service()
    await test_llm_service()
    await test_tts_service()
    
    print("\n" + "=" * 60)
    print("🎉 调试完成")
    
    # 提供调试建议
    print("\n💡 调试建议:")
    print("1. 如果 ASR 转录不准确，检查音频质量和格式")
    print("2. 如果 LLM 返回 '...'，检查 API 密钥和网络连接")
    print("3. 如果 TTS 失败，检查文本内容和服务配置")
    print("4. 查看后端日志获取详细错误信息")

if __name__ == "__main__":
    asyncio.run(main())