#!/usr/bin/env python3
"""
独立的TTS合成测试脚本
测试Volcano Engine TTS API的语音合成功能
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('backend/.env')

# 添加backend目录到路径
sys.path.append('backend')

from services.tts_service import TTSService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tts_service():
    """测试TTS服务"""
    logger.info("=" * 60)
    logger.info("TTS Service Test")
    logger.info("=" * 60)

    # 1. 初始化TTS服务
    try:
        logger.info("Initializing TTS service...")
        api_key = os.getenv("MINIMAX_API_KEY")
        group_id = os.getenv("MINIMAX_GROUP_ID")

        if not api_key or not group_id:
            logger.error("❌ Missing MINIMAX_API_KEY or MINIMAX_GROUP_ID")
            return False

        tts_service = TTSService(api_key, group_id)
        logger.info("✅ TTS service initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize TTS service: {e}")
        return False
    
    # 2. 测试语音合成
    test_texts = [
        "你好，欢迎使用语音助手！",
        "1加1等于2",
        "今天天气很好，适合出门散步。",
        "人工智能技术正在快速发展。",
        "感谢您的使用，再见！"
    ]
    
    # 创建输出目录
    output_dir = Path("tts_test_output")
    output_dir.mkdir(exist_ok=True)
    
    for i, text in enumerate(test_texts, 1):
        logger.info(f"\n--- Test {i}/5 ---")
        logger.info(f"🔊 Text: {text}")
        
        try:
            # 调用TTS服务 - 使用正确的方法
            import asyncio
            result = asyncio.run(tts_service.synthesize_speech(
                text=text,
                emotion="neutral",
                speed=1.0,
                output_file=None
            ))

            if result and result.get("success") and result.get("audio_data"):
                audio_data = result["audio_data"]
                # 保存音频文件
                output_file = output_dir / f"tts_test_{i}.wav"
                with open(output_file, 'wb') as f:
                    f.write(audio_data)

                logger.info(f"✅ Audio generated: {len(audio_data)} bytes")
                logger.info(f"✅ Saved to: {output_file}")
            else:
                logger.error(f"❌ TTS synthesis failed: {result}")
                return False
                
        except Exception as e:
            logger.error(f"❌ TTS synthesis failed: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    logger.info("\n" + "=" * 60)
    logger.info("✅ All TTS tests passed successfully!")
    logger.info(f"✅ Audio files saved in: {output_dir.absolute()}")
    logger.info("=" * 60)
    return True

def test_tts_parameters():
    """测试TTS参数配置"""
    logger.info("\n" + "=" * 60)
    logger.info("TTS Parameters Test")
    logger.info("=" * 60)
    
    try:
        api_key = os.getenv("MINIMAX_API_KEY")
        group_id = os.getenv("MINIMAX_GROUP_ID")
        tts_service = TTSService(api_key, group_id)
        test_text = "这是一个参数测试"
        
        # 测试不同的语音参数
        test_configs = [
            {"voice_type": "BV700_streaming", "emotion": "happy"},
            {"voice_type": "BV700_streaming", "emotion": "sad"},
            {"voice_type": "BV700_streaming", "speed": 1.2},
            {"voice_type": "BV700_streaming", "speed": 0.8},
        ]
        
        output_dir = Path("tts_test_output")
        output_dir.mkdir(exist_ok=True)
        
        for i, config in enumerate(test_configs, 1):
            logger.info(f"\n--- Parameter Test {i} ---")
            logger.info(f"🔊 Text: {test_text}")
            logger.info(f"🎛️ Config: {config}")
            
            # 使用配置参数进行TTS合成
            import asyncio
            result = asyncio.run(tts_service.synthesize_speech(
                text=test_text,
                emotion=config.get("emotion", "neutral"),
                speed=config.get("speed", 1.0),
                voice_id=config.get("voice_type"),
                output_file=None
            ))

            if result and result.get("success") and result.get("audio_data"):
                audio_data = result["audio_data"]
                output_file = output_dir / f"tts_param_test_{i}.wav"
                with open(output_file, 'wb') as f:
                    f.write(audio_data)

                logger.info(f"✅ Audio generated: {len(audio_data)} bytes")
                logger.info(f"✅ Saved to: {output_file}")
            else:
                logger.error(f"❌ Failed with config: {config}, result: {result}")
                return False
        
        logger.info("\n✅ TTS parameters test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ TTS parameters test failed: {e}")
        return False

def test_long_text():
    """测试长文本合成"""
    logger.info("\n" + "=" * 60)
    logger.info("TTS Long Text Test")
    logger.info("=" * 60)
    
    try:
        api_key = os.getenv("MINIMAX_API_KEY")
        group_id = os.getenv("MINIMAX_GROUP_ID")
        tts_service = TTSService(api_key, group_id)

        long_text = """
        人工智能是计算机科学的一个分支，它企图了解智能的实质，
        并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
        人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。
        """

        logger.info(f"🔊 Long text ({len(long_text)} characters)")

        import asyncio
        result = asyncio.run(tts_service.synthesize_speech(
            text=long_text.strip(),
            emotion="neutral",
            speed=1.0,
            output_file=None
        ))

        if result and result.get("success") and result.get("audio_data"):
            audio_data = result["audio_data"]
            output_dir = Path("tts_test_output")
            output_dir.mkdir(exist_ok=True)
            output_file = output_dir / "tts_long_text.wav"

            with open(output_file, 'wb') as f:
                f.write(audio_data)

            logger.info(f"✅ Long text audio generated: {len(audio_data)} bytes")
            logger.info(f"✅ Saved to: {output_file}")
            return True
        else:
            logger.error(f"❌ Long text synthesis failed: {result}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Long text test failed: {e}")
        return False

def main():
    """主函数"""
    logger.info("Starting TTS standalone tests...")
    
    # 检查环境变量
    required_env_vars = [
        "MINIMAX_API_KEY",
        "MINIMAX_GROUP_ID"
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        logger.error("Please set these variables in your .env file")
        return False
    
    # 运行测试
    success = True
    
    # 1. 基本TTS测试
    if not test_tts_service():
        success = False
    
    # 2. TTS参数测试
    if not test_tts_parameters():
        success = False
    
    # 3. 长文本测试
    if not test_long_text():
        success = False
    
    if success:
        logger.info("\n🎉 All TTS tests completed successfully!")
        logger.info("🎵 You can now play the generated audio files to verify quality")
    else:
        logger.error("\n❌ Some TTS tests failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
