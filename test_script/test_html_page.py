#!/usr/bin/env python3
"""
HTML页面自动化测试脚本
使用Selenium来测试HTML页面的WebSocket功能
"""
import time
import logging
import asyncio
import websockets
import json
import base64
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_html_page_websocket():
    """测试HTML页面的WebSocket功能"""
    logger.info("🌐 开始HTML页面WebSocket测试...")
    
    # 模拟HTML页面的WebSocket连接
    try:
        uri = "ws://localhost:8000/ws/1"
        logger.info(f"🔌 连接WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 1. 启动会话（模拟HTML页面的行为）
            start_session_msg = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_session_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动响应
            session_started = False
            for attempt in range(3):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    
                    logger.info(f"📥 收到响应: {response_data.get('type')} - {response_data}")
                    
                    if response_data.get("type") == "session_started":
                        session_id = response_data.get("session_id")
                        npc_id = response_data.get("npc_id")
                        logger.info(f"✅ 会话启动成功，ID: {session_id}, NPC: {npc_id}")
                        session_started = True
                        break
                    elif response_data.get("type") == "connection_established":
                        logger.info("✅ 连接已建立，继续等待会话启动...")
                        continue
                        
                except asyncio.TimeoutError:
                    logger.error(f"❌ 等待响应超时 (尝试 {attempt + 1}/3)")
                    if attempt == 2:
                        return False
            
            if not session_started:
                logger.error("❌ 会话启动失败")
                return False
            
            # 2. 发送音频数据（模拟录音）
            audio_dir = Path("backend/audio_recordings")
            audio_files = list(audio_dir.glob("*.wav"))
            
            if audio_files:
                # 使用一个较大的音频文件
                test_file = max(audio_files, key=lambda f: f.stat().st_size)
                logger.info(f"📁 使用音频文件: {test_file.name} ({test_file.stat().st_size} bytes)")
                
                # 读取音频文件
                with open(test_file, 'rb') as f:
                    audio_data = f.read()
                
                # 转换为base64（模拟HTML页面的处理）
                base64_audio = base64.b64encode(audio_data).decode('utf-8')
                
                # 发送音频消息
                audio_msg = {
                    "type": "audio_chunk",
                    "data": base64_audio
                }
                
                await websocket.send(json.dumps(audio_msg))
                logger.info(f"📤 发送音频数据: {len(audio_data)} bytes (base64: {len(base64_audio)} chars)")
                
                # 等待处理响应
                responses_received = []
                timeout_count = 0
                max_timeout = 15
                
                while timeout_count < max_timeout:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=1)
                        response_data = json.loads(response)
                        responses_received.append(response_data)
                        
                        msg_type = response_data.get('type')
                        logger.info(f"📥 收到响应: {msg_type}")
                        
                        if msg_type == 'transcription':
                            text = response_data.get('text', '')
                            confidence = response_data.get('confidence', 0)
                            logger.info(f"🎯 转录结果: '{text}' (置信度: {confidence:.2f})")
                        elif msg_type == 'audio_chunk':
                            logger.info("🔊 收到AI音频响应")
                        elif msg_type == 'response_complete':
                            logger.info("✅ 响应完成")
                            break
                        elif msg_type == 'error':
                            error_msg = response_data.get('message', '未知错误')
                            logger.error(f"❌ 处理错误: {error_msg}")
                            break
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        if timeout_count % 5 == 0:
                            logger.info(f"⏰ 等待响应中... ({timeout_count}s)")
                        continue
                
                # 统计结果
                logger.info("=" * 50)
                logger.info("📊 HTML页面WebSocket测试结果:")
                logger.info(f"   发送音频大小: {len(audio_data)} bytes")
                logger.info(f"   收到响应数: {len(responses_received)}")
                
                # 检查响应类型
                transcriptions = [r for r in responses_received if r.get('type') == 'transcription']
                audio_outputs = [r for r in responses_received if r.get('type') == 'audio_chunk']
                
                if transcriptions:
                    logger.info("✅ 语音识别成功")
                else:
                    logger.warning("⚠️ 没有收到转录结果")
                
                if audio_outputs:
                    logger.info("✅ 音频响应成功")
                else:
                    logger.warning("⚠️ 没有收到音频响应")
                
                # 统计响应类型
                response_types = {}
                for resp in responses_received:
                    resp_type = resp.get('type', 'unknown')
                    response_types[resp_type] = response_types.get(resp_type, 0) + 1
                
                for resp_type, count in response_types.items():
                    logger.info(f"   {resp_type}: {count} 次")
                
                logger.info("=" * 50)
                
                return len(responses_received) > 0
            
            else:
                logger.warning("⚠️ 没有找到音频文件，跳过音频测试")
                return True
            
    except Exception as e:
        logger.error(f"❌ WebSocket测试异常: {e}")
        return False

def test_html_page_access():
    """测试HTML页面访问"""
    logger.info("🌐 测试HTML页面访问...")
    
    import requests
    try:
        response = requests.get("http://localhost:8080/test_page.html", timeout=5)
        if response.status_code == 200:
            logger.info("✅ HTML页面访问成功")
            logger.info(f"   页面大小: {len(response.text)} 字符")
            return True
        else:
            logger.error(f"❌ HTML页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ HTML页面访问异常: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 HTML页面测试开始...")
    logger.info("=" * 60)
    
    # 1. 测试HTML页面访问
    html_access = test_html_page_access()
    
    # 2. 测试WebSocket功能
    websocket_test = await test_html_page_websocket()
    
    logger.info("=" * 60)
    logger.info("📊 测试结果总结:")
    logger.info(f"   HTML页面访问: {'✅ 成功' if html_access else '❌ 失败'}")
    logger.info(f"   WebSocket功能: {'✅ 成功' if websocket_test else '❌ 失败'}")
    
    if html_access and websocket_test:
        logger.info("🎉 HTML页面测试全部成功！")
        logger.info("💡 可以在浏览器中打开 http://localhost:8080/test_page.html 进行手动测试")
    else:
        logger.error("❌ HTML页面测试失败！")

if __name__ == "__main__":
    asyncio.run(main())