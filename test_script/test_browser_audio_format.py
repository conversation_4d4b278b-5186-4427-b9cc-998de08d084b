#!/usr/bin/env python3
"""
测试浏览器音频格式
"""

import requests
import numpy as np
import io
import wave

def create_webm_like_audio():
    """创建类似WebM格式的音频数据"""
    # 生成一个更复杂的音频信号，模拟真实语音
    sample_rate = 16000
    duration = 2.0
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 模拟语音：基频 + 谐波 + 包络
    fundamental = 150  # 基频
    signal = np.sin(2 * np.pi * fundamental * t) * 0.4
    signal += np.sin(2 * np.pi * fundamental * 2 * t) * 0.2
    signal += np.sin(2 * np.pi * fundamental * 3 * t) * 0.1
    
    # 添加语音特征的频率成分
    signal += np.sin(2 * np.pi * 800 * t) * 0.15  # 元音共振峰
    signal += np.sin(2 * np.pi * 1200 * t) * 0.1  # 另一个共振峰
    
    # 添加包络（模拟语音的音量变化）
    envelope = np.ones_like(t)
    # 添加一些音量变化
    envelope[:len(t)//4] *= np.linspace(0, 1, len(t)//4)  # 渐入
    envelope[-len(t)//4:] *= np.linspace(1, 0, len(t)//4)  # 渐出
    
    signal *= envelope
    
    # 添加轻微噪声
    signal += np.random.normal(0, 0.02, len(signal))
    
    # 转换为16位整数
    audio_int16 = (signal * 32767).astype(np.int16)
    
    return audio_int16, sample_rate

def test_browser_audio_format():
    """测试浏览器音频格式"""
    print("🎯 测试浏览器音频格式...")
    
    # 1. 创建模拟语音音频
    audio_data, sample_rate = create_webm_like_audio()
    print(f"📊 生成音频: {len(audio_data)} 样本, {len(audio_data)/sample_rate:.2f}秒")
    
    # 2. 转换为WAV字节流
    wav_buffer = io.BytesIO()
    with wave.open(wav_buffer, 'wb') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())
    
    audio_bytes = wav_buffer.getvalue()
    print(f"📊 WAV数据: {len(audio_bytes)} bytes")
    
    # 3. 测试不同的content-type
    test_cases = [
        ('audio/wav', 'recording.wav'),
        ('audio/webm', 'recording.webm'),
        ('audio/webm;codecs=opus', 'recording.webm'),
        ('audio/ogg', 'recording.ogg'),
    ]
    
    for content_type, filename in test_cases:
        print(f"\n{'='*50}")
        print(f"测试格式: {content_type}")
        print(f"文件名: {filename}")
        print(f"{'='*50}")
        
        try:
            files = {'file': (filename, audio_bytes, content_type)}
            data = {'user_id': '1', 'npc_id': '1'}
            
            print("📤 发送请求...")
            response = requests.post('http://localhost:8000/process-audio', files=files, data=data)
            
            print(f"📥 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 请求成功!")
                print(f"📝 ASR结果: '{result.get('transcription', '')}'")
                print(f"🔧 ASR提供商: {result.get('asr_provider', 'N/A')}")
                print(f"🎯 置信度: {result.get('asr_confidence', 0.0):.2f}")
                print(f"🧠 LLM回复: '{result.get('response_text', '')[:50]}...'")
            else:
                print("❌ 请求失败!")
                print(f"错误: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def test_health_check():
    """测试健康检查"""
    try:
        print("🔌 测试后端健康状态...")
        response = requests.get('http://localhost:8000/health')
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 后端健康检查成功")
            
            # 检查enhanced_asr状态
            enhanced_asr = data.get('services', {}).get('enhanced_asr', {})
            if isinstance(enhanced_asr, dict):
                print(f"🔧 Enhanced ASR状态:")
                print(f"   Qwen: {'✅' if enhanced_asr.get('qwen') else '❌'}")
                print(f"   Gemini: {'✅' if enhanced_asr.get('gemini') else '❌'}")
                print(f"   可用服务: {enhanced_asr.get('available', [])}")
            else:
                print(f"🔧 Enhanced ASR: {enhanced_asr}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

if __name__ == "__main__":
    test_health_check()
    print()
    test_browser_audio_format()