#!/usr/bin/env python3
"""
基础WebSocket测试 - 验证连接和基本消息传递
"""
import asyncio
import websockets
import json
import requests
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_basic_websocket():
    """基础WebSocket连接测试"""
    backend_url = "http://localhost:8000"
    websocket_url = "ws://localhost:8000"
    
    logger.info("🔌 开始基础WebSocket测试...")
    
    # 1. 检查后端状态
    try:
        response = requests.get(f"{backend_url}/health", timeout=5)
        if response.status_code == 200:
            logger.info("✅ 后端服务正常")
        else:
            logger.error(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 后端连接失败: {e}")
        return False
    
    # 2. 用户登录
    try:
        response = requests.post(
            f"{backend_url}/auth/login",
            params={"username": "test_user", "password": "test_password"},
            timeout=10
        )
        if response.status_code != 200:
            logger.error(f"❌ 登录失败: {response.status_code}")
            return False
        
        user_data = response.json()
        user_id = user_data.get('user', {}).get('id', 1)
        logger.info(f"✅ 用户登录成功，ID: {user_id}")
    except Exception as e:
        logger.error(f"❌ 登录异常: {e}")
        return False
    
    # 3. WebSocket连接测试
    try:
        uri = f"{websocket_url}/ws/{user_id}"
        logger.info(f"🔌 连接WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 测试会话启动
            start_msg = {"type": "start_session", "npc_id": 1}
            await websocket.send(json.dumps(start_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待响应 - 可能需要接收多个消息
            session_started = False
            session_id = None
            
            for attempt in range(3):  # 尝试接收3次消息
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    
                    logger.info(f"📥 收到响应 {attempt+1}: {response_data}")
                    
                    if response_data.get("type") == "session_started":
                        session_id = response_data.get("session_id")
                        logger.info(f"✅ 会话启动成功，ID: {session_id}")
                        session_started = True
                        break
                    elif response_data.get("type") == "connection_established":
                        logger.info("✅ 连接已建立，继续等待会话启动...")
                        continue
                    else:
                        logger.warning(f"⚠️ 收到意外响应: {response_data}")
                        
                except asyncio.TimeoutError:
                    logger.error(f"❌ 等待响应超时 (尝试 {attempt + 1}/3)")
                    if attempt == 2:
                        return False
            
            if session_started:
                # 测试结束会话
                end_msg = {"type": "end_session"}
                await websocket.send(json.dumps(end_msg))
                logger.info("📤 发送结束会话请求")
                
                # 等待结束响应
                try:
                    end_response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    end_data = json.loads(end_response)
                    logger.info(f"📥 结束响应: {end_data}")
                    
                    if end_data.get("type") == "session_ended":
                        logger.info("✅ 会话正常结束")
                        return True
                    else:
                        logger.warning(f"⚠️ 意外的结束响应: {end_data}")
                        return True  # 仍然认为测试成功
                except asyncio.TimeoutError:
                    logger.warning("⚠️ 等待结束响应超时，但会话启动成功")
                    return True
                        
            else:
                logger.error("❌ 未收到会话启动确认")
                return False
                    
    except asyncio.TimeoutError:
        logger.error("❌ 等待响应超时")
        return False
    except Exception as e:
        logger.error(f"❌ WebSocket测试异常: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 基础WebSocket测试开始...")
    
    success = await test_basic_websocket()
    
    if success:
        logger.info("🎉 基础WebSocket测试成功！")
        logger.info("💡 WebSocket连接和基本消息传递正常")
    else:
        logger.error("❌ 基础WebSocket测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
