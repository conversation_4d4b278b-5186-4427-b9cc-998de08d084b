#!/usr/bin/env python3
"""
测试修复后的backend主链路
包括完整的语音对话流程：音频 -> ASR -> LLM -> TTS -> 音频输出
"""

import asyncio
import websockets
import json
import base64
import numpy as np
import logging
import wave
import os
from pathlib import Path
import requests
import time

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 服务器配置
SERVER_HOST = "localhost"
SERVER_PORT = 8000
WS_URL = f"ws://{SERVER_HOST}:{SERVER_PORT}/ws/test_user"
HTTP_URL = f"http://{SERVER_HOST}:{SERVER_PORT}"

def create_test_audio(duration_seconds=2, sample_rate=16000, frequency=440):
    """创建测试音频数据"""
    t = np.linspace(0, duration_seconds, int(sample_rate * duration_seconds), False)
    # 创建正弦波 + 一些噪声来模拟语音
    audio = 0.3 * np.sin(2 * np.pi * frequency * t) + 0.1 * np.random.normal(0, 1, len(t))
    # 转换为int16格式
    audio_int16 = (audio * 32767).astype(np.int16)
    return audio_int16

def save_audio_file(audio_data, filename, sample_rate=16000):
    """保存音频文件"""
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    filepath = output_dir / filename
    with wave.open(str(filepath), 'wb') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())
    
    logger.info(f"💾 Saved audio to {filepath}")
    return filepath

async def test_health_check():
    """测试健康检查端点"""
    logger.info("=" * 60)
    logger.info("Health Check Test")
    logger.info("=" * 60)
    
    try:
        response = requests.get(f"{HTTP_URL}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            logger.info("✅ Health check passed")
            logger.info(f"Services status: {health_data['services']}")
            return True
        else:
            logger.error(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return False

async def test_process_audio_endpoint():
    """测试音频处理端点"""
    logger.info("\n" + "=" * 60)
    logger.info("Process Audio Endpoint Test")
    logger.info("=" * 60)
    
    try:
        # 创建测试音频
        test_audio = create_test_audio(duration_seconds=3)
        audio_file = save_audio_file(test_audio, "test_input.wav")
        
        # 发送音频文件
        with open(audio_file, 'rb') as f:
            files = {'file': ('test_audio.wav', f, 'audio/wav')}
            data = {'user_id': 1, 'npc_id': 1}
            
            response = requests.post(f"{HTTP_URL}/process-audio", files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ Audio processing successful")
            logger.info(f"📝 Transcription: {result.get('transcription', 'N/A')}")
            logger.info(f"💬 Response: {result.get('response_text', 'N/A')}")
            logger.info(f"😊 Emotion: {result.get('emotion', 'N/A')}")
            logger.info(f"⚡ Speed: {result.get('speed', 'N/A')}")
            logger.info(f"🔊 Audio size: {result.get('audio_size', 0)} bytes")
            logger.info(f"🎭 Mock ASR: {result.get('mock_asr', False)}")
            logger.info(f"🎭 Mock LLM: {result.get('mock_llm', False)}")
            return True
        else:
            logger.error(f"❌ Audio processing failed: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Audio processing test error: {e}")
        return False

async def test_websocket_pipeline():
    """测试WebSocket完整语音对话流程"""
    logger.info("\n" + "=" * 60)
    logger.info("WebSocket Pipeline Test")
    logger.info("=" * 60)

    try:
        # 增加连接超时时间和添加更多连接参数
        logger.info(f"🔗 Connecting to WebSocket: {WS_URL}")

        # 设置更长的超时时间和连接参数
        connect_timeout = 30  # 30秒连接超时
        ping_interval = 20    # 20秒ping间隔
        ping_timeout = 10     # 10秒ping超时

        async with websockets.connect(
            WS_URL,
            open_timeout=connect_timeout,
            ping_interval=ping_interval,
            ping_timeout=ping_timeout,
            close_timeout=10
        ) as websocket:
            logger.info("✅ WebSocket connected")
            
            # 1. 启动会话
            logger.info("🚀 Starting session...")
            await websocket.send(json.dumps({
                "type": "start_session",
                "npc_id": 1
            }))
            
            # 等待会话启动确认
            response = await websocket.recv()
            session_data = json.loads(response)
            logger.info(f"📋 Session response: {session_data}")
            
            if session_data.get("type") != "session_started":
                logger.error("❌ Session start failed")
                return False
            
            logger.info("✅ Session started successfully")
            
            # 2. 发送音频数据
            logger.info("🎤 Sending audio data...")
            test_audio = create_test_audio(duration_seconds=2)
            audio_bytes = test_audio.tobytes()
            audio_b64 = base64.b64encode(audio_bytes).decode()
            
            await websocket.send(json.dumps({
                "type": "audio_chunk",
                "data": audio_b64
            }))
            
            logger.info(f"📤 Sent audio chunk: {len(audio_bytes)} bytes")
            
            # 3. 接收响应
            logger.info("👂 Waiting for responses...")
            responses_received = []
            timeout_count = 0
            max_timeout = 30  # 30秒超时
            
            while timeout_count < max_timeout:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    data = json.loads(response)
                    responses_received.append(data)
                    
                    response_type = data.get("type")
                    logger.info(f"📥 Received: {response_type}")
                    
                    if response_type == "transcription":
                        logger.info(f"📝 Transcription: {data.get('text', 'N/A')}")
                        logger.info(f"🎯 Confidence: {data.get('confidence', 0):.3f}")
                    
                    elif response_type == "text_response":
                        logger.info(f"💬 AI Response: {data.get('text', 'N/A')}")
                        logger.info(f"😊 Emotion: {data.get('emotion', 'N/A')}")
                        logger.info(f"⚡ Speed: {data.get('speed', 'N/A')}")
                    
                    elif response_type == "audio_response":
                        audio_data = base64.b64decode(data.get('data', ''))
                        logger.info(f"🔊 Audio Response: {len(audio_data)} bytes")
                        
                        # 保存音频响应
                        if audio_data:
                            audio_array = np.frombuffer(audio_data, dtype=np.uint8)
                            output_file = save_audio_file(audio_array, "websocket_response.wav")
                            logger.info(f"💾 Saved response audio to: {output_file}")
                    
                    elif response_type == "response_complete":
                        logger.info("✅ Response complete")
                        break
                    
                    elif response_type == "error":
                        logger.error(f"❌ Error: {data.get('message', 'Unknown error')}")
                        break
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 5 == 0:
                        logger.info(f"⏰ Waiting... ({timeout_count}s)")
                    continue
                except Exception as e:
                    logger.error(f"❌ Error receiving response: {e}")
                    break
            
            # 4. 结束会话
            logger.info("🏁 Ending session...")
            await websocket.send(json.dumps({
                "type": "end_session"
            }))
            
            # 等待结束确认
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                end_data = json.loads(response)
                logger.info(f"📋 End session response: {end_data}")
            except asyncio.TimeoutError:
                logger.warning("⏰ End session timeout")
            
            logger.info(f"📊 Total responses received: {len(responses_received)}")
            
            # 检查是否收到了预期的响应
            response_types = [r.get("type") for r in responses_received]
            expected_types = ["transcription", "text_response", "audio_response"]
            
            success = all(rt in response_types for rt in expected_types)
            if success:
                logger.info("🎉 WebSocket pipeline test successful!")
            else:
                logger.warning(f"⚠️ Missing some expected responses. Got: {response_types}")
            
            return success
            
    except websockets.exceptions.InvalidURI as e:
        logger.error(f"❌ Invalid WebSocket URI: {WS_URL}")
        logger.error(f"Error: {e}")
        return False
    except websockets.exceptions.ConnectionClosedError as e:
        logger.error(f"❌ WebSocket connection closed unexpectedly: {e}")
        return False
    except TimeoutError as e:
        logger.error(f"❌ WebSocket connection timeout: {e}")
        logger.error(f"🔍 Check if server is running on {SERVER_HOST}:{SERVER_PORT}")
        logger.error(f"🔍 Try: curl {HTTP_URL}/health")
        return False
    except ConnectionRefusedError as e:
        logger.error(f"❌ Connection refused to {WS_URL}")
        logger.error(f"🔍 Server might not be running or WebSocket endpoint not available")
        return False
    except Exception as e:
        logger.error(f"❌ WebSocket test error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def test_websocket_connection():
    """测试基本WebSocket连接"""
    logger.info("\n" + "=" * 60)
    logger.info("WebSocket Connection Test")
    logger.info("=" * 60)

    try:
        logger.info(f"🔗 Testing basic WebSocket connection to: {WS_URL}")

        async with websockets.connect(
            WS_URL,
            open_timeout=15,
            ping_interval=None,  # 禁用ping以简化测试
            ping_timeout=None,
            close_timeout=5
        ) as websocket:
            logger.info("✅ WebSocket connection established")

            # 发送简单的ping消息
            test_message = {"type": "ping", "data": "test"}
            await websocket.send(json.dumps(test_message))
            logger.info("📤 Sent ping message")

            # 等待响应（短超时）
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"📥 Received response: {response[:100]}...")
                logger.info("✅ Basic WebSocket communication successful")
                return True
            except asyncio.TimeoutError:
                logger.warning("⏰ No response received, but connection was established")
                return True  # 连接成功就算通过

    except Exception as e:
        logger.error(f"❌ WebSocket connection test failed: {e}")
        logger.error(f"🔍 Make sure server is running: curl {HTTP_URL}/health")
        return False

async def test_text_message():
    """测试文本消息处理"""
    logger.info("\n" + "=" * 60)
    logger.info("Text Message Test")
    logger.info("=" * 60)

    try:
        # 使用相同的连接参数
        async with websockets.connect(
            WS_URL,
            open_timeout=30,
            ping_interval=20,
            ping_timeout=10,
            close_timeout=10
        ) as websocket:
            logger.info("✅ WebSocket connected")
            
            # 启动会话
            await websocket.send(json.dumps({
                "type": "start_session",
                "npc_id": 1
            }))
            
            # 等待会话启动
            response = await websocket.recv()
            session_data = json.loads(response)
            
            if session_data.get("type") != "session_started":
                logger.error("❌ Session start failed")
                return False
            
            # 发送文本消息
            test_message = "你好，请介绍一下自己"
            logger.info(f"📝 Sending text: {test_message}")
            
            await websocket.send(json.dumps({
                "type": "text",
                "text": test_message
            }))
            
            # 接收响应
            response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
            data = json.loads(response)
            
            if data.get("type") == "text_response":
                logger.info(f"💬 AI Response: {data.get('text', 'N/A')}")
                logger.info("✅ Text message test successful!")
                return True
            else:
                logger.error(f"❌ Unexpected response: {data}")
                return False
                
    except Exception as e:
        logger.error(f"❌ Text message test error: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 Starting Backend Pipeline Tests...")
    
    # 等待服务器启动
    logger.info("⏰ Waiting for server to be ready...")
    await asyncio.sleep(2)
    
    success_count = 0
    total_tests = 5

    # 1. 健康检查测试
    if await test_health_check():
        success_count += 1

    # 2. 音频处理端点测试
    if await test_process_audio_endpoint():
        success_count += 1

    # 3. WebSocket连接测试
    if await test_websocket_connection():
        success_count += 1

    # 4. 文本消息测试
    if await test_text_message():
        success_count += 1

    # 5. WebSocket完整流程测试
    if await test_websocket_pipeline():
        success_count += 1
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"✅ Passed: {success_count}/{total_tests}")
    logger.info(f"❌ Failed: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 All tests passed! Backend pipeline is working correctly!")
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
