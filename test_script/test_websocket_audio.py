import asyncio
import websockets
import json
import base64
import time

async def test_websocket_audio():
    uri = "ws://localhost:8000/ws/999"  # 使用用户ID 999进行测试
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ 连接到WebSocket服务器")
            
            # 1. 开始会话
            start_session_msg = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_session_msg))
            print("📤 发送开始会话消息")
            
            # 等待服务器响应
            response = await websocket.recv()
            response_data = json.loads(response)
            print(f"📥 收到响应: {response_data}")
            
            if response_data.get("type") == "session_started":
                print("✅ 会话已成功启动")
                
                # 2. 发送音频数据块（模拟录音）
                print("📤 开始发送音频数据...")
                
                # 发送多个音频块来模拟录音
                for i in range(10):
                    # 生成一些测试音频数据（模拟16位PCM音频）
                    audio_chunk = bytes([i % 256 for i in range(1600)])  # 1600字节的音频数据
                    
                    audio_msg = {
                        "type": "audio_chunk",
                        "data": base64.b64encode(audio_chunk).decode('utf-8')
                    }
                    await websocket.send(json.dumps(audio_msg))
                    print(f"📤 发送音频块 {i+1}/10")
                    await asyncio.sleep(0.1)  # 模拟录音间隔
                
                print("✅ 音频数据发送完成")
                
                # 3. 等待服务器处理结果
                print("⏳ 等待服务器响应...")
                try:
                    # 等待一段时间以接收所有响应
                    for _ in range(20):  # 最多等待20条消息
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                            response_data = json.loads(response)
                            print(f"📥 收到响应: {response_data}")
                            
                            if response_data.get("type") == "response_complete":
                                print("✅ 服务器处理完成")
                                break
                        except asyncio.TimeoutError:
                            print("⏳ 等待响应超时")
                            break
                except Exception as e:
                    print(f"❌ 等待响应时出错: {e}")
            
            # 4. 结束会话
            end_session_msg = {
                "type": "end_session"
            }
            await websocket.send(json.dumps(end_session_msg))
            print("📤 发送结束会话消息")
            
    except Exception as e:
        print(f"❌ WebSocket连接错误: {e}")

if __name__ == "__main__":
    print("🚀 开始WebSocket音频测试...")
    asyncio.run(test_websocket_audio())
    print("🏁 测试完成")
