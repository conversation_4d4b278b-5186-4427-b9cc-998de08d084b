#!/usr/bin/env python3
"""
Simple Frontend-Backend Connection Test Script
简化版的前端-后端连接测试，不依赖pyaudio
"""

import asyncio
import websockets
import json
import base64
import numpy as np
import wave
import os
import time
from datetime import datetime

# 配置参数
USER_ID = "999"  # 测试用户ID
SERVER_URL = "ws://localhost:8000/ws/999"
RATE = 16000

# 创建保存目录
os.makedirs("test_recordings", exist_ok=True)
os.makedirs("test_output", exist_ok=True)

class FrontendBackendTester:
    def __init__(self):
        self.websocket = None
        self.transcription = ""
        self.received_audio_chunks = []
        self.messages = []
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "steps": [],
            "errors": [],
            "success": False
        }
    
    def log_step(self, step, details=""):
        """记录测试步骤"""
        log_entry = {
            "step": step,
            "timestamp": datetime.now().isoformat(),
            "details": details
        }
        self.test_results["steps"].append(log_entry)
        print(f"📋 [{step}] {details}")
    
    def log_error(self, error):
        """记录错误"""
        error_entry = {
            "error": error,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results["errors"].append(error_entry)
        print(f"❌ 错误: {error}")
    
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.log_step("连接WebSocket", f"尝试连接到 {SERVER_URL}")
            self.websocket = await websockets.connect(SERVER_URL)
            self.log_step("WebSocket连接", "连接成功")
            return True
        except Exception as e:
            self.log_error(f"WebSocket连接失败: {e}")
            return False
    
    async def start_session(self, npc_id=1):
        """开始会话"""
        try:
            message = {
                "type": "start_session",
                "npc_id": npc_id
            }
            await self.websocket.send(json.dumps(message))
            self.log_step("开始会话", f"已发送开始会话请求 (NPC ID: {npc_id})")
        except Exception as e:
            self.log_error(f"发送开始会话请求失败: {e}")
    
    def generate_test_audio(self):
        """生成测试音频数据"""
        self.log_step("生成测试音频", "创建测试音频数据")
        
        # 生成简单的测试音频（正弦波）
        duration = 2.0  # 2秒
        frequency = 440.0  # A音符
        samples = int(RATE * duration)
        x = np.linspace(0, duration * 2 * np.pi, samples)
        audio_data = (np.sin(frequency * x) * 32767).astype(np.int16)
        
        # 保存测试音频
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_recordings/test_tone_{timestamp}.wav"
        
        wf = wave.open(filename, 'wb')
        wf.setnchannels(1)  # Mono
        wf.setsampwidth(2)  # 16-bit
        wf.setframerate(RATE)
        wf.writeframes(audio_data.tobytes())
        wf.close()
        
        self.log_step("保存测试音频", f"测试音频已保存到: {filename}")
        return audio_data
    
    def send_audio_chunk(self, audio_data):
        """发送音频块到服务器"""
        try:
            # 转换为base64
            audio_bytes = audio_data.tobytes()
            audio_b64 = base64.b64encode(audio_bytes).decode('utf-8')
            
            message = {
                "type": "audio_chunk",
                "data": audio_b64
            }
            
            # 发送消息
            asyncio.run_coroutine_threadsafe(
                self.websocket.send(json.dumps(message)), 
                asyncio.get_event_loop()
            )
            self.log_step("发送音频块", f"发送音频块: {len(audio_bytes)} 字节")
        except Exception as e:
            self.log_error(f"发送音频块失败: {e}")
    
    async def listen_for_messages(self):
        """监听服务器消息"""
        try:
            self.log_step("监听消息", "开始监听服务器消息")
            async for message in self.websocket:
                data = json.loads(message)
                self.messages.append(data)
                
                msg_type = data.get("type")
                self.log_step("接收消息", f"收到消息类型: {msg_type}")
                
                if msg_type == "session_started":
                    session_id = data.get('session_id')
                    self.log_step("会话开始", f"会话已开始, Session ID: {session_id}")
                    
                elif msg_type == "transcription":
                    text = data.get("text", "")
                    confidence = data.get("confidence", 0)
                    self.transcription = text
                    self.log_step("转录结果", f"转录文本: {text} (置信度: {confidence})")
                    
                elif msg_type == "audio_chunk":
                    # 接收到音频块
                    audio_b64 = data.get("data", "")
                    if audio_b64:
                        audio_data = base64.b64decode(audio_b64)
                        self.received_audio_chunks.append(audio_data)
                        self.log_step("接收音频块", f"接收到音频块: {len(audio_data)} 字节")
                        
                elif msg_type == "response_complete":
                    self.log_step("响应完成", "服务器响应完成")
                    self.test_results["success"] = True
                    
                elif msg_type == "error":
                    error_msg = data.get("message", "未知错误")
                    self.log_error(f"服务器错误: {error_msg}")
                    
        except Exception as e:
            self.log_error(f"监听消息时出错: {e}")
    
    async def run_test(self):
        """运行完整测试"""
        self.log_step("开始测试", "启动前端-后端连接测试")
        
        # 连接服务器
        if not await self.connect():
            return
        
        # 开始会话
        await self.start_session()
        
        # 启动消息监听器
        listen_task = asyncio.create_task(self.listen_for_messages())
        
        # 等待一会儿让会话建立
        await asyncio.sleep(1)
        
        # 生成并发送测试音频
        self.log_step("发送测试音频", "生成并发送测试音频数据")
        test_audio = self.generate_test_audio()
        
        # 分块发送音频数据
        chunk_size = 1600  # 100ms of 16kHz audio
        for i in range(0, len(test_audio), chunk_size):
            if i + chunk_size > len(test_audio):
                chunk = test_audio[i:]
            else:
                chunk = test_audio[i:i + chunk_size]
            
            self.send_audio_chunk(chunk)
            await asyncio.sleep(0.1)  # 模拟实时发送
        
        # 等待一段时间让处理完成
        self.log_step("等待处理", "等待服务器处理完成...")
        await asyncio.sleep(5)
        
        # 发送结束会话消息
        try:
            end_message = {"type": "end_session"}
            await self.websocket.send(json.dumps(end_message))
            self.log_step("结束会话", "已发送结束会话消息")
        except Exception as e:
            self.log_error(f"发送结束会话消息失败: {e}")
        
        # 关闭连接
        try:
            await self.websocket.close()
            self.log_step("关闭连接", "WebSocket连接已关闭")
        except Exception as e:
            self.log_error(f"关闭WebSocket连接失败: {e}")
        
        # 等待监听器完成
        await listen_task
        
        # 输出测试结果
        self.print_test_results()
    
    def print_test_results(self):
        """打印测试结果"""
        print("\n" + "="*60)
        print("📊 测试结果汇总")
        print("="*60)
        
        print(f"✅ 测试成功: {self.test_results['success']}")
        print(f"📝 转录文本: {self.transcription}")
        print(f"🎵 接收到的音频块数量: {len(self.received_audio_chunks)}")
        print(f"📥 接收到的消息数量: {len(self.messages)}")
        
        # 保存测试结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"test_output/frontend_backend_test_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 测试结果已保存到: {result_file}")
        
        # 如果测试失败，提供调试建议
        if not self.test_results["success"]:
            print("\n🔧 调试建议:")
            print("1. 检查后端日志是否有错误信息")
            print("2. 确认ASR服务是否正常工作")
            print("3. 检查LLM和TTS服务连接")
            print("4. 验证网络连接是否稳定")

async def main():
    """主函数"""
    print("🧪 前端-后端连接测试工具")
    print("="*40)
    
    tester = FrontendBackendTester()
    await tester.run_test()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
