#!/usr/bin/env python3
"""
测试前后端联通性的脚本
"""
import requests
import json
import time
import sys
import os

def test_backend_connectivity():
    """测试后端联通性"""
    backend_url = "http://8.152.125.193:8000"
    
    print("🔍 测试后端联通性...")
    print(f"后端地址: {backend_url}")
    
    try:
        # 测试根端点
        print("\n1. 测试根端点 (/) ...")
        response = requests.get(f"{backend_url}/", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print("   ✅ 后端联通性测试通过")
            return True
        else:
            print(f"   ❌ 后端联通性测试失败: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 后端联通性测试失败: {e}")
        return False

def test_supabase_connectivity():
    """测试Supabase联通性"""
    supabase_url = "http://8.152.125.193:8000"
    
    print("\n🔍 测试Supabase联通性...")
    print(f"Supabase地址: {supabase_url}")
    
    try:
        # 测试Supabase根端点
        print("\n1. 测试Supabase根端点 ...")
        response = requests.get(f"{supabase_url}/", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Supabase联通性测试通过")
            return True
        else:
            print(f"   ❌ Supabase联通性测试失败: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Supabase联通性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始前后端联通性测试...")
    print("=" * 50)
    
    backend_ok = test_backend_connectivity()
    supabase_ok = test_supabase_connectivity()
    
    print("\n" + "=" * 50)
    if backend_ok and supabase_ok:
        print("🎉 前后端联通性测试全部通过！")
    else:
        print("⚠️  前后端联通性测试部分失败，请检查配置。")
        
    return backend_ok and supabase_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
