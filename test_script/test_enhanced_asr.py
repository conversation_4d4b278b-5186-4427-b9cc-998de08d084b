#!/usr/bin/env python3
"""
测试增强的ASR服务
"""

import sys
import os
import numpy as np
import logging

# 添加backend目录到路径
sys.path.append('backend')

from services.enhanced_asr_service import EnhancedASRService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_asr():
    """测试增强ASR服务"""
    logger.info("=" * 60)
    logger.info("增强ASR服务测试")
    logger.info("=" * 60)
    
    # 1. 初始化服务
    try:
        logger.info("初始化增强ASR服务...")
        asr_service = EnhancedASRService()
        logger.info("✅ 增强ASR服务初始化成功")
    except Exception as e:
        logger.error(f"❌ 增强ASR服务初始化失败: {e}")
        return
    
    # 2. 测试连接
    try:
        logger.info("测试ASR服务连接...")
        connection_result = asr_service.test_connection()
        
        logger.info(f"🔧 Qwen2-Audio-7B: {'✅' if connection_result['qwen'] else '❌'}")
        logger.info(f"🔧 Gemini-2.0-Flash: {'✅' if connection_result['gemini'] else '❌'}")
        logger.info(f"📊 可用服务: {connection_result['available_services']}")
        
        if not connection_result['available_services']:
            logger.warning("⚠️ 没有可用的ASR服务")
            return
            
    except Exception as e:
        logger.error(f"❌ ASR连接测试失败: {e}")
        return
    
    # 3. 生成测试音频 (正弦波)
    try:
        logger.info("生成测试音频...")
        sample_rate = 16000
        duration = 2.0  # 2秒
        frequency = 440  # A4音符
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t).astype(np.float32)
        
        logger.info(f"📊 测试音频: {len(audio_data)} 样本, {duration}秒, {frequency}Hz")
        
    except Exception as e:
        logger.error(f"❌ 测试音频生成失败: {e}")
        return
    
    # 4. 测试ASR转录
    try:
        logger.info("开始ASR转录测试...")
        result = asr_service.transcribe(audio_data, sample_rate)
        
        logger.info("ASR转录测试完成")
        logger.info(f"✅ 成功: {result.get('success', False)}")
        logger.info(f"📝 文本: '{result.get('text', '')}'")
        logger.info(f"🎯 置信度: {result.get('confidence', 0.0):.3f}")
        logger.info(f"⏱️ 时长: {result.get('duration', 0.0):.3f}秒")
        logger.info(f"🔧 提供商: {result.get('provider', 'unknown')}")
        logger.info(f"🔤 词数: {result.get('tokens', 0)}")
        
        if result.get('success'):
            logger.info("🎉 增强ASR服务测试成功！")
        else:
            logger.error(f"❌ ASR转录失败: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        logger.error(f"❌ ASR转录测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_enhanced_asr()