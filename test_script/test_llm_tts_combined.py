#!/usr/bin/env python3
"""
LLM + TTS 组合测试脚本
测试完整的文本对话 -> 语音合成流程
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('backend/.env')

# 添加backend目录到路径
sys.path.append('backend')

from services.llm_service import LLMService
from services.tts_service import TTSService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_llm_tts_pipeline():
    """测试LLM + TTS完整流程"""
    logger.info("=" * 60)
    logger.info("LLM + TTS Pipeline Test")
    logger.info("=" * 60)
    
    # 1. 初始化服务
    try:
        logger.info("Initializing services...")

        # LLM服务
        llm_api_key = os.getenv("VOLCANO_API_KEY")
        llm_endpoint = os.getenv("VOLCANO_ENDPOINT")
        if not llm_api_key or not llm_endpoint:
            logger.error("❌ Missing VOLCANO_API_KEY or VOLCANO_ENDPOINT")
            return False
        llm_service = LLMService(llm_api_key, llm_endpoint)

        # TTS服务
        tts_api_key = os.getenv("MINIMAX_API_KEY")
        tts_group_id = os.getenv("MINIMAX_GROUP_ID")
        if not tts_api_key or not tts_group_id:
            logger.error("❌ Missing MINIMAX_API_KEY or MINIMAX_GROUP_ID")
            return False
        tts_service = TTSService(tts_api_key, tts_group_id)

        logger.info("✅ Both services initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize services: {e}")
        return False
    
    # 2. 测试对话场景
    test_conversations = [
        {
            "user": "你好，请介绍一下自己",
            "scenario": "greeting"
        },
        {
            "user": "1加1等于几？",
            "scenario": "math"
        },
        {
            "user": "今天天气怎么样？",
            "scenario": "weather"
        },
        {
            "user": "请简单解释一下人工智能",
            "scenario": "explanation"
        },
        {
            "user": "谢谢你的帮助，再见！",
            "scenario": "farewell"
        }
    ]
    
    # 创建输出目录
    output_dir = Path("llm_tts_test_output")
    output_dir.mkdir(exist_ok=True)
    
    for i, conv in enumerate(test_conversations, 1):
        logger.info(f"\n--- Pipeline Test {i}/5: {conv['scenario']} ---")
        logger.info(f"🤖 User: {conv['user']}")
        
        try:
            # Step 1: LLM对话
            logger.info("🧠 Calling LLM...")
            import asyncio
            llm_response = asyncio.run(llm_service.generate_response(
                user_input=conv['user'],
                conversation_history=[],
                system_prompt="你是一个友好的AI助手，请用中文回答问题。",
                use_tools=False
            ))

            if not llm_response or not llm_response.get("success"):
                logger.error(f"❌ LLM failed for test {i}: {llm_response}")
                return False

            speak_content = llm_response.get("speak_content", {})
            ai_text = speak_content.get("text", "No response")
            emotion = speak_content.get("emotion", "neutral")
            speed = speak_content.get("speed", 1.0)
            logger.info(f"✅ AI Response: {ai_text}")

            # Step 2: TTS合成
            logger.info("🔊 Calling TTS...")
            tts_result = asyncio.run(tts_service.synthesize_speech(
                text=ai_text,
                emotion=emotion,
                speed=speed,
                output_file=None
            ))

            if not tts_result or not tts_result.get("success") or not tts_result.get("audio_data"):
                logger.error(f"❌ TTS failed for test {i}: {tts_result}")
                return False

            audio_data = tts_result["audio_data"]
            
            # Step 3: 保存音频
            output_file = output_dir / f"conversation_{i}_{conv['scenario']}.wav"
            with open(output_file, 'wb') as f:
                f.write(audio_data)
            
            logger.info(f"✅ Audio generated: {len(audio_data)} bytes")
            logger.info(f"✅ Saved to: {output_file}")
            
            # 保存对话记录
            transcript_file = output_dir / f"conversation_{i}_{conv['scenario']}.txt"
            with open(transcript_file, 'w', encoding='utf-8') as f:
                f.write(f"User: {conv['user']}\n")
                f.write(f"AI: {ai_text}\n")
                f.write(f"Emotion: {emotion}\n")
                f.write(f"Speed: {speed}\n")
                f.write(f"Mock Mode: {llm_response.get('mock_mode', False)}\n")
            
        except Exception as e:
            logger.error(f"❌ Pipeline test {i} failed: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    logger.info("\n" + "=" * 60)
    logger.info("✅ All LLM + TTS pipeline tests passed!")
    logger.info(f"✅ Files saved in: {output_dir.absolute()}")
    logger.info("=" * 60)
    return True

def test_conversation_flow():
    """测试多轮对话流程"""
    logger.info("\n" + "=" * 60)
    logger.info("Multi-turn Conversation Flow Test")
    logger.info("=" * 60)
    
    try:
        # 初始化服务
        llm_api_key = os.getenv("VOLCANO_API_KEY")
        llm_endpoint = os.getenv("VOLCANO_ENDPOINT")
        llm_service = LLMService(llm_api_key, llm_endpoint)

        tts_api_key = os.getenv("MINIMAX_API_KEY")
        tts_group_id = os.getenv("MINIMAX_GROUP_ID")
        tts_service = TTSService(tts_api_key, tts_group_id)
        
        # 多轮对话场景
        conversation_flow = [
            "你好，我想了解一下你的功能",
            "你能帮我做什么？",
            "那你能帮我计算数学题吗？",
            "好的，那请计算 15 乘以 8",
            "谢谢你的帮助！"
        ]
        
        output_dir = Path("llm_tts_test_output")
        output_dir.mkdir(exist_ok=True)
        
        conversation_log = []
        
        for i, user_input in enumerate(conversation_flow, 1):
            logger.info(f"\n--- Turn {i}/5 ---")
            logger.info(f"🤖 User: {user_input}")
            
            # LLM对话
            import asyncio
            llm_response = asyncio.run(llm_service.generate_response(
                user_input=user_input,
                conversation_history=conversation_log,
                system_prompt="你是一个友好的AI助手，请记住之前的对话内容。",
                use_tools=False
            ))

            if not llm_response or not llm_response.get("success"):
                logger.error(f"❌ LLM failed at turn {i}: {llm_response}")
                return False

            speak_content = llm_response.get("speak_content", {})
            ai_text = speak_content.get("text", "No response")
            logger.info(f"✅ AI: {ai_text}")

            # TTS合成
            tts_result = asyncio.run(tts_service.synthesize_speech(
                text=ai_text,
                emotion=speak_content.get("emotion", "neutral"),
                speed=speak_content.get("speed", 1.0),
                output_file=None
            ))

            if not tts_result or not tts_result.get("success") or not tts_result.get("audio_data"):
                logger.error(f"❌ TTS failed at turn {i}: {tts_result}")
                return False

            audio_data = tts_result["audio_data"]
            
            # 保存音频
            output_file = output_dir / f"conversation_flow_turn_{i}.wav"
            with open(output_file, 'wb') as f:
                f.write(audio_data)
            
            logger.info(f"✅ Turn {i} completed: {len(audio_data)} bytes audio")
            
            # 记录对话 - 更新对话历史用于下一轮
            conversation_log.append({"role": "user", "content": user_input})
            conversation_log.append({"role": "assistant", "content": ai_text})
        
        # 保存完整对话记录
        log_file = output_dir / "conversation_flow_log.txt"
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("Multi-turn Conversation Log\n")
            f.write("=" * 40 + "\n\n")
            for i, entry in enumerate(conversation_log):
                if entry["role"] == "user":
                    f.write(f"User: {entry['content']}\n")
                elif entry["role"] == "assistant":
                    f.write(f"AI: {entry['content']}\n")
                    f.write("-" * 40 + "\n\n")
        
        logger.info(f"\n✅ Conversation flow test completed!")
        logger.info(f"✅ Log saved to: {log_file}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Conversation flow test failed: {e}")
        return False

def main():
    """主函数"""
    logger.info("Starting LLM + TTS combined tests...")
    
    # 检查环境变量
    required_env_vars = [
        "VOLCANO_API_KEY",
        "VOLCANO_ENDPOINT",
        "MINIMAX_API_KEY",
        "MINIMAX_GROUP_ID"
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        logger.error("Please set these variables in your .env file")
        return False
    
    # 运行测试
    success = True
    
    # 1. LLM + TTS流水线测试
    if not test_llm_tts_pipeline():
        success = False
    
    # 2. 多轮对话流程测试
    if not test_conversation_flow():
        success = False
    
    if success:
        logger.info("\n🎉 All LLM + TTS combined tests completed successfully!")
        logger.info("🎵 You can now play the generated audio files to verify the complete pipeline")
        logger.info("📝 Check the transcript files for conversation details")
    else:
        logger.error("\n❌ Some combined tests failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
