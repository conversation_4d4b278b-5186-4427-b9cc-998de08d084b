#!/usr/bin/env python3
"""
完整语音流端到端测试
测试ASR -> LLM -> TTS的完整链路
"""
import asyncio
import websockets
import json
import base64
import requests
import wave
import logging
from pathlib import Path
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_complete_voice_flow():
    """测试完整的语音对话流程"""
    logger.info("🎤 开始完整语音流端到端测试...")
    
    # 1. 检查后端健康状态
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            logger.info("✅ 后端服务健康检查通过")
            
            services = health_data.get('services', {})
            for service, status in services.items():
                status_icon = "✅" if "error" not in str(status).lower() else "⚠️"
                logger.info(f"   {service}: {status_icon} {status}")
        else:
            logger.error(f"❌ 后端健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 后端连接失败: {e}")
        return False
    
    # 2. 用户登录
    try:
        response = requests.post(
            "http://localhost:8000/auth/login",
            params={"username": "test_user", "password": "test_password"},
            timeout=10
        )
        if response.status_code != 200:
            logger.error(f"❌ 登录失败: {response.status_code}")
            return False
        
        user_data = response.json()
        user_id = user_data.get('user', {}).get('id', 1)
        logger.info(f"✅ 用户登录成功，ID: {user_id}")
    except Exception as e:
        logger.error(f"❌ 登录异常: {e}")
        return False
    
    # 3. WebSocket连接和完整对话测试
    try:
        uri = f"ws://localhost:8000/ws/{user_id}"
        logger.info(f"🔌 连接WebSocket: {uri}")
        
        async with websockets.connect(
            uri,
            max_size=10 * 1024 * 1024,  # 10MB限制
            ping_interval=20,
            ping_timeout=10
        ) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 启动会话
            start_msg = {"type": "start_session", "npc_id": 1}
            await websocket.send(json.dumps(start_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动确认
            session_started = False
            session_id = None
            
            for attempt in range(3):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    
                    logger.info(f"📥 收到响应: {response_data.get('type')}")
                    
                    if response_data.get("type") == "session_started":
                        session_id = response_data.get("session_id")
                        logger.info(f"✅ 会话启动成功，ID: {session_id}")
                        session_started = True
                        break
                    elif response_data.get("type") == "connection_established":
                        logger.info("✅ 连接已建立，继续等待会话启动...")
                        continue
                        
                except asyncio.TimeoutError:
                    logger.error(f"❌ 等待响应超时 (尝试 {attempt + 1}/3)")
                    if attempt == 2:
                        return False
            
            if not session_started:
                logger.error("❌ 会话启动失败")
                return False
            
            # 4. 发送音频数据进行完整的语音对话测试
            audio_dir = Path("backend/audio_recordings")
            audio_files = list(audio_dir.glob("*.wav"))
            
            if not audio_files:
                logger.error("❌ 没有找到音频文件")
                return False
            
            # 选择一个较大的音频文件进行测试
            test_file = max(audio_files, key=lambda f: f.stat().st_size)
            logger.info(f"📁 使用音频文件: {test_file.name} ({test_file.stat().st_size} bytes)")
            
            # 读取音频文件
            with open(test_file, 'rb') as f:
                audio_data = f.read()
            
            logger.info(f"📊 音频数据: {len(audio_data)} bytes")
            
            # 转换为base64并发送
            base64_audio = base64.b64encode(audio_data).decode('utf-8')
            
            audio_msg = {
                "type": "audio_chunk",
                "data": base64_audio
            }
            
            json_msg = json.dumps(audio_msg)
            json_size = len(json_msg.encode('utf-8'))
            logger.info(f"📏 JSON消息大小: {json_size} bytes")
            
            if json_size > 10 * 1024 * 1024:  # 10MB
                logger.error(f"❌ 消息太大: {json_size} bytes > 10MB")
                return False
            
            # 发送音频数据
            await websocket.send(json_msg)
            logger.info("📤 发送音频数据完成")
            
            # 等待完整的处理响应
            responses_received = []
            transcription_received = False
            llm_response_received = False
            tts_audio_received = False
            
            logger.info("⏳ 等待完整的ASR -> LLM -> TTS处理...")
            
            timeout_count = 0
            max_timeout = 60  # 增加到60秒，因为LLM和TTS处理需要时间
            
            while timeout_count < max_timeout:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1)
                    response_data = json.loads(response)
                    responses_received.append(response_data)
                    
                    msg_type = response_data.get('type')
                    logger.info(f"📥 收到响应: {msg_type}")
                    
                    if msg_type == 'transcription':
                        text = response_data.get('text', '')
                        confidence = response_data.get('confidence', 0)
                        logger.info(f"🎯 ASR转录结果: '{text}' (置信度: {confidence:.2f})")
                        transcription_received = True
                        
                    elif msg_type == 'llm_response' or msg_type == 'response':
                        text = response_data.get('text', '')
                        logger.info(f"🧠 LLM响应: '{text}'")
                        llm_response_received = True
                        
                    elif msg_type == 'audio_chunk':
                        audio_data_b64 = response_data.get('data', '')
                        logger.info(f"🔊 收到TTS音频响应: {len(audio_data_b64)} chars base64")
                        tts_audio_received = True
                        
                        # 保存音频响应用于验证
                        try:
                            audio_bytes = base64.b64decode(audio_data_b64)
                            response_audio_path = Path("tts_response_audio.wav")
                            with open(response_audio_path, 'wb') as f:
                                f.write(audio_bytes)
                            logger.info(f"💾 TTS音频已保存: {response_audio_path} ({len(audio_bytes)} bytes)")
                        except Exception as e:
                            logger.warning(f"⚠️ 保存TTS音频失败: {e}")
                        
                    elif msg_type == 'response_complete':
                        logger.info("✅ 完整响应处理完成")
                        break
                        
                    elif msg_type == 'error':
                        error_msg = response_data.get('message', '未知错误')
                        logger.error(f"❌ 处理错误: {error_msg}")
                        break
                    
                    else:
                        logger.info(f"📥 其他响应: {msg_type}")
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 10 == 0:
                        logger.info(f"⏰ 等待处理中... ({timeout_count}s)")
                    continue
            
            # 统计和分析结果
            logger.info("=" * 60)
            logger.info("📊 完整语音流测试结果:")
            logger.info(f"   发送音频大小: {len(audio_data)} bytes")
            logger.info(f"   收到响应数: {len(responses_received)}")
            logger.info(f"   处理时间: {timeout_count} 秒")
            
            # 检查各个环节
            logger.info("\n🔍 各环节验证:")
            logger.info(f"   ASR (语音识别): {'✅ 成功' if transcription_received else '❌ 失败'}")
            logger.info(f"   LLM (对话生成): {'✅ 成功' if llm_response_received else '❌ 失败'}")
            logger.info(f"   TTS (语音合成): {'✅ 成功' if tts_audio_received else '❌ 失败'}")
            
            # 统计响应类型
            response_types = {}
            for resp in responses_received:
                resp_type = resp.get('type', 'unknown')
                response_types[resp_type] = response_types.get(resp_type, 0) + 1
            
            logger.info("\n📈 响应类型统计:")
            for resp_type, count in response_types.items():
                logger.info(f"   {resp_type}: {count} 次")
            
            logger.info("=" * 60)
            
            # 判断测试是否成功
            success = transcription_received  # 至少要有语音识别成功
            complete_success = transcription_received and llm_response_received and tts_audio_received
            
            if complete_success:
                logger.info("🎉 完整语音流测试全部成功！ASR -> LLM -> TTS 链路完整")
            elif success:
                logger.info("✅ 语音流测试部分成功！ASR环节正常工作")
            else:
                logger.error("❌ 语音流测试失败！")
            
            # 结束会话
            end_msg = {"type": "end_session"}
            await websocket.send(json.dumps(end_msg))
            logger.info("📤 发送结束会话请求")
            
            try:
                end_response = await asyncio.wait_for(websocket.recv(), timeout=5)
                end_data = json.loads(end_response)
                logger.info(f"📥 会话结束响应: {end_data.get('type')}")
            except asyncio.TimeoutError:
                logger.info("⏰ 会话结束响应超时")
            
            return complete_success or success
            
    except Exception as e:
        logger.error(f"❌ WebSocket测试异常: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 完整语音流端到端测试开始...")
    logger.info("🎯 目标: 验证 ASR -> LLM -> TTS 完整链路")
    logger.info("=" * 60)
    
    success = await test_complete_voice_flow()
    
    logger.info("=" * 60)
    if success:
        logger.info("🎉 完整语音流测试成功！")
        logger.info("💡 语音对话系统已准备就绪，可以进行实际使用")
        logger.info("🌐 可以在浏览器中打开 http://localhost:8080/test_page.html 进行手动测试")
    else:
        logger.error("❌ 完整语音流测试失败！")
        logger.info("🔧 请检查ASR/LLM/TTS服务配置")

if __name__ == "__main__":
    asyncio.run(main())