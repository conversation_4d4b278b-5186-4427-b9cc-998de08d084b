#!/usr/bin/env python3
"""
完整的音频修复测试脚本
测试前端和后端的音频输入输出功能
"""

import asyncio
import websockets
import json
import base64
import requests
import time
from datetime import datetime
import numpy as np

# 配置
BACKEND_URL = "http://localhost:8000"
WEBSOCKET_URL = "ws://localhost:8000/ws"
TEST_USER_ID = "test_user"

def generate_test_audio_data(duration_ms=1000, sample_rate=16000):
    """生成测试音频数据"""
    samples = int(sample_rate * duration_ms / 1000)
    # 生成简单的正弦波
    t = np.linspace(0, duration_ms/1000, samples)
    frequency = 440  # A4音符
    audio = np.sin(2 * np.pi * frequency * t) * 0.5
    # 转换为16位整数
    audio_int16 = (audio * 32767).astype(np.int16)
    return audio_int16.tobytes()

async def test_backend_health():
    """测试后端健康状态"""
    print("🏥 Testing backend health...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Backend health check passed")
            print(f"   Services: {list(health_data.get('services', {}).keys())}")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend health check error: {e}")
        return False

async def test_websocket_connection():
    """测试WebSocket连接"""
    print("🔌 Testing WebSocket connection...")
    try:
        uri = f"{WEBSOCKET_URL}/{TEST_USER_ID}"
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected successfully")
            
            # 发送ping消息
            ping_message = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(ping_message))
            print("📤 Ping message sent")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                response_data = json.loads(response)
                print(f"📥 Received response: {response_data.get('type', 'unknown')}")
                return True
            except asyncio.TimeoutError:
                print("⏰ WebSocket response timeout")
                return False
                
    except Exception as e:
        print(f"❌ WebSocket connection error: {e}")
        return False

async def test_session_management():
    """测试会话管理"""
    print("📋 Testing session management...")
    try:
        uri = f"{WEBSOCKET_URL}/{TEST_USER_ID}"
        async with websockets.connect(uri) as websocket:
            # 等待连接确认
            await asyncio.sleep(1)
            
            # 开始会话
            start_session_message = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_session_message))
            print("📤 Start session message sent")
            
            # 等待会话开始确认
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                response_data = json.loads(response)
                if response_data.get("type") == "session_started":
                    session_id = response_data.get("session_id")
                    print(f"✅ Session started: {session_id}")
                    return True
                else:
                    print(f"❌ Unexpected response: {response_data}")
                    return False
            except asyncio.TimeoutError:
                print("⏰ Session start timeout")
                return False
                
    except Exception as e:
        print(f"❌ Session management error: {e}")
        return False

async def test_audio_streaming():
    """测试音频流传输"""
    print("🎵 Testing audio streaming...")
    try:
        uri = f"{WEBSOCKET_URL}/{TEST_USER_ID}"
        async with websockets.connect(uri) as websocket:
            # 等待连接确认
            await asyncio.sleep(1)
            
            # 开始会话
            start_session_message = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_session_message))
            
            # 等待会话开始
            await asyncio.wait_for(websocket.recv(), timeout=5)
            
            # 发送音频块
            audio_chunks_sent = 0
            for i in range(5):  # 发送5个音频块
                audio_data = generate_test_audio_data(200)  # 200ms音频
                audio_b64 = base64.b64encode(audio_data).decode('utf-8')
                
                audio_message = {
                    "type": "audio_chunk",
                    "data": audio_b64,
                    "format": "pcm16",
                    "sample_rate": 16000,
                    "channels": 1
                }
                
                await websocket.send(json.dumps(audio_message))
                audio_chunks_sent += 1
                print(f"📤 Audio chunk {i+1} sent ({len(audio_data)} bytes)")
                await asyncio.sleep(0.1)  # 模拟实时传输
            
            print(f"✅ Sent {audio_chunks_sent} audio chunks")
            
            # 等待处理结果
            responses_received = 0
            timeout_count = 0
            max_timeout = 3
            
            while responses_received < 3 and timeout_count < max_timeout:  # 期望收到转录、音频响应、完成信号
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    response_type = response_data.get("type", "unknown")
                    
                    print(f"📥 Received: {response_type}")
                    
                    if response_type == "transcription":
                        print(f"   Text: {response_data.get('text', 'N/A')}")
                        responses_received += 1
                    elif response_type == "audio_chunk":
                        audio_data = response_data.get('data', '')
                        print(f"   Audio: {len(audio_data)} chars (base64)")
                        responses_received += 1
                    elif response_type == "response_complete":
                        print("   Processing completed")
                        responses_received += 1
                        break
                    elif response_type == "error":
                        print(f"   Error: {response_data.get('message', 'Unknown error')}")
                        return False
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    print(f"⏰ Response timeout {timeout_count}/{max_timeout}")
            
            if responses_received > 0:
                print(f"✅ Audio streaming test passed ({responses_received} responses)")
                return True
            else:
                print("❌ No responses received")
                return False
                
    except Exception as e:
        print(f"❌ Audio streaming error: {e}")
        return False

async def test_audio_file_processing():
    """测试音频文件处理API"""
    print("📁 Testing audio file processing API...")
    try:
        # 生成测试音频文件
        audio_data = generate_test_audio_data(2000)  # 2秒音频
        
        # 创建WAV文件头
        import struct
        sample_rate = 16000
        num_channels = 1
        bits_per_sample = 16
        byte_rate = sample_rate * num_channels * bits_per_sample // 8
        block_align = num_channels * bits_per_sample // 8
        data_size = len(audio_data)
        
        wav_header = struct.pack('<4sI4s4sIHHIIHH4sI',
            b'RIFF',
            36 + data_size,
            b'WAVE',
            b'fmt ',
            16,
            1,
            num_channels,
            sample_rate,
            byte_rate,
            block_align,
            bits_per_sample,
            b'data',
            data_size
        )
        
        wav_data = wav_header + audio_data
        
        # 发送到处理API
        files = {'file': ('test_audio.wav', wav_data, 'audio/wav')}
        data = {'user_id': 1, 'npc_id': 1}
        
        response = requests.post(
            f"{BACKEND_URL}/process-audio",
            files=files,
            data=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Audio file processing successful")
            print(f"   Transcription: {result.get('transcription', 'N/A')}")
            print(f"   Response: {result.get('response', 'N/A')[:100]}...")
            print(f"   Audio output: {len(result.get('audio_data', ''))} chars")
            return True
        else:
            print(f"❌ Audio file processing failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Audio file processing error: {e}")
        return False

async def run_all_tests():
    """运行所有测试"""
    print("🚀 Starting comprehensive audio fix tests...")
    print("=" * 60)
    
    test_results = {}
    
    # 测试1: 后端健康检查
    test_results["backend_health"] = await test_backend_health()
    print()
    
    # 测试2: WebSocket连接
    test_results["websocket_connection"] = await test_websocket_connection()
    print()
    
    # 测试3: 会话管理
    test_results["session_management"] = await test_session_management()
    print()
    
    # 测试4: 音频流传输
    test_results["audio_streaming"] = await test_audio_streaming()
    print()
    
    # 测试5: 音频文件处理
    test_results["audio_file_processing"] = await test_audio_file_processing()
    print()
    
    # 汇总结果
    print("=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title():<25} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Total: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Audio system is working correctly.")
    elif passed >= total * 0.8:
        print("⚠️ Most tests passed. Minor issues may exist.")
    else:
        print("❌ Multiple test failures. Audio system needs attention.")
    
    return test_results

if __name__ == "__main__":
    print("🎵 Audio Fix Complete Test Suite")
    print(f"Backend URL: {BACKEND_URL}")
    print(f"WebSocket URL: {WEBSOCKET_URL}")
    print(f"Test User ID: {TEST_USER_ID}")
    print()
    
    try:
        results = asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")