#!/usr/bin/env python3
"""
直接测试登录功能
"""
import requests
import json

def test_backend_auth():
    """测试后端认证功能"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试后端认证功能...")
    
    # 1. 测试健康检查
    print("\n1. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"✅ 健康检查状态: {response.status_code}")
        if response.status_code == 200:
            health_data = response.json()
            print(f"   数据库状态: {health_data.get('services', {}).get('database', 'unknown')}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False
    
    # 2. 测试用户注册
    print("\n2. 测试用户注册...")
    register_params = {
        "username": "test_user_new",
        "password": "test_password",
        "email": "<EMAIL>",
        "nickname": "测试用户"
    }
    
    try:
        response = requests.post(
            f"{base_url}/auth/register",
            params=register_params,
            timeout=10
        )
        print(f"   注册响应状态: {response.status_code}")
        if response.status_code == 200:
            print("✅ 注册成功")
            register_result = response.json()
            print(f"   用户ID: {register_result.get('user', {}).get('id')}")
        else:
            print(f"❌ 注册失败: {response.text}")
    except Exception as e:
        print(f"❌ 注册请求失败: {e}")
    
    # 3. 测试用户登录
    print("\n3. 测试用户登录...")
    login_params = {
        "username": "test_user",
        "password": "test_password"
    }
    
    try:
        response = requests.post(
            f"{base_url}/auth/login",
            params=login_params,
            timeout=10
        )
        print(f"   登录响应状态: {response.status_code}")
        if response.status_code == 200:
            print("✅ 登录成功")
            login_result = response.json()
            print(f"   用户: {login_result.get('user', {})}")
            print(f"   Token: {login_result.get('token', 'N/A')}")
        else:
            print(f"❌ 登录失败: {response.text}")
            # 尝试解析错误信息
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data.get('detail', 'Unknown error')}")
            except:
                pass
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
    
    # 4. 测试获取NPCs
    print("\n4. 测试获取NPCs...")
    try:
        response = requests.get(f"{base_url}/npcs", timeout=5)
        print(f"   NPCs响应状态: {response.status_code}")
        if response.status_code == 200:
            npcs_data = response.json()
            print(f"✅ 获取NPCs成功")
            print(f"   NPCs数量: {len(npcs_data.get('npcs', []))}")
            print(f"   数据源: {npcs_data.get('source', 'unknown')}")
        else:
            print(f"❌ 获取NPCs失败: {response.text}")
    except Exception as e:
        print(f"❌ 获取NPCs请求失败: {e}")
    
    # 5. 测试简单的API调用
    print("\n5. 测试简单API调用...")
    try:
        response = requests.get(f"{base_url}/test", timeout=10)
        print(f"   测试API响应状态: {response.status_code}")
        if response.status_code == 200:
            test_data = response.json()
            print("✅ 测试API成功")
            print(f"   响应: {test_data.get('response', 'N/A')}")
        else:
            print(f"❌ 测试API失败: {response.text}")
    except Exception as e:
        print(f"❌ 测试API请求失败: {e}")
    
    return True

if __name__ == "__main__":
    print("🚀 开始测试后端登录功能...")
    test_backend_auth()
    print("\n✅ 测试完成！")