#!/usr/bin/env python3
"""
测试LLM和TTS服务是否使用真实API
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('backend/.env')

# 添加backend目录到路径
sys.path.append('backend')

from services.llm_service import LLMService
from services.tts_service import TTSService

async def test_llm_service():
    """测试LLM服务"""
    print("🧠 测试LLM服务...")
    
    # 检查环境变量
    api_key = os.getenv("VOLCANO_API_KEY")
    endpoint = os.getenv("VOLCANO_ENDPOINT")
    
    print(f"API Key: {api_key[:10] if api_key else 'None'}...")
    print(f"Endpoint: {endpoint}")
    
    if not api_key or not endpoint:
        print("❌ 缺少LLM环境变量")
        return
    
    # 初始化服务
    llm_service = LLMService(api_key, endpoint)
    
    # 测试一个特定的问题
    test_question = "请告诉我今天是星期几？"
    print(f"🤔 测试问题: {test_question}")
    
    try:
        response = await llm_service.generate_response(
            user_input=test_question,
            conversation_history=[],
            system_prompt="你是一个AI助手，请直接回答用户的问题。",
            use_tools=False
        )
        
        print(f"📝 LLM响应: {response}")
        
        if response.get("mock_mode"):
            print("❌ LLM使用了mock模式")
        else:
            print("✅ LLM使用了真实API")
            print(f"🎯 AI回复: {response.get('speak_content', {}).get('text', 'No text')}")
        
        return response
        
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")
        return None

async def test_tts_service():
    """测试TTS服务"""
    print("\n🔊 测试TTS服务...")
    
    # 检查环境变量
    api_key = os.getenv("MINIMAX_API_KEY")
    group_id = os.getenv("MINIMAX_GROUP_ID")
    
    print(f"API Key: {api_key[:10] if api_key else 'None'}...")
    print(f"Group ID: {group_id}")
    
    if not api_key or not group_id:
        print("❌ 缺少TTS环境变量")
        return
    
    # 初始化服务
    tts_service = TTSService(api_key, group_id)
    
    # 测试文本
    test_text = "这是一个TTS测试，请生成语音。"
    print(f"📝 测试文本: {test_text}")
    
    try:
        result = await tts_service.synthesize_speech(
            text=test_text,
            emotion="neutral",
            speed=1.0,
            output_file=None
        )
        
        print(f"🎵 TTS结果: {result}")
        
        if result.get("success") and result.get("audio_data"):
            audio_size = len(result["audio_data"])
            print(f"✅ TTS成功生成音频: {audio_size} 字节")
            
            # 保存音频文件用于验证
            with open("tts_test_output.wav", "wb") as f:
                f.write(result["audio_data"])
            print("💾 音频已保存到: tts_test_output.wav")
            
            return True
        else:
            print("❌ TTS生成失败")
            return False
        
    except Exception as e:
        print(f"❌ TTS测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🧪 测试LLM和TTS真实服务调用")
    print("=" * 50)
    
    # 测试LLM
    llm_result = await test_llm_service()
    
    # 测试TTS
    tts_result = await test_tts_service()
    
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print(f"LLM服务: {'✅ 真实API' if llm_result and not llm_result.get('mock_mode') else '❌ Mock模式'}")
    print(f"TTS服务: {'✅ 真实API' if tts_result else '❌ 失败'}")

if __name__ == "__main__":
    asyncio.run(main())