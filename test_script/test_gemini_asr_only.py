#!/usr/bin/env python3
"""
直接测试Gemini ASR服务 (使用新API)
"""

import sys
import os
import numpy as np
import logging
import io
import wave
import tempfile

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gemini_asr_only():
    """直接测试Gemini ASR (新API)"""
    logger.info("=" * 60)
    logger.info("Gemini ASR 直接测试 (新API)")
    logger.info("=" * 60)
    
    # 1. 检查API Key
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        logger.error("❌ GEMINI_API_KEY 未配置")
        return
    
    logger.info(f"✅ GEMINI_API_KEY: {gemini_api_key[:10]}...")
    
    # 2. 初始化Gemini Client
    try:
        logger.info("初始化Gemini Client...")
        from google import genai
        client = genai.Client(api_key=gemini_api_key)
        logger.info("✅ Gemini Client初始化成功")
    except Exception as e:
        logger.error(f"❌ Gemini Client初始化失败: {e}")
        return
    
    # 3. 测试简单连接
    try:
        logger.info("测试Gemini连接...")
        test_response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=["测试连接，请回复'连接成功'"]
        )
        if test_response and test_response.text:
            logger.info("✅ Gemini连接正常")
            logger.info(f"📝 测试响应: {test_response.text[:50]}...")
        else:
            logger.error("❌ Gemini连接失败")
            return
    except Exception as e:
        logger.error(f"❌ Gemini连接测试失败: {e}")
        return
    
    # 4. 生成测试音频
    try:
        logger.info("生成测试音频...")
        sample_rate = 16000
        duration = 1.0  # 1秒
        frequency = 440  # A4音符
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t).astype(np.float32)
        
        # 转换为16位整数
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        # 创建WAV字节流
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        audio_bytes = wav_buffer.getvalue()
        logger.info(f"📊 测试音频: {len(audio_bytes)} bytes, {duration}秒")
        
    except Exception as e:
        logger.error(f"❌ 测试音频生成失败: {e}")
        return
    
    # 5. 测试Gemini ASR (新API)
    try:
        logger.info("开始Gemini ASR测试 (新API)...")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_file.write(audio_bytes)
            temp_file_path = temp_file.name
        
        try:
            # 上传音频文件到Gemini
            logger.info("📤 上传音频到Gemini...")
            myfile = client.files.upload(file=temp_file_path)
            logger.info("✅ 音频上传成功")
            
            # 调用Gemini API进行转录
            logger.info("🎯 调用Gemini进行转录...")
            response = client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=["请转录这段音频的内容，只返回转录的中文文本，不要添加任何解释或标点符号。", myfile]
            )
            
            transcribed_text = response.text.strip()
            
            logger.info("✅ Gemini ASR测试完成")
            logger.info(f"📝 转录结果: '{transcribed_text}'")
            
            if transcribed_text:
                logger.info("🎉 Gemini ASR测试成功！")
            else:
                logger.warning("⚠️ Gemini返回空文本")
                
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
            
    except Exception as e:
        logger.error(f"❌ Gemini ASR测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    # 从.env文件加载环境变量
    from dotenv import load_dotenv
    load_dotenv('backend/.env')
    
    test_gemini_asr_only()