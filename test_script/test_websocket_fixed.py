#!/usr/bin/env python3
"""
修复后的WebSocket测试 - 解决消息大小限制问题
"""
import asyncio
import websockets
import json
import base64
import requests
import wave
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_fixed():
    """修复后的WebSocket测试"""
    backend_url = "http://localhost:8000"
    websocket_url = "ws://localhost:8000"
    audio_dir = Path("backend/audio_recordings")
    
    logger.info("🔌 开始修复后的WebSocket测试...")
    
    # 1. 用户登录获取ID
    try:
        response = requests.post(
            f"{backend_url}/auth/login",
            params={"username": "test_user", "password": "test_password"},
            timeout=10
        )
        if response.status_code != 200:
            logger.error(f"❌ 登录失败: {response.status_code}")
            return False
        
        user_data = response.json()
        user_id = user_data.get('user', {}).get('id', 1)
        logger.info(f"✅ 用户登录成功，ID: {user_id}")
    except Exception as e:
        logger.error(f"❌ 登录异常: {e}")
        return False
    
    # 2. WebSocket连接测试
    try:
        uri = f"{websocket_url}/ws/{user_id}"
        logger.info(f"🔌 连接WebSocket: {uri}")
        
        # 设置WebSocket连接参数，增加消息大小限制
        async with websockets.connect(
            uri,
            max_size=10 * 1024 * 1024,  # 10MB限制
            ping_interval=20,
            ping_timeout=10
        ) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 启动会话
            start_msg = {"type": "start_session", "npc_id": 1}
            await websocket.send(json.dumps(start_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动确认
            session_started = False
            for attempt in range(3):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    
                    logger.info(f"📥 收到响应: {response_data.get('type')}")
                    
                    if response_data.get("type") == "session_started":
                        session_id = response_data.get("session_id")
                        logger.info(f"✅ 会话启动成功，ID: {session_id}")
                        session_started = True
                        break
                    elif response_data.get("type") == "connection_established":
                        logger.info("✅ 连接已建立，继续等待会话启动...")
                        continue
                        
                except asyncio.TimeoutError:
                    logger.error(f"❌ 等待响应超时 (尝试 {attempt + 1}/3)")
                    if attempt == 2:
                        return False
            
            if not session_started:
                logger.error("❌ 会话启动失败")
                return False
            
            # 3. 测试音频流 - 使用更小的块
            audio_files = list(audio_dir.glob("*.wav"))
            if not audio_files:
                logger.error("❌ 没有找到音频文件")
                return False
            
            # 选择最小的音频文件
            test_file = min(audio_files, key=lambda f: f.stat().st_size)
            logger.info(f"📁 使用最小的音频文件: {test_file.name} ({test_file.stat().st_size} bytes)")
            
            # 读取音频文件
            with wave.open(str(test_file), 'rb') as wav_file:
                audio_data = wav_file.readframes(wav_file.getnframes())
                sample_rate = wav_file.getframerate()
            
            logger.info(f"📊 音频信息: {len(audio_data)} bytes, {sample_rate} Hz")
            
            # 使用非常小的块大小，避免base64编码后超过限制
            chunk_size = 256  # 256字节原始数据，base64编码后约342字节
            chunks = [audio_data[i:i + chunk_size] for i in range(0, len(audio_data), chunk_size)]
            
            # 限制发送的块数量进行测试
            max_test_chunks = 5
            test_chunks = chunks[:max_test_chunks]
            
            logger.info(f"🔪 音频分为 {len(chunks)} 个块，测试发送前 {len(test_chunks)} 个块")
            logger.info(f"📏 每块大小: {chunk_size} bytes (base64后约 {chunk_size * 4 // 3} bytes)")
            
            # 发送音频流
            responses_received = []
            
            for i, chunk in enumerate(test_chunks):
                # 编码为base64
                base64_chunk = base64.b64encode(chunk).decode('utf-8')
                
                # 检查编码后的大小
                encoded_size = len(base64_chunk)
                logger.info(f"📤 发送音频块 {i+1}/{len(test_chunks)} (原始: {len(chunk)} bytes, base64: {encoded_size} bytes)")
                
                # 创建音频消息
                audio_msg = {"type": "audio_chunk", "data": base64_chunk}
                json_msg = json.dumps(audio_msg)
                
                # 检查JSON消息的总大小
                json_size = len(json_msg.encode('utf-8'))
                logger.info(f"📏 JSON消息大小: {json_size} bytes")
                
                if json_size > 1048576:  # 1MB
                    logger.error(f"❌ 消息太大: {json_size} bytes > 1MB")
                    break
                
                # 发送音频块
                await websocket.send(json_msg)
                
                # 检查响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2)
                    response_data = json.loads(response)
                    responses_received.append(response_data)
                    
                    msg_type = response_data.get('type')
                    if msg_type == 'transcription':
                        text = response_data.get('text', '')
                        confidence = response_data.get('confidence', 0)
                        logger.info(f"🎯 转录结果: '{text}' (置信度: {confidence:.2f})")
                    elif msg_type == 'audio_chunk':
                        logger.info("🔊 收到AI音频响应")
                    elif msg_type == 'response_complete':
                        logger.info("✅ AI响应完成")
                        break
                    else:
                        logger.info(f"📥 收到响应: {msg_type}")
                        
                except asyncio.TimeoutError:
                    # 没有立即响应，继续发送
                    pass
                
                # 模拟实时流的延迟
                await asyncio.sleep(0.2)
            
            # 等待最终响应
            logger.info("⏳ 等待最终处理结果...")
            try:
                timeout_count = 0
                max_timeout = 10
                
                while timeout_count < max_timeout:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=1)
                        response_data = json.loads(response)
                        responses_received.append(response_data)
                        
                        msg_type = response_data.get('type')
                        logger.info(f"📥 最终响应: {msg_type}")
                        
                        if msg_type == 'response_complete':
                            logger.info("✅ 语音处理流程完成")
                            break
                        elif msg_type == 'transcription':
                            text = response_data.get('text', '')
                            logger.info(f"🎯 最终转录: '{text}'")
                        elif msg_type == 'audio_chunk':
                            logger.info("🔊 收到AI音频块")
                        elif msg_type == 'error':
                            error_msg = response_data.get('message', '未知错误')
                            logger.error(f"❌ 处理错误: {error_msg}")
                            break
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        continue
                        
            except Exception as e:
                logger.error(f"❌ 等待最终响应异常: {e}")
            
            # 统计结果
            logger.info("=" * 50)
            logger.info("📊 WebSocket语音流测试结果:")
            logger.info(f"   发送音频块数: {len(test_chunks)}")
            logger.info(f"   收到响应数: {len(responses_received)}")
            
            # 检查是否有转录结果
            transcriptions = [r for r in responses_received if r.get('type') == 'transcription']
            audio_outputs = [r for r in responses_received if r.get('type') == 'audio_chunk']
            
            if transcriptions:
                logger.info("✅ 语音输入成功 - 收到转录结果")
            else:
                logger.warning("⚠️ 语音输入可能有问题 - 没有收到转录结果")
            
            if audio_outputs:
                logger.info("✅ 语音输出成功 - 收到音频响应")
            else:
                logger.warning("⚠️ 语音输出可能有问题 - 没有收到音频响应")
            
            # 统计响应类型
            response_types = {}
            for resp in responses_received:
                resp_type = resp.get('type', 'unknown')
                response_types[resp_type] = response_types.get(resp_type, 0) + 1
            
            for resp_type, count in response_types.items():
                logger.info(f"   {resp_type}: {count} 次")
            
            logger.info("=" * 50)
            
            # 测试结束会话
            end_msg = {"type": "end_session"}
            await websocket.send(json.dumps(end_msg))
            logger.info("📤 发送结束会话请求")
            
            try:
                end_response = await asyncio.wait_for(websocket.recv(), timeout=5)
                end_data = json.loads(end_response)
                logger.info(f"📥 结束响应: {end_data}")
            except asyncio.TimeoutError:
                logger.info("⏰ 结束会话响应超时，但测试继续")
            
            return len(transcriptions) > 0 or len(audio_outputs) > 0 or len(responses_received) > 0
            
    except Exception as e:
        logger.error(f"❌ WebSocket测试异常: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 修复后的WebSocket语音流测试开始...")
    
    success = await test_websocket_fixed()
    
    if success:
        logger.info("🎉 WebSocket语音流测试成功！")
        logger.info("💡 前端和后端的语音流连通性正常")
    else:
        logger.error("❌ WebSocket语音流测试失败！")

if __name__ == "__main__":
    asyncio.run(main())