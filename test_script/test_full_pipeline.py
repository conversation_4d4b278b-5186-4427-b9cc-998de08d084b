#!/usr/bin/env python3
"""
全链路测试脚本 - 测试语音聊天应用的完整流程
"""

import asyncio
import requests
import json
import time
import websockets
import base64
import numpy as np
from pathlib import Path

# 配置
BACKEND_URL = "http://localhost:8000"
WEBSOCKET_URL = "ws://localhost:8000/ws"

def test_backend_health():
    """测试后端健康状态"""
    print("🏥 测试后端健康状态...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 后端服务健康")
            print(f"   状态: {data['status']}")
            print("   服务状态:")
            for service, status in data['services'].items():
                print(f"     {service}: {status}")
            return True
        else:
            print(f"❌ 后端健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🔌 测试API端点...")
    
    # 测试根端点
    try:
        response = requests.get(f"{BACKEND_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ 根端点正常")
        else:
            print(f"❌ 根端点异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 根端点连接失败: {e}")
    
    # 测试NPCs端点
    try:
        response = requests.get(f"{BACKEND_URL}/npcs", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ NPCs端点正常，获取到 {len(data['npcs'])} 个NPC")
            for npc in data['npcs'][:2]:  # 显示前2个
                print(f"     - {npc['name']}: {npc['description']}")
        else:
            print(f"❌ NPCs端点异常: {response.status_code}")
    except Exception as e:
        print(f"❌ NPCs端点连接失败: {e}")
    
    # 测试MCP端点
    try:
        response = requests.get(f"{BACKEND_URL}/mcp/servers", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ MCP服务器端点正常，服务器数量: {len(data['servers'])}")
        else:
            print(f"❌ MCP服务器端点异常: {response.status_code}")
    except Exception as e:
        print(f"❌ MCP服务器端点连接失败: {e}")

def test_llm_service():
    """测试LLM服务"""
    print("\n🤖 测试LLM服务...")
    try:
        response = requests.get(f"{BACKEND_URL}/test", timeout=30)
        if response.status_code == 200:
            data = response.json()
            if "error" in data:
                print(f"⚠️  LLM服务有问题: {data['error']}")
                if "details" in data:
                    print(f"   详情: {data['details']}")
            else:
                print("✅ LLM服务正常")
                print(f"   问题: {data['question']}")
                print(f"   回答: {data['response']}")
                print(f"   数据库状态: {data['database_status']}")
        else:
            print(f"❌ LLM测试失败: {response.status_code}")
    except Exception as e:
        print(f"❌ LLM测试连接失败: {e}")

async def test_websocket_connection():
    """测试WebSocket连接"""
    print("\n🔌 测试WebSocket连接...")
    try:
        uri = f"{WEBSOCKET_URL}/test_user"
        async with websockets.connect(uri, timeout=10) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送开始会话消息
            start_message = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_message))
            print("📤 发送开始会话消息")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                data = json.loads(response)
                print(f"📥 收到响应: {data['type']}")
                if data['type'] == 'session_started':
                    print(f"   会话ID: {data['session_id']}")
                    print("✅ WebSocket会话创建成功")
                else:
                    print(f"   响应内容: {data}")
            except asyncio.TimeoutError:
                print("⚠️  WebSocket响应超时")
            
            # 发送结束会话消息
            end_message = {"type": "end_session"}
            await websocket.send(json.dumps(end_message))
            print("📤 发送结束会话消息")
            
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")

def test_audio_processing():
    """测试音频处理端点"""
    print("\n🎵 测试音频处理...")
    
    # 创建模拟音频数据 (1秒的正弦波)
    sample_rate = 16000
    duration = 1.0
    frequency = 440  # A4音符
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(2 * np.pi * frequency * t) * 0.5
    audio_bytes = (audio_data * 32767).astype(np.int16).tobytes()
    
    try:
        files = {'file': ('test_audio.wav', audio_bytes, 'audio/wav')}
        data = {'user_id': 1, 'npc_id': 1}
        
        response = requests.post(
            f"{BACKEND_URL}/process-audio", 
            files=files, 
            data=data, 
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if "error" in result:
                print(f"⚠️  音频处理有问题: {result['error']}")
            else:
                print("✅ 音频处理成功")
                print(f"   转录结果: {result.get('transcription', 'N/A')}")
                print(f"   回复文本: {result.get('response_text', 'N/A')}")
        else:
            print(f"❌ 音频处理失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 音频处理测试失败: {e}")

async def run_full_test():
    """运行完整测试"""
    print("🧪 开始全链路测试")
    print("=" * 50)
    
    # 基础连接测试
    if not test_backend_health():
        print("❌ 后端服务不可用，停止测试")
        return
    
    # API端点测试
    test_api_endpoints()
    
    # LLM服务测试
    test_llm_service()
    
    # WebSocket测试
    await test_websocket_connection()
    
    # 音频处理测试
    test_audio_processing()
    
    print("\n" + "=" * 50)
    print("🎉 全链路测试完成")
    print("=" * 50)

def main():
    """主函数"""
    try:
        asyncio.run(run_full_test())
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
