#!/usr/bin/env python3
"""
测试真实语音的ASR
"""

import sys
import os
import numpy as np
import logging
import requests

# 添加backend目录到路径
sys.path.append('backend')

from services.enhanced_asr_service import EnhancedASRService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_real_speech_asr():
    """测试真实语音ASR"""
    logger.info("=" * 60)
    logger.info("真实语音ASR测试")
    logger.info("=" * 60)
    
    # 1. 检查是否有真实的音频文件
    audio_files = [
        "/Users/<USER>/LocalRepo/20250729t163822/开拓路-3.m4a",
        "backend/audio_recordings/test.wav",
        "test_recording.wav"
    ]
    
    audio_file = None
    for file_path in audio_files:
        if os.path.exists(file_path):
            audio_file = file_path
            logger.info(f"✅ 找到音频文件: {audio_file}")
            break
    
    if not audio_file:
        logger.warning("⚠️ 没有找到真实音频文件，使用合成语音测试")
        # 生成一个模拟的"你好"语音信号
        audio_data = generate_speech_like_signal()
        sample_rate = 16000
    else:
        # 加载真实音频文件
        try:
            import librosa
            audio_data, sample_rate = librosa.load(audio_file, sr=16000, mono=True)
            logger.info(f"📊 加载音频: {len(audio_data)} 样本, {len(audio_data)/sample_rate:.2f}秒")
        except Exception as e:
            logger.error(f"❌ 加载音频文件失败: {e}")
            return
    
    # 2. 初始化ASR服务
    try:
        logger.info("初始化Enhanced ASR服务...")
        asr_service = EnhancedASRService(
            qwen_timeout=3,      # 短超时
            gemini_timeout=10,   # 短超时
            gemini_first=True    # Gemini优先
        )
        logger.info("✅ Enhanced ASR服务初始化成功")
    except Exception as e:
        logger.error(f"❌ Enhanced ASR服务初始化失败: {e}")
        return
    
    # 3. 测试ASR转录
    try:
        logger.info("开始真实语音ASR转录...")
        result = asr_service.transcribe(audio_data, sample_rate)
        
        logger.info("=" * 40)
        logger.info("🎉 ASR转录结果")
        logger.info("=" * 40)
        logger.info(f"✅ 成功: {result.get('success', False)}")
        logger.info(f"📝 识别文本: '{result.get('text', '')}'")
        logger.info(f"🎯 置信度: {result.get('confidence', 0.0):.3f}")
        logger.info(f"🔧 提供商: {result.get('provider', 'unknown')}")
        logger.info(f"⏱️ 时长: {result.get('duration', 0.0):.3f}秒")
        logger.info(f"🔤 词数: {result.get('tokens', 0)}")
        
        # 分析结果
        text = result.get('text', '')
        if text:
            if any(word in text for word in ['你好', '测试', '语音', '识别']):
                logger.info("🔍 分析: 识别结果包含常见中文词汇，可能是真实转录")
            else:
                logger.info(f"🔍 分析: 识别结果 '{text}' - 需要验证是否为真实转录")
        else:
            logger.info("🔍 分析: 没有识别出文本")
            
    except Exception as e:
        logger.error(f"❌ ASR转录失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

def generate_speech_like_signal() -> np.ndarray:
    """生成类似语音的信号"""
    sample_rate = 16000
    duration = 2.0
    
    # 生成多个频率的混合信号，模拟语音的频谱特征
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 基频和谐波（模拟人声）
    fundamental = 150  # 基频150Hz
    signal = np.sin(2 * np.pi * fundamental * t) * 0.3
    signal += np.sin(2 * np.pi * fundamental * 2 * t) * 0.2  # 二次谐波
    signal += np.sin(2 * np.pi * fundamental * 3 * t) * 0.1  # 三次谐波
    
    # 添加一些高频成分（模拟辅音）
    signal += np.sin(2 * np.pi * 2000 * t) * 0.1 * np.random.random(len(t))
    
    # 添加包络（模拟语音的音量变化）
    envelope = np.exp(-((t - duration/2) ** 2) / (2 * (duration/4) ** 2))
    signal *= envelope
    
    # 添加一些噪声
    signal += np.random.normal(0, 0.05, len(signal))
    
    return signal.astype(np.float32)

def test_backend_api():
    """测试后端API"""
    logger.info("=" * 60)
    logger.info("测试后端API")
    logger.info("=" * 60)
    
    try:
        # 生成测试音频
        audio_data = generate_speech_like_signal()
        
        # 转换为WAV字节
        import io
        import wave
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(16000)
            wav_file.writeframes(audio_int16.tobytes())
        
        audio_bytes = wav_buffer.getvalue()
        
        # 发送到后端
        files = {'file': ('test.wav', audio_bytes, 'audio/wav')}
        data = {'user_id': '1', 'npc_id': '1'}
        
        logger.info("📤 发送到后端API...")
        response = requests.post('http://localhost:8000/process-audio', files=files, data=data)
        
        logger.info(f"📥 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ 后端API成功!")
            logger.info(f"📝 ASR结果: '{result.get('transcription', '')}'")
            logger.info(f"🔧 ASR提供商: {result.get('asr_provider', 'N/A')}")
            logger.info(f"🧠 LLM回复: '{result.get('response_text', '')}'")
        else:
            logger.error(f"❌ 后端API失败: {response.text}")
            
    except Exception as e:
        logger.error(f"❌ 后端API测试失败: {e}")

if __name__ == "__main__":
    # 从.env文件加载环境变量
    from dotenv import load_dotenv
    load_dotenv('backend/.env')
    
    test_real_speech_asr()
    print()
    test_backend_api()