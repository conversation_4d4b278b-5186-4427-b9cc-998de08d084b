# import dashscope

# dashscope.api_key = "sk-c3aad9f9de37477b988fa95cef5edf98"
# messages = [
#     {
#         "role": "user",
#         "content": [
#             {"audio": "https://dashscope.oss-cn-beijing.aliyuncs.com/audios/welcome.mp3"},
#         ]
#     }
# ]
# response = dashscope.MultiModalConversation.call(
#     model="qwen-audio-asr",
#     messages=messages,
#     result_format="message")
# print(response)

# -----
import base64

import requests
from openai import OpenAI


openai_api_key = "EMPTY"
openai_api_base = "http://************:20257/v1"

client = OpenAI(
    # defaults to os.environ.get("OPENAI_API_KEY")
    api_key=openai_api_key,
    base_url=openai_api_base,
)
audio_url = "https://dashscope.oss-cn-beijing.aliyuncs.com/audios/welcome.mp3"

import time

start_time = time.time()

# HTTP URL with streaming
print("Starting streaming transcription...")
result = ""
for chunk in client.chat.completions.create(
    messages=[
        {
            "role": "user", 
            "content": [
                {"type": "text", "text": "音频说的是什么话?"},
                {
                    "type": "audio_url",
                    "audio_url": {
                        "url": audio_url
                    },
                },
            ],
        }
    ],
    model="Qwen2-Audio-7B-Instruct",
    max_completion_tokens=64,
    stream=True,
):
    content = chunk.choices[0].delta.content
    if content:
        result += content
        print(content, end="", flush=True)

end_time = time.time()
print("\nFinal transcription:", result)
print("elapsed time:", end_time-start_time)
