import sys
import os
import json
from dotenv import load_dotenv

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 加载环境变量
load_dotenv(os.path.join(os.path.dirname(__file__), 'backend', '.env'))

from utils.reranker_service import RerankerService

def test_reranker_service():
    """
    测试Reranker服务的功能
    """
    print("开始测试Reranker服务...")
    
    try:
        # 初始化Reranker服务
        reranker_service = RerankerService()
        print("✓ Reranker服务初始化成功")
        
        # 测试相关性计算
        print("\n1. 测试相关性计算...")
        query = "人工智能在医疗领域的应用"
        documents = [
            "人工智能在医疗影像诊断中的应用越来越广泛，能够提高诊断准确率",
            "机器学习算法在金融风控中的应用",
            "自然语言处理技术在智能客服中的应用",
            "AI辅助药物研发可以大大缩短新药上市时间",
            "量子计算是计算科学的一个前沿领域"
        ]
        
        results = reranker_service.calculate_relevance(query, documents, top_n=3)
        if results:
            print("   相关性计算结果:")
            for i, result in enumerate(results):
                print(f"   {i+1}. 文档: {result['document']['text'][:50]}...")
                print(f"      相关性得分: {result['relevance_score']}")
        else:
            print("   相关性计算失败")
        
        # 测试工具相关性排序
        print("\n2. 测试工具相关性排序...")
        user_question = "我想知道今天的天气怎么样"
        tool_descriptions = [
            {
                "name": "fetch_news",
                "description": "获取新闻。可以根据多种条件进行筛选和排序，比如获取用户已读的、未读的、最近读过的，或者根据特定主题进行搜索。"
            },
            {
                "name": "search_and_summarize",
                "description": "搜索并总结网络信息。当用户需要了解实时信息或超出个人知识范围的内容时使用，如天气、股票价格等。"
            },
            {
                "name": "recall_current_activity",
                "description": "回忆当前活动。用于获取角色当前的状态和正在进行的活动。"
            },
            {
                "name": "get_weather_info",
                "description": "获取天气信息。提供当前位置或指定位置的天气情况，包括温度、湿度、风力等。"
            }
        ]
        
        ranked_tools = reranker_service.rank_tools_by_relevance(user_question, tool_descriptions)
        if ranked_tools:
            print("   工具相关性排序结果:")
            for i, tool in enumerate(ranked_tools):
                print(f"   {i+1}. 工具名称: {tool['name']}")
                print(f"      相关性得分: {tool['relevance_score']}")
        else:
            print("   工具相关性排序失败")
        
        # 测试获取最相关的工具
        print("\n3. 测试获取最相关的工具...")
        top_tool = reranker_service.get_top_relevant_tool(user_question, tool_descriptions)
        if top_tool:
            print(f"   最相关的工具: {top_tool['name']}")
            print(f"   工具描述: {top_tool['description']}")
            print(f"   相关性得分: {top_tool['relevance_score']}")
        else:
            print("   获取最相关工具失败")
        
        # 测试在MasterService中的使用场景
        print("\n4. 测试在MasterService中的使用场景...")
        user_query = "给我讲个笑话吧"
        available_tools = [
            {
                "name": "fetch_news",
                "description": "获取新闻。可以根据多种条件进行筛选和排序，比如获取用户已读的、未读的、最近读过的，或者根据特定主题进行搜索。"
            },
            {
                "name": "search_and_summarize",
                "description": "搜索并总结网络信息。当用户需要了解实时信息或超出个人知识范围的内容时使用，如天气、股票价格等。"
            },
            {
                "name": "recall_current_activity",
                "description": "回忆当前活动。用于获取角色当前的状态和正在进行的活动。"
            },
            {
                "name": "tell_joke",
                "description": "讲笑话。当用户需要娱乐或放松时使用，可以提供各种类型的笑话。"
            }
        ]
        
        # 使用reranker服务来选择最合适的工具
        best_tool = reranker_service.get_top_relevant_tool(user_query, available_tools)
        if best_tool:
            print(f"   对于用户问题 '{user_query}'，推荐的工具是: {best_tool['name']}")
            print(f"   推荐理由: 相关性得分 {best_tool['relevance_score']:.4f}")
        else:
            print("   未能为用户问题推荐合适的工具")
        
        print("\n✓ 所有测试完成")
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_edge_cases():
    """
    测试边界情况
    """
    print("\n开始测试边界情况...")
    
    try:
        reranker_service = RerankerService()
        
        # 测试空文档列表
        print("\n1. 测试空文档列表...")
        results = reranker_service.calculate_relevance("测试查询", [])
        if results is not None and len(results) == 0:
            print("   ✓ 空文档列表处理正确")
        else:
            print("   ✗ 空文档列表处理不正确")
        
        # 测试单个文档
        print("\n2. 测试单个文档...")
        results = reranker_service.calculate_relevance("测试查询", ["这是一个测试文档"])
        if results is not None and len(results) == 1:
            print("   ✓ 单个文档处理正确")
        else:
            print("   ✗ 单个文档处理不正确")
        
        # 测试空工具列表
        print("\n3. 测试空工具列表...")
        result = reranker_service.get_top_relevant_tool("测试问题", [])
        if result is None:
            print("   ✓ 空工具列表处理正确")
        else:
            print("   ✗ 空工具列表处理不正确")
        
        print("\n✓ 边界情况测试完成")
        
    except Exception as e:
        print(f"✗ 边界情况测试过程中出现错误: {e}")

if __name__ == "__main__":
    test_reranker_service()
    test_edge_cases()
