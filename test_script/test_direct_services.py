#!/usr/bin/env python3
"""
直接测试ASR、LLM、TTS服务
不通过WebSocket接口，直接调用各个服务
"""
import asyncio
import numpy as np
import wave
import logging
import os
import sys
from pathlib import Path
from datetime import datetime
import json

# 添加backend目录到Python路径
sys.path.append('backend')

from services.multimodal_asr_service import MultimodalASRService
from services.llm_service import LLMService
from services.tts_service import TTSService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DirectServiceTester:
    def __init__(self):
        self.test_results = {
            'test_time': datetime.now().isoformat(),
            'services': {},
            'pipeline_test': {},
            'issues': []
        }
        
        # 初始化服务
        self.init_services()
    
    def init_services(self):
        """初始化各个服务"""
        logger.info("🔧 初始化服务...")
        
        try:
            # ASR服务
            self.asr_service = MultimodalASRService(
                model="Qwen2-Audio-7B-Instruct",
                api_key="EMPTY",
                api_base="http://************:20257/v1"
            )
            logger.info("✅ ASR服务初始化完成")
            
            # LLM服务
            volcano_api_key = os.getenv("VOLCANO_API_KEY")
            volcano_endpoint = os.getenv("VOLCANO_ENDPOINT")
            
            if volcano_api_key and volcano_endpoint:
                self.llm_service = LLMService(volcano_api_key, volcano_endpoint)
                logger.info("✅ LLM服务初始化完成")
            else:
                logger.warning("⚠️ LLM服务环境变量未设置，将使用模拟响应")
                self.llm_service = LLMService("mock_key", "mock_endpoint")
            
            # TTS服务
            minimax_api_key = os.getenv("MINIMAX_API_KEY")
            minimax_group_id = os.getenv("MINIMAX_GROUP_ID")
            
            if minimax_api_key and minimax_group_id:
                self.tts_service = TTSService(minimax_api_key, minimax_group_id)
                logger.info("✅ TTS服务初始化完成")
            else:
                logger.warning("⚠️ TTS服务环境变量未设置，将使用模拟响应")
                self.tts_service = TTSService("mock_key", "mock_group")
                
        except Exception as e:
            logger.error(f"❌ 服务初始化失败: {e}")
            raise
    
    def generate_test_audio(self, duration=2.0, frequency=440, sample_rate=16000):
        """生成测试音频"""
        logger.info(f"🎵 生成测试音频: {duration}秒, {frequency}Hz, {sample_rate}Hz采样率")
        
        # 生成正弦波
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = (np.sin(2 * np.pi * frequency * t) * 0.8).astype(np.float32)
        
        # 保存为WAV文件用于测试
        test_audio_path = Path("test_direct_audio.wav")
        with wave.open(str(test_audio_path), 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            # 转换为int16
            audio_int16 = (audio_data * 32767).astype(np.int16)
            wav_file.writeframes(audio_int16.tobytes())
        
        logger.info(f"📁 测试音频已保存: {test_audio_path}")
        return audio_data, test_audio_path
    
    def test_asr_service(self):
        """测试ASR服务"""
        logger.info("🎯 测试ASR服务...")
        start_time = datetime.now()
        
        try:
            # 生成测试音频
            audio_data, audio_path = self.generate_test_audio()
            
            # 调用ASR服务
            logger.info("📤 调用ASR服务进行转录...")
            result = self.asr_service.transcribe(audio_data)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 记录结果
            self.test_results['services']['asr'] = {
                'status': 'success' if result.get('text') else 'failed',
                'duration': duration,
                'result': result,
                'audio_samples': len(audio_data),
                'audio_file': str(audio_path)
            }
            
            if result.get('text'):
                logger.info(f"✅ ASR测试成功 ({duration:.2f}s)")
                logger.info(f"   转录结果: '{result['text']}'")
                logger.info(f"   置信度: {result.get('confidence', 'N/A')}")
                logger.info(f"   是否为fallback: {result.get('fallback', False)}")
                return result
            else:
                logger.error(f"❌ ASR测试失败: 没有转录结果")
                return None
                
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error(f"❌ ASR测试异常 ({duration:.2f}s): {e}")
            self.test_results['services']['asr'] = {
                'status': 'error',
                'duration': duration,
                'error': str(e)
            }
            self.test_results['issues'].append({
                'service': 'asr',
                'error': str(e)
            })
            return None
    
    async def test_llm_service(self, input_text="你好，我想测试一下语音识别功能"):
        """测试LLM服务"""
        logger.info("🧠 测试LLM服务...")
        start_time = datetime.now()
        
        try:
            # 准备对话历史和系统提示
            conversation_history = []
            system_prompt = """你是一个友好的AI助手。请用中文回答用户的问题。
请在回答中使用以下格式：
<SPEAK><emotion>friendly</emotion><speed>1.0</speed><text>你的回答内容</text></SPEAK>"""
            
            logger.info(f"📤 调用LLM服务，输入: '{input_text}'")
            
            # 调用LLM服务
            result = await self.llm_service.generate_response(
                input_text, 
                conversation_history, 
                system_prompt
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 记录结果
            self.test_results['services']['llm'] = {
                'status': 'success' if result.get('success') else 'failed',
                'duration': duration,
                'result': result,
                'input_text': input_text
            }
            
            if result.get('success'):
                logger.info(f"✅ LLM测试成功 ({duration:.2f}s)")
                logger.info(f"   完整响应: {result.get('full_response', '')[:100]}...")
                
                speak_content = result.get('speak_content', {})
                logger.info(f"   提取的语音内容: '{speak_content.get('text', '')}'")
                logger.info(f"   情感: {speak_content.get('emotion', 'N/A')}")
                logger.info(f"   语速: {speak_content.get('speed', 'N/A')}")
                logger.info(f"   是否为模拟模式: {result.get('mock_mode', False)}")
                
                return result
            else:
                logger.error(f"❌ LLM测试失败: {result}")
                return None
                
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error(f"❌ LLM测试异常 ({duration:.2f}s): {e}")
            self.test_results['services']['llm'] = {
                'status': 'error',
                'duration': duration,
                'error': str(e)
            }
            self.test_results['issues'].append({
                'service': 'llm',
                'error': str(e)
            })
            return None
    
    async def test_tts_service(self, text="你好，这是一个TTS测试"):
        """测试TTS服务"""
        logger.info("🔊 测试TTS服务...")
        start_time = datetime.now()
        
        try:
            logger.info(f"📤 调用TTS服务，文本: '{text}'")
            
            # 调用TTS服务
            result = await self.tts_service.synthesize_speech(
                text=text,
                emotion="friendly",
                speed=1.0,
                output_file="test_direct_tts_output.wav"
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 记录结果
            self.test_results['services']['tts'] = {
                'status': 'success' if result.get('success') else 'failed',
                'duration': duration,
                'result': result,
                'input_text': text
            }
            
            if result.get('success'):
                logger.info(f"✅ TTS测试成功 ({duration:.2f}s)")
                logger.info(f"   音频大小: {result.get('size', 0)} bytes")
                logger.info(f"   输出文件: {result.get('file_path', 'N/A')}")
                logger.info(f"   语音ID: {result.get('voice_id', 'N/A')}")
                
                # 验证音频文件
                output_file = result.get('file_path')
                if output_file and Path(output_file).exists():
                    file_size = Path(output_file).stat().st_size
                    logger.info(f"   文件验证: {file_size} bytes")
                
                return result
            else:
                logger.error(f"❌ TTS测试失败: {result.get('error', 'Unknown error')}")
                return None
                
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error(f"❌ TTS测试异常 ({duration:.2f}s): {e}")
            self.test_results['services']['tts'] = {
                'status': 'error',
                'duration': duration,
                'error': str(e)
            }
            self.test_results['issues'].append({
                'service': 'tts',
                'error': str(e)
            })
            return None
    
    async def test_full_pipeline(self):
        """测试完整流水线"""
        logger.info("🚀 测试完整语音流水线...")
        pipeline_start_time = datetime.now()
        
        try:
            # 1. ASR阶段
            logger.info("=" * 50)
            logger.info("阶段1: ASR语音识别")
            asr_result = self.test_asr_service()
            
            if not asr_result or not asr_result.get('text'):
                logger.error("❌ ASR阶段失败，停止流水线测试")
                return False
            
            transcribed_text = asr_result['text']
            
            # 2. LLM阶段
            logger.info("=" * 50)
            logger.info("阶段2: LLM对话生成")
            llm_result = await self.test_llm_service(transcribed_text)
            
            if not llm_result or not llm_result.get('success'):
                logger.error("❌ LLM阶段失败，停止流水线测试")
                return False
            
            speak_content = llm_result.get('speak_content', {})
            response_text = speak_content.get('text', llm_result.get('full_response', ''))
            
            # 3. TTS阶段
            logger.info("=" * 50)
            logger.info("阶段3: TTS语音合成")
            tts_result = await self.test_tts_service(response_text)
            
            if not tts_result or not tts_result.get('success'):
                logger.error("❌ TTS阶段失败")
                return False
            
            # 计算总时间
            pipeline_end_time = datetime.now()
            total_duration = (pipeline_end_time - pipeline_start_time).total_seconds()
            
            # 记录流水线结果
            self.test_results['pipeline_test'] = {
                'status': 'success',
                'total_duration': total_duration,
                'stages': {
                    'asr': {
                        'input': 'Generated sine wave audio',
                        'output': transcribed_text,
                        'duration': self.test_results['services']['asr']['duration']
                    },
                    'llm': {
                        'input': transcribed_text,
                        'output': response_text,
                        'duration': self.test_results['services']['llm']['duration']
                    },
                    'tts': {
                        'input': response_text,
                        'output': tts_result.get('file_path', 'Audio file'),
                        'duration': self.test_results['services']['tts']['duration']
                    }
                }
            }
            
            logger.info("=" * 50)
            logger.info("🎉 完整流水线测试成功！")
            logger.info(f"   总耗时: {total_duration:.2f}秒")
            logger.info(f"   ASR: {transcribed_text}")
            logger.info(f"   LLM: {response_text[:50]}...")
            logger.info(f"   TTS: {tts_result.get('size', 0)} bytes音频")
            
            return True
            
        except Exception as e:
            pipeline_end_time = datetime.now()
            total_duration = (pipeline_end_time - pipeline_start_time).total_seconds()
            
            logger.error(f"❌ 流水线测试异常 ({total_duration:.2f}s): {e}")
            self.test_results['pipeline_test'] = {
                'status': 'error',
                'total_duration': total_duration,
                'error': str(e)
            }
            return False
    
    def save_test_report(self):
        """保存测试报告"""
        report_file = f"direct_services_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 测试报告已保存: {report_file}")
        return report_file
    
    def print_summary(self):
        """打印测试总结"""
        logger.info("=" * 60)
        logger.info("📊 直接服务测试总结")
        logger.info("=" * 60)
        
        # 服务测试结果
        for service, result in self.test_results['services'].items():
            status = result.get('status', 'unknown')
            duration = result.get('duration', 0)
            
            if status == 'success':
                logger.info(f"✅ {service.upper()}: 成功 ({duration:.2f}s)")
            elif status == 'failed':
                logger.info(f"❌ {service.upper()}: 失败 ({duration:.2f}s)")
            else:
                logger.info(f"⚠️ {service.upper()}: 错误 ({duration:.2f}s)")
        
        # 流水线测试结果
        pipeline = self.test_results.get('pipeline_test', {})
        if pipeline:
            status = pipeline.get('status', 'unknown')
            duration = pipeline.get('total_duration', 0)
            
            if status == 'success':
                logger.info(f"🎉 完整流水线: 成功 ({duration:.2f}s)")
            else:
                logger.info(f"❌ 完整流水线: 失败 ({duration:.2f}s)")
        
        # 问题列表
        if self.test_results['issues']:
            logger.info("\n🔍 发现的问题:")
            for issue in self.test_results['issues']:
                logger.info(f"   - {issue['service']}: {issue['error']}")
        
        logger.info("=" * 60)

async def main():
    """主函数"""
    logger.info("🧪 直接服务测试")
    logger.info("🎯 目标: 直接测试ASR、LLM、TTS服务的连通性")
    logger.info("=" * 60)
    
    try:
        # 创建测试器
        tester = DirectServiceTester()
        
        # 运行完整流水线测试
        success = await tester.test_full_pipeline()
        
        # 打印总结
        tester.print_summary()
        
        # 保存报告
        report_file = tester.save_test_report()
        
        if success:
            logger.info("🎉 所有测试通过！")
            logger.info("💡 各个服务都能正常工作")
        else:
            logger.error("❌ 部分测试失败！")
            logger.info("🔧 请检查失败的服务配置")
        
        logger.info(f"📄 详细报告: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main())