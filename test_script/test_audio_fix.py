#!/usr/bin/env python3
"""
测试脚本，用于验证前后端音频传输修复
"""

import asyncio
import websockets
import json
import base64
import numpy as np
import time
from datetime import datetime

# 测试WebSocket连接
async def test_websocket_connection():
    """测试WebSocket连接和基本通信"""
    uri = "ws://localhost:8000/ws/1"  # 使用数字ID而不是字符串
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送开始会话消息
            start_session_msg = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_session_msg))
            print("📤 发送开始会话消息")
            
            # 等待响应
            response = await websocket.recv()
            response_data = json.loads(response)
            print(f"📥 收到响应: {response_data}")
            
            if response_data.get("type") == "session_started":
                print("✅ 会话开始成功")
                return True
            else:
                print("❌ 会话开始失败")
                return False
                
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")
        return False

# 测试音频数据传输
async def test_audio_transmission():
    """测试音频数据传输"""
    uri = "ws://localhost:8000/ws/1"  # 使用数字ID而不是字符串
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送开始会话消息
            start_session_msg = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_session_msg))
            print("📤 发送开始会话消息")
            
            # 等待会话开始响应
            response = await websocket.recv()
            response_data = json.loads(response)
            print(f"📥 收到会话响应: {response_data}")
            
            if response_data.get("type") != "session_started":
                print("❌ 无法开始会话")
                return False
            
            # 生成测试音频数据
            print("🎵 生成测试音频数据...")
            # 生成1秒的16位PCM音频数据 (16kHz采样率)
            sample_rate = 16000
            duration = 1.0  # 1秒
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            # 生成440Hz的正弦波作为测试音频
            audio_data = np.sin(2 * np.pi * 440 * t)
            # 转换为16位整数
            audio_int16 = (audio_data * 32767).astype(np.int16)
            # 转换为字节
            audio_bytes = audio_int16.tobytes()
            
            print(f"📊 音频数据大小: {len(audio_bytes)} 字节")
            
            # 发送音频数据块
            chunk_size = 1600  # 100ms的音频数据
            for i in range(0, len(audio_bytes), chunk_size):
                chunk = audio_bytes[i:i+chunk_size]
                base64_chunk = base64.b64encode(chunk).decode('utf-8')
                
                audio_msg = {
                    "type": "audio_chunk",
                    "data": base64_chunk
                }
                
                await websocket.send(json.dumps(audio_msg))
                print(f"📤 发送音频块 {i//chunk_size + 1}: {len(chunk)} 字节")
                
                # 等待一小段时间再发送下一个块
                await asyncio.sleep(0.1)
            
            print("✅ 音频数据发送完成")
            
            # 等待一段时间接收响应
            print("⏳ 等待服务器响应...")
            try:
                for i in range(10):  # 最多等待10次
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    response_data = json.loads(response)
                    print(f"📥 收到响应: {response_data}")
                    
                    if response_data.get("type") == "transcription":
                        print("✅ 收到转录结果")
                    elif response_data.get("type") == "audio_chunk":
                        print("✅ 收到音频响应")
                    elif response_data.get("type") == "response_complete":
                        print("✅ 响应完成")
                        break
                        
            except asyncio.TimeoutError:
                print("⏰ 等待响应超时")
            
            return True
            
    except Exception as e:
        print(f"❌ 音频传输测试失败: {e}")
        return False

# 测试HTTP端点
async def test_http_endpoint():
    """测试HTTP端点"""
    import aiohttp
    
    try:
        # 生成测试音频数据
        sample_rate = 16000
        duration = 1.0
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * 440 * t)
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        # 保存为WAV格式
        import wave
        with wave.open('test_audio.wav', 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(16000)
            wav_file.writeframes(audio_int16.tobytes())
        
        # 读取WAV文件
        with open('test_audio.wav', 'rb') as f:
            audio_content = f.read()
        
        print(f"🎵 测试音频文件大小: {len(audio_content)} 字节")
        
        # 发送到HTTP端点
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:8000/process-audio',
                data={
                    'file': audio_content,
                    'user_id': '1',
                    'npc_id': '1'
                }
            ) as response:
                result = await response.json()
                print(f"📥 HTTP端点响应: {result}")
                
                if 'transcription' in result:
                    print("✅ HTTP端点测试成功")
                    return True
                else:
                    print("❌ HTTP端点测试失败")
                    return False
                    
    except Exception as e:
        print(f"❌ HTTP端点测试失败: {e}")
        return False

# 测试音频格式解析
def test_audio_parsing():
    """测试音频格式解析"""
    print("🎵 测试音频格式解析...")
    
    # 生成测试数据
    test_data = bytes([i % 256 for i in range(1000)])
    print(f"📊 测试数据大小: {len(test_data)} 字节")
    
    # 尝试解析为int16
    try:
        if len(test_data) % 2 == 0:
            audio_array = np.frombuffer(test_data, dtype=np.int16).astype(np.float32) / 32768.0
            print(f"✅ int16解析成功: shape={audio_array.shape}")
        else:
            print("⚠️ 数据长度不是2的倍数，跳过int16解析")
    except Exception as e:
        print(f"❌ int16解析失败: {e}")
    
    # 尝试解析为uint8
    try:
        audio_uint8 = np.frombuffer(test_data, dtype=np.uint8)
        audio_array = (audio_uint8.astype(np.float32) - 128.0) / 128.0
        print(f"✅ uint8解析成功: shape={audio_array.shape}")
    except Exception as e:
        print(f"❌ uint8解析失败: {e}")
    
    # 尝试解析为float32
    try:
        if len(test_data) % 4 == 0:
            audio_array = np.frombuffer(test_data, dtype=np.float32)
            print(f"✅ float32解析成功: shape={audio_array.shape}")
        else:
            print("⚠️ 数据长度不是4的倍数，跳过float32解析")
    except Exception as e:
        print(f"❌ float32解析失败: {e}")

async def main():
    """主测试函数"""
    print("=" * 50)
    print("🎙️  前后端音频传输修复测试")
    print("=" * 50)
    print(f"🕒 开始时间: {datetime.now()}")
    print()
    
    # 测试音频格式解析
    test_audio_parsing()
    print()
    
    # 测试WebSocket连接
    print("🔗 测试WebSocket连接...")
    websocket_success = await test_websocket_connection()
    print()
    
    if websocket_success:
        # 测试音频传输
        print("🔊 测试音频数据传输...")
        audio_success = await test_audio_transmission()
        print()
    
    # 测试HTTP端点
    print("🌐 测试HTTP端点...")
    http_success = await test_http_endpoint()
    print()
    
    # 总结
    print("=" * 50)
    print("📊 测试结果总结:")
    print(f"   WebSocket连接: {'✅ 成功' if websocket_success else '❌ 失败'}")
    print(f"   音频传输: {'✅ 成功' if websocket_success and audio_success else '❌ 失败'}")
    print(f"   HTTP端点: {'✅ 成功' if http_success else '❌ 失败'}")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
