#!/usr/bin/env python3
"""
使用模拟服务测试完整流水线
为了验证流程，所有服务都使用模拟响应
"""
import asyncio
import numpy as np
import wave
import logging
import os
import sys
from pathlib import Path
from datetime import datetime
import json
import base64

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockASRService:
    """模拟ASR服务"""
    def __init__(self):
        self.responses = [
            "你好，我想测试一下语音识别功能",
            "这是一个测试音频，请回复我",
            "请问你能听到我说话吗？",
            "我正在测试完整的语音对话流程",
            "希望这个测试能够成功运行"
        ]
        self.call_count = 0
    
    def transcribe(self, audio_data):
        """模拟转录功能"""
        import random
        
        # 模拟处理时间
        import time
        time.sleep(0.1)
        
        selected_text = random.choice(self.responses)
        self.call_count += 1
        
        logger.info(f"🎯 Mock ASR: '{selected_text}'")
        
        return {
            "text": selected_text,
            "confidence": 0.9,
            "tokens": [],
            "duration": len(audio_data) / 16000 if hasattr(audio_data, '__len__') else 2.0,
            "mock": True
        }

class MockLLMService:
    """模拟LLM服务"""
    def __init__(self):
        self.responses = {
            "你好": "你好！很高兴见到你！我是你的AI助手，有什么可以帮助你的吗？",
            "测试": "测试成功！系统运行正常，所有功能都在正常工作。",
            "请问你能听到我说话吗": "是的，我能听到你说话！语音识别功能工作正常，我们可以进行语音对话了。",
            "语音识别": "语音识别功能已经成功运行，现在我们可以通过语音进行交流了。",
            "流程": "完整的语音对话流程包括：录音→语音识别→对话生成→语音合成→播放，现在所有环节都在正常工作。"
        }
    
    async def generate_response(self, user_input, conversation_history, system_prompt):
        """模拟生成响应"""
        # 模拟处理时间
        await asyncio.sleep(0.2)
        
        # 查找最匹配的响应
        response_text = None
        for key, value in self.responses.items():
            if key in user_input or any(word in user_input for word in key.split()):
                response_text = value
                break
        
        if not response_text:
            response_text = f"我收到了你的消息：{user_input}。这是一个智能回复，表明语音对话系统正在正常工作。"
        
        logger.info(f"🧠 Mock LLM: '{response_text}'")
        
        return {
            "success": True,
            "full_response": response_text,
            "speak_content": {
                "emotion": "friendly",
                "speed": 1.0,
                "text": response_text
            },
            "mock_mode": True
        }

class MockTTSService:
    """模拟TTS服务"""
    def __init__(self):
        pass
    
    async def synthesize_speech(self, text, emotion="neutral", speed=1.0, output_file=None):
        """模拟语音合成"""
        # 模拟处理时间
        await asyncio.sleep(0.3)
        
        # 生成模拟音频数据 (1秒的440Hz正弦波)
        sample_rate = 16000
        duration = min(len(text) * 0.1, 3.0)  # 根据文本长度估算时长
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = (np.sin(2 * np.pi * 440 * t) * 0.5).astype(np.float32)
        
        # 转换为WAV格式的字节数据
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        # 创建WAV文件数据
        import io
        buffer = io.BytesIO()
        with wave.open(buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        audio_bytes = buffer.getvalue()
        
        # 保存到文件（如果指定）
        if output_file:
            with open(output_file, 'wb') as f:
                f.write(audio_bytes)
        
        logger.info(f"🔊 Mock TTS: {len(audio_bytes)} bytes, {duration:.1f}s, '{text[:30]}...'")
        
        return {
            "success": True,
            "audio_data": audio_bytes,
            "file_path": output_file,
            "size": len(audio_bytes),
            "text": text,
            "emotion": emotion,
            "speed": speed,
            "duration": duration,
            "mock": True
        }

class MockPipelineTester:
    """模拟服务流水线测试器"""
    def __init__(self):
        self.asr_service = MockASRService()
        self.llm_service = MockLLMService()
        self.tts_service = MockTTSService()
        
        self.test_results = {
            'test_time': datetime.now().isoformat(),
            'mode': 'mock_services',
            'stages': {},
            'performance': {},
            'pipeline_success': False
        }
    
    def generate_test_audio(self, duration=2.0, frequency=440, sample_rate=16000):
        """生成测试音频"""
        logger.info(f"🎵 生成测试音频: {duration}秒, {frequency}Hz")
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = (np.sin(2 * np.pi * frequency * t) * 0.8).astype(np.float32)
        
        # 保存测试音频
        test_audio_path = Path("test_mock_input_audio.wav")
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        with wave.open(str(test_audio_path), 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        logger.info(f"📁 测试音频已保存: {test_audio_path}")
        return audio_data, test_audio_path
    
    async def test_stage_asr(self, audio_data):
        """测试ASR阶段"""
        logger.info("🎯 阶段1: ASR语音识别")
        start_time = datetime.now()
        
        try:
            result = self.asr_service.transcribe(audio_data)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.test_results['stages']['asr'] = {
                'status': 'success',
                'duration': duration,
                'input_samples': len(audio_data),
                'output_text': result['text'],
                'confidence': result['confidence']
            }
            
            logger.info(f"✅ ASR完成 ({duration:.3f}s): '{result['text']}'")
            return result
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.test_results['stages']['asr'] = {
                'status': 'error',
                'duration': duration,
                'error': str(e)
            }
            
            logger.error(f"❌ ASR失败 ({duration:.3f}s): {e}")
            return None
    
    async def test_stage_llm(self, transcription_text):
        """测试LLM阶段"""
        logger.info("🧠 阶段2: LLM对话生成")
        start_time = datetime.now()
        
        try:
            result = await self.llm_service.generate_response(
                transcription_text, 
                [], 
                "你是一个友好的AI助手"
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            speak_content = result.get('speak_content', {})
            response_text = speak_content.get('text', '')
            
            self.test_results['stages']['llm'] = {
                'status': 'success',
                'duration': duration,
                'input_text': transcription_text,
                'output_text': response_text,
                'emotion': speak_content.get('emotion', 'neutral'),
                'speed': speak_content.get('speed', 1.0)
            }
            
            logger.info(f"✅ LLM完成 ({duration:.3f}s): '{response_text[:50]}...'")
            return result
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.test_results['stages']['llm'] = {
                'status': 'error',
                'duration': duration,
                'error': str(e)
            }
            
            logger.error(f"❌ LLM失败 ({duration:.3f}s): {e}")
            return None
    
    async def test_stage_tts(self, response_text, emotion="friendly", speed=1.0):
        """测试TTS阶段"""
        logger.info("🔊 阶段3: TTS语音合成")
        start_time = datetime.now()
        
        try:
            result = await self.tts_service.synthesize_speech(
                response_text,
                emotion=emotion,
                speed=speed,
                output_file="test_mock_tts_output.wav"
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.test_results['stages']['tts'] = {
                'status': 'success',
                'duration': duration,
                'input_text': response_text,
                'output_file': result.get('file_path'),
                'audio_size': result.get('size', 0),
                'audio_duration': result.get('duration', 0)
            }
            
            logger.info(f"✅ TTS完成 ({duration:.3f}s): {result.get('size', 0)} bytes")
            return result
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.test_results['stages']['tts'] = {
                'status': 'error',
                'duration': duration,
                'error': str(e)
            }
            
            logger.error(f"❌ TTS失败 ({duration:.3f}s): {e}")
            return None
    
    async def test_full_pipeline(self):
        """测试完整流水线"""
        logger.info("🚀 开始完整模拟流水线测试...")
        pipeline_start_time = datetime.now()
        
        try:
            # 生成测试音频
            audio_data, audio_path = self.generate_test_audio()
            
            # 阶段1: ASR
            logger.info("=" * 50)
            asr_result = await self.test_stage_asr(audio_data)
            if not asr_result:
                return False
            
            # 阶段2: LLM
            logger.info("=" * 50)
            llm_result = await self.test_stage_llm(asr_result['text'])
            if not llm_result:
                return False
            
            # 阶段3: TTS
            logger.info("=" * 50)
            speak_content = llm_result.get('speak_content', {})
            tts_result = await self.test_stage_tts(
                speak_content.get('text', ''),
                speak_content.get('emotion', 'friendly'),
                speak_content.get('speed', 1.0)
            )
            if not tts_result:
                return False
            
            # 计算总体性能
            pipeline_end_time = datetime.now()
            total_duration = (pipeline_end_time - pipeline_start_time).total_seconds()
            
            self.test_results['performance'] = {
                'total_duration': total_duration,
                'asr_duration': self.test_results['stages']['asr']['duration'],
                'llm_duration': self.test_results['stages']['llm']['duration'],
                'tts_duration': self.test_results['stages']['tts']['duration'],
                'pipeline_success': True
            }
            
            self.test_results['pipeline_success'] = True
            
            logger.info("=" * 50)
            logger.info("🎉 完整模拟流水线测试成功！")
            logger.info(f"   总耗时: {total_duration:.3f}秒")
            logger.info(f"   ASR耗时: {self.test_results['stages']['asr']['duration']:.3f}秒")
            logger.info(f"   LLM耗时: {self.test_results['stages']['llm']['duration']:.3f}秒")
            logger.info(f"   TTS耗时: {self.test_results['stages']['tts']['duration']:.3f}秒")
            
            return True
            
        except Exception as e:
            pipeline_end_time = datetime.now()
            total_duration = (pipeline_end_time - pipeline_start_time).total_seconds()
            
            logger.error(f"❌ 流水线测试异常: {e}")
            self.test_results['performance'] = {
                'total_duration': total_duration,
                'pipeline_success': False,
                'error': str(e)
            }
            
            return False
    
    def print_detailed_results(self):
        """打印详细结果"""
        logger.info("=" * 60)
        logger.info("📊 详细测试结果")
        logger.info("=" * 60)
        
        # 流水线概览
        if self.test_results['pipeline_success']:
            logger.info("🎉 流水线状态: 全部成功")
        else:
            logger.info("❌ 流水线状态: 失败")
        
        # 各阶段详情
        for stage_name, stage_data in self.test_results['stages'].items():
            status = stage_data.get('status', 'unknown')
            duration = stage_data.get('duration', 0)
            
            logger.info(f"\n📋 {stage_name.upper()}阶段:")
            logger.info(f"   状态: {'✅ 成功' if status == 'success' else '❌ 失败'}")
            logger.info(f"   耗时: {duration:.3f}秒")
            
            if stage_name == 'asr':
                logger.info(f"   输入: {stage_data.get('input_samples', 0)} 音频样本")
                logger.info(f"   输出: '{stage_data.get('output_text', '')}'")
                logger.info(f"   置信度: {stage_data.get('confidence', 0)}")
                
            elif stage_name == 'llm':
                logger.info(f"   输入: '{stage_data.get('input_text', '')}'")
                logger.info(f"   输出: '{stage_data.get('output_text', '')[:50]}...'")
                logger.info(f"   情感: {stage_data.get('emotion', 'N/A')}")
                logger.info(f"   语速: {stage_data.get('speed', 'N/A')}")
                
            elif stage_name == 'tts':
                logger.info(f"   输入: '{stage_data.get('input_text', '')[:30]}...'")
                logger.info(f"   输出: {stage_data.get('audio_size', 0)} bytes音频")
                logger.info(f"   文件: {stage_data.get('output_file', 'N/A')}")
                logger.info(f"   时长: {stage_data.get('audio_duration', 0):.1f}秒")
        
        # 性能统计
        perf = self.test_results.get('performance', {})
        if perf:
            logger.info(f"\n⚡ 性能统计:")
            logger.info(f"   总耗时: {perf.get('total_duration', 0):.3f}秒")
            logger.info(f"   ASR占比: {(perf.get('asr_duration', 0) / perf.get('total_duration', 1) * 100):.1f}%")
            logger.info(f"   LLM占比: {(perf.get('llm_duration', 0) / perf.get('total_duration', 1) * 100):.1f}%")
            logger.info(f"   TTS占比: {(perf.get('tts_duration', 0) / perf.get('total_duration', 1) * 100):.1f}%")
        
        logger.info("=" * 60)
    
    def save_test_report(self):
        """保存测试报告"""
        report_file = f"mock_pipeline_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 测试报告已保存: {report_file}")
        return report_file

async def main():
    """主函数"""
    logger.info("🎭 模拟服务完整流水线测试")
    logger.info("🎯 目标: 验证完整的录音→ASR→LLM→TTS→播放流程")
    logger.info("=" * 60)
    
    try:
        # 创建测试器
        tester = MockPipelineTester()
        
        # 运行完整流水线测试
        success = await tester.test_full_pipeline()
        
        # 打印详细结果
        tester.print_detailed_results()
        
        # 保存报告
        report_file = tester.save_test_report()
        
        if success:
            logger.info("🎉 模拟流水线测试全部通过！")
            logger.info("💡 流程逻辑正确，可以替换为真实服务")
            logger.info("🔧 建议: 逐个配置真实的ASR、LLM、TTS服务")
        else:
            logger.error("❌ 模拟流水线测试失败！")
            logger.info("🔧 请检查代码逻辑")
        
        logger.info(f"📄 详细报告: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main())