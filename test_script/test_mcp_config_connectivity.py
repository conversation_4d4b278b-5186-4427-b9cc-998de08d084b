#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试MCP配置文件联通性的脚本
该脚本将测试backend/mcp_config/目录下所有JSON文件的联通性
"""

import sys
import os
import json
import glob
import logging

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_mcp_config(config_path):
    """
    加载MCP配置文件
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                logger.warning(f"配置文件 {config_path} 为空")
                return None
            return json.loads(content)
    except json.JSONDecodeError as e:
        logger.error(f"配置文件 {config_path} JSON格式错误: {e}")
        return None
    except Exception as e:
        logger.error(f"无法加载配置文件 {config_path}: {e}")
        return None

def validate_mcp_config(config_data, config_name):
    """
    验证MCP配置数据的结构
    """
    if not config_data:
        logger.error(f"配置数据为空: {config_name}")
        return False
    
    if "mcpServers" not in config_data:
        logger.error(f"配置文件 {config_name} 缺少 'mcpServers' 字段")
        return False
    
    mcp_servers = config_data["mcpServers"]
    if not isinstance(mcp_servers, dict):
        logger.error(f"配置文件 {config_name} 中 'mcpServers' 应该是字典类型")
        return False
    
    for server_name, server_config in mcp_servers.items():
        if not isinstance(server_config, dict):
            logger.error(f"配置文件 {config_name} 中服务器 {server_name} 配置应该是字典类型")
            return False
        
        if "command" not in server_config:
            logger.error(f"配置文件 {config_name} 中服务器 {server_name} 缺少 'command' 字段")
            return False
        
        if "args" in server_config and not isinstance(server_config["args"], list):
            logger.error(f"配置文件 {config_name} 中服务器 {server_name} 的 'args' 应该是列表类型")
            return False
        
        if "env" in server_config and not isinstance(server_config["env"], dict):
            logger.error(f"配置文件 {config_name} 中服务器 {server_name} 的 'env' 应该是字典类型")
            return False
    
    return True

def test_mcp_config_connectivity():
    """
    测试MCP配置文件联通性
    """
    logger.info("开始测试MCP配置文件联通性...")
    
    try:
        # 1. 查找所有MCP配置文件
        logger.info("查找MCP配置文件...")
        mcp_config_dir = os.path.join(os.path.dirname(__file__), 'backend', 'mcp_config')
        config_files = glob.glob(os.path.join(mcp_config_dir, "*.json"))
        
        logger.info(f"找到 {len(config_files)} 个配置文件")
        for config_file in config_files:
            logger.info(f"  - {os.path.basename(config_file)}")
        
        # 2. 加载并验证所有配置文件
        logger.info("加载并验证MCP配置文件...")
        valid_configs = []
        invalid_configs = []
        
        for config_file in config_files:
            config_name = os.path.splitext(os.path.basename(config_file))[0]
            logger.info(f"处理配置文件: {config_name}")
            
            # 加载配置
            config_data = load_mcp_config(config_file)
            if config_data is not None:
                # 验证配置
                if validate_mcp_config(config_data, config_name):
                    valid_configs.append((config_name, config_data))
                    logger.info(f"  ✓ 配置文件 {config_name} 验证通过")
                else:
                    invalid_configs.append(config_name)
                    logger.error(f"  ✗ 配置文件 {config_name} 验证失败")
            else:
                invalid_configs.append(config_name)
                logger.error(f"  ✗ 配置文件 {config_name} 加载失败")
        
        # 3. 测试注册有效的配置文件
        logger.info("测试注册有效的配置文件...")
        registered_servers = []
        
        if valid_configs:
            # 导入服务
            from functions.master_service import master_service
            
            for config_name, config_data in valid_configs:
                logger.info(f"注册配置文件: {config_name}")
                mcp_servers = config_data.get("mcpServers", {})
                
                for server_name, server_config in mcp_servers.items():
                    command = server_config.get("command")
                    args = server_config.get("args", [])
                    env = server_config.get("env", {})
                    
                    # 创建服务器URL表示
                    server_url = f"local://{command} {' '.join(args)}"
                    
                    # 注册服务器
                    result = master_service.call(
                        "register_server", 
                        server_name=f"{config_name}-{server_name}", 
                        server_url=server_url,
                        server_info={
                            "command": command,
                            "args": args,
                            "env": env,
                            "type": "local_command",
                            "source_config": config_name
                        }
                    )
                    
                    if result:
                        registered_servers.append(f"{config_name}-{server_name}")
                        logger.info(f"  ✓ 注册服务器: {config_name}-{server_name}")
                    else:
                        logger.error(f"  ✗ 注册服务器失败: {config_name}-{server_name}")
        else:
            logger.warning("没有有效的配置文件可以注册")
        
        # 4. 显示所有已注册的服务器
        logger.info("所有已注册的服务器:")
        if registered_servers:
            from functions.master_service import master_service
            servers = master_service.call("list_servers")
            for server in servers:
                logger.info(f"  - {server['name']}: {server['url']}")
                if 'source_config' in server:
                    logger.info(f"    来源配置: {server['source_config']}")
            logger.info(f"总共注册了 {len(servers)} 个服务器")
        else:
            logger.info("没有服务器被注册")
        
        # 5. 测试工具发现功能
        logger.info("测试工具发现功能...")
        if valid_configs:
            from functions.master_service import master_service
            all_tools = master_service.get_all_tools_for_reranker()
            logger.info(f"发现 {len(all_tools)} 个工具用于reranker")
            for tool in all_tools[:5]:  # 只显示前5个工具
                logger.info(f"  - {tool['name']}: {tool['description'][:50]}...")
        else:
            logger.info("没有工具可发现")
        
        # 6. 清理注册的服务器
        logger.info("清理注册的服务器...")
        if registered_servers:
            from functions.master_service import master_service
            for server_name in registered_servers:
                result = master_service.call("unregister_server", server_name=server_name)
                if result:
                    logger.info(f"  ✓ 注销服务器: {server_name}")
                else:
                    logger.error(f"  ✗ 注销服务器失败: {server_name}")
        
        # 7. 总结测试结果
        logger.info("测试结果总结:")
        logger.info(f"  有效配置文件: {len(valid_configs)}")
        logger.info(f"  无效配置文件: {len(invalid_configs)}")
        logger.info(f"  成功注册服务器: {len(registered_servers)}")
        
        if invalid_configs:
            logger.warning(f"无效配置文件列表: {', '.join(invalid_configs)}")
        
        logger.info("MCP配置文件联通性测试完成")
        return len(invalid_configs) == 0  # 如果没有无效配置文件，返回成功
        
    except Exception as e:
        logger.error(f"测试MCP配置文件联通性时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数
    """
    logger.info("开始MCP配置文件联通性测试")
    
    success = test_mcp_config_connectivity()
    
    if success:
        logger.info("MCP配置文件联通性测试全部通过")
    else:
        logger.warning("MCP配置文件联通性测试存在错误")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
