#!/usr/bin/env python3
"""
系统录音能力完整测试
测试录音功能的各个方面，保存详细的问题报告
"""
import asyncio
import subprocess
import time
import json
import requests
import websockets
import base64
from pathlib import Path
from datetime import datetime
import logging
import sys
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RecordingCapabilityTester:
    def __init__(self):
        self.test_results = {
            'test_time': datetime.now().isoformat(),
            'system_info': self.get_system_info(),
            'tests': {},
            'issues': [],
            'recommendations': []
        }
        self.server_process = None
        self.server_url = "http://localhost:8001"
        self.websocket_url = "ws://localhost:8001/ws/recording"
        
    def get_system_info(self):
        """获取系统信息"""
        try:
            import platform
            return {
                'platform': platform.system(),
                'platform_version': platform.version(),
                'python_version': platform.python_version(),
                'architecture': platform.architecture()[0]
            }
        except Exception as e:
            return {'error': str(e)}
    
    def log_test(self, test_name, status, details=None, error=None):
        """记录测试结果"""
        result = {
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'details': details or {},
            'error': error
        }
        self.test_results['tests'][test_name] = result
        
        if status == 'PASS':
            logger.info(f"✅ {test_name}: 通过")
        elif status == 'FAIL':
            logger.error(f"❌ {test_name}: 失败 - {error}")
            self.test_results['issues'].append({
                'test': test_name,
                'error': error,
                'details': details
            })
        elif status == 'WARN':
            logger.warning(f"⚠️ {test_name}: 警告 - {error}")
        else:
            logger.info(f"ℹ️ {test_name}: {status}")
    
    def start_recording_server(self):
        """启动录音测试服务器"""
        logger.info("🚀 启动录音测试服务器...")
        
        try:
            # 检查服务器文件是否存在
            if not Path("recording_test_server.py").exists():
                self.log_test("server_file_check", "FAIL", error="recording_test_server.py 文件不存在")
                return False
            
            # 启动服务器
            self.server_process = subprocess.Popen([
                sys.executable, "recording_test_server.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待服务器启动
            time.sleep(3)
            
            # 检查服务器是否运行
            if self.server_process.poll() is None:
                self.log_test("server_startup", "PASS", details={"pid": self.server_process.pid})
                return True
            else:
                stdout, stderr = self.server_process.communicate()
                self.log_test("server_startup", "FAIL", 
                            details={"stdout": stdout.decode(), "stderr": stderr.decode()},
                            error="服务器启动失败")
                return False
                
        except Exception as e:
            self.log_test("server_startup", "FAIL", error=str(e))
            return False
    
    def test_server_health(self):
        """测试服务器健康状态"""
        logger.info("🔍 测试服务器健康状态...")
        
        try:
            response = requests.get(f"{self.server_url}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                self.log_test("server_health", "PASS", details=status_data)
                return True
            else:
                self.log_test("server_health", "FAIL", 
                            details={"status_code": response.status_code},
                            error=f"服务器返回状态码: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            self.log_test("server_health", "FAIL", error="无法连接到服务器")
            return False
        except Exception as e:
            self.log_test("server_health", "FAIL", error=str(e))
            return False
    
    def test_html_page_access(self):
        """测试HTML页面访问"""
        logger.info("🌐 测试HTML页面访问...")
        
        try:
            response = requests.get(self.server_url, timeout=5)
            if response.status_code == 200:
                content_length = len(response.text)
                has_recording_ui = "录音测试工具" in response.text
                
                self.log_test("html_page_access", "PASS", details={
                    "content_length": content_length,
                    "has_recording_ui": has_recording_ui
                })
                return True
            else:
                self.log_test("html_page_access", "FAIL", 
                            details={"status_code": response.status_code},
                            error=f"页面访问失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("html_page_access", "FAIL", error=str(e))
            return False
    
    async def test_websocket_connection(self):
        """测试WebSocket连接"""
        logger.info("🔌 测试WebSocket连接...")
        
        try:
            async with websockets.connect(self.websocket_url) as websocket:
                # 等待连接确认消息
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                message = json.loads(response)
                
                if message.get("type") == "connected":
                    self.log_test("websocket_connection", "PASS", details=message)
                    
                    # 测试心跳
                    await websocket.send(json.dumps({"type": "ping"}))
                    pong_response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    pong_message = json.loads(pong_response)
                    
                    if pong_message.get("type") == "pong":
                        self.log_test("websocket_heartbeat", "PASS", details=pong_message)
                        return True
                    else:
                        self.log_test("websocket_heartbeat", "FAIL", 
                                    details=pong_message,
                                    error="心跳响应不正确")
                        return False
                else:
                    self.log_test("websocket_connection", "FAIL", 
                                details=message,
                                error="连接确认消息不正确")
                    return False
                    
        except asyncio.TimeoutError:
            self.log_test("websocket_connection", "FAIL", error="WebSocket连接超时")
            return False
        except Exception as e:
            self.log_test("websocket_connection", "FAIL", error=str(e))
            return False
    
    async def test_audio_upload(self):
        """测试音频数据上传"""
        logger.info("🎵 测试音频数据上传...")
        
        # 创建测试音频数据 (模拟16kHz单声道PCM数据)
        import numpy as np
        
        # 生成1秒的440Hz正弦波
        sample_rate = 16000
        duration = 1.0
        frequency = 440
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = (np.sin(2 * np.pi * frequency * t) * 32767).astype(np.int16)
        audio_bytes = audio_data.tobytes()
        
        try:
            async with websockets.connect(self.websocket_url) as websocket:
                # 等待连接确认
                await websocket.recv()
                
                # 发送音频数据
                message = {
                    "type": "audio_data",
                    "data": base64.b64encode(audio_bytes).decode(),
                    "metadata": {
                        "format": "raw_pcm",
                        "sample_rate": sample_rate,
                        "channels": 1,
                        "duration": duration,
                        "test_type": "generated_sine_wave"
                    }
                }
                
                await websocket.send(json.dumps(message))
                
                # 等待响应
                response = await asyncio.wait_for(websocket.recv(), timeout=10)
                result = json.loads(response)
                
                if result.get("type") == "recording_saved":
                    self.log_test("audio_upload", "PASS", details={
                        "filename": result.get("filename"),
                        "recording_id": result.get("recording_id"),
                        "size": result.get("size"),
                        "analysis": result.get("analysis")
                    })
                    return result.get("recording_id")
                else:
                    self.log_test("audio_upload", "FAIL", 
                                details=result,
                                error="音频上传失败")
                    return None
                    
        except Exception as e:
            self.log_test("audio_upload", "FAIL", error=str(e))
            return None
    
    def test_recording_analysis(self, recording_id):
        """测试录音分析功能"""
        if not recording_id:
            self.log_test("recording_analysis", "SKIP", error="没有录音ID")
            return False
            
        logger.info(f"📊 测试录音分析功能 (ID: {recording_id})...")
        
        try:
            response = requests.get(f"{self.server_url}/recordings/{recording_id}/analyze", timeout=10)
            
            if response.status_code == 200:
                analysis = response.json()
                
                # 检查分析结果的完整性
                required_fields = ['filename', 'duration', 'sample_rate', 'channels', 'is_valid']
                missing_fields = [field for field in required_fields if field not in analysis]
                
                if not missing_fields and analysis.get('is_valid'):
                    self.log_test("recording_analysis", "PASS", details=analysis)
                    return True
                else:
                    self.log_test("recording_analysis", "FAIL", 
                                details=analysis,
                                error=f"分析结果不完整或无效: {missing_fields}")
                    return False
            else:
                self.log_test("recording_analysis", "FAIL", 
                            details={"status_code": response.status_code},
                            error=f"分析请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("recording_analysis", "FAIL", error=str(e))
            return False
    
    def test_recording_download(self, recording_id):
        """测试录音下载功能"""
        if not recording_id:
            self.log_test("recording_download", "SKIP", error="没有录音ID")
            return False
            
        logger.info(f"📥 测试录音下载功能 (ID: {recording_id})...")
        
        try:
            response = requests.get(f"{self.server_url}/recordings/{recording_id}/download", timeout=10)
            
            if response.status_code == 200:
                content_length = len(response.content)
                content_type = response.headers.get('content-type', '')
                
                self.log_test("recording_download", "PASS", details={
                    "content_length": content_length,
                    "content_type": content_type
                })
                return True
            else:
                self.log_test("recording_download", "FAIL", 
                            details={"status_code": response.status_code},
                            error=f"下载请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("recording_download", "FAIL", error=str(e))
            return False
    
    def test_recordings_list(self):
        """测试录音列表功能"""
        logger.info("📋 测试录音列表功能...")
        
        try:
            response = requests.get(f"{self.server_url}/recordings", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                recordings = data.get('recordings', [])
                count = data.get('count', 0)
                
                self.log_test("recordings_list", "PASS", details={
                    "count": count,
                    "recordings_found": len(recordings) > 0
                })
                return True
            else:
                self.log_test("recordings_list", "FAIL", 
                            details={"status_code": response.status_code},
                            error=f"列表请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("recordings_list", "FAIL", error=str(e))
            return False
    
    def test_file_system_access(self):
        """测试文件系统访问"""
        logger.info("📁 测试文件系统访问...")
        
        try:
            recordings_dir = Path("test_recordings")
            
            # 检查录音目录
            if recordings_dir.exists():
                files = list(recordings_dir.glob("*.wav"))
                self.log_test("file_system_access", "PASS", details={
                    "recordings_dir_exists": True,
                    "wav_files_count": len(files),
                    "directory_path": str(recordings_dir.absolute())
                })
                return True
            else:
                self.log_test("file_system_access", "WARN", 
                            error="录音目录不存在，但这可能是正常的")
                return True
                
        except Exception as e:
            self.log_test("file_system_access", "FAIL", error=str(e))
            return False
    
    def generate_recommendations(self):
        """生成改进建议"""
        logger.info("💡 生成改进建议...")
        
        recommendations = []
        
        # 基于测试结果生成建议
        failed_tests = [name for name, result in self.test_results['tests'].items() 
                       if result['status'] == 'FAIL']
        
        if 'server_startup' in failed_tests:
            recommendations.append({
                'category': '服务器启动',
                'issue': '录音测试服务器无法启动',
                'solution': '检查Python环境和依赖包安装，确保端口8001未被占用',
                'priority': 'HIGH'
            })
        
        if 'websocket_connection' in failed_tests:
            recommendations.append({
                'category': 'WebSocket连接',
                'issue': 'WebSocket连接失败',
                'solution': '检查防火墙设置，确保WebSocket协议未被阻止',
                'priority': 'HIGH'
            })
        
        if 'audio_upload' in failed_tests:
            recommendations.append({
                'category': '音频处理',
                'issue': '音频数据上传或处理失败',
                'solution': '检查音频格式支持，确保numpy和wave库正确安装',
                'priority': 'HIGH'
            })
        
        if 'recording_analysis' in failed_tests:
            recommendations.append({
                'category': '录音分析',
                'issue': '录音文件分析功能异常',
                'solution': '检查WAV文件处理逻辑，确保音频格式转换正确',
                'priority': 'MEDIUM'
            })
        
        # 通用建议
        if len(failed_tests) == 0:
            recommendations.append({
                'category': '系统优化',
                'issue': '所有测试通过',
                'solution': '系统录音功能正常，建议进行实际录音测试验证用户体验',
                'priority': 'LOW'
            })
        
        self.test_results['recommendations'] = recommendations
        
        for rec in recommendations:
            logger.info(f"💡 [{rec['priority']}] {rec['category']}: {rec['solution']}")
    
    def save_test_report(self):
        """保存测试报告"""
        logger.info("📄 保存测试报告...")
        
        # 计算测试统计
        total_tests = len(self.test_results['tests'])
        passed_tests = len([t for t in self.test_results['tests'].values() if t['status'] == 'PASS'])
        failed_tests = len([t for t in self.test_results['tests'].values() if t['status'] == 'FAIL'])
        
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
        }
        
        # 保存JSON报告
        json_report_path = f"recording_capability_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_report_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # 生成Markdown报告
        md_report_path = f"recording_capability_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        self.generate_markdown_report(md_report_path)
        
        logger.info(f"📄 测试报告已保存:")
        logger.info(f"   JSON: {json_report_path}")
        logger.info(f"   Markdown: {md_report_path}")
        
        return json_report_path, md_report_path
    
    def generate_markdown_report(self, filepath):
        """生成Markdown格式的测试报告"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("# 系统录音能力测试报告\n\n")
            
            # 测试概要
            summary = self.test_results['summary']
            f.write("## 测试概要\n\n")
            f.write(f"- **测试时间**: {self.test_results['test_time']}\n")
            f.write(f"- **总测试数**: {summary['total_tests']}\n")
            f.write(f"- **通过测试**: {summary['passed_tests']}\n")
            f.write(f"- **失败测试**: {summary['failed_tests']}\n")
            f.write(f"- **成功率**: {summary['success_rate']}\n\n")
            
            # 系统信息
            f.write("## 系统信息\n\n")
            for key, value in self.test_results['system_info'].items():
                f.write(f"- **{key}**: {value}\n")
            f.write("\n")
            
            # 测试结果详情
            f.write("## 测试结果详情\n\n")
            for test_name, result in self.test_results['tests'].items():
                status_emoji = "✅" if result['status'] == 'PASS' else "❌" if result['status'] == 'FAIL' else "⚠️"
                f.write(f"### {status_emoji} {test_name}\n\n")
                f.write(f"- **状态**: {result['status']}\n")
                f.write(f"- **时间**: {result['timestamp']}\n")
                
                if result.get('error'):
                    f.write(f"- **错误**: {result['error']}\n")
                
                if result.get('details'):
                    f.write("- **详情**:\n")
                    for key, value in result['details'].items():
                        f.write(f"  - {key}: {value}\n")
                f.write("\n")
            
            # 问题列表
            if self.test_results['issues']:
                f.write("## 发现的问题\n\n")
                for i, issue in enumerate(self.test_results['issues'], 1):
                    f.write(f"{i}. **{issue['test']}**: {issue['error']}\n")
                f.write("\n")
            
            # 改进建议
            if self.test_results['recommendations']:
                f.write("## 改进建议\n\n")
                for rec in self.test_results['recommendations']:
                    priority_emoji = "🔴" if rec['priority'] == 'HIGH' else "🟡" if rec['priority'] == 'MEDIUM' else "🟢"
                    f.write(f"### {priority_emoji} {rec['category']}\n\n")
                    f.write(f"**问题**: {rec['issue']}\n\n")
                    f.write(f"**解决方案**: {rec['solution']}\n\n")
    
    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理测试资源...")
        
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                logger.info("✅ 录音测试服务器已停止")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                logger.warning("⚠️ 强制终止录音测试服务器")
            except Exception as e:
                logger.error(f"❌ 停止服务器时出错: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始系统录音能力完整测试...")
        
        try:
            # 1. 启动服务器
            if not self.start_recording_server():
                logger.error("❌ 无法启动录音测试服务器，跳过后续测试")
                return
            
            # 等待服务器完全启动
            time.sleep(2)
            
            # 2. 测试服务器健康状态
            if not self.test_server_health():
                logger.error("❌ 服务器健康检查失败，跳过后续测试")
                return
            
            # 3. 测试HTML页面访问
            self.test_html_page_access()
            
            # 4. 测试WebSocket连接
            await self.test_websocket_connection()
            
            # 5. 测试音频上传
            recording_id = await self.test_audio_upload()
            
            # 6. 测试录音分析
            self.test_recording_analysis(recording_id)
            
            # 7. 测试录音下载
            self.test_recording_download(recording_id)
            
            # 8. 测试录音列表
            self.test_recordings_list()
            
            # 9. 测试文件系统访问
            self.test_file_system_access()
            
            # 10. 生成改进建议
            self.generate_recommendations()
            
            # 11. 保存测试报告
            json_path, md_path = self.save_test_report()
            
            logger.info("✅ 系统录音能力测试完成!")
            logger.info(f"📄 详细报告: {md_path}")
            
        except Exception as e:
            logger.error(f"❌ 测试过程中出现异常: {e}")
            self.log_test("test_execution", "FAIL", error=str(e))
        
        finally:
            self.cleanup()

async def main():
    """主函数"""
    print("🎤 系统录音能力完整测试")
    print("=" * 50)
    
    tester = RecordingCapabilityTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())