#!/usr/bin/env python3
"""
测试 /process-audio 端点
"""

import requests
import numpy as np
import wave
import io
import tempfile

def create_test_audio():
    """创建测试音频文件"""
    sample_rate = 16000
    duration = 1.0
    frequency = 440
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(2 * np.pi * frequency * t).astype(np.float32)
    audio_int16 = (audio_data * 32767).astype(np.int16)
    
    # 创建WAV字节流
    wav_buffer = io.BytesIO()
    with wave.open(wav_buffer, 'wb') as wav_file:
        wav_file.setnchannels(1)
        wav_file.setsampwidth(2)
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_int16.tobytes())
    
    return wav_buffer.getvalue()

def test_process_audio():
    """测试 /process-audio 端点"""
    print("🎯 测试 /process-audio 端点...")
    
    # 创建测试音频
    audio_bytes = create_test_audio()
    print(f"📊 测试音频: {len(audio_bytes)} bytes")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
        temp_file.write(audio_bytes)
        temp_file_path = temp_file.name
    
    try:
        # 发送请求
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test.wav', f, 'audio/wav')}
            data = {'user_id': '1', 'npc_id': '1'}
            
            print("📤 发送请求到 /process-audio...")
            response = requests.post('http://localhost:8000/process-audio', files=files, data=data)
            
            print(f"📥 响应状态: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 请求成功!")
                print(f"📝 转录结果: {result.get('transcription', 'N/A')}")
                print(f"🔧 ASR提供商: {result.get('asr_provider', 'N/A')}")
                print(f"🧠 LLM回复: {result.get('response_text', 'N/A')}")
            else:
                print("❌ 请求失败!")
                
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    finally:
        # 清理临时文件
        import os
        try:
            os.unlink(temp_file_path)
        except:
            pass

if __name__ == "__main__":
    test_process_audio()