#!/usr/bin/env python3
"""
测试指定音频文件的ASR识别
使用 chunk_test-user_20250805_232853_058.wav
"""
import asyncio
import websockets
import json
import base64
import requests
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_specific_audio():
    """测试指定音频文件"""
    logger.info("🎯 测试指定音频文件的ASR识别...")
    
    # 检查音频文件是否存在
    # audio_file = Path("backend/audio_recordings/chunk_test-user_20250805_232853_058.wav")
    # audio_file = Path("/Users/<USER>/LocalRepo/20250729t163822/welcome.mp3")
    # audio_file = Path("/Users/<USER>/LocalRepo/20250729t163822/开拓路-3.m4a")

    audio_file = Path("/Users/<USER>/LocalRepo/20250729t163822/录音测试工具.webm")
    
    
    if not audio_file.exists():
        logger.error(f"❌ 音频文件不存在: {audio_file}")
        return False
    
    logger.info(f"📁 使用音频文件: {audio_file.name} ({audio_file.stat().st_size} bytes)")
    
    # 1. 用户登录
    try:
        response = requests.post(
            "http://localhost:8000/auth/login",
            params={"username": "test_user", "password": "test_password"},
            timeout=10
        )
        if response.status_code != 200:
            logger.error(f"❌ 登录失败: {response.status_code}")
            return False
        
        user_data = response.json()
        user_id = user_data.get('user', {}).get('id', 1)
        logger.info(f"✅ 用户登录成功，ID: {user_id}")
    except Exception as e:
        logger.error(f"❌ 登录异常: {e}")
        return False
    
    # 2. WebSocket连接测试
    try:
        uri = f"ws://localhost:8000/ws/{user_id}"
        logger.info(f"🔌 连接WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 启动会话
            start_msg = {"type": "start_session", "npc_id": 1}
            await websocket.send(json.dumps(start_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动确认
            session_started = False
            for attempt in range(3):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    
                    if response_data.get("type") == "session_started":
                        session_id = response_data.get("session_id")
                        logger.info(f"✅ 会话启动成功，ID: {session_id}")
                        session_started = True
                        break
                    elif response_data.get("type") == "connection_established":
                        logger.info("✅ 连接已建立，继续等待会话启动...")
                        continue
                        
                except asyncio.TimeoutError:
                    logger.error(f"❌ 等待响应超时 (尝试 {attempt + 1}/3)")
                    if attempt == 2:
                        return False
            
            if not session_started:
                logger.error("❌ 会话启动失败")
                return False
            
            # 3. 读取并发送指定的音频文件
            logger.info(f"🎤 读取音频文件: {audio_file}")
            
            with open(audio_file, 'rb') as f:
                audio_data = f.read()
            
            logger.info(f"📊 音频文件信息:")
            logger.info(f"   文件大小: {len(audio_data)} bytes")
            logger.info(f"   文件路径: {audio_file}")
            
            # 转换为base64并发送
            base64_audio = base64.b64encode(audio_data).decode('utf-8')
            
            audio_msg = {
                "type": "audio_chunk",
                "data": base64_audio,
                "metadata": {
                    "filename": audio_file.name,
                    "size": len(audio_data)
                }
            }
            
            await websocket.send(json.dumps(audio_msg))
            logger.info(f"📤 发送音频数据: {len(audio_data)} bytes")
            
            # 4. 等待ASR处理结果
            logger.info("⏳ 等待ASR处理结果...")
            
            responses_received = []
            transcription_received = False
            llm_response_received = False
            
            timeout_count = 0
            max_timeout = 45  # 45秒超时，给ASR更多时间
            
            while timeout_count < max_timeout:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1)
                    response_data = json.loads(response)
                    responses_received.append(response_data)
                    
                    msg_type = response_data.get('type')
                    logger.info(f"📥 收到响应: {msg_type}")
                    
                    if msg_type == 'transcription':
                        text = response_data.get('text', '')
                        confidence = response_data.get('confidence', 0)
                        logger.info("=" * 60)
                        logger.info(f"🎯 ASR转录结果:")
                        logger.info(f"   文本: '{text}'")
                        logger.info(f"   置信度: {confidence:.3f}")
                        logger.info(f"   是否为回退响应: {'是' if text == '你好，请问有什么可以帮助您的吗？' else '否'}")
                        logger.info("=" * 60)
                        transcription_received = True
                        
                    elif msg_type == 'response' or msg_type == 'llm_response':
                        text = response_data.get('text', '')
                        logger.info(f"🧠 LLM响应: '{text}'")
                        llm_response_received = True
                        
                    elif msg_type == 'audio_chunk':
                        logger.info("🔊 收到TTS音频响应")
                        
                    elif msg_type == 'response_complete':
                        logger.info("✅ 处理完成")
                        break
                        
                    elif msg_type == 'error':
                        error_msg = response_data.get('message', '未知错误')
                        logger.error(f"❌ 处理错误: {error_msg}")
                        break
                    
                    else:
                        logger.info(f"📥 其他响应: {msg_type}")
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 10 == 0:
                        logger.info(f"⏰ 等待ASR处理中... ({timeout_count}s)")
                    continue
            
            # 5. 分析结果
            logger.info("=" * 60)
            logger.info("📊 指定音频文件ASR测试结果:")
            logger.info(f"   音频文件: {audio_file.name}")
            logger.info(f"   文件大小: {len(audio_data)} bytes")
            logger.info(f"   收到响应数: {len(responses_received)}")
            logger.info(f"   ASR转录: {'✅ 成功' if transcription_received else '❌ 失败'}")
            logger.info(f"   LLM响应: {'✅ 成功' if llm_response_received else '❌ 失败'}")
            
            # 统计响应类型
            response_types = {}
            for resp in responses_received:
                resp_type = resp.get('type', 'unknown')
                response_types[resp_type] = response_types.get(resp_type, 0) + 1
            
            logger.info("\n📈 响应类型统计:")
            for resp_type, count in response_types.items():
                logger.info(f"   {resp_type}: {count} 次")
            
            # 显示所有转录结果
            transcriptions = [r for r in responses_received if r.get('type') == 'transcription']
            if transcriptions:
                logger.info("\n🎯 所有转录结果:")
                for i, trans in enumerate(transcriptions):
                    text = trans.get('text', '')
                    confidence = trans.get('confidence', 0)
                    logger.info(f"   {i+1}. '{text}' (置信度: {confidence:.3f})")
            
            logger.info("=" * 60)
            
            return transcription_received
            
    except Exception as e:
        logger.error(f"❌ WebSocket测试异常: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 指定音频文件ASR测试开始...")
    logger.info("🎯 目标: 测试 chunk_test-user_20250805_232853_058.wav 的ASR识别")
    logger.info("=" * 60)
    
    success = await test_specific_audio()
    
    logger.info("=" * 60)
    if success:
        logger.info("🎉 指定音频文件ASR测试成功！")
        logger.info("💡 已获得该音频文件的转录结果")
    else:
        logger.error("❌ 指定音频文件ASR测试失败！")
        logger.info("🔧 可能的原因:")
        logger.info("   1. 音频文件不存在或格式不正确")
        logger.info("   2. ASR服务不可用，使用了回退响应")
        logger.info("   3. 网络连接问题")

if __name__ == "__main__":
    asyncio.run(main())