import os
import sys
import requests
from supabase import create_client, Client
from dotenv import load_dotenv

# 获取当前脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))
# 构建backend/.env的绝对路径
env_path = os.path.join(script_dir, 'backend', '.env')

print(f"尝试加载环境变量文件: {env_path}")

# 加载环境变量
load_dotenv(env_path)

# 初始化Supabase客户端
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_KEY")

print("SUPABASE_URL:", supabase_url)
print("SUPABASE_KEY:", supabase_key)

if not supabase_url or not supabase_key:
    print("环境变量未从文件加载，尝试使用默认值...")
    # 如果环境变量未设置，使用默认值（从之前查看的.env文件中获取）
    supabase_url = "http://*************:8000"
    supabase_key = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiYW5vbiIsInJlZjoiY215YmZ1c3JycmVjdGlyb3NrcyIsImlzcyI6InN1cGFiYXNlIiwiaWF0IjoxNzIyODQ0MTA0LCJleHAiOjE3NTQzODAxMDR9.9j3BlbJhJzXvFzXvH6ZvQJQJQJQJQJQJQJQJQJQJQJQ"
    
    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL和SUPABASE_KEY环境变量未设置")

try:
    supabase = create_client(supabase_url, supabase_key)
    print("Supabase客户端创建成功")
except Exception as e:
    print(f"创建Supabase客户端失败: {e}")
    sys.exit(1)

# 执行测试查询
print("正在测试Supabase连接...")
try:
    # 先测试连接users表
    response = supabase.table("users").select("count", count="exact").limit(1).execute()
    print("✓ Supabase连接成功")
    print(f"Users表记录数: {response.count}")
    
    # 再测试连接npcs表
    response = supabase.table("npcs").select("count", count="exact").limit(1).execute()
    print(f"NPCs表记录数: {response.count}")
    
    # 测试conversation_sessions表
    response = supabase.table("conversation_sessions").select("count", count="exact").limit(1).execute()
    print(f"Conversation sessions表记录数: {response.count}")
     
    
except Exception as e:
    print(f"✗ Supabase连接失败: {e}")
    import traceback
    traceback.print_exc()
