#!/usr/bin/env python3
"""
独立的LLM对话测试脚本
测试Volcano Engine LLM API的对话功能
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('backend/.env')

# 添加backend目录到路径
sys.path.append('backend')

from services.llm_service import LLMService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_llm_service():
    """测试LLM服务"""
    logger.info("=" * 60)
    logger.info("LLM Service Test")
    logger.info("=" * 60)

    # 1. 初始化LLM服务
    try:
        logger.info("Initializing LLM service...")
        api_key = os.getenv("VOLCANO_API_KEY")
        endpoint = os.getenv("VOLCANO_ENDPOINT")

        if not api_key or not endpoint:
            logger.error("❌ Missing VOLCANO_API_KEY or VOLCANO_ENDPOINT")
            return False

        llm_service = LLMService(api_key, endpoint)
        logger.info("✅ LLM service initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize LLM service: {e}")
        return False
    
    # 2. 测试对话功能
    test_messages = [
        "你好，请介绍一下自己",
        "1加1等于几？",
        "请用中文回答：What is the capital of China?",
        "今天天气怎么样？",
        "请简单解释一下人工智能"
    ]
    
    for i, message in enumerate(test_messages, 1):
        logger.info(f"\n--- Test {i}/5 ---")
        logger.info(f"🤖 User: {message}")
        
        try:
            # 调用LLM服务 - 使用正确的方法和参数
            import asyncio
            response = asyncio.run(llm_service.generate_response(
                user_input=message,
                conversation_history=[],
                system_prompt="你是一个友好的AI助手，请用中文回答问题。",
                use_tools=False
            ))

            if response and response.get("success"):
                speak_content = response.get("speak_content", {})
                logger.info(f"✅ AI: {speak_content.get('text', 'No text')}")
                logger.info(f"✅ Emotion: {speak_content.get('emotion', 'neutral')}")
                logger.info(f"✅ Speed: {speak_content.get('speed', 1.0)}")
                logger.info(f"✅ Mock Mode: {response.get('mock_mode', False)}")
            else:
                logger.error(f"❌ Empty or invalid response: {response}")
                return False
                
        except Exception as e:
            logger.error(f"❌ LLM chat failed: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    logger.info("\n" + "=" * 60)
    logger.info("✅ All LLM tests passed successfully!")
    logger.info("=" * 60)
    return True

def test_conversation_context():
    """测试对话上下文功能"""
    logger.info("\n" + "=" * 60)
    logger.info("LLM Conversation Context Test")
    logger.info("=" * 60)

    try:
        api_key = os.getenv("VOLCANO_API_KEY")
        endpoint = os.getenv("VOLCANO_ENDPOINT")
        llm_service = LLMService(api_key, endpoint)

        # 测试多轮对话
        conversation_history = []
        conversation = [
            "我的名字是小明",
            "你记得我的名字吗？",
            "我今年25岁",
            "你知道我多大了吗？"
        ]

        for i, message in enumerate(conversation, 1):
            logger.info(f"\n--- Conversation Turn {i} ---")
            logger.info(f"🤖 User: {message}")

            import asyncio
            response = asyncio.run(llm_service.generate_response(
                user_input=message,
                conversation_history=conversation_history,
                system_prompt="你是一个友好的AI助手，请记住用户告诉你的信息。",
                use_tools=False
            ))

            if response and response.get("success"):
                speak_content = response.get("speak_content", {})
                ai_text = speak_content.get('text', 'No response')
                logger.info(f"✅ AI: {ai_text}")

                # 更新对话历史
                conversation_history.append({"role": "user", "content": message})
                conversation_history.append({"role": "assistant", "content": ai_text})
            else:
                logger.error(f"❌ Failed at conversation turn {i}")
                return False

        logger.info("\n✅ Conversation context test passed!")
        return True

    except Exception as e:
        logger.error(f"❌ Conversation context test failed: {e}")
        return False

def main():
    """主函数"""
    logger.info("Starting LLM standalone tests...")
    
    # 检查环境变量
    required_env_vars = [
        "VOLCANO_API_KEY",
        "VOLCANO_ENDPOINT"
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        logger.error("Please set these variables in your .env file")
        return False
    
    # 运行测试
    success = True
    
    # 1. 基本LLM测试
    if not test_llm_service():
        success = False
    
    # 2. 对话上下文测试
    if not test_conversation_context():
        success = False
    
    if success:
        logger.info("\n🎉 All LLM tests completed successfully!")
    else:
        logger.error("\n❌ Some LLM tests failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
