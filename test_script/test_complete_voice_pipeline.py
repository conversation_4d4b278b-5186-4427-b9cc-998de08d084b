#!/usr/bin/env python3
"""
Complete Voice Pipeline Test Script
测试完整的语音处理流水线，包括录音、ASR、LLM、TTS和播放
"""

import asyncio
import websockets
import json
import base64
import numpy as np
import wave
import os
import time
from datetime import datetime
import threading
import pyaudio

# 配置参数
USER_ID = "999"  # 测试用户ID
SERVER_URL = "ws://localhost:8000/ws/999"
CHUNK = 1024
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000
RECORD_SECONDS = 5  # 录音时长

# 创建保存目录
os.makedirs("test_recordings", exist_ok=True)
os.makedirs("test_output", exist_ok=True)

class VoicePipelineTester:
    def __init__(self):
        self.websocket = None
        self.is_recording = False
        self.audio_buffer = []
        self.transcription = ""
        self.tts_audio_url = ""
        self.messages = []
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.websocket = await websockets.connect(SERVER_URL)
            print("✅ WebSocket连接成功")
            return True
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
            return False
    
    async def start_session(self, npc_id=1):
        """开始会话"""
        message = {
            "type": "start_session",
            "npc_id": npc_id
        }
        await self.websocket.send(json.dumps(message))
        print(f"🚀 已发送开始会话请求 (NPC ID: {npc_id})")
    
    def record_audio(self):
        """录制音频"""
        print("🎤 开始录音...")
        p = pyaudio.PyAudio()
        
        stream = p.open(format=FORMAT,
                       channels=CHANNELS,
                       rate=RATE,
                       input=True,
                       frames_per_buffer=CHUNK)
        
        frames = []
        for i in range(0, int(RATE / CHUNK * RECORD_SECONDS)):
            if not self.is_recording:
                break
            data = stream.read(CHUNK)
            frames.append(data)
            # 发送音频数据到服务器
            audio_data = np.frombuffer(data, dtype=np.int16)
            self.send_audio_chunk(audio_data)
        
        print("🛑 录音结束")
        
        # 保存录音文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_recordings/test_recording_{timestamp}.wav"
        
        wf = wave.open(filename, 'wb')
        wf.setnchannels(CHANNELS)
        wf.setsampwidth(p.get_sample_size(FORMAT))
        wf.setframerate(RATE)
        wf.writeframes(b''.join(frames))
        wf.close()
        
        print(f"💾 录音已保存到: {filename}")
        
        stream.stop_stream()
        stream.close()
        p.terminate()
    
    def send_audio_chunk(self, audio_data):
        """发送音频块到服务器"""
        try:
            # 转换为base64
            audio_bytes = audio_data.tobytes()
            audio_b64 = base64.b64encode(audio_bytes).decode('utf-8')
            
            message = {
                "type": "audio_chunk",
                "data": audio_b64
            }
            
            # 在新线程中发送消息以避免阻塞
            asyncio.run_coroutine_threadsafe(
                self.websocket.send(json.dumps(message)), 
                asyncio.get_event_loop()
            )
        except Exception as e:
            print(f"❌ 发送音频块失败: {e}")
    
    async def listen_for_messages(self):
        """监听服务器消息"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                self.messages.append(data)
                
                msg_type = data.get("type")
                print(f"📥 收到消息: {msg_type}")
                
                if msg_type == "session_started":
                    print("✅ 会话已开始")
                    print(f"   Session ID: {data.get('session_id')}")
                    
                elif msg_type == "transcription":
                    text = data.get("text", "")
                    confidence = data.get("confidence", 0)
                    self.transcription = text
                    print(f"📝 转录结果: {text} (置信度: {confidence})")
                    
                elif msg_type == "tts_audio":
                    url = data.get("url", "")
                    self.tts_audio_url = url
                    print(f"🔊 TTS音频URL: {url}")
                    
                elif msg_type == "audio_chunk":
                    # 接收到音频块
                    audio_b64 = data.get("data", "")
                    if audio_b64:
                        audio_data = base64.b64decode(audio_b64)
                        print(f"🎵 接收到音频块: {len(audio_data)} 字节")
                        
                elif msg_type == "response_complete":
                    print("✅ 响应完成")
                    
                elif msg_type == "error":
                    error_msg = data.get("message", "未知错误")
                    print(f"❌ 错误: {error_msg}")
                    
        except Exception as e:
            print(f"❌ 监听消息时出错: {e}")
    
    async def run_test(self):
        """运行完整测试"""
        print("🚀 开始完整语音流水线测试")
        
        # 连接服务器
        if not await self.connect():
            return
        
        # 开始会话
        await self.start_session()
        
        # 启动消息监听器
        listen_task = asyncio.create_task(self.listen_for_messages())
        
        # 等待一会儿让会话建立
        await asyncio.sleep(1)
        
        # 开始录音
        self.is_recording = True
        record_thread = threading.Thread(target=self.record_audio)
        record_thread.start()
        
        # 等待录音完成
        record_thread.join()
        self.is_recording = False
        
        # 等待一段时间让处理完成
        print("⏳ 等待处理完成...")
        await asyncio.sleep(10)
        
        # 发送结束会话消息
        end_message = {"type": "end_session"}
        await self.websocket.send(json.dumps(end_message))
        
        # 关闭连接
        await self.websocket.close()
        
        # 等待监听器完成
        await listen_task
        
        # 输出测试结果
        self.print_test_results()
    
    def print_test_results(self):
        """打印测试结果"""
        print("\n" + "="*50)
        print("📊 测试结果汇总")
        print("="*50)
        
        print(f"📝 转录文本: {self.transcription}")
        print(f"🔊 TTS音频URL: {self.tts_audio_url}")
        print(f"📥 接收到的消息数量: {len(self.messages)}")
        
        # 保存测试结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"test_output/test_result_{timestamp}.json"
        
        result = {
            "timestamp": timestamp,
            "transcription": self.transcription,
            "tts_audio_url": self.tts_audio_url,
            "messages": self.messages
        }
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"💾 测试结果已保存到: {result_file}")

async def main():
    """主函数"""
    tester = VoicePipelineTester()
    await tester.run_test()

if __name__ == "__main__":
    print("🎙️ 语音流水线测试工具")
    print("="*30)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
