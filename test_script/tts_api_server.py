#!/usr/bin/env python3
"""
TTS API服务器
为前端提供真实的TTS音频合成服务
"""

import os
import sys
import asyncio
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
from pydantic import BaseModel
from dotenv import load_dotenv
import uvicorn

# 加载环境变量
load_dotenv('backend/.env')

# 添加backend目录到路径
sys.path.append('backend')

from services.tts_service import TTSService
from services.llm_service import LLMService

# 初始化FastAPI应用
app = FastAPI(title="TTS API Server", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 初始化服务
tts_service = None
llm_service = None

class TTSRequest(BaseModel):
    text: str
    emotion: str = "neutral"
    speed: float = 1.0
    voice_id: str = None

@app.on_event("startup")
async def startup_event():
    """启动时初始化服务"""
    global tts_service, llm_service
    
    print("🚀 初始化TTS和LLM服务...")
    
    # 初始化TTS服务
    tts_api_key = os.getenv("MINIMAX_API_KEY")
    tts_group_id = os.getenv("MINIMAX_GROUP_ID")
    
    if tts_api_key and tts_group_id:
        tts_service = TTSService(tts_api_key, tts_group_id)
        print("✅ TTS服务初始化成功")
    else:
        print("⚠️ TTS服务配置缺失，将使用mock模式")
    
    # 初始化LLM服务
    llm_api_key = os.getenv("VOLCANO_API_KEY")
    llm_endpoint = os.getenv("VOLCANO_ENDPOINT")
    
    if llm_api_key and llm_endpoint:
        llm_service = LLMService(llm_api_key, llm_endpoint)
        print("✅ LLM服务初始化成功")
    else:
        print("⚠️ LLM服务配置缺失，将使用mock模式")

@app.get("/")
async def root():
    """根路径"""
    return {"message": "TTS API Server", "status": "running"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "services": {
            "tts": "configured" if tts_service else "not configured",
            "llm": "configured" if llm_service else "not configured"
        }
    }

@app.get("/test")
async def test_llm():
    """测试LLM服务"""
    if not llm_service:
        return {
            "question": "测试问题",
            "response": "这是一个模拟的LLM响应，用于测试TTS功能。",
            "status": "mock"
        }
    
    try:
        test_question = "今天是星期几？"
        
        response = await llm_service.generate_response(
            user_input=test_question,
            conversation_history=[],
            system_prompt="你是一个友好的AI助手，请简洁地回答问题。",
            use_tools=False
        )
        
        if response.get("success"):
            speak_content = response.get("speak_content", {})
            ai_text = speak_content.get("text", "")
            
            return {
                "question": test_question,
                "response": ai_text,
                "status": "success",
                "mock_mode": response.get("mock_mode", False)
            }
        else:
            return {
                "question": test_question,
                "response": "LLM服务暂时不可用，这是一个测试响应。",
                "status": "fallback"
            }
            
    except Exception as e:
        print(f"LLM测试失败: {e}")
        return {
            "question": "测试问题",
            "response": "LLM服务出现错误，这是一个备用响应。",
            "status": "error"
        }

@app.post("/api/tts/synthesize")
async def synthesize_speech(request: TTSRequest):
    """合成语音"""
    if not tts_service:
        raise HTTPException(status_code=503, detail="TTS服务未配置")
    
    try:
        print(f"🔊 TTS请求: {request.text[:50]}...")
        
        result = await tts_service.synthesize_speech(
            text=request.text,
            emotion=request.emotion,
            speed=request.speed,
            voice_id=request.voice_id,
            output_file=None
        )
        
        if result.get("success") and result.get("audio_data"):
            audio_data = result["audio_data"]
            print(f"✅ TTS合成成功: {len(audio_data)} 字节")
            
            return Response(
                content=audio_data,
                media_type="audio/wav",
                headers={
                    "Content-Disposition": "attachment; filename=tts_output.wav",
                    "Content-Length": str(len(audio_data))
                }
            )
        else:
            error_msg = result.get("error", "TTS合成失败")
            print(f"❌ TTS合成失败: {error_msg}")
            raise HTTPException(status_code=500, detail=error_msg)
            
    except Exception as e:
        print(f"❌ TTS API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/tts/voices")
async def get_available_voices():
    """获取可用的语音"""
    if not tts_service:
        return {"voices": {}}
    
    return {"voices": tts_service.get_available_voices()}

@app.post("/api/tts/test")
async def test_tts():
    """测试TTS服务"""
    test_text = "这是一个TTS测试，请生成语音。"
    
    request = TTSRequest(
        text=test_text,
        emotion="neutral",
        speed=1.0
    )
    
    return await synthesize_speech(request)

def main():
    """主函数"""
    print("🎤 启动TTS API服务器...")
    print("📍 服务地址: http://localhost:8001")
    print("📖 API文档: http://localhost:8001/docs")
    print("🔊 TTS测试: http://localhost:8001/api/tts/test")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )

if __name__ == "__main__":
    main()