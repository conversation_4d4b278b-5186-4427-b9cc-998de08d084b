<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音对话测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .pipeline-status {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin: 30px 0;
        }

        .stage {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 3px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }

        .stage.waiting {
            border-color: #e9ecef;
            background: #f8f9fa;
        }

        .stage.active {
            border-color: #007bff;
            background: #e3f2fd;
            animation: pulse-border 2s infinite;
        }

        .stage.success {
            border-color: #28a745;
            background: #d4edda;
        }

        .stage.error {
            border-color: #dc3545;
            background: #f8d7da;
        }

        @keyframes pulse-border {
            0% { border-color: #007bff; }
            50% { border-color: #0056b3; }
            100% { border-color: #007bff; }
        }

        .stage-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .stage-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stage-status {
            font-size: 0.9em;
            color: #666;
        }

        .stage-details {
            margin-top: 10px;
            font-size: 0.8em;
            color: #888;
            min-height: 20px;
        }

        .record-section {
            text-align: center;
            margin: 40px 0;
        }

        .record-button {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            font-size: 2em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }

        .record-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
        }

        .record-button.recording {
            background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);
            animation: pulse 1.5s infinite;
        }

        .record-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .record-text {
            margin-top: 20px;
            font-size: 1.2em;
            color: #666;
        }

        .conversation {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }

        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #e9ecef;
        }

        .message.system {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            text-align: center;
            font-style: italic;
            max-width: 100%;
        }

        .audio-player {
            margin: 20px 0;
            text-align: center;
        }

        .audio-player audio {
            width: 100%;
            max-width: 400px;
            margin: 10px 0;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .debug-info {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .debug-info.hidden {
            display: none;
        }

        .toggle-debug {
            text-align: center;
            margin: 10px 0;
        }

        .toggle-debug button {
            background: none;
            border: 1px solid #ccc;
            color: #666;
            padding: 5px 15px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 语音流水线测试</h1>
        
        <!-- 流水线状态 -->
        <div class="pipeline-status">
            <div class="stage" id="stage-recording">
                <div class="stage-icon">🎤</div>
                <div class="stage-title">录音</div>
                <div class="stage-status">待开始</div>
            </div>
            <div class="stage" id="stage-asr">
                <div class="stage-icon">🎯</div>
                <div class="stage-title">ASR识别</div>
                <div class="stage-status">等待中</div>
            </div>
            <div class="stage" id="stage-llm">
                <div class="stage-icon">🧠</div>
                <div class="stage-title">LLM对话</div>
                <div class="stage-status">等待中</div>
            </div>
            <div class="stage" id="stage-tts">
                <div class="stage-icon">🔊</div>
                <div class="stage-title">TTS合成</div>
                <div class="stage-status">等待中</div>
            </div>
            <div class="stage" id="stage-playback">
                <div class="stage-icon">▶️</div>
                <div class="stage-title">播放</div>
                <div class="stage-status">等待中</div>
            </div>
        </div>

        <!-- 录音控制 -->
        <div class="record-section">
            <button class="record-button" id="recordButton" onclick="toggleRecording()" disabled>
                🎤
            </button>
            <div class="record-text" id="recordText">点击开始录音</div>
        </div>

        <!-- 性能指标 -->
        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="recordingTime">0</div>
                <div class="metric-label">录音时长(秒)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="responseTime">0</div>
                <div class="metric-label">响应时间(秒)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="totalMessages">0</div>
                <div class="metric-label">对话轮数</div>
            </div>
        </div>

        <!-- 对话记录 -->
        <div class="conversation" id="conversation">
            <div class="message system">欢迎使用语音对话测试！请先连接服务器，然后点击录音按钮开始对话。</div>
        </div>

        <!-- 音频播放器 -->
        <div class="audio-player">
            <div>最新AI回复音频：</div>
            <audio id="responseAudio" controls style="display: none;"></audio>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <button class="btn btn-primary" onclick="testASRService()">测试ASR服务</button>
            <button class="btn btn-primary" onclick="testLLMService()">测试LLM服务</button>
            <button class="btn btn-primary" onclick="testTTSService()">测试TTS服务</button>
            <button class="btn btn-secondary" onclick="clearConversation()">清空对话</button>
        </div>

        <!-- 调试信息切换 -->
        <div class="toggle-debug">
            <button onclick="toggleDebug()">显示/隐藏调试信息</button>
        </div>

        <!-- 调试信息 -->
        <div class="debug-info hidden" id="debugInfo">
            <div>调试日志将在这里显示...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;
        let recordingStartTime = null;
        let responseStartTime = null;
        let messageCount = 0;
        let currentAudioData = null;
        let currentTranscription = null;
        let currentLLMResponse = null;
        let stageTimers = {};

        // DOM元素
        const recordButton = document.getElementById('recordButton');
        const recordText = document.getElementById('recordText');
        const conversation = document.getElementById('conversation');
        const responseAudio = document.getElementById('responseAudio');
        const debugInfo = document.getElementById('debugInfo');
        const recordingTimeEl = document.getElementById('recordingTime');
        const responseTimeEl = document.getElementById('responseTime');
        const totalMessagesEl = document.getElementById('totalMessages');

        // 调试日志函数
        function debugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#fc8181' : type === 'success' ? '#68d391' : '#e2e8f0';
            logEntry.textContent = `[${timestamp}] ${message}`;
            debugInfo.appendChild(logEntry);
            debugInfo.scrollTop = debugInfo.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 更新流水线阶段状态
        function updateStageStatus(stageName, status, statusText, details = '') {
            const stageEl = document.getElementById(`stage-${stageName}`);
            if (!stageEl) return;

            // 移除所有状态类
            stageEl.classList.remove('waiting', 'active', 'success', 'error');
            
            // 添加新状态类
            stageEl.classList.add(status);
            
            // 更新状态文本
            const statusEl = stageEl.querySelector('.stage-status');
            if (statusEl) {
                statusEl.textContent = statusText;
            }
            
            // 更新详情（如果有）
            let detailsEl = stageEl.querySelector('.stage-details');
            if (!detailsEl) {
                detailsEl = document.createElement('div');
                detailsEl.className = 'stage-details';
                stageEl.appendChild(detailsEl);
            }
            detailsEl.textContent = details;
            
            // 记录时间
            if (status === 'active') {
                stageTimers[stageName] = Date.now();
                debugLog(`🔄 开始 ${stageName} 阶段`, 'info');
            } else if (status === 'success') {
                const duration = stageTimers[stageName] ? 
                    ((Date.now() - stageTimers[stageName]) / 1000).toFixed(2) : '0';
                debugLog(`✅ ${stageName} 阶段完成 (${duration}s)`, 'success');
                detailsEl.textContent = `${details} (${duration}s)`;
            } else if (status === 'error') {
                const duration = stageTimers[stageName] ? 
                    ((Date.now() - stageTimers[stageName]) / 1000).toFixed(2) : '0';
                debugLog(`❌ ${stageName} 阶段失败 (${duration}s): ${statusText}`, 'error');
            }
        }

        // 重置所有阶段状态
        function resetAllStages() {
            const stages = ['recording', 'asr', 'llm', 'tts', 'playback'];
            stages.forEach(stage => {
                updateStageStatus(stage, 'waiting', '等待中', '');
            });
            updateStageStatus('recording', 'waiting', '待开始', '');
        }

        // 添加消息到对话
        function addMessage(content, type = 'system') {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.textContent = content;
            conversation.appendChild(messageEl);
            conversation.scrollTop = conversation.scrollHeight;
            
            if (type !== 'system') {
                messageCount++;
                totalMessagesEl.textContent = Math.ceil(messageCount / 2);
            }
        }

        // 直接调用ASR服务
        async function callASRService(audioData) {
            updateStageStatus('asr', 'active', '识别中', '正在处理音频...');
            
            try {
                // 将音频数据转换为base64
                const base64Audio = btoa(String.fromCharCode(...new Uint8Array(audioData)));
                
                const response = await fetch('http://localhost:8000/api/asr', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        audio_data: base64Audio,
                        format: 'webm'
                    })
                });

                if (!response.ok) {
                    throw new Error(`ASR服务响应错误: ${response.status}`);
                }

                const result = await response.json();
                
                if (result.text) {
                    updateStageStatus('asr', 'success', '识别完成', `"${result.text}"`);
                    currentTranscription = result.text;
                    addMessage(`${result.text} (置信度: ${(result.confidence * 100).toFixed(1)}%)`, 'user');
                    return result;
                } else {
                    throw new Error('ASR服务未返回识别结果');
                }
                
            } catch (error) {
                updateStageStatus('asr', 'error', '识别失败', error.message);
                debugLog(`ASR服务调用失败: ${error.message}`, 'error');
                
                // 使用模拟ASR响应
                const mockResult = {
                    text: "你好，我想测试一下语音识别功能",
                    confidence: 0.8,
                    mock: true
                };
                updateStageStatus('asr', 'success', '识别完成(模拟)', `"${mockResult.text}"`);
                currentTranscription = mockResult.text;
                addMessage(`${mockResult.text} (模拟结果)`, 'user');
                return mockResult;
            }
        }

        // 直接调用LLM服务
        async function callLLMService(inputText) {
            updateStageStatus('llm', 'active', '生成中', '正在思考回复...');
            
            try {
                const response = await fetch('http://localhost:8000/api/llm', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: inputText,
                        conversation_history: [],
                        system_prompt: "你是一个友好的AI助手"
                    })
                });

                if (!response.ok) {
                    throw new Error(`LLM服务响应错误: ${response.status}`);
                }

                const result = await response.json();
                
                if (result.text) {
                    updateStageStatus('llm', 'success', '生成完成', `"${result.text.substring(0, 30)}..."`);
                    currentLLMResponse = result.text;
                    addMessage(result.text, 'assistant');
                    return result;
                } else {
                    throw new Error('LLM服务未返回回复');
                }
                
            } catch (error) {
                updateStageStatus('llm', 'error', '生成失败', error.message);
                debugLog(`LLM服务调用失败: ${error.message}`, 'error');
                
                // 使用模拟LLM响应
                const mockResponses = {
                    "你好": "你好！很高兴见到你！",
                    "测试": "测试成功！系统运行正常。",
                    "语音识别": "语音识别功能工作正常，我们可以进行对话了。"
                };
                
                let mockText = "我收到了你的消息，这是一个智能回复。";
                for (const [key, value] of Object.entries(mockResponses)) {
                    if (inputText.includes(key)) {
                        mockText = value;
                        break;
                    }
                }
                
                const mockResult = { text: mockText, mock: true };
                updateStageStatus('llm', 'success', '生成完成(模拟)', `"${mockText.substring(0, 30)}..."`);
                currentLLMResponse = mockText;
                addMessage(mockText + " (模拟回复)", 'assistant');
                return mockResult;
            }
        }

        // 直接调用TTS服务
        async function callTTSService(text) {
            updateStageStatus('tts', 'active', '合成中', '正在生成语音...');
            
            try {
                const response = await fetch('http://localhost:8000/api/tts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text,
                        emotion: "friendly",
                        speed: 1.0
                    })
                });

                if (!response.ok) {
                    throw new Error(`TTS服务响应错误: ${response.status}`);
                }

                const result = await response.json();
                
                if (result.audio_data) {
                    updateStageStatus('tts', 'success', '合成完成', `${(result.size / 1024).toFixed(1)}KB音频`);
                    return result;
                } else {
                    throw new Error('TTS服务未返回音频数据');
                }
                
            } catch (error) {
                updateStageStatus('tts', 'error', '合成失败', error.message);
                debugLog(`TTS服务调用失败: ${error.message}`, 'error');
                
                // 生成模拟音频
                const mockAudio = generateMockAudio(text);
                const mockResult = {
                    audio_data: mockAudio,
                    size: mockAudio.length,
                    mock: true
                };
                updateStageStatus('tts', 'success', '合成完成(模拟)', `${(mockAudio.length / 1024).toFixed(1)}KB音频`);
                return mockResult;
            }
        }

        // 生成模拟音频
        function generateMockAudio(text) {
            const sampleRate = 16000;
            const duration = Math.min(text.length * 0.1, 3.0);
            const samples = Math.floor(sampleRate * duration);
            
            // 生成440Hz正弦波
            const audioData = new Float32Array(samples);
            for (let i = 0; i < samples; i++) {
                audioData[i] = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.3;
            }
            
            // 转换为WAV格式
            const buffer = new ArrayBuffer(44 + samples * 2);
            const view = new DataView(buffer);
            
            // WAV头部
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + samples * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, samples * 2, true);
            
            // 音频数据
            let offset = 44;
            for (let i = 0; i < samples; i++) {
                const sample = Math.max(-1, Math.min(1, audioData[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
            
            return new Uint8Array(buffer);
        }



        // 切换录音状态
        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                stopRecording();
            }
        }

        // 开始录音
        async function startRecording() {
            try {
                debugLog('请求麦克风权限...', 'info');
                resetAllStages();
                updateStageStatus('recording', 'active', '录音中', '正在录制音频...');
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                // 检查支持的格式
                const options = {};
                if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
                    options.mimeType = 'audio/webm;codecs=opus';
                    debugLog('使用WebM/Opus格式录音', 'info');
                } else {
                    debugLog('使用默认录音格式', 'info');
                }

                mediaRecorder = new MediaRecorder(stream, options);
                audioChunks = [];
                recordingStartTime = Date.now();
                responseStartTime = Date.now(); // 开始计算总响应时间

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = async function() {
                    const recordingDuration = ((Date.now() - recordingStartTime) / 1000).toFixed(1);
                    recordingTimeEl.textContent = recordingDuration;
                    
                    const audioBlob = new Blob(audioChunks, { type: options.mimeType || 'audio/webm' });
                    debugLog(`录音完成: ${audioBlob.size} bytes, ${recordingDuration}秒`, 'success');
                    
                    updateStageStatus('recording', 'success', '录音完成', `${recordingDuration}秒, ${(audioBlob.size/1024).toFixed(1)}KB`);
                    
                    // 开始完整的流水线处理
                    await processFullPipeline(audioBlob);
                    
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.start(1000);
                isRecording = true;
                
                recordButton.className = 'record-button recording';
                recordButton.textContent = '🛑';
                recordText.textContent = '正在录音... 点击停止';
                
                debugLog('录音已开始', 'success');

            } catch (error) {
                debugLog(`录音启动失败: ${error.message}`, 'error');
                updateStageStatus('recording', 'error', '录音失败', error.message);
                addMessage(`录音失败: ${error.message}`, 'system');
            }
        }

        // 停止录音
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                debugLog('停止录音...', 'info');
                mediaRecorder.stop();
                isRecording = false;
                
                recordButton.className = 'record-button';
                recordButton.textContent = '🎤';
                recordText.textContent = '处理中... 请稍候';
            }
        }

        // 处理完整流水线
        async function processFullPipeline(audioBlob) {
            try {
                recordText.textContent = '正在处理...';
                
                // 转换音频为ArrayBuffer
                const arrayBuffer = await audioBlob.arrayBuffer();
                currentAudioData = arrayBuffer;
                
                // 1. ASR阶段
                const asrResult = await callASRService(arrayBuffer);
                if (!asrResult || !asrResult.text) {
                    throw new Error('ASR处理失败');
                }
                
                // 2. LLM阶段
                const llmResult = await callLLMService(asrResult.text);
                if (!llmResult || !llmResult.text) {
                    throw new Error('LLM处理失败');
                }
                
                // 3. TTS阶段
                const ttsResult = await callTTSService(llmResult.text);
                if (!ttsResult || !ttsResult.audio_data) {
                    throw new Error('TTS处理失败');
                }
                
                // 4. 播放阶段
                await playAudio(ttsResult.audio_data);
                
                // 计算总响应时间
                if (responseStartTime) {
                    const totalTime = ((Date.now() - responseStartTime) / 1000).toFixed(1);
                    responseTimeEl.textContent = totalTime;
                    debugLog(`完整流水线处理完成，总耗时: ${totalTime}秒`, 'success');
                }
                
                recordText.textContent = '点击开始录音';
                
            } catch (error) {
                debugLog(`流水线处理失败: ${error.message}`, 'error');
                recordText.textContent = '处理失败，点击重试';
            }
        }

        // 播放音频
        async function playAudio(audioData) {
            updateStageStatus('playback', 'active', '播放中', '正在播放音频...');
            
            try {
                // 创建音频blob
                const audioBlob = new Blob([audioData], {type: 'audio/wav'});
                const audioUrl = URL.createObjectURL(audioBlob);
                
                // 更新音频播放器
                responseAudio.src = audioUrl;
                responseAudio.style.display = 'block';
                
                // 播放音频
                await responseAudio.play();
                
                updateStageStatus('playback', 'success', '播放完成', `${(audioData.length/1024).toFixed(1)}KB音频`);
                debugLog(`音频播放成功: ${audioData.length} bytes`, 'success');
                
            } catch (error) {
                updateStageStatus('playback', 'error', '播放失败', error.message);
                debugLog(`音频播放失败: ${error.message}`, 'error');
            }
        }



        // 清空对话
        function clearConversation() {
            conversation.innerHTML = '<div class="message system">对话已清空</div>';
            messageCount = 0;
            totalMessagesEl.textContent = '0';
            recordingTimeEl.textContent = '0';
            responseTimeEl.textContent = '0';
            responseAudio.style.display = 'none';
            resetAllStages();
            debugLog('对话已清空', 'info');
        }

        // 测试ASR服务
        async function testASRService() {
            debugLog('开始测试ASR服务...', 'info');
            addMessage('正在测试ASR服务...', 'system');
            
            // 生成测试音频
            const testAudio = generateMockAudio("测试音频");
            const result = await callASRService(testAudio.buffer);
            
            if (result) {
                addMessage(`ASR测试结果: ${result.text} ${result.mock ? '(模拟)' : ''}`, 'system');
            } else {
                addMessage('ASR测试失败', 'system');
            }
        }

        // 测试LLM服务
        async function testLLMService() {
            debugLog('开始测试LLM服务...', 'info');
            addMessage('正在测试LLM服务...', 'system');
            
            const testText = "你好，这是一个测试消息";
            const result = await callLLMService(testText);
            
            if (result) {
                addMessage(`LLM测试结果: ${result.text} ${result.mock ? '(模拟)' : ''}`, 'system');
            } else {
                addMessage('LLM测试失败', 'system');
            }
        }

        // 测试TTS服务
        async function testTTSService() {
            debugLog('开始测试TTS服务...', 'info');
            addMessage('正在测试TTS服务...', 'system');
            
            const testText = "你好，这是TTS测试音频";
            const result = await callTTSService(testText);
            
            if (result) {
                addMessage(`TTS测试结果: 生成了 ${(result.size/1024).toFixed(1)}KB 音频 ${result.mock ? '(模拟)' : ''}`, 'system');
                await playAudio(result.audio_data);
            } else {
                addMessage('TTS测试失败', 'system');
            }
        }

        // 切换调试信息显示
        function toggleDebug() {
            debugInfo.classList.toggle('hidden');
        }

        // 页面加载完成后初始化
        window.onload = function() {
            debugLog('页面加载完成', 'info');
            recordText.textContent = '点击开始录音';
            recordButton.disabled = false;
            resetAllStages();
            
            // 显示初始说明
            addMessage('语音流水线测试已准备就绪！', 'system');
            addMessage('点击录音按钮开始完整测试，或使用下方按钮单独测试各个服务', 'system');
        };

        // 定期更新录音时间
        setInterval(() => {
            if (isRecording && recordingStartTime) {
                const elapsed = ((Date.now() - recordingStartTime) / 1000).toFixed(1);
                recordingTimeEl.textContent = elapsed;
            }
            
            if (!isRecording && recordText.textContent === '等待AI回复...' || recordText.textContent === '正在识别...') {
                // 重置录音按钮状态
                setTimeout(() => {
                    if (!isRecording) {
                        recordText.textContent = '点击开始录音';
                    }
                }, 5000);
            }
        }, 100);
    </script>
</body>
</html>