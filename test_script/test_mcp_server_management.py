import sys
import os
import json

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_mcp_server_management():
    """
    测试MCP服务器管理功能
    """
    print("开始测试MCP服务器管理功能...")
    
    try:
        # Import the services directly
        from services.mcp_service import mcp_service
        from functions.master_service import master_service

        # 1. 测试注册MCP服务器
        print("\n1. 测试注册MCP服务器...")
        
        # 注册本地服务器
        local_server_registered = master_service.call(
            "register_server", 
            server_name="local_server", 
            server_url="http://localhost:3000"
        )
        print(f"✓ 本地服务器注册: {'成功' if local_server_registered else '失败'}")
        
        # 注册远程服务器
        remote_server_registered = master_service.call(
            "register_server", 
            server_name="remote_server", 
            server_url="http://remote.example.com:3000"
        )
        print(f"✓ 远程服务器注册: {'成功' if remote_server_registered else '失败'}")
        
        # 尝试重复注册同一个服务器（应该失败）
        duplicate_registration = master_service.call(
            "register_server", 
            server_name="local_server", 
            server_url="http://localhost:3000/duplicate"
        )
        print(f"✓ 重复服务器注册: {'成功' if duplicate_registration else '失败（预期）'}")
        
        # 2. 测试列出所有服务器
        print("\n2. 测试列出所有服务器...")
        servers = master_service.call("list_servers")
        print(f"✓ 成功获取到 {len(servers)} 个服务器")
        for server in servers:
            print(f"  - {server['name']}: {server['url']}")
        
        # 3. 测试获取服务器信息
        print("\n3. 测试获取服务器信息...")
        local_server_info = master_service.call("get_server_info", server_name="local_server")
        if local_server_info:
            print(f"✓ 本地服务器信息获取成功")
            print(f"  服务器名称: {local_server_info['name']}")
            print(f"  服务器URL: {local_server_info['url']}")
            print(f"  注册时间: {local_server_info.get('registered_at', '未知')}")
        else:
            print("✗ 本地服务器信息获取失败")
        
        # 尝试获取不存在的服务器信息（应该返回None）
        non_existent_server_info = master_service.call("get_server_info", server_name="non_existent_server")
        print(f"✓ 非存在服务器信息获取: {'成功' if non_existent_server_info else '失败（预期）'}")
        
        # 4. 测试获取服务器工具
        print("\n4. 测试获取服务器工具...")
        local_server_tools = master_service.call("get_server_tools", server_name="local_server")
        print(f"✓ 本地服务器工具获取: {'成功' if local_server_tools is not None else '失败'}")
        if local_server_tools:
            print(f"  获取到 {len(local_server_tools)} 个工具")
            for tool in local_server_tools:
                print(f"  - {tool['name']}: {tool['description']}")
        
        remote_server_tools = master_service.call("get_server_tools", server_name="remote_server")
        print(f"✓ 远程服务器工具获取: {'成功' if remote_server_tools is not None else '失败'}")
        if remote_server_tools:
            print(f"  获取到 {len(remote_server_tools)} 个工具")
            for tool in remote_server_tools:
                print(f"  - {tool['name']}: {tool['description']}")
        
        # 尝试获取不存在服务器的工具（应该返回空列表）
        non_existent_server_tools = master_service.call("get_server_tools", server_name="non_existent_server")
        print(f"✓ 非存在服务器工具获取: {'成功' if non_existent_server_tools is not None else '失败'}")
        if non_existent_server_tools is not None:
            print(f"  获取到 {len(non_existent_server_tools)} 个工具")
        
        # 5. 测试获取所有工具用于reranker
        print("\n5. 测试获取所有工具用于reranker...")
        all_tools = master_service.get_all_tools_for_reranker()
        print(f"✓ 成功获取到 {len(all_tools)} 个工具用于reranker")
        for tool in all_tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 6. 测试注销服务器
        print("\n6. 测试注销服务器...")
        remote_server_unregistered = master_service.call("unregister_server", server_name="remote_server")
        print(f"✓ 远程服务器注销: {'成功' if remote_server_unregistered else '失败'}")
        
        # 尝试注销不存在的服务器（应该失败）
        non_existent_server_unregistered = master_service.call("unregister_server", server_name="non_existent_server")
        print(f"✓ 非存在服务器注销: {'成功' if non_existent_server_unregistered else '失败（预期）'}")
        
        # 再次列出所有服务器，确认远程服务器已被注销
        print("\n7. 确认服务器注销结果...")
        servers_after_unregistration = master_service.call("list_servers")
        print(f"✓ 注销后服务器数量: {len(servers_after_unregistration)}")
        for server in servers_after_unregistration:
            print(f"  - {server['name']}: {server['url']}")
        
        print("\n" + "="*50)
        print("✓ MCP服务器管理功能测试完成")
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def demo_mcp_server_workflow():
    """
    演示MCP服务器管理完整工作流程
    """
    print("\n" + "="*50)
    print("演示MCP服务器管理完整工作流程")
    print("="*50)
    
    try:
        # Import the services directly
        from services.mcp_service import mcp_service
        from functions.master_service import master_service

        # 模拟实际使用场景
        print("场景：为新的AI助手项目配置MCP服务器")
        
        # 1. 注册多个MCP服务器
        print("\n1. 注册MCP服务器...")
        servers_to_register = [
            {"name": "search_server", "url": "http://search-service:8000"},
            {"name": "news_server", "url": "http://news-service:8000"},
            {"name": "memory_server", "url": "http://memory-service:8000"}
        ]
        
        for server_info in servers_to_register:
            result = master_service.call(
                "register_server", 
                server_name=server_info["name"], 
                server_url=server_info["url"]
            )
            print(f"  注册 {server_info['name']}: {'✓' if result else '✗'}")
        
        # 2. 查看所有已注册的服务器
        print("\n2. 当前已注册的服务器:")
        servers = master_service.call("list_servers")
        for server in servers:
            print(f"  - {server['name']}: {server['url']}")
        
        # 3. 获取每个服务器的工具信息
        print("\n3. 各服务器提供的工具:")
        for server in servers:
            tools = master_service.call("get_server_tools", server_name=server["name"])
            print(f"  {server['name']} ({len(tools)} 个工具):")
            if tools:
                for tool in tools:
                    print(f"    - {tool['name']}: {tool['description']}")
            else:
                print("    - 暂无工具信息")
        
        # 4. 获取所有工具用于智能推荐
        print("\n4. 所有可用工具（用于智能推荐）:")
        all_tools = master_service.get_all_tools_for_reranker()
        for tool in all_tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 5. 模拟根据用户问题选择最合适的工具
        print("\n5. 工具智能推荐示例:")
        user_questions = [
            "今天北京的天气怎么样？",
            "给我找一些关于人工智能的最新新闻",
            "你现在在做什么？"
        ]
        
        # 这里简化处理，实际应该使用reranker服务
        for question in user_questions:
            print(f"  用户问题: {question}")
            print(f"  推荐处理方式: 根据问题内容和工具描述进行匹配")
        
        # 6. 注销不再需要的服务器
        print("\n6. 注销测试服务器...")
        servers_to_unregister = ["search_server", "news_server", "memory_server"]
        for server_name in servers_to_unregister:
            result = master_service.call("unregister_server", server_name=server_name)
            print(f"  注销 {server_name}: {'✓' if result else '✗'}")
        
        # 7. 确认所有服务器已注销
        print("\n7. 确认服务器注销结果:")
        final_servers = master_service.call("list_servers")
        if not final_servers:
            print("  ✓ 所有服务器已成功注销")
        else:
            print("  仍有服务器未注销:")
            for server in final_servers:
                print(f"    - {server['name']}: {server['url']}")
        
        print("\n" + "="*50)
        print("✓ MCP服务器管理完整工作流程演示完成")

    except Exception as e:
        print(f"✗ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行服务器管理功能测试
    test_mcp_server_management()
    
    # 运行完整工作流程演示
    demo_mcp_server_workflow()
