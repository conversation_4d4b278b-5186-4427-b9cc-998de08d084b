#!/usr/bin/env python3
"""
调试音频流问题 - 检查前端发送的音频数据是否是真实的
"""

import asyncio
import websockets
import json
import base64
import numpy as np
from datetime import datetime

WEBSOCKET_URL = "ws://localhost:8000/ws"
TEST_USER_ID = "debug_user"

async def debug_audio_flow():
    """调试音频流，检查前端发送的数据"""
    print("🔍 Debugging audio flow...")
    
    try:
        uri = f"{WEBSOCKET_URL}/{TEST_USER_ID}"
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected")
            
            # 等待连接确认
            await asyncio.sleep(1)
            
            # 开始会话
            start_session = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_session))
            print("📤 Session start sent")
            
            # 等待会话开始确认
            response = await websocket.recv()
            response_data = json.loads(response)
            print(f"📥 Session response: {response_data.get('type')}")
            
            # 发送一个音频块并分析其内容
            print("\n🎵 Analyzing audio data...")
            
            # 生成测试音频数据（模拟前端的行为）
            sample_rate = 16000
            duration_ms = 100
            samples = int(sample_rate * duration_ms / 1000)  # 1600 samples
            
            # 生成包含特定模式的音频数据
            audio_data = bytearray()
            for i in range(samples):
                # 生成一个简单的正弦波
                import math
                frequency = 440  # A4音符
                time = i / sample_rate
                amplitude = 0.5
                sample = amplitude * math.sin(2 * math.pi * frequency * time)
                
                # 转换为16位整数
                sample_int16 = int(sample * 32767)
                sample_int16 = max(-32768, min(32767, sample_int16))
                
                # 小端字节序
                audio_data.extend(sample_int16.to_bytes(2, byteorder='little', signed=True))
            
            print(f"📊 Generated audio data: {len(audio_data)} bytes")
            print(f"📊 First 20 bytes: {list(audio_data[:20])}")
            
            # 分析音频数据的特征
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            print(f"📊 Audio array shape: {audio_array.shape}")
            print(f"📊 Audio range: {audio_array.min()} to {audio_array.max()}")
            print(f"📊 Audio mean: {audio_array.mean():.2f}")
            print(f"📊 Audio std: {audio_array.std():.2f}")
            
            # 发送音频数据
            audio_b64 = base64.b64encode(audio_data).decode('utf-8')
            audio_message = {
                "type": "audio_chunk",
                "data": audio_b64,
                "format": "pcm16",
                "sample_rate": 16000,
                "channels": 1
            }
            
            await websocket.send(json.dumps(audio_message))
            print("📤 Audio chunk sent")
            
            # 发送多个音频块以触发处理
            for i in range(10):  # 发送10个块，总共1秒音频
                await websocket.send(json.dumps(audio_message))
                await asyncio.sleep(0.1)
            
            print("📤 Multiple audio chunks sent, waiting for responses...")
            
            # 等待并分析响应
            responses_received = 0
            while responses_received < 5:  # 等待最多5个响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    response_type = response_data.get("type")
                    
                    print(f"📥 Response {responses_received + 1}: {response_type}")
                    
                    if response_type == "transcription":
                        text = response_data.get("text", "")
                        confidence = response_data.get("confidence", 0)
                        print(f"   📝 Transcription: '{text}' (confidence: {confidence:.2f})")
                        print(f"   🔍 Analysis: This shows what ASR thinks the audio contains")
                        
                    elif response_type == "audio_chunk":
                        audio_data_response = response_data.get("data", "")
                        print(f"   🎵 Audio response: {len(audio_data_response)} chars (base64)")
                        print(f"   🔍 Analysis: This is TTS-generated audio from backend")
                        
                    elif response_type == "response_complete":
                        print(f"   ✅ Processing complete")
                        break
                        
                    elif response_type == "error":
                        error_msg = response_data.get("message", "Unknown error")
                        print(f"   ❌ Error: {error_msg}")
                        
                    responses_received += 1
                    
                except asyncio.TimeoutError:
                    print("⏰ Response timeout")
                    break
            
            print(f"\n📊 Received {responses_received} responses")
            
    except Exception as e:
        print(f"❌ Debug error: {e}")

async def main():
    print("🔍 Audio Flow Debug Tool")
    print("=" * 50)
    print("This tool will:")
    print("1. Connect to the WebSocket")
    print("2. Send simulated audio data")
    print("3. Analyze what the backend receives and processes")
    print("4. Show the complete audio processing pipeline")
    print("=" * 50)
    
    await debug_audio_flow()
    
    print("\n" + "=" * 50)
    print("🎯 KEY FINDINGS:")
    print("- If transcription shows random text: ASR is processing simulated data")
    print("- If transcription is empty: Audio format/processing issue")
    print("- If no audio response: TTS or pipeline issue")
    print("- This explains why frontend doesn't reflect real user speech")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())