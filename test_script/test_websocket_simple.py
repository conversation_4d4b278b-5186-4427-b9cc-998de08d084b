#!/usr/bin/env python3
"""
简化的WebSocket测试 - 测试语音流的输入输出
"""
import asyncio
import websockets
import json
import base64
import requests
import wave
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_simple():
    """简化的WebSocket测试"""
    backend_url = "http://localhost:8000"
    websocket_url = "ws://localhost:8000"
    audio_dir = Path("backend/audio_recordings")
    
    logger.info("🔌 开始简化WebSocket测试...")
    
    # 1. 用户登录获取ID
    try:
        response = requests.post(
            f"{backend_url}/auth/login",
            params={"username": "test_user", "password": "test_password"},
            timeout=10
        )
        if response.status_code != 200:
            logger.error(f"❌ 登录失败: {response.status_code}")
            return False
        
        user_data = response.json()
        user_id = user_data.get('user', {}).get('id', 1)
        logger.info(f"✅ 用户登录成功，ID: {user_id}")
    except Exception as e:
        logger.error(f"❌ 登录异常: {e}")
        return False
    
    # 2. WebSocket连接测试
    try:
        uri = f"{websocket_url}/ws/{user_id}"
        logger.info(f"🔌 连接WebSocket: {uri}")
        
        # 移除timeout参数，使用默认连接
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 启动会话
            start_msg = {"type": "start_session", "npc_id": 1}
            await websocket.send(json.dumps(start_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动确认
            session_started = False
            session_id = None
            
            for attempt in range(3):  # 尝试接收3次消息
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    
                    logger.info(f"📥 收到响应: {response_data.get('type')} - {response_data}")
                    
                    if response_data.get("type") == "session_started":
                        session_id = response_data.get("session_id")
                        logger.info(f"✅ 会话启动成功，ID: {session_id}")
                        session_started = True
                        break
                    elif response_data.get("type") == "connection_established":
                        logger.info("✅ 连接已建立，继续等待会话启动...")
                        continue
                    else:
                        logger.warning(f"⚠️ 收到意外响应: {response_data}")
                        
                except asyncio.TimeoutError:
                    logger.error(f"❌ 等待响应超时 (尝试 {attempt + 1}/3)")
                    if attempt == 2:
                        return False
            
            if not session_started:
                logger.error("❌ 会话启动失败")
                return False
            
            # 3. 测试音频流
            audio_files = list(audio_dir.glob("*.wav"))
            if not audio_files:
                logger.error("❌ 没有找到音频文件")
                return False
            
            test_file = audio_files[0]
            logger.info(f"📁 使用测试音频: {test_file.name}")
            
            # 读取音频文件
            with wave.open(str(test_file), 'rb') as wav_file:
                audio_data = wav_file.readframes(wav_file.getnframes())
                sample_rate = wav_file.getframerate()
            
            logger.info(f"📊 音频信息: {len(audio_data)} bytes, {sample_rate} Hz")
            
            # 分块发送音频数据 - 使用更小的块避免WebSocket消息大小限制
            chunk_size = 512  # 减小块大小到512字节
            chunks = [audio_data[i:i + chunk_size] for i in range(0, len(audio_data), chunk_size)]
            
            # 限制发送的块数量，先测试连通性
            max_chunks = min(10, len(chunks))  # 最多发送10个块进行测试
            chunks = chunks[:max_chunks]
            
            logger.info(f"🔪 音频分为 {len(chunks)} 个块 (每块 {chunk_size} 字节，限制发送 {max_chunks} 个块)")
            
            # 发送音频流
            responses_received = []
            
            for i, chunk in enumerate(chunks):
                # 发送音频块
                base64_chunk = base64.b64encode(chunk).decode('utf-8')
                audio_msg = {"type": "audio_chunk", "data": base64_chunk}
                await websocket.send(json.dumps(audio_msg))
                
                logger.info(f"📤 发送音频块 {i+1}/{len(chunks)} ({len(chunk)} bytes)")
                
                # 检查响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=0.5)
                    response_data = json.loads(response)
                    responses_received.append(response_data)
                    
                    msg_type = response_data.get('type')
                    if msg_type == 'transcription':
                        text = response_data.get('text', '')
                        confidence = response_data.get('confidence', 0)
                        logger.info(f"🎯 转录结果: '{text}' (置信度: {confidence:.2f})")
                    elif msg_type == 'audio_chunk':
                        logger.info("🔊 收到AI音频响应")
                    elif msg_type == 'response_complete':
                        logger.info("✅ AI响应完成")
                        break
                    else:
                        logger.info(f"📥 收到响应: {msg_type}")
                        
                except asyncio.TimeoutError:
                    # 没有立即响应，继续发送
                    pass
                
                # 模拟实时流的延迟 - 增加延迟避免发送过快
                await asyncio.sleep(0.2)
            
            # 等待最终响应
            logger.info("⏳ 等待最终处理结果...")
            try:
                timeout_count = 0
                max_timeout = 15  # 15秒超时
                
                while timeout_count < max_timeout:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=1)
                        response_data = json.loads(response)
                        responses_received.append(response_data)
                        
                        msg_type = response_data.get('type')
                        logger.info(f"📥 最终响应: {msg_type}")
                        
                        if msg_type == 'response_complete':
                            logger.info("✅ 语音处理流程完成")
                            break
                        elif msg_type == 'transcription':
                            text = response_data.get('text', '')
                            logger.info(f"🎯 最终转录: '{text}'")
                        elif msg_type == 'audio_chunk':
                            logger.info("🔊 收到AI音频块")
                        elif msg_type == 'error':
                            error_msg = response_data.get('message', '未知错误')
                            logger.error(f"❌ 处理错误: {error_msg}")
                            break
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        if timeout_count % 5 == 0:
                            logger.info(f"⏰ 等待响应中... ({timeout_count}s)")
                        continue
                        
            except Exception as e:
                logger.error(f"❌ 等待最终响应异常: {e}")
            
            # 统计结果
            logger.info("=" * 50)
            logger.info("📊 WebSocket语音流测试结果:")
            logger.info(f"   发送音频块数: {len(chunks)}")
            logger.info(f"   收到响应数: {len(responses_received)}")
            
            # 检查是否有转录结果
            transcriptions = [r for r in responses_received if r.get('type') == 'transcription']
            audio_outputs = [r for r in responses_received if r.get('type') == 'audio_chunk']
            
            if transcriptions:
                logger.info("✅ 语音输入成功 - 收到转录结果")
            else:
                logger.warning("⚠️ 语音输入可能有问题 - 没有收到转录结果")
            
            if audio_outputs:
                logger.info("✅ 语音输出成功 - 收到音频响应")
            else:
                logger.warning("⚠️ 语音输出可能有问题 - 没有收到音频响应")
            
            # 统计响应类型
            response_types = {}
            for resp in responses_received:
                resp_type = resp.get('type', 'unknown')
                response_types[resp_type] = response_types.get(resp_type, 0) + 1
            
            for resp_type, count in response_types.items():
                logger.info(f"   {resp_type}: {count} 次")
            
            logger.info("=" * 50)
            
            return len(transcriptions) > 0 or len(audio_outputs) > 0
            
    except Exception as e:
        logger.error(f"❌ WebSocket测试异常: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 简化WebSocket语音流测试开始...")
    
    success = await test_websocket_simple()
    
    if success:
        logger.info("🎉 WebSocket语音流测试成功！")
        logger.info("💡 前端和后端的语音流连通性正常")
    else:
        logger.error("❌ WebSocket语音流测试失败！")

if __name__ == "__main__":
    asyncio.run(main())