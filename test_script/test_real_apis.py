#!/usr/bin/env python3
"""
测试真实的LLM和TTS API接口
"""

import os
import sys
import logging
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('backend/.env')

# 添加backend目录到路径
sys.path.append('backend')

from services.llm_service import LLMService
from services.tts_service import TTSService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_real_llm_api():
    """测试真实的LLM API"""
    logger.info("=" * 60)
    logger.info("Real LLM API Test")
    logger.info("=" * 60)
    
    try:
        # 初始化LLM服务
        api_key = os.getenv("VOLCANO_API_KEY")
        endpoint = os.getenv("VOLCANO_ENDPOINT")
        
        logger.info(f"API Key: {api_key[:20]}..." if api_key else "None")
        logger.info(f"Endpoint: {endpoint}")
        
        if not api_key or not endpoint:
            logger.error("❌ Missing VOLCANO_API_KEY or VOLCANO_ENDPOINT")
            return False
            
        llm_service = LLMService(api_key, endpoint)
        logger.info("✅ LLM service initialized")
        
        # 测试简单对话
        test_message = "你好，请简单介绍一下自己"
        logger.info(f"🤖 User: {test_message}")
        
        response = await llm_service.generate_response(
            user_input=test_message,
            conversation_history=[],
            system_prompt="你是一个友好的AI助手，请用中文回答问题。",
            use_tools=False
        )
        
        if response and response.get("success"):
            speak_content = response.get("speak_content", {})
            logger.info(f"✅ AI: {speak_content.get('text', 'No text')}")
            logger.info(f"✅ Mock Mode: {response.get('mock_mode', 'Unknown')}")
            
            if not response.get('mock_mode'):
                logger.info("🎉 Real API call successful!")
                return True
            else:
                logger.warning("⚠️ Fallback to mock response")
                return False
        else:
            logger.error(f"❌ LLM API failed: {response}")
            return False
            
    except Exception as e:
        logger.error(f"❌ LLM test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def test_real_tts_api():
    """测试真实的TTS API"""
    logger.info("\n" + "=" * 60)
    logger.info("Real TTS API Test")
    logger.info("=" * 60)
    
    try:
        # 初始化TTS服务
        api_key = os.getenv("MINIMAX_API_KEY")
        group_id = os.getenv("MINIMAX_GROUP_ID")
        
        logger.info(f"API Key: {api_key[:50]}..." if api_key else "None")
        logger.info(f"Group ID: {group_id}")
        
        if not api_key or not group_id:
            logger.error("❌ Missing MINIMAX_API_KEY or MINIMAX_GROUP_ID")
            return False
            
        tts_service = TTSService(api_key, group_id)
        logger.info("✅ TTS service initialized")
        
        # 测试语音合成
        test_text = "你好，这是一个TTS测试"
        logger.info(f"🔊 Text: {test_text}")
        
        result = await tts_service.synthesize_speech(
            text=test_text,
            emotion="neutral",
            speed=1.0,
            output_file=None
        )
        
        if result and result.get("success") and result.get("audio_data"):
            audio_data = result["audio_data"]
            logger.info(f"✅ Audio generated: {len(audio_data)} bytes")
            
            # 保存测试音频
            output_dir = Path("api_test_output")
            output_dir.mkdir(exist_ok=True)
            output_file = output_dir / "tts_real_api_test.wav"
            
            with open(output_file, 'wb') as f:
                f.write(audio_data)
            
            logger.info(f"✅ Audio saved to: {output_file}")
            logger.info("🎉 Real TTS API call successful!")
            return True
        else:
            logger.error(f"❌ TTS API failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ TTS test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def test_combined_real_apis():
    """测试LLM + TTS组合真实API"""
    logger.info("\n" + "=" * 60)
    logger.info("Combined Real APIs Test")
    logger.info("=" * 60)
    
    try:
        # 初始化服务
        llm_api_key = os.getenv("VOLCANO_API_KEY")
        llm_endpoint = os.getenv("VOLCANO_ENDPOINT")
        llm_service = LLMService(llm_api_key, llm_endpoint)
        
        tts_api_key = os.getenv("MINIMAX_API_KEY")
        tts_group_id = os.getenv("MINIMAX_GROUP_ID")
        tts_service = TTSService(tts_api_key, tts_group_id)
        
        logger.info("✅ Both services initialized")
        
        # 测试对话
        user_input = "请用一句话介绍人工智能"
        logger.info(f"🤖 User: {user_input}")
        
        # Step 1: LLM对话
        logger.info("🧠 Calling LLM API...")
        llm_response = await llm_service.generate_response(
            user_input=user_input,
            conversation_history=[],
            system_prompt="你是一个友好的AI助手，请用中文简洁回答问题。",
            use_tools=False
        )
        
        if not llm_response or not llm_response.get("success"):
            logger.error(f"❌ LLM failed: {llm_response}")
            return False
        
        speak_content = llm_response.get("speak_content", {})
        ai_text = speak_content.get("text", "No response")
        emotion = speak_content.get("emotion", "neutral")
        speed = speak_content.get("speed", 1.0)
        
        logger.info(f"✅ AI: {ai_text}")
        logger.info(f"✅ Mock Mode: {llm_response.get('mock_mode', 'Unknown')}")
        
        # Step 2: TTS合成
        logger.info("🔊 Calling TTS API...")
        tts_result = await tts_service.synthesize_speech(
            text=ai_text,
            emotion=emotion,
            speed=speed,
            output_file=None
        )
        
        if not tts_result or not tts_result.get("success") or not tts_result.get("audio_data"):
            logger.error(f"❌ TTS failed: {tts_result}")
            return False
        
        audio_data = tts_result["audio_data"]
        logger.info(f"✅ Audio generated: {len(audio_data)} bytes")
        
        # 保存结果
        output_dir = Path("api_test_output")
        output_dir.mkdir(exist_ok=True)
        
        # 保存音频
        audio_file = output_dir / "combined_real_api_test.wav"
        with open(audio_file, 'wb') as f:
            f.write(audio_data)
        
        # 保存对话记录
        transcript_file = output_dir / "combined_real_api_test.txt"
        with open(transcript_file, 'w', encoding='utf-8') as f:
            f.write(f"User: {user_input}\n")
            f.write(f"AI: {ai_text}\n")
            f.write(f"Emotion: {emotion}\n")
            f.write(f"Speed: {speed}\n")
            f.write(f"Mock Mode: {llm_response.get('mock_mode', False)}\n")
            f.write(f"Audio File: {audio_file}\n")
        
        logger.info(f"✅ Results saved to: {output_dir}")
        
        if not llm_response.get('mock_mode'):
            logger.info("🎉 Combined real APIs test successful!")
            return True
        else:
            logger.warning("⚠️ LLM used mock response, but TTS was real")
            return False
            
    except Exception as e:
        logger.error(f"❌ Combined test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    logger.info("Starting Real APIs Test...")
    
    # 检查环境变量
    required_vars = ["VOLCANO_API_KEY", "VOLCANO_ENDPOINT", "MINIMAX_API_KEY", "MINIMAX_GROUP_ID"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    success = True
    
    # 1. 测试LLM API
    if not await test_real_llm_api():
        success = False
    
    # 2. 测试TTS API
    if not await test_real_tts_api():
        success = False
    
    # 3. 测试组合API
    if not await test_combined_real_apis():
        success = False
    
    if success:
        logger.info("\n🎉 All real API tests completed successfully!")
    else:
        logger.error("\n❌ Some real API tests failed!")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
