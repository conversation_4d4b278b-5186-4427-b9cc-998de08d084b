import requests
import json

def test_backend_connectivity():
    # Test the root endpoint
    try:
        response = requests.get("http://localhost:8000/")
        print(f"Root endpoint status code: {response.status_code}")
        print(f"Root endpoint response: {response.json()}")
    except Exception as e:
        print(f"Error accessing root endpoint: {e}")
    
    # Test the test endpoint
    try:
        response = requests.get("http://localhost:8000/test")
        print(f"Test endpoint status code: {response.status_code}")
        print(f"Test endpoint response: {response.json()}")
    except Exception as e:
        print(f"Error accessing test endpoint: {e}")
    
    # Test the health endpoint
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"Health endpoint status code: {response.status_code}")
        print(f"Health endpoint response: {response.json()}")
    except Exception as e:
        print(f"Error accessing health endpoint: {e}")

if __name__ == "__main__":
    test_backend_connectivity()
