#!/usr/bin/env python3
"""
前端模拟测试 - 模拟Flutter前端的WebSocket连接和音频流行为
"""
import asyncio
import websockets
import json
import base64
import requests
import os
import wave
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FlutterFrontendSimulator:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.websocket_url = "ws://localhost:8000"
        self.audio_dir = Path("backend/audio_recordings")
        self.user_data = None
        self.websocket = None
        self.session_id = None
        
    async def simulate_app_startup(self):
        """模拟应用启动流程"""
        logger.info("📱 模拟Flutter应用启动...")
        
        # 1. 检查后端连接
        logger.info("🔍 检查后端连接...")
        try:
            response = requests.get(f"{self.backend_url}/", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 后端连接正常")
            else:
                logger.error(f"❌ 后端连接异常: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 后端连接失败: {e}")
            return False
        
        # 2. 获取NPC列表
        logger.info("👥 获取NPC列表...")
        try:
            response = requests.get(f"{self.backend_url}/npcs", timeout=5)
            if response.status_code == 200:
                npcs_data = response.json()
                logger.info(f"✅ 获取到 {len(npcs_data.get('npcs', []))} 个NPC")
                for npc in npcs_data.get('npcs', []):
                    logger.info(f"  - {npc.get('name')} (ID: {npc.get('id')})")
            else:
                logger.error(f"❌ 获取NPC列表失败: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ 获取NPC列表异常: {e}")
        
        return True
    
    async def simulate_user_login(self):
        """模拟用户登录"""
        logger.info("🔐 模拟用户登录...")
        
        try:
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params={
                    "username": "test_user",
                    "password": "test_password"
                },
                timeout=10
            )
            
            if response.status_code == 200:
                self.user_data = response.json()
                user_info = self.user_data.get('user', {})
                logger.info("✅ 用户登录成功")
                logger.info(f"  用户ID: {user_info.get('id')}")
                logger.info(f"  用户名: {user_info.get('username')}")
                logger.info(f"  昵称: {user_info.get('nickname')}")
                return True
            else:
                logger.error(f"❌ 用户登录失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 用户登录异常: {e}")
            return False
    
    async def simulate_voice_chat_screen_init(self):
        """模拟语音聊天界面初始化"""
        logger.info("🎤 模拟语音聊天界面初始化...")
        
        if not self.user_data:
            logger.error("❌ 用户未登录")
            return False
        
        user_id = self.user_data.get('user', {}).get('id')
        
        # 建立WebSocket连接
        try:
            uri = f"{self.websocket_url}/ws/{user_id}"
            logger.info(f"🔌 连接WebSocket: {uri}")
            
            self.websocket = await websockets.connect(uri, timeout=10)
            logger.info("✅ WebSocket连接成功")
            
            # 启动监听任务
            asyncio.create_task(self.listen_to_websocket())
            
            return True
            
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
            return False
    
    async def listen_to_websocket(self):
        """监听WebSocket消息"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_websocket_message(data)
                except json.JSONDecodeError:
                    logger.warning(f"⚠️ 无法解析WebSocket消息: {message}")
                except Exception as e:
                    logger.error(f"❌ 处理WebSocket消息异常: {e}")
        except websockets.exceptions.ConnectionClosed:
            logger.info("🔌 WebSocket连接已关闭")
        except Exception as e:
            logger.error(f"❌ WebSocket监听异常: {e}")
    
    async def handle_websocket_message(self, data):
        """处理WebSocket消息"""
        message_type = data.get('type', 'unknown')
        
        if message_type == 'session_started':
            self.session_id = data.get('session_id')
            npc_id = data.get('npc_id')
            logger.info(f"✅ 会话启动成功 - 会话ID: {self.session_id}, NPC ID: {npc_id}")
            
        elif message_type == 'transcription':
            text = data.get('text', '')
            confidence = data.get('confidence', 0)
            logger.info(f"🎯 收到转录: {text} (置信度: {confidence:.2f})")
            
        elif message_type == 'audio_chunk':
            logger.info("🔊 收到音频响应块")
            
        elif message_type == 'response_complete':
            logger.info("✅ AI响应完成")
            
        elif message_type == 'error':
            error_msg = data.get('message', '未知错误')
            logger.error(f"❌ 服务器错误: {error_msg}")
            
        elif message_type == 'system':
            logger.info(f"ℹ️ 系统消息: {data.get('data', data)}")
            
        else:
            logger.info(f"📥 收到消息: {message_type} - {data}")
    
    async def simulate_start_conversation(self, npc_id=1):
        """模拟开始对话"""
        logger.info(f"💬 模拟开始与NPC {npc_id} 的对话...")
        
        if not self.websocket:
            logger.error("❌ WebSocket未连接")
            return False
        
        try:
            start_message = {
                "type": "start_session",
                "npc_id": npc_id
            }
            
            await self.websocket.send(json.dumps(start_message))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动确认
            await asyncio.sleep(2)
            
            if self.session_id:
                logger.info("✅ 对话会话已建立")
                return True
            else:
                logger.error("❌ 会话启动失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 启动对话异常: {e}")
            return False
    
    async def simulate_voice_recording(self):
        """模拟语音录制和发送"""
        logger.info("🎤 模拟语音录制...")
        
        # 获取音频文件
        audio_files = list(self.audio_dir.glob("*.wav"))
        if not audio_files:
            logger.error("❌ 没有可用的音频文件")
            return False
        
        # 选择一个音频文件
        test_file = audio_files[0]
        logger.info(f"📁 使用音频文件: {test_file.name}")
        
        try:
            # 读取音频文件
            with wave.open(str(test_file), 'rb') as wav_file:
                frames = wav_file.readframes(wav_file.getnframes())
                sample_rate = wav_file.getframerate()
                
            logger.info(f"📊 音频信息: 采样率 {sample_rate} Hz, 数据长度 {len(frames)} bytes")
            
            # 模拟实时录制 - 分块发送
            chunk_size = 1024
            chunks = [frames[i:i + chunk_size] for i in range(0, len(frames), chunk_size)]
            
            logger.info(f"🔪 音频分为 {len(chunks)} 个块进行流式发送")
            
            for i, chunk in enumerate(chunks):
                # 编码为base64
                base64_chunk = base64.b64encode(chunk).decode('utf-8')
                
                # 发送音频块
                audio_message = {
                    "type": "audio_chunk",
                    "data": base64_chunk
                }
                
                await self.websocket.send(json.dumps(audio_message))
                logger.info(f"📤 发送音频块 {i+1}/{len(chunks)}")
                
                # 模拟实时录制的延迟
                await asyncio.sleep(0.05)
            
            logger.info("✅ 语音录制模拟完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 语音录制模拟异常: {e}")
            return False
    
    async def simulate_complete_conversation(self):
        """模拟完整的对话流程"""
        logger.info("🗣️ 模拟完整对话流程...")
        
        # 1. 启动对话
        if not await self.simulate_start_conversation():
            return False
        
        # 2. 等待系统准备
        await asyncio.sleep(1)
        
        # 3. 模拟多轮语音交互
        for round_num in range(2):
            logger.info(f"🔄 第 {round_num + 1} 轮语音交互")
            
            # 发送语音
            if await self.simulate_voice_recording():
                logger.info("✅ 语音发送成功")
                
                # 等待AI响应
                logger.info("⏳ 等待AI响应...")
                await asyncio.sleep(5)
            else:
                logger.error("❌ 语音发送失败")
                break
        
        logger.info("✅ 完整对话流程模拟完成")
        return True
    
    async def cleanup(self):
        """清理资源"""
        if self.websocket:
            try:
                # 发送结束会话消息
                end_message = {"type": "end_session"}
                await self.websocket.send(json.dumps(end_message))
                await asyncio.sleep(0.5)
                
                await self.websocket.close()
                logger.info("🔌 WebSocket连接已关闭")
            except Exception as e:
                logger.error(f"❌ 关闭WebSocket异常: {e}")
    
    async def run_full_simulation(self):
        """运行完整的前端模拟"""
        logger.info("🚀 开始完整的前端模拟测试...")
        logger.info("=" * 60)
        
        try:
            # 1. 应用启动
            if not await self.simulate_app_startup():
                logger.error("❌ 应用启动模拟失败")
                return False
            
            # 2. 用户登录
            if not await self.simulate_user_login():
                logger.error("❌ 用户登录模拟失败")
                return False
            
            # 3. 语音聊天界面初始化
            if not await self.simulate_voice_chat_screen_init():
                logger.error("❌ 语音聊天界面初始化失败")
                return False
            
            # 4. 完整对话流程
            if not await self.simulate_complete_conversation():
                logger.error("❌ 对话流程模拟失败")
                return False
            
            logger.info("=" * 60)
            logger.info("🎉 前端模拟测试全部完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 前端模拟测试异常: {e}")
            return False
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    simulator = FlutterFrontendSimulator()
    
    # 检查音频文件
    if not simulator.audio_dir.exists():
        logger.error(f"❌ 音频目录不存在: {simulator.audio_dir}")
        return
    
    audio_files = list(simulator.audio_dir.glob("*.wav"))
    if not audio_files:
        logger.error("❌ 没有找到音频文件")
        return
    
    logger.info(f"📁 找到 {len(audio_files)} 个音频文件")
    
    # 运行模拟
    success = await simulator.run_full_simulation()
    
    if success:
        logger.info("✅ 前端模拟测试全部通过！")
    else:
        logger.error("❌ 前端模拟测试失败！")

if __name__ == "__main__":
    asyncio.run(main())