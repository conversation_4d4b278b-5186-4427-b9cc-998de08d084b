#!/usr/bin/env python3
"""
快速ASR测试 - 使用短超时时间
"""

import sys
import os
import numpy as np
import logging

# 添加backend目录到路径
sys.path.append('backend')

from services.enhanced_asr_service import EnhancedASRService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_fast_asr():
    """快速ASR测试"""
    logger.info("=" * 60)
    logger.info("快速ASR服务测试 (短超时)")
    logger.info("=" * 60)
    
    # 1. 初始化服务 - 使用短超时时间
    try:
        logger.info("初始化增强ASR服务 (Qwen超时:5秒, Gemini超时:10秒)...")
        asr_service = EnhancedASRService(qwen_timeout=5, gemini_timeout=10)
        logger.info("✅ 增强ASR服务初始化成功")
    except Exception as e:
        logger.error(f"❌ 增强ASR服务初始化失败: {e}")
        return
    
    # 2. 快速连接测试
    try:
        logger.info("快速连接测试...")
        connection_result = asr_service.test_connection()
        
        logger.info(f"🔧 Qwen2-Audio-7B: {'✅' if connection_result['qwen'] else '❌'}")
        logger.info(f"🔧 Gemini: {'✅' if connection_result['gemini'] else '❌'}")
        logger.info(f"📊 可用服务: {connection_result['available_services']}")
        
        if not connection_result['available_services']:
            logger.warning("⚠️ 没有可用的ASR服务")
            return
            
    except Exception as e:
        logger.error(f"❌ ASR连接测试失败: {e}")
        return
    
    # 3. 生成简短测试音频
    try:
        logger.info("生成简短测试音频...")
        sample_rate = 16000
        duration = 1.0  # 1秒
        frequency = 440  # A4音符
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t).astype(np.float32)
        
        logger.info(f"📊 测试音频: {len(audio_data)} 样本, {duration}秒")
        
    except Exception as e:
        logger.error(f"❌ 测试音频生成失败: {e}")
        return
    
    # 4. 快速ASR转录测试
    try:
        logger.info("开始快速ASR转录测试...")
        import time
        start_time = time.time()
        
        result = asr_service.transcribe(audio_data, sample_rate)
        
        total_time = time.time() - start_time
        logger.info(f"⏱️ 总耗时: {total_time:.2f}秒")
        
        logger.info("ASR转录测试完成")
        logger.info(f"✅ 成功: {result.get('success', False)}")
        logger.info(f"📝 文本: '{result.get('text', '')}'")
        logger.info(f"🎯 置信度: {result.get('confidence', 0.0):.3f}")
        logger.info(f"🔧 提供商: {result.get('provider', 'unknown')}")
        
        if result.get('success'):
            logger.info(f"🎉 快速ASR服务测试成功！(总耗时: {total_time:.2f}秒)")
        else:
            logger.error(f"❌ ASR转录失败: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        logger.error(f"❌ ASR转录测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    # 从.env文件加载环境变量
    from dotenv import load_dotenv
    load_dotenv('backend/.env')
    
    test_fast_asr()