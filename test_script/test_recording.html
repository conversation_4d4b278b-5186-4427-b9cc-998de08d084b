<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>录音测试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        .status.ready {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        .status.recording {
            background-color: #ffebee;
            color: #d32f2f;
            border: 1px solid #ffcdd2;
            animation: pulse 1.5s infinite;
        }
        .status.processing {
            background-color: #fff3e0;
            color: #f57c00;
            border: 1px solid #ffcc02;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .record-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            font-size: 18px;
            padding: 20px 40px;
        }
        .record-btn.recording {
            background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);
            animation: pulse 1.5s infinite;
        }
        .info-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .recordings-list {
            margin-top: 30px;
        }
        .recording-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }
        .recording-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .recording-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        audio {
            flex: 1;
            min-width: 200px;
        }
        .download-btn, .test-btn {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            padding: 8px 16px;
            font-size: 14px;
            min-width: auto;
        }
        .test-btn {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .settings {
            background: #f1f3f4;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .setting-item {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        select, input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 录音测试工具</h1>
        
        <div id="status" class="status ready">
            准备就绪 - 点击开始录音
        </div>
        
        <div class="settings">
            <h3>录音设置</h3>
            <div class="setting-item">
                <label for="sampleRate">采样率 (Hz)</label>
                <select id="sampleRate">
                    <option value="16000" selected>16000 (推荐)</option>
                    <option value="44100">44100 (CD质量)</option>
                    <option value="48000">48000 (专业)</option>
                </select>
            </div>
            <div class="setting-item">
                <label for="channels">声道数</label>
                <select id="channels">
                    <option value="1" selected>单声道 (推荐)</option>
                    <option value="2">立体声</option>
                </select>
            </div>
            <div class="setting-item">
                <label for="format">音频格式</label>
                <select id="format">
                    <option value="audio/webm;codecs=opus" selected>WebM (Opus)</option>
                    <option value="audio/mp4">MP4</option>
                    <option value="audio/wav">WAV (如果支持)</option>
                </select>
            </div>
        </div>
        
        <div class="info-panel">
            <div class="info-item">
                <span>录音状态:</span>
                <span id="recordingStatus">未开始</span>
            </div>
            <div class="info-item">
                <span>录音时长:</span>
                <span id="duration">0秒</span>
            </div>
            <div class="info-item">
                <span>音频大小:</span>
                <span id="audioSize">0 KB</span>
            </div>
            <div class="info-item">
                <span>支持的格式:</span>
                <span id="supportedFormats">检测中...</span>
            </div>
        </div>
        
        <div class="controls">
            <button id="recordBtn" class="record-btn" onclick="toggleRecording()">
                开始录音
            </button>
            <button onclick="clearRecordings()">清空录音</button>
            <button onclick="testMicrophone()">测试麦克风</button>
        </div>
        
        <div class="recordings-list">
            <h3>录音列表</h3>
            <div id="recordingsList">
                <p style="text-align: center; color: #666;">暂无录音</p>
            </div>
        </div>
        
        <div class="log" id="log">
            <div>📋 录音测试工具已加载</div>
            <div>💡 提示: 首次使用需要授权麦克风权限</div>
        </div>
    </div>

    <script>
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;
        let recordingStartTime = null;
        let durationTimer = null;
        let recordings = [];
        let recordingCounter = 0;

        const statusEl = document.getElementById('status');
        const recordBtn = document.getElementById('recordBtn');
        const recordingStatusEl = document.getElementById('recordingStatus');
        const durationEl = document.getElementById('duration');
        const audioSizeEl = document.getElementById('audioSize');
        const supportedFormatsEl = document.getElementById('supportedFormats');
        const recordingsListEl = document.getElementById('recordingsList');
        const logEl = document.getElementById('log');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;
        }

        function updateStatus(status, className) {
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        function updateDuration() {
            if (recordingStartTime) {
                const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
                durationEl.textContent = `${elapsed}秒`;
            }
        }

        function checkSupportedFormats() {
            const formats = [
                'audio/webm;codecs=opus',
                'audio/webm',
                'audio/mp4',
                'audio/wav',
                'audio/ogg;codecs=opus'
            ];
            
            const supported = formats.filter(format => MediaRecorder.isTypeSupported(format));
            supportedFormatsEl.textContent = supported.length > 0 ? supported.join(', ') : '无支持格式';
            log(`支持的音频格式: ${supported.join(', ')}`);
        }

        async function testMicrophone() {
            log('🎤 测试麦克风权限和可用性...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: parseInt(document.getElementById('sampleRate').value),
                        channelCount: parseInt(document.getElementById('channels').value),
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });
                
                log('✅ 麦克风权限获取成功');
                log(`🎵 音频轨道数: ${stream.getAudioTracks().length}`);
                
                const audioTrack = stream.getAudioTracks()[0];
                if (audioTrack) {
                    const settings = audioTrack.getSettings();
                    log(`📊 音频设置: 采样率=${settings.sampleRate}Hz, 声道=${settings.channelCount}`);
                }
                
                // 停止测试流
                stream.getTracks().forEach(track => track.stop());
                
                updateStatus('麦克风测试成功 - 可以开始录音', 'ready');
                
            } catch (error) {
                log(`❌ 麦克风测试失败: ${error.message}`);
                updateStatus('麦克风不可用', 'ready');
            }
        }

        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                stopRecording();
            }
        }

        async function startRecording() {
            try {
                log('🎤 开始录音...');
                
                const sampleRate = parseInt(document.getElementById('sampleRate').value);
                const channelCount = parseInt(document.getElementById('channels').value);
                const mimeType = document.getElementById('format').value;
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: sampleRate,
                        channelCount: channelCount,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                // 检查格式支持
                const options = {};
                if (MediaRecorder.isTypeSupported(mimeType)) {
                    options.mimeType = mimeType;
                    log(`✅ 使用音频格式: ${mimeType}`);
                } else {
                    log(`⚠️ 不支持格式 ${mimeType}，使用默认格式`);
                }

                mediaRecorder = new MediaRecorder(stream, options);
                audioChunks = [];

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                        log(`📦 收到音频数据块: ${event.data.size} bytes`);
                        
                        // 更新音频大小显示
                        const totalSize = audioChunks.reduce((sum, chunk) => sum + chunk.size, 0);
                        audioSizeEl.textContent = `${(totalSize / 1024).toFixed(1)} KB`;
                    }
                };

                mediaRecorder.onstop = function() {
                    log('🛑 录音停止');
                    const audioBlob = new Blob(audioChunks, { type: mimeType });
                    saveRecording(audioBlob);
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.onerror = function(event) {
                    log(`❌ 录音错误: ${event.error}`);
                };

                mediaRecorder.start(1000); // 每秒收集一次数据
                isRecording = true;
                recordingStartTime = Date.now();
                
                // 更新UI
                recordBtn.textContent = '停止录音';
                recordBtn.className = 'record-btn recording';
                updateStatus('正在录音...', 'recording');
                recordingStatusEl.textContent = '录音中';
                
                // 开始计时器
                durationTimer = setInterval(updateDuration, 1000);
                
                log('✅ 录音已开始');

            } catch (error) {
                log(`❌ 开始录音失败: ${error.message}`);
                updateStatus('录音失败 - 请检查麦克风权限', 'ready');
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                log('🛑 停止录音...');
                
                mediaRecorder.stop();
                isRecording = false;
                recordingStartTime = null;
                
                // 停止计时器
                if (durationTimer) {
                    clearInterval(durationTimer);
                    durationTimer = null;
                }
                
                // 更新UI
                recordBtn.textContent = '开始录音';
                recordBtn.className = 'record-btn';
                updateStatus('处理录音中...', 'processing');
                recordingStatusEl.textContent = '处理中';
            }
        }

        function saveRecording(audioBlob) {
            recordingCounter++;
            const timestamp = new Date().toLocaleString();
            const filename = `recording_${recordingCounter}_${Date.now()}.webm`;
            
            const recording = {
                id: recordingCounter,
                blob: audioBlob,
                filename: filename,
                timestamp: timestamp,
                size: audioBlob.size,
                duration: durationEl.textContent
            };
            
            recordings.push(recording);
            
            log(`💾 录音已保存: ${filename} (${(audioBlob.size / 1024).toFixed(1)} KB)`);
            
            // 更新UI
            updateStatus('录音完成 - 可以开始新的录音', 'ready');
            recordingStatusEl.textContent = '已完成';
            
            // 重置显示
            durationEl.textContent = '0秒';
            audioSizeEl.textContent = '0 KB';
            
            // 更新录音列表
            updateRecordingsList();
        }

        function updateRecordingsList() {
            if (recordings.length === 0) {
                recordingsListEl.innerHTML = '<p style="text-align: center; color: #666;">暂无录音</p>';
                return;
            }

            recordingsListEl.innerHTML = recordings.map(recording => `
                <div class="recording-item">
                    <h4>📁 ${recording.filename}</h4>
                    <p>🕒 录制时间: ${recording.timestamp}</p>
                    <p>⏱️ 时长: ${recording.duration} | 📦 大小: ${(recording.size / 1024).toFixed(1)} KB</p>
                    <div class="recording-controls">
                        <audio controls>
                            <source src="${URL.createObjectURL(recording.blob)}" type="${recording.blob.type}">
                            您的浏览器不支持音频播放
                        </audio>
                        <button class="download-btn" onclick="downloadRecording(${recording.id})">下载</button>
                        <button class="test-btn" onclick="testRecording(${recording.id})">发送测试</button>
                    </div>
                </div>
            `).join('');
        }

        function downloadRecording(recordingId) {
            const recording = recordings.find(r => r.id === recordingId);
            if (recording) {
                const url = URL.createObjectURL(recording.blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = recording.filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                log(`📥 下载录音: ${recording.filename}`);
            }
        }

        function testRecording(recordingId) {
            const recording = recordings.find(r => r.id === recordingId);
            if (recording) {
                log(`🧪 测试录音: ${recording.filename}`);
                log(`📊 文件信息: ${(recording.size / 1024).toFixed(1)} KB, ${recording.blob.type}`);
                
                // 这里可以添加发送到后端的逻辑
                // 例如通过WebSocket发送到ASR服务进行测试
                alert(`测试录音: ${recording.filename}\n大小: ${(recording.size / 1024).toFixed(1)} KB\n格式: ${recording.blob.type}`);
            }
        }

        function clearRecordings() {
            if (confirm('确定要清空所有录音吗？')) {
                recordings = [];
                updateRecordingsList();
                log('🗑️ 已清空所有录音');
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            checkSupportedFormats();
            testMicrophone();
            log('🚀 录音测试工具初始化完成');
        };
    </script>
</body>
</html>