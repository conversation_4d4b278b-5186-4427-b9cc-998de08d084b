#!/usr/bin/env python3
"""
测试后端音频处理是否正常工作
"""

import requests
import numpy as np
import struct
import io

def create_test_wav_file():
    """创建一个测试WAV文件"""
    # 生成1秒的440Hz正弦波
    sample_rate = 16000
    duration = 1.0
    frequency = 440
    
    # 生成音频数据
    t = np.linspace(0, duration, int(sample_rate * duration))
    audio_data = np.sin(2 * np.pi * frequency * t) * 0.5
    audio_int16 = (audio_data * 32767).astype(np.int16)
    
    # 创建WAV文件头
    wav_buffer = io.BytesIO()
    
    # WAV文件头
    wav_buffer.write(b'RIFF')
    wav_buffer.write(struct.pack('<I', 36 + len(audio_int16) * 2))
    wav_buffer.write(b'WAVE')
    wav_buffer.write(b'fmt ')
    wav_buffer.write(struct.pack('<I', 16))  # fmt chunk size
    wav_buffer.write(struct.pack('<H', 1))   # PCM format
    wav_buffer.write(struct.pack('<H', 1))   # mono
    wav_buffer.write(struct.pack('<I', sample_rate))
    wav_buffer.write(struct.pack('<I', sample_rate * 2))  # byte rate
    wav_buffer.write(struct.pack('<H', 2))   # block align
    wav_buffer.write(struct.pack('<H', 16))  # bits per sample
    wav_buffer.write(b'data')
    wav_buffer.write(struct.pack('<I', len(audio_int16) * 2))
    wav_buffer.write(audio_int16.tobytes())
    
    return wav_buffer.getvalue()

def test_backend_audio_api():
    """测试后端音频处理API"""
    print("🎵 Testing backend audio processing API...")
    
    try:
        # 创建测试音频文件
        wav_data = create_test_wav_file()
        print(f"📊 Created test WAV file: {len(wav_data)} bytes")
        
        # 发送到后端API
        files = {'file': ('test_audio.wav', wav_data, 'audio/wav')}
        data = {'user_id': 1, 'npc_id': 1}
        
        print("📤 Sending audio to backend...")
        response = requests.post(
            'http://localhost:8000/process-audio',
            files=files,
            data=data,
            timeout=60  # 增加超时时间
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Backend audio processing successful!")
            print(f"📝 Transcription: {result.get('transcription', 'N/A')}")
            print(f"💬 Response: {result.get('response', 'N/A')[:100]}...")
            print(f"🎵 Audio output size: {len(result.get('audio_data', ''))} chars")
            return True
        else:
            print(f"❌ Backend returned error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_backend_health():
    """测试后端健康状态"""
    print("🏥 Testing backend health...")
    
    try:
        response = requests.get('http://localhost:8000/health', timeout=10)
        if response.status_code == 200:
            health = response.json()
            print("✅ Backend is healthy")
            
            services = health.get('services', {})
            for service, status in services.items():
                if isinstance(status, dict):
                    print(f"   {service}: {status}")
                else:
                    print(f"   {service}: {status}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def main():
    print("🔧 Backend Audio Processing Test")
    print("=" * 50)
    
    # 测试1: 健康检查
    health_ok = test_backend_health()
    print()
    
    # 测试2: 音频处理API
    if health_ok:
        audio_ok = test_backend_audio_api()
        print()
        
        if audio_ok:
            print("🎉 Backend audio processing is working!")
            print("💡 The issue might be in WebSocket audio streaming")
        else:
            print("❌ Backend audio processing has issues")
            print("💡 This explains why WebSocket audio doesn't work")
    else:
        print("❌ Backend is not healthy, skipping audio test")
    
    print("=" * 50)

if __name__ == "__main__":
    main()