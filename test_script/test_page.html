<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Chat Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .record-btn {
            background-color: #dc3545;
        }
        .record-btn:hover {
            background-color: #c82333;
        }
        .record-btn.recording {
            background-color: #28a745;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .message {
            margin: 10px 0;
            padding: 8px;
            border-radius: 5px;
        }
        .message.user {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .message.assistant {
            background-color: #e9ecef;
            color: #333;
        }
        .message.system {
            background-color: #fff3cd;
            color: #856404;
            font-style: italic;
        }
        .audio-controls {
            margin: 10px 0;
        }
        audio {
            width: 100%;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Voice Chat Test</h1>
        
        <div id="status" class="status disconnected">
            Disconnected
        </div>
        
        <div class="controls">
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <button id="recordBtn" class="record-btn" onclick="toggleRecording()" disabled>Start Recording</button>
            <button onclick="sendTestMessage()" disabled id="testBtn">Send Test Message</button>
        </div>
        
        <div class="messages" id="messages">
            <div class="message system">Welcome! Click Connect to start the voice chat.</div>
        </div>
        
        <div class="audio-controls">
            <label>Last Response Audio:</label>
            <audio id="responseAudio" controls style="display: none;"></audio>
        </div>
    </div>

    <script>
        let ws = null;
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;
        let isConnected = false;

        const statusEl = document.getElementById('status');
        const messagesEl = document.getElementById('messages');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const recordBtn = document.getElementById('recordBtn');
        const responseAudio = document.getElementById('responseAudio');

        function addMessage(content, type = 'system') {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.textContent = content;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function updateStatus(connected) {
            isConnected = connected;
            const testBtn = document.getElementById('testBtn');
            if (connected) {
                statusEl.textContent = 'Connected';
                statusEl.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                recordBtn.disabled = false;
                testBtn.disabled = false;
            } else {
                statusEl.textContent = 'Disconnected';
                statusEl.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                recordBtn.disabled = true;
                testBtn.disabled = true;
                if (isRecording) {
                    stopRecording();
                }
            }
        }

        function connect() {
            const wsUrl = 'ws://localhost:8000/ws/1';  // Use user ID 1 to match our backend
            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                addMessage('Connected to voice chat server', 'system');
                updateStatus(true);

                // Start a session immediately after connecting
                const startSessionMessage = {
                    type: "start_session",
                    npc_id: 1
                };
                ws.send(JSON.stringify(startSessionMessage));
                addMessage('Starting conversation session...', 'system');
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    console.log('Received:', data);
                    
                    if (data.type === 'session_started') {
                        addMessage(`Session started with NPC ${data.npc_id} (Session ID: ${data.session_id})`, 'system');
                    } else if (data.type === 'connection_established') {
                        addMessage(`Connection established (ID: ${data.connection_id})`, 'system');
                    } else if (data.type === 'transcription') {
                        addMessage(`You said: ${data.text} (confidence: ${(data.confidence * 100).toFixed(1)}%)`, 'user');
                    } else if (data.type === 'response') {
                        addMessage(`Assistant: ${data.text}`, 'assistant');
                    } else if (data.type === 'audio_chunk') {
                        // Handle audio response chunk
                        try {
                            const audioData = atob(data.data);
                            const audioArray = new Uint8Array(audioData.length);
                            for (let i = 0; i < audioData.length; i++) {
                                audioArray[i] = audioData.charCodeAt(i);
                            }
                            const audioBlob = new Blob([audioArray], {type: 'audio/wav'});
                            const audioUrl = URL.createObjectURL(audioBlob);
                            responseAudio.src = audioUrl;
                            responseAudio.style.display = 'block';
                            addMessage('Received audio response', 'assistant');
                        } catch (e) {
                            console.error('Error processing audio chunk:', e);
                        }
                    } else if (data.type === 'response_complete') {
                        addMessage('Response complete', 'system');
                    } else if (data.type === 'session_ended') {
                        addMessage('Session ended', 'system');
                    } else if (data.type === 'error') {
                        addMessage(`Error: ${data.message}`, 'system');
                    } else {
                        addMessage(`Received: ${data.type}`, 'system');
                        console.log('Unknown message type:', data);
                    }
                } catch (e) {
                    console.error('Error parsing message:', e);
                }
            };
            
            ws.onclose = function(event) {
                addMessage('Disconnected from server', 'system');
                updateStatus(false);
            };
            
            ws.onerror = function(error) {
                addMessage('Connection error occurred', 'system');
                console.error('WebSocket error:', error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                stopRecording();
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,  // 16kHz sample rate
                        channelCount: 1,    // Mono
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });
                
                // Use specific MIME type for better compatibility
                const options = {
                    mimeType: 'audio/webm;codecs=opus',
                    audioBitsPerSecond: 16000
                };
                
                // Fallback to default if webm not supported
                if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                    delete options.mimeType;
                    addMessage('Using default audio format (webm not supported)', 'system');
                }
                
                mediaRecorder = new MediaRecorder(stream, options);
                audioChunks = [];
                
                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };
                
                mediaRecorder.onstop = function() {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    addMessage(`Audio recorded: ${audioBlob.size} bytes`, 'system');
                    sendAudio(audioBlob);
                    stream.getTracks().forEach(track => track.stop());
                };
                
                mediaRecorder.start(1000); // Collect data every 1 second
                isRecording = true;
                recordBtn.textContent = 'Stop Recording';
                recordBtn.className = 'record-btn recording';
                addMessage('Recording started...', 'system');
                
            } catch (error) {
                addMessage('Error accessing microphone: ' + error.message, 'system');
                console.error('Error accessing microphone:', error);
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                recordBtn.textContent = 'Start Recording';
                recordBtn.className = 'record-btn';
                addMessage('Recording stopped, processing...', 'system');
            }
        }

        function sendAudio(audioBlob) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage(`Processing audio blob: ${audioBlob.size} bytes, type: ${audioBlob.type}`, 'system');
                
                const reader = new FileReader();
                reader.onload = function() {
                    try {
                        const arrayBuffer = reader.result;
                        const uint8Array = new Uint8Array(arrayBuffer);
                        
                        addMessage(`Audio buffer: ${uint8Array.byteLength} bytes`, 'system');
                        
                        // Convert to base64
                        let binary = '';
                        const chunkSize = 8192; // Process in chunks to avoid stack overflow
                        for (let i = 0; i < uint8Array.byteLength; i += chunkSize) {
                            const chunk = uint8Array.subarray(i, Math.min(i + chunkSize, uint8Array.byteLength));
                            binary += String.fromCharCode.apply(null, chunk);
                        }
                        const base64Audio = btoa(binary);
                        
                        // Check size before sending
                        const message = {
                            type: "audio_chunk",
                            data: base64Audio,
                            metadata: {
                                originalSize: uint8Array.byteLength,
                                base64Size: base64Audio.length,
                                mimeType: audioBlob.type
                            }
                        };
                        
                        const messageStr = JSON.stringify(message);
                        const messageSize = new Blob([messageStr]).size;
                        
                        if (messageSize > 10 * 1024 * 1024) { // 10MB limit
                            addMessage(`Error: Message too large (${messageSize} bytes)`, 'system');
                            return;
                        }
                        
                        ws.send(messageStr);
                        addMessage(`Sent audio: ${uint8Array.byteLength} bytes → ${base64Audio.length} base64 chars`, 'system');
                        
                    } catch (error) {
                        addMessage(`Error processing audio: ${error.message}`, 'system');
                        console.error('Audio processing error:', error);
                    }
                };
                
                reader.onerror = function(error) {
                    addMessage(`Error reading audio file: ${error}`, 'system');
                };
                
                reader.readAsArrayBuffer(audioBlob);
            } else {
                addMessage('WebSocket not connected', 'system');
            }
        }

        function sendTestMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: "text",
                    text: "Hello, this is a test message!"
                };
                ws.send(JSON.stringify(message));
                addMessage('Sent test message: ' + message.text, 'user');
            } else {
                addMessage('WebSocket not connected', 'system');
            }
        }

        // Auto-connect on page load
        window.onload = function() {
            // Wait a moment for the page to fully load
            setTimeout(connect, 1000);
        };
    </script>
</body>
</html>
