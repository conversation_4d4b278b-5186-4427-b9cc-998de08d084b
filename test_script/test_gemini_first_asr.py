#!/usr/bin/env python3
"""
Gemini优先ASR测试 - 直接使用Gemini，跳过Qwen
"""

import sys
import os
import numpy as np
import logging

# 添加backend目录到路径
sys.path.append('backend')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gemini_first_asr():
    """Gemini优先ASR测试"""
    logger.info("=" * 60)
    logger.info("Gemini优先ASR服务测试")
    logger.info("=" * 60)
    
    # 1. 直接初始化Gemini ASR
    try:
        logger.info("直接初始化Gemini ASR...")
        from google import genai
        
        gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not gemini_api_key:
            logger.error("❌ GEMINI_API_KEY 未配置")
            return
            
        client = genai.Client(api_key=gemini_api_key)
        logger.info("✅ Gemini Client初始化成功")
    except Exception as e:
        logger.error(f"❌ Gemini初始化失败: {e}")
        return
    
    # 2. 生成测试音频
    try:
        logger.info("生成测试音频...")
        sample_rate = 16000
        duration = 1.0  # 1秒
        frequency = 440  # A4音符
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t).astype(np.float32)
        
        # 转换为16位整数
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        # 创建WAV字节流
        import io
        import wave
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        audio_bytes = wav_buffer.getvalue()
        logger.info(f"📊 测试音频: {len(audio_bytes)} bytes, {duration}秒")
        
    except Exception as e:
        logger.error(f"❌ 测试音频生成失败: {e}")
        return
    
    # 3. 直接测试Gemini ASR
    try:
        logger.info("开始Gemini ASR测试...")
        import time
        import tempfile
        
        start_time = time.time()
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_file.write(audio_bytes)
            temp_file_path = temp_file.name
        
        try:
            # 上传音频文件到Gemini
            logger.info("📤 上传音频到Gemini...")
            upload_start = time.time()
            myfile = client.files.upload(file=temp_file_path)
            upload_time = time.time() - upload_start
            logger.info(f"✅ 音频上传成功 ({upload_time:.2f}秒)")
            
            # 调用Gemini API进行转录
            logger.info("🎯 调用Gemini进行转录...")
            transcribe_start = time.time()
            response = client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=["请转录这段音频的内容，只返回转录的中文文本，不要添加任何解释或标点符号。", myfile]
            )
            transcribe_time = time.time() - transcribe_start
            logger.info(f"✅ 转录完成 ({transcribe_time:.2f}秒)")
            
            transcribed_text = response.text.strip()
            total_time = time.time() - start_time
            
            logger.info("=" * 40)
            logger.info("🎉 Gemini ASR测试成功！")
            logger.info(f"📝 转录结果: '{transcribed_text}'")
            logger.info(f"⏱️ 上传耗时: {upload_time:.2f}秒")
            logger.info(f"⏱️ 转录耗时: {transcribe_time:.2f}秒")
            logger.info(f"⏱️ 总耗时: {total_time:.2f}秒")
            logger.info("=" * 40)
            
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
            
    except Exception as e:
        logger.error(f"❌ Gemini ASR测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    # 从.env文件加载环境变量
    from dotenv import load_dotenv
    load_dotenv('backend/.env')
    
    test_gemini_first_asr()