#!/usr/bin/env python3
"""
调试ASR调用 - 追踪所有ASR请求
找出为什么会有多次调用和错误的转录结果
"""
import asyncio
import logging
import json
from datetime import datetime
from pathlib import Path
import numpy as np
import wave
import io

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ASRCallTracker:
    """ASR调用追踪器"""
    def __init__(self):
        self.calls = []
        self.call_count = 0
    
    def log_call(self, audio_data, source, result=None):
        """记录ASR调用"""
        self.call_count += 1
        
        call_info = {
            'call_id': self.call_count,
            'timestamp': datetime.now().isoformat(),
            'source': source,  # 'chunk_processing' 或 'complete_audio'
            'audio_length': len(audio_data),
            'audio_duration': len(audio_data) / 16000,
            'result': result
        }
        
        self.calls.append(call_info)
        
        logger.info(f"🎯 ASR调用 #{self.call_count}")
        logger.info(f"   来源: {source}")
        logger.info(f"   音频长度: {len(audio_data)} samples ({len(audio_data)/16000:.2f}s)")
        if result:
            logger.info(f"   转录结果: '{result.get('text', 'N/A')}'")
        
        # 保存音频用于分析
        self.save_audio_for_analysis(audio_data, self.call_count, source)
    
    def save_audio_for_analysis(self, audio_data, call_id, source):
        """保存音频用于分析"""
        try:
            # 确保音频数据格式正确
            if audio_data.dtype != np.int16:
                if audio_data.dtype == np.float32 or audio_data.dtype == np.float64:
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                else:
                    audio_int16 = audio_data.astype(np.int16)
            else:
                audio_int16 = audio_data
            
            # 创建文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_asr_call_{call_id}_{source}_{timestamp}.wav"
            
            # 保存WAV文件
            with wave.open(filename, 'wb') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(16000)  # 16kHz
                wav_file.writeframes(audio_int16.tobytes())
            
            logger.info(f"   📁 音频已保存: {filename}")
            
        except Exception as e:
            logger.error(f"   ❌ 保存音频失败: {e}")
    
    def analyze_calls(self):
        """分析所有ASR调用"""
        logger.info("=" * 60)
        logger.info("📊 ASR调用分析报告")
        logger.info("=" * 60)
        
        logger.info(f"总调用次数: {self.call_count}")
        
        # 按来源分组
        chunk_calls = [call for call in self.calls if call['source'] == 'chunk_processing']
        complete_calls = [call for call in self.calls if call['source'] == 'complete_audio']
        
        logger.info(f"音频块处理调用: {len(chunk_calls)}")
        logger.info(f"完整音频处理调用: {len(complete_calls)}")
        
        # 详细分析每个调用
        for call in self.calls:
            logger.info(f"\n📋 调用 #{call['call_id']} ({call['source']})")
            logger.info(f"   时间: {call['timestamp']}")
            logger.info(f"   音频: {call['audio_length']} samples ({call['audio_duration']:.2f}s)")
            if call['result']:
                text = call['result'].get('text', 'N/A')
                confidence = call['result'].get('confidence', 0)
                logger.info(f"   结果: '{text}' (置信度: {confidence})")
        
        # 保存报告
        report = {
            'summary': {
                'total_calls': self.call_count,
                'chunk_calls': len(chunk_calls),
                'complete_calls': len(complete_calls),
                'analysis_time': datetime.now().isoformat()
            },
            'calls': self.calls
        }
        
        report_file = f"asr_calls_debug_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n📄 详细报告已保存: {report_file}")

# 全局追踪器
asr_tracker = ASRCallTracker()

def patch_enhanced_asr_service():
    """给enhanced_asr_service打补丁，追踪所有调用"""
    try:
        import sys
        sys.path.append('backend')
        from services.enhanced_asr_service import EnhancedASRService
        
        # 保存原始方法
        original_transcribe = EnhancedASRService.transcribe
        
        def tracked_transcribe(self, audio_data, source="unknown"):
            """带追踪的转录方法"""
            # 记录调用
            asr_tracker.log_call(audio_data, source)
            
            # 调用原始方法
            result = original_transcribe(self, audio_data)
            
            # 更新结果
            if asr_tracker.calls:
                asr_tracker.calls[-1]['result'] = result
            
            return result
        
        # 替换方法
        EnhancedASRService.transcribe = tracked_transcribe
        logger.info("✅ ASR服务已打补丁，开始追踪调用")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 打补丁失败: {e}")
        return False

async def simulate_problematic_scenario():
    """模拟问题场景"""
    logger.info("🎭 模拟问题场景...")
    
    # 打补丁
    if not patch_enhanced_asr_service():
        logger.error("❌ 无法打补丁，退出测试")
        return
    
    # 导入服务
    import sys
    sys.path.append('backend')
    from services.enhanced_asr_service import EnhancedASRService
    
    # 创建ASR服务
    asr_service = EnhancedASRService()
    
    # 模拟3种不同的音频数据
    logger.info("\n1️⃣ 模拟低质量音频（可能导致幻觉）")
    low_quality_audio = np.random.normal(0, 0.01, 16000).astype(np.float32)  # 低音量噪音
    result1 = asr_service.transcribe(low_quality_audio, "low_quality_test")
    
    logger.info("\n2️⃣ 模拟静音音频")
    silent_audio = np.zeros(16000, dtype=np.float32)  # 静音
    result2 = asr_service.transcribe(silent_audio, "silent_test")
    
    logger.info("\n3️⃣ 模拟正常音频（合成语音信号）")
    # 生成440Hz正弦波（模拟语音）
    t = np.linspace(0, 1, 16000, False)
    normal_audio = (np.sin(2 * np.pi * 440 * t) * 0.5).astype(np.float32)
    result3 = asr_service.transcribe(normal_audio, "normal_test")
    
    # 分析结果
    asr_tracker.analyze_calls()

async def main():
    """主函数"""
    logger.info("🔍 ASR调用调试工具")
    logger.info("🎯 目标：追踪所有ASR调用，找出多次调用和幻觉的原因")
    logger.info("=" * 60)
    
    try:
        await simulate_problematic_scenario()
        
        logger.info("=" * 60)
        logger.info("🎉 调试完成！")
        logger.info("💡 请检查生成的音频文件和报告")
        
    except Exception as e:
        logger.error(f"❌ 调试过程出错: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main())