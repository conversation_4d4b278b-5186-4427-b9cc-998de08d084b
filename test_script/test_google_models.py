#!/usr/bin/env python3
"""
Google模型SDK测试脚本
测试Google Generative AI和Google Cloud AI Platform的集成
"""

import os
import json
from datetime import datetime

def test_google_generative_ai():
    """测试Google Generative AI (Gemini)"""
    try:
        import google.generativeai as genai
        
        print("✅ Google Generative AI SDK imported successfully")
        
        # 检查API密钥
        api_key = os.getenv("GOOGLE_API_KEY")
        if api_key:
            print(f"✅ Google API Key found: {api_key[:10]}...")
            
            # 配置API
            genai.configure(api_key=api_key)
            
            # 列出可用模型
            try:
                models = list(genai.list_models())
                print(f"✅ Found {len(models)} available models")
                
                # 显示前几个模型
                for i, model in enumerate(models[:3]):
                    print(f"  - {model.name}: {model.display_name}")
                
                # 测试文本生成
                try:
                    model = genai.GenerativeModel('gemini-pro')
                    response = model.generate_content("Hello, how are you?")
                    print(f"✅ Text generation test successful: {response.text[:50]}...")
                    return True
                except Exception as e:
                    print(f"⚠️ Text generation test failed: {e}")
                    return False
                    
            except Exception as e:
                print(f"⚠️ Failed to list models: {e}")
                return False
        else:
            print("⚠️ Google API Key not found in environment variables")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import Google Generative AI: {e}")
        return False
    except Exception as e:
        print(f"❌ Google Generative AI test failed: {e}")
        return False

def test_google_cloud_aiplatform():
    """测试Google Cloud AI Platform"""
    try:
        from google.cloud import aiplatform
        
        print("✅ Google Cloud AI Platform SDK imported successfully")
        
        # 检查项目ID和认证
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        
        if project_id:
            print(f"✅ Google Cloud Project ID found: {project_id}")
        else:
            print("⚠️ Google Cloud Project ID not found in environment variables")
        
        if credentials_path and os.path.exists(credentials_path):
            print(f"✅ Google Cloud credentials file found: {credentials_path}")
        else:
            print("⚠️ Google Cloud credentials file not found")
        
        # 如果有项目ID，尝试初始化
        if project_id:
            try:
                aiplatform.init(project=project_id, location="us-central1")
                print("✅ Google Cloud AI Platform initialized successfully")
                return True
            except Exception as e:
                print(f"⚠️ Failed to initialize Google Cloud AI Platform: {e}")
                return False
        else:
            print("⚠️ Skipping Google Cloud AI Platform initialization (no project ID)")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import Google Cloud AI Platform: {e}")
        return False
    except Exception as e:
        print(f"❌ Google Cloud AI Platform test failed: {e}")
        return False

def test_google_auth():
    """测试Google认证"""
    try:
        from google.auth import default
        from google.auth.exceptions import DefaultCredentialsError
        
        print("✅ Google Auth SDK imported successfully")
        
        try:
            credentials, project = default()
            print(f"✅ Default credentials found for project: {project}")
            return True
        except DefaultCredentialsError:
            print("⚠️ No default credentials found")
            return False
        except Exception as e:
            print(f"⚠️ Google Auth test failed: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import Google Auth: {e}")
        return False

def check_environment_setup():
    """检查环境变量设置"""
    print("\n🔍 Checking Environment Variables:")
    
    env_vars = [
        "GOOGLE_API_KEY",
        "GOOGLE_CLOUD_PROJECT", 
        "GOOGLE_APPLICATION_CREDENTIALS"
    ]
    
    found_vars = 0
    for var in env_vars:
        value = os.getenv(var)
        if value:
            if var == "GOOGLE_API_KEY":
                print(f"  ✅ {var}: {value[:10]}...")
            else:
                print(f"  ✅ {var}: {value}")
            found_vars += 1
        else:
            print(f"  ❌ {var}: Not set")
    
    print(f"\nEnvironment setup: {found_vars}/{len(env_vars)} variables configured")
    return found_vars

def create_sample_env_file():
    """创建示例环境变量文件"""
    env_content = """# Google AI/ML Services Configuration
# 
# 1. Google Generative AI (Gemini) API Key
# Get from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here

# 2. Google Cloud Project ID
# Your Google Cloud Project ID
GOOGLE_CLOUD_PROJECT=your_project_id_here

# 3. Google Cloud Service Account Credentials
# Path to your service account JSON file
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json

# Additional Google Cloud settings (optional)
GOOGLE_CLOUD_REGION=us-central1
GOOGLE_CLOUD_ZONE=us-central1-a
"""
    
    try:
        with open(".env.google.example", "w") as f:
            f.write(env_content)
        print("✅ Created .env.google.example file with sample configuration")
        return True
    except Exception as e:
        print(f"❌ Failed to create sample env file: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    report = {
        "test_timestamp": datetime.now().isoformat(),
        "google_sdk_status": {
            "generative_ai": False,
            "cloud_aiplatform": False,
            "auth": False
        },
        "environment_setup": {
            "configured_vars": 0,
            "total_vars": 3
        },
        "recommendations": []
    }
    
    print(f"\n{'='*60}")
    print("🧪 GOOGLE MODELS SDK TEST REPORT")
    print(f"{'='*60}")
    
    # 测试各个组件
    print("\n1. Testing Google Generative AI...")
    report["google_sdk_status"]["generative_ai"] = test_google_generative_ai()
    
    print("\n2. Testing Google Cloud AI Platform...")
    report["google_sdk_status"]["cloud_aiplatform"] = test_google_cloud_aiplatform()
    
    print("\n3. Testing Google Auth...")
    report["google_sdk_status"]["auth"] = test_google_auth()
    
    print("\n4. Checking Environment Setup...")
    report["environment_setup"]["configured_vars"] = check_environment_setup()
    
    # 生成建议
    if not report["google_sdk_status"]["generative_ai"]:
        report["recommendations"].append("Set up GOOGLE_API_KEY for Generative AI access")
    
    if not report["google_sdk_status"]["cloud_aiplatform"]:
        report["recommendations"].append("Configure Google Cloud project and credentials")
    
    if report["environment_setup"]["configured_vars"] == 0:
        report["recommendations"].append("Set up environment variables using .env.google.example as template")
    
    # 显示总结
    total_tests = len(report["google_sdk_status"])
    passed_tests = sum(report["google_sdk_status"].values())
    
    print(f"\n📊 TEST SUMMARY:")
    print(f"  Total Tests: {total_tests}")
    print(f"  Passed: {passed_tests}")
    print(f"  Failed: {total_tests - passed_tests}")
    print(f"  Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    if report["recommendations"]:
        print(f"\n💡 RECOMMENDATIONS:")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"  {i}. {rec}")
    
    # 保存报告
    report_file = f"google_models_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\n📄 Report saved to: {report_file}")
    except Exception as e:
        print(f"\n❌ Failed to save report: {e}")
    
    return report

def main():
    """主函数"""
    print("🚀 Starting Google Models SDK Test")
    
    # 创建示例环境文件
    print("\n📝 Creating sample environment configuration...")
    create_sample_env_file()
    
    # 运行测试并生成报告
    report = generate_test_report()
    
    # 显示下一步指导
    print(f"\n🎯 NEXT STEPS:")
    print("1. Review the .env.google.example file")
    print("2. Set up your Google API keys and credentials")
    print("3. Copy .env.google.example to .env and fill in your values")
    print("4. Re-run this test to verify your setup")
    
    return report

if __name__ == "__main__":
    main()
