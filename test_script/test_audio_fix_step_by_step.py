#!/usr/bin/env python3
"""
macOS 音频录制修复验证脚本
按照正确的调用顺序测试 Flutter Sound
"""

import subprocess
import sys
import time
import os

def run_command(cmd, cwd=None, timeout=30):
    """运行命令并返回结果"""
    try:
        print(f"🔧 Running: {cmd}")
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        print(f"⏰ Command timed out: {cmd}")
        return False, "", "Command timed out"
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False, "", str(e)

def check_macos_permissions():
    """检查 macOS 权限设置"""
    print("\n🔍 检查 macOS 权限配置...")
    
    # 检查 Info.plist
    info_plist_path = "frontend/macos/Runner/Info.plist"
    if os.path.exists(info_plist_path):
        with open(info_plist_path, 'r') as f:
            content = f.read()
            if "NSMicrophoneUsageDescription" in content:
                print("✅ Info.plist 包含麦克风权限描述")
            else:
                print("❌ Info.plist 缺少麦克风权限描述")
    
    # 检查 entitlements
    entitlements_files = [
        "frontend/macos/Runner/DebugProfile.entitlements",
        "frontend/macos/Runner/Release.entitlements"
    ]
    
    for file_path in entitlements_files:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                content = f.read()
                if "com.apple.security.device.microphone" in content:
                    print(f"✅ {file_path} 包含麦克风权限")
                else:
                    print(f"❌ {file_path} 缺少麦克风权限")

def reset_permissions():
    """重置麦克风权限"""
    print("\n🔄 重置麦克风权限...")
    success, stdout, stderr = run_command("tccutil reset Microphone")
    if success:
        print("✅ 麦克风权限已重置")
    else:
        print(f"⚠️ 权限重置可能失败: {stderr}")

def test_flutter_dependencies():
    """测试 Flutter 依赖"""
    print("\n📦 检查 Flutter 依赖...")
    
    # 检查 pubspec.yaml
    pubspec_path = "frontend/pubspec.yaml"
    if os.path.exists(pubspec_path):
        with open(pubspec_path, 'r') as f:
            content = f.read()
            if "flutter_sound:" in content:
                print("✅ flutter_sound 依赖存在")
            else:
                print("❌ flutter_sound 依赖缺失")
            
            if "permission_handler:" in content:
                print("✅ permission_handler 依赖存在")
            else:
                print("❌ permission_handler 依赖缺失")
    
    # 获取依赖
    print("\n📥 获取 Flutter 依赖...")
    success, stdout, stderr = run_command("flutter pub get", cwd="frontend")
    if success:
        print("✅ Flutter 依赖获取成功")
    else:
        print(f"❌ Flutter 依赖获取失败: {stderr}")
        return False
    
    return True

def build_and_test():
    """构建并测试应用"""
    print("\n🔨 构建 macOS 应用...")
    
    # 清理构建
    success, stdout, stderr = run_command("flutter clean", cwd="frontend")
    if not success:
        print(f"⚠️ Flutter clean 失败: {stderr}")
    
    # 构建 macOS 应用
    success, stdout, stderr = run_command(
        "flutter build macos --debug", 
        cwd="frontend", 
        timeout=120
    )
    
    if success:
        print("✅ macOS 应用构建成功")
        return True
    else:
        print(f"❌ macOS 应用构建失败: {stderr}")
        return False

def test_minimal_app():
    """测试最小音频应用"""
    print("\n🧪 准备测试最小音频应用...")
    
    # 创建测试入口文件
    test_main_content = '''
import 'package:flutter/material.dart';
import 'test_mic_minimal.dart';

void main() {
  runApp(const MyApp());
}
'''
    
    with open("frontend/lib/main_test.dart", "w") as f:
        f.write(test_main_content)
    
    print("✅ 测试入口文件已创建")
    print("📱 请手动运行以下命令测试最小应用:")
    print("   cd frontend")
    print("   flutter run -d macos -t lib/main_test.dart")
    print("\n🎤 测试步骤:")
    print("1. 应用启动后，检查是否显示 'Recorder: OPEN'")
    print("2. 点击 'Start Recording' 按钮")
    print("3. 如果弹出权限请求，点击 '允许'")
    print("4. 检查状态是否变为 'RECORDING'")
    print("5. 点击 'Stop Recording' 按钮")
    print("6. 检查是否显示录音文件路径")

def create_debug_guide():
    """创建调试指南"""
    guide_content = '''# macOS 音频录制调试指南

## 快速检查清单

### 1. 权限配置
- ✅ Info.plist 包含 NSMicrophoneUsageDescription
- ✅ entitlements 包含 com.apple.security.device.microphone
- ✅ App Sandbox 启用且 Audio Input 勾选

### 2. 依赖检查
- ✅ flutter_sound: ^9.2.13
- ✅ permission_handler: ^11.3.1

### 3. 正确的调用顺序
```dart
// 1. 请求权限
final permission = await Permission.microphone.request();
if (!permission.isGranted) return;

// 2. 打开录音器
await recorder.openRecorder();

// 3. 检查状态
if (!recorder.isOpen || !recorder.isStopped) return;

// 4. 开始录音
await recorder.startRecorder(...);

// 5. 停止录音
await recorder.stopRecorder();

// 6. 关闭录音器
await recorder.closeRecorder();
```

### 4. 常见错误及解决方案

#### "Recorder is not open"
- 原因: 没有先调用 openRecorder() 或调用失败
- 解决: 确保 await recorder.openRecorder() 成功完成

#### 权限弹窗不出现
- 重置权限: `tccutil reset Microphone`
- 检查 entitlements 是否正确配置
- 确保应用已签名

#### 录音无声音
- 检查系统设置 > 安全性与隐私 > 麦克风
- 确认应用已获得麦克风权限
- 测试系统麦克风是否正常工作

### 5. 测试命令
```bash
# 重置权限
tccutil reset Microphone

# 运行测试应用
cd frontend
flutter run -d macos -t lib/main_test.dart

# 查看详细日志
flutter run -d macos -t lib/main_test.dart --verbose
```

### 6. 调试日志关键词
- ✅ "Recorder opened successfully"
- ✅ "recording started"
- ✅ "recording stopped"
- ❌ "Recorder is not open"
- ❌ "Permission denied"
'''
    
    with open("MACOS_AUDIO_DEBUG_GUIDE.md", "w") as f:
        f.write(guide_content)
    
    print("✅ 调试指南已创建: MACOS_AUDIO_DEBUG_GUIDE.md")

def main():
    """主函数"""
    print("🎤 macOS 音频录制修复验证")
    print("=" * 50)
    
    # 检查权限配置
    check_macos_permissions()
    
    # 重置权限（可选）
    reset_choice = input("\n❓ 是否重置麦克风权限? (y/N): ").lower()
    if reset_choice == 'y':
        reset_permissions()
    
    # 测试依赖
    if not test_flutter_dependencies():
        print("❌ 依赖检查失败，请修复后重试")
        return
    
    # 构建应用
    build_choice = input("\n❓ 是否构建 macOS 应用? (y/N): ").lower()
    if build_choice == 'y':
        if not build_and_test():
            print("❌ 构建失败，请检查错误信息")
            return
    
    # 创建测试应用
    test_minimal_app()
    
    # 创建调试指南
    create_debug_guide()
    
    print("\n🎉 修复验证完成!")
    print("📋 请按照上述说明测试最小应用")
    print("📖 详细调试信息请查看: MACOS_AUDIO_DEBUG_GUIDE.md")

if __name__ == "__main__":
    main()