#!/usr/bin/env python3
"""
测试浏览器录音格式
"""

import requests
import json

def test_browser_recording():
    """测试浏览器录音上传"""
    print("🎯 测试浏览器录音格式...")
    
    # 模拟浏览器发送的multipart/form-data请求
    try:
        # 创建一个简单的测试音频数据 (模拟webm格式)
        test_audio_data = b"webm_audio_data_placeholder"
        
        files = {
            'file': ('recording.wav', test_audio_data, 'audio/webm')
        }
        data = {
            'user_id': '1',
            'npc_id': '1'
        }
        
        print("📤 发送模拟浏览器录音请求...")
        response = requests.post('http://localhost:8000/process-audio', files=files, data=data)
        
        print(f"📥 响应状态: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 浏览器录音格式处理成功!")
        else:
            print("❌ 浏览器录音格式处理失败!")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_health():
    """测试健康检查"""
    try:
        print("🔌 测试后端健康状态...")
        response = requests.get('http://localhost:8000/health')
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 后端健康检查成功")
            print(f"📊 服务状态: {json.dumps(data.get('services', {}), indent=2)}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

if __name__ == "__main__":
    test_health()
    print()
    test_browser_recording()