#!/usr/bin/env python3
"""
测试音频格式修复
模拟浏览器发送不同格式的音频数据
"""
import asyncio
import websockets
import json
import base64
import requests
import logging
import numpy as np

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_audio_format_fix():
    """测试音频格式修复"""
    logger.info("🔧 测试音频格式修复...")
    
    # 1. 用户登录
    try:
        response = requests.post(
            "http://localhost:8000/auth/login",
            params={"username": "test_user", "password": "test_password"},
            timeout=10
        )
        if response.status_code != 200:
            logger.error(f"❌ 登录失败: {response.status_code}")
            return False
        
        user_data = response.json()
        user_id = user_data.get('user', {}).get('id', 1)
        logger.info(f"✅ 用户登录成功，ID: {user_id}")
    except Exception as e:
        logger.error(f"❌ 登录异常: {e}")
        return False
    
    # 2. WebSocket连接测试
    try:
        uri = f"ws://localhost:8000/ws/{user_id}"
        logger.info(f"🔌 连接WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 启动会话
            start_msg = {"type": "start_session", "npc_id": 1}
            await websocket.send(json.dumps(start_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动确认
            session_started = False
            for attempt in range(3):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    
                    if response_data.get("type") == "session_started":
                        session_id = response_data.get("session_id")
                        logger.info(f"✅ 会话启动成功，ID: {session_id}")
                        session_started = True
                        break
                    elif response_data.get("type") == "connection_established":
                        logger.info("✅ 连接已建立，继续等待会话启动...")
                        continue
                        
                except asyncio.TimeoutError:
                    logger.error(f"❌ 等待响应超时 (尝试 {attempt + 1}/3)")
                    if attempt == 2:
                        return False
            
            if not session_started:
                logger.error("❌ 会话启动失败")
                return False
            
            # 3. 测试不同格式的音频数据
            test_cases = [
                {
                    "name": "正常int16格式",
                    "data": np.random.randint(-32768, 32767, 1000, dtype=np.int16).tobytes(),
                    "expected": "success"
                },
                {
                    "name": "奇数长度数据",
                    "data": np.random.randint(0, 255, 1001, dtype=np.uint8).tobytes(),
                    "expected": "success_with_warning"
                },
                {
                    "name": "float32格式",
                    "data": np.random.uniform(-1.0, 1.0, 250).astype(np.float32).tobytes(),
                    "expected": "success"
                },
                {
                    "name": "小数据块",
                    "data": np.random.randint(-32768, 32767, 10, dtype=np.int16).tobytes(),
                    "expected": "success"
                }
            ]
            
            success_count = 0
            
            for i, test_case in enumerate(test_cases):
                logger.info(f"🧪 测试用例 {i+1}: {test_case['name']}")
                logger.info(f"   数据长度: {len(test_case['data'])} bytes")
                
                # 转换为base64并发送
                base64_audio = base64.b64encode(test_case['data']).decode('utf-8')
                
                audio_msg = {
                    "type": "audio_chunk",
                    "data": base64_audio,
                    "test_case": test_case['name']
                }
                
                await websocket.send(json.dumps(audio_msg))
                logger.info(f"📤 发送测试音频数据")
                
                # 等待处理结果
                try:
                    # 给后端一些时间处理
                    await asyncio.sleep(1)
                    
                    # 检查是否有响应
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=2)
                        response_data = json.loads(response)
                        logger.info(f"📥 收到响应: {response_data.get('type')}")
                        
                        if response_data.get('type') == 'transcription':
                            logger.info(f"✅ 测试用例 {i+1} 成功 - 收到转录结果")
                            success_count += 1
                        elif response_data.get('type') == 'error':
                            logger.error(f"❌ 测试用例 {i+1} 失败 - 错误: {response_data.get('message')}")
                        else:
                            logger.info(f"📥 测试用例 {i+1} - 其他响应: {response_data.get('type')}")
                            success_count += 1  # 至少没有崩溃
                            
                    except asyncio.TimeoutError:
                        logger.info(f"⏰ 测试用例 {i+1} - 没有立即响应（可能正在处理）")
                        success_count += 1  # 没有崩溃就算成功
                        
                except Exception as e:
                    logger.error(f"❌ 测试用例 {i+1} 异常: {e}")
                
                # 短暂等待，避免过快发送
                await asyncio.sleep(0.5)
            
            # 统计结果
            logger.info("=" * 50)
            logger.info("📊 音频格式修复测试结果:")
            logger.info(f"   总测试用例: {len(test_cases)}")
            logger.info(f"   成功处理: {success_count}")
            logger.info(f"   成功率: {success_count/len(test_cases)*100:.1f}%")
            
            if success_count == len(test_cases):
                logger.info("🎉 所有音频格式都能正确处理！")
                return True
            elif success_count > 0:
                logger.info("✅ 大部分音频格式能正确处理")
                return True
            else:
                logger.error("❌ 音频格式处理仍有问题")
                return False
            
    except Exception as e:
        logger.error(f"❌ WebSocket测试异常: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 音频格式修复测试开始...")
    logger.info("🎯 目标: 验证后端能处理各种格式的音频数据")
    logger.info("=" * 60)
    
    success = await test_audio_format_fix()
    
    logger.info("=" * 60)
    if success:
        logger.info("🎉 音频格式修复测试成功！")
        logger.info("💡 现在可以在浏览器中测试 http://localhost:8080/test_page.html")
    else:
        logger.error("❌ 音频格式修复测试失败！")

if __name__ == "__main__":
    asyncio.run(main())