#!/usr/bin/env python3
"""
测试ASR触发 - 发送足够大的音频数据来触发语音识别
"""
import asyncio
import websockets
import json
import base64
import requests
import logging
import numpy as np
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_asr_trigger():
    """测试ASR触发"""
    logger.info("🎯 测试ASR触发...")
    
    # 1. 用户登录
    try:
        response = requests.post(
            "http://localhost:8000/auth/login",
            params={"username": "test_user", "password": "test_password"},
            timeout=10
        )
        if response.status_code != 200:
            logger.error(f"❌ 登录失败: {response.status_code}")
            return False
        
        user_data = response.json()
        user_id = user_data.get('user', {}).get('id', 1)
        logger.info(f"✅ 用户登录成功，ID: {user_id}")
    except Exception as e:
        logger.error(f"❌ 登录异常: {e}")
        return False
    
    # 2. WebSocket连接测试
    try:
        uri = f"ws://localhost:8000/ws/{user_id}"
        logger.info(f"🔌 连接WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 启动会话
            start_msg = {"type": "start_session", "npc_id": 1}
            await websocket.send(json.dumps(start_msg))
            logger.info("📤 发送会话启动请求")
            
            # 等待会话启动确认
            session_started = False
            for attempt in range(3):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    
                    if response_data.get("type") == "session_started":
                        session_id = response_data.get("session_id")
                        logger.info(f"✅ 会话启动成功，ID: {session_id}")
                        session_started = True
                        break
                    elif response_data.get("type") == "connection_established":
                        logger.info("✅ 连接已建立，继续等待会话启动...")
                        continue
                        
                except asyncio.TimeoutError:
                    logger.error(f"❌ 等待响应超时 (尝试 {attempt + 1}/3)")
                    if attempt == 2:
                        return False
            
            if not session_started:
                logger.error("❌ 会话启动失败")
                return False
            
            # 3. 发送足够大的音频数据来触发ASR
            logger.info("🎤 生成足够大的音频数据来触发ASR...")
            
            # 方法1: 使用存储的音频文件
            audio_dir = Path("backend/audio_recordings")
            audio_files = list(audio_dir.glob("*.wav"))
            
            if audio_files:
                # 选择最大的音频文件
                test_file = max(audio_files, key=lambda f: f.stat().st_size)
                logger.info(f"📁 使用存储的音频文件: {test_file.name} ({test_file.stat().st_size} bytes)")
                
                with open(test_file, 'rb') as f:
                    audio_data = f.read()
                
                # 转换为base64并发送
                base64_audio = base64.b64encode(audio_data).decode('utf-8')
                
                audio_msg = {
                    "type": "audio_chunk",
                    "data": base64_audio
                }
                
                await websocket.send(json.dumps(audio_msg))
                logger.info(f"📤 发送真实音频文件: {len(audio_data)} bytes")
                
            else:
                # 方法2: 生成足够大的模拟音频数据
                logger.info("📁 没有找到音频文件，生成模拟音频数据...")
                
                # 生成2秒的16kHz音频数据 (32,000个样本)
                sample_rate = 16000
                duration = 2.0  # 2秒
                num_samples = int(sample_rate * duration)
                
                # 生成包含一些频率成分的音频信号（模拟语音）
                t = np.linspace(0, duration, num_samples, False)
                # 混合几个频率来模拟语音
                audio_signal = (
                    0.3 * np.sin(2 * np.pi * 200 * t) +  # 200Hz
                    0.2 * np.sin(2 * np.pi * 400 * t) +  # 400Hz
                    0.1 * np.sin(2 * np.pi * 800 * t) +  # 800Hz
                    0.05 * np.random.normal(0, 1, num_samples)  # 添加一些噪声
                )
                
                # 转换为int16格式
                audio_int16 = (audio_signal * 32767).astype(np.int16)
                audio_bytes = audio_int16.tobytes()
                
                logger.info(f"🎵 生成音频数据: {len(audio_bytes)} bytes, {num_samples} samples, {duration}秒")
                
                # 转换为base64并发送
                base64_audio = base64.b64encode(audio_bytes).decode('utf-8')
                
                audio_msg = {
                    "type": "audio_chunk",
                    "data": base64_audio
                }
                
                await websocket.send(json.dumps(audio_msg))
                logger.info(f"📤 发送模拟音频数据: {len(audio_bytes)} bytes")
            
            # 4. 等待ASR处理结果
            logger.info("⏳ 等待ASR处理结果...")
            
            responses_received = []
            asr_triggered = False
            transcription_received = False
            
            timeout_count = 0
            max_timeout = 30  # 30秒超时
            
            while timeout_count < max_timeout:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1)
                    response_data = json.loads(response)
                    responses_received.append(response_data)
                    
                    msg_type = response_data.get('type')
                    logger.info(f"📥 收到响应: {msg_type}")
                    
                    if msg_type == 'transcription':
                        text = response_data.get('text', '')
                        confidence = response_data.get('confidence', 0)
                        logger.info(f"🎯 ASR转录结果: '{text}' (置信度: {confidence:.2f})")
                        transcription_received = True
                        asr_triggered = True
                        
                    elif msg_type == 'response_complete':
                        logger.info("✅ 处理完成")
                        break
                        
                    elif msg_type == 'error':
                        error_msg = response_data.get('message', '未知错误')
                        logger.error(f"❌ 处理错误: {error_msg}")
                        break
                    
                    else:
                        logger.info(f"📥 其他响应: {msg_type}")
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 5 == 0:
                        logger.info(f"⏰ 等待ASR处理中... ({timeout_count}s)")
                    continue
            
            # 5. 分析结果
            logger.info("=" * 50)
            logger.info("📊 ASR触发测试结果:")
            logger.info(f"   收到响应数: {len(responses_received)}")
            logger.info(f"   ASR是否触发: {'✅ 是' if asr_triggered else '❌ 否'}")
            logger.info(f"   是否收到转录: {'✅ 是' if transcription_received else '❌ 否'}")
            
            # 统计响应类型
            response_types = {}
            for resp in responses_received:
                resp_type = resp.get('type', 'unknown')
                response_types[resp_type] = response_types.get(resp_type, 0) + 1
            
            logger.info("\n📈 响应类型统计:")
            for resp_type, count in response_types.items():
                logger.info(f"   {resp_type}: {count} 次")
            
            logger.info("=" * 50)
            
            return asr_triggered
            
    except Exception as e:
        logger.error(f"❌ WebSocket测试异常: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 ASR触发测试开始...")
    logger.info("🎯 目标: 发送足够大的音频数据来触发语音识别")
    logger.info("=" * 60)
    
    success = await test_asr_trigger()
    
    logger.info("=" * 60)
    if success:
        logger.info("🎉 ASR触发测试成功！")
        logger.info("💡 语音识别功能正常工作")
    else:
        logger.error("❌ ASR触发测试失败！")
        logger.info("🔧 可能的原因:")
        logger.info("   1. 音频数据量不足（需要至少1秒的音频）")
        logger.info("   2. VAD模型配置问题")
        logger.info("   3. ASR服务配置问题")

if __name__ == "__main__":
    asyncio.run(main())