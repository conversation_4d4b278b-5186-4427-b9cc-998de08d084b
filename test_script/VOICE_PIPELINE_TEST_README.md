# 🎤 完整语音流水线测试工具

这是一套完整的语音流水线测试工具，实现了从录音到播放的完整流程测试。

## 📋 功能特性

### 🔄 完整流水线
1. **录音阶段**: 录制音频，显示时长和大小
2. **ASR阶段**: 调用语音识别服务，显示识别结果  
3. **LLM阶段**: 调用对话生成服务，显示AI回复
4. **TTS阶段**: 调用语音合成服务，生成音频
5. **播放阶段**: 播放合成的音频

### 📊 性能监控
- 实时显示各阶段处理时间
- 录音时长统计
- 总处理时间计算
- 详细的日志记录

### 🎯 测试模式
- **手动录音测试**: 用户控制录音开始/停止
- **自动化测试**: 3秒自动录音 + 完整流水线处理
- **批量测试**: 支持多个音频文件测试

## 🚀 快速开始

### 1. 环境准备

确保在 `backend/.env` 文件中设置了以下环境变量：

```bash
# LLM服务配置
VOLCANO_API_KEY=your_volcano_api_key
VOLCANO_ENDPOINT=your_volcano_endpoint

# TTS服务配置  
MINIMAX_API_KEY=your_minimax_api_key
MINIMAX_GROUP_ID=your_minimax_group_id

# 可选配置
SILERO_VAD_MODEL_PATH=./models/silero_vad.onnx
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
```

**注意**: 如果缺少某些环境变量，系统会自动使用mock模式运行，确保测试能够正常进行。

### 2. 安装依赖

```bash
pip install librosa numpy aiohttp aiofiles websockets python-dotenv fastapi uvicorn supabase
```

### 3. 运行测试

#### 方式1: 统一启动器（推荐）

```bash
python start_voice_test.py
```

启动器提供以下选项：
1. **Web界面测试** - 可视化测试界面
2. **Python命令行测试** - 单个测试
3. **完整环境** - Web界面 + 后端API服务器
4. **多个Python测试** - 批量测试
5. **退出**

#### 方式2: 直接启动Web界面

```bash
python run_voice_pipeline_test.py
```

#### 方式3: 命令行测试

```bash
# 单个测试
python test_complete_voice_pipeline.py

# 多个测试
python test_complete_voice_pipeline.py --multiple
```

#### 方式4: 启动完整后端服务

```bash
cd backend
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 📁 文件说明

### 核心文件
- `test_complete_voice_pipeline.py` - 完整流水线测试脚本
- `complete_voice_pipeline_test.html` - Web测试界面
- `run_voice_pipeline_test.py` - 测试环境启动器

### 测试音频
- `开拓路-3.m4a` - 中文测试音频1
- `开拓路-2.m4a` - 中文测试音频2

### 输出目录
- `voice_pipeline_test_output/` - 测试结果输出目录
  - 生成的音频文件
  - 测试报告JSON文件
  - 对话记录文本文件

## 🎯 使用指南

### Web界面操作

1. **开始录音**: 点击红色录音按钮，授权麦克风权限后开始录音
2. **停止录音**: 再次点击按钮停止录音，自动开始流水线处理
3. **完整测试**: 点击蓝色按钮进行3秒自动录音测试
4. **查看结果**: 在结果面板查看ASR识别和LLM回复
5. **播放音频**: 在音频区域播放录制和生成的音频
6. **查看日志**: 在底部日志面板查看详细处理过程

### 命令行操作

```bash
# 运行单个测试（使用第一个找到的音频文件）
python test_complete_voice_pipeline.py

# 运行多个测试（测试所有可用音频文件）
python test_complete_voice_pipeline.py --multiple
```

## 📊 性能指标

测试工具会记录以下性能指标：

- **录音时长**: 音频文件的实际时长
- **ASR耗时**: 语音识别处理时间
- **LLM耗时**: 对话生成处理时间  
- **TTS耗时**: 语音合成处理时间
- **总处理时间**: 完整流水线处理时间

## 🔧 故障排除

### 常见问题

1. **麦克风权限被拒绝**
   - 检查浏览器麦克风权限设置
   - 确保使用HTTPS或localhost访问

2. **ASR服务连接失败**
   - 检查ASR服务器是否运行在 `http://************:20257`
   - 当前版本使用fallback模式，会生成模拟识别结果

3. **LLM API调用失败**
   - 检查 `VOLCANO_API_KEY` 和 `VOLCANO_ENDPOINT` 配置
   - 系统会自动使用mock响应作为fallback

4. **TTS合成失败**
   - 检查 `MINIMAX_API_KEY` 和 `MINIMAX_GROUP_ID` 配置
   - 确保网络连接正常

### 调试模式

启用详细日志：

```bash
export PYTHONPATH=backend:$PYTHONPATH
python -u test_complete_voice_pipeline.py
```

## 📈 测试报告

每次测试完成后，会在 `voice_pipeline_test_output/` 目录生成：

1. **JSON报告**: `pipeline_test_report_YYYYMMDD_HHMMSS.json`
   - 包含完整的性能指标
   - 各阶段处理结果
   - 成功/失败状态

2. **音频文件**: `pipeline_output_*.wav`
   - TTS生成的音频文件
   - 可直接播放验证效果

3. **对话记录**: `conversation_*.txt`
   - 用户输入和AI回复
   - 情感和语速参数

## 🎉 成功标准

测试成功的标准：

✅ 录音功能正常，能够捕获音频  
✅ ASR识别返回文本结果  
✅ LLM生成合理的对话回复  
✅ TTS合成生成音频文件  
✅ 音频文件可以正常播放  
✅ 整个流程在合理时间内完成  

## 🔄 持续改进

这个测试工具支持：

- 添加新的测试音频文件
- 调整各服务的配置参数
- 扩展性能监控指标
- 集成更多的语音服务

欢迎根据实际需求进行定制和扩展！