#!/usr/bin/env python3
"""
调试enhanced_asr_service
"""

import sys
import os
import numpy as np
import logging

# 添加backend目录到路径
sys.path.append('backend')

from services.enhanced_asr_service import EnhancedASRService

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_asr_debug():
    """调试enhanced_asr_service"""
    logger.info("=" * 60)
    logger.info("调试Enhanced ASR服务")
    logger.info("=" * 60)
    
    # 1. 初始化服务
    try:
        logger.info("初始化Enhanced ASR服务...")
        asr_service = EnhancedASRService(
            qwen_timeout=3,      # 短超时
            gemini_timeout=8,    # 短超时
            gemini_first=True    # Gemini优先
        )
        logger.info("✅ Enhanced ASR服务初始化成功")
    except Exception as e:
        logger.error(f"❌ Enhanced ASR服务初始化失败: {e}")
        return
    
    # 2. 测试连接
    try:
        logger.info("测试ASR服务连接...")
        connection_result = asr_service.test_connection()
        
        logger.info(f"🔧 Qwen2-Audio-7B: {'✅' if connection_result['qwen'] else '❌'}")
        logger.info(f"🔧 Gemini: {'✅' if connection_result['gemini'] else '❌'}")
        logger.info(f"📊 可用服务: {connection_result['available_services']}")
        
    except Exception as e:
        logger.error(f"❌ ASR连接测试失败: {e}")
    
    # 3. 生成不同类型的测试音频
    test_cases = [
        {
            'name': '正弦波440Hz',
            'audio': generate_sine_wave(440, 1.0, 16000),
            'expected': '可能识别为铃声或音调'
        },
        {
            'name': '白噪声',
            'audio': generate_white_noise(1.0, 16000),
            'expected': '可能识别为噪声或无内容'
        },
        {
            'name': '静音',
            'audio': generate_silence(1.0, 16000),
            'expected': '应该识别为静音'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n{'='*40}")
        logger.info(f"测试 {i}: {test_case['name']}")
        logger.info(f"预期: {test_case['expected']}")
        logger.info(f"{'='*40}")
        
        try:
            result = asr_service.transcribe(test_case['audio'], 16000)
            
            logger.info(f"✅ 成功: {result.get('success', False)}")
            logger.info(f"📝 识别文本: '{result.get('text', '')}'")
            logger.info(f"🎯 置信度: {result.get('confidence', 0.0):.3f}")
            logger.info(f"🔧 提供商: {result.get('provider', 'unknown')}")
            logger.info(f"⏱️ 时长: {result.get('duration', 0.0):.3f}秒")
            
            # 分析结果
            text = result.get('text', '')
            if text:
                logger.info(f"🔍 分析: 识别出文本 '{text}' - 这可能是Gemini对音频的解释")
            else:
                logger.info("🔍 分析: 没有识别出文本")
                
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

def generate_sine_wave(frequency: float, duration: float, sample_rate: int) -> np.ndarray:
    """生成正弦波"""
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    return np.sin(2 * np.pi * frequency * t).astype(np.float32)

def generate_white_noise(duration: float, sample_rate: int) -> np.ndarray:
    """生成白噪声"""
    samples = int(sample_rate * duration)
    return np.random.normal(0, 0.1, samples).astype(np.float32)

def generate_silence(duration: float, sample_rate: int) -> np.ndarray:
    """生成静音"""
    samples = int(sample_rate * duration)
    return np.zeros(samples, dtype=np.float32)

if __name__ == "__main__":
    # 从.env文件加载环境变量
    from dotenv import load_dotenv
    load_dotenv('backend/.env')
    
    test_enhanced_asr_debug()