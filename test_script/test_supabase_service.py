import sys
import os
import json
from dotenv import load_dotenv

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 加载环境变量
load_dotenv(os.path.join(os.path.dirname(__file__), 'backend', '.env'))

from utils.supabase_service import SupabaseService

def test_supabase_service():
    """
    测试Supabase服务的功能
    """
    print("开始测试Supabase服务...")
    
    try:
        # 初始化Supabase服务
        supabase_service = SupabaseService()
        print("✓ Supabase服务初始化成功")
        
        # 测试获取所有NPC角色信息
        print("\n1. 测试获取所有NPC角色信息...")
        all_npc_personnas = supabase_service.get_all_npc_personnas()
        print(f"   找到 {len(all_npc_personnas)} 个NPC角色")
        if all_npc_personnas:
            print(f"   第一个NPC角色ID: {all_npc_personnas[0].get('npc_id', 'N/A')}")
            print(f"   第一个NPC角色名称: {all_npc_personnas[0].get('persona_data', {}).get('name', 'N/A') if isinstance(all_npc_personnas[0].get('persona_data'), dict) else 'N/A'}")
        
        # 测试根据用户ID获取NPC角色信息
        print("\n2. 测试根据用户ID获取NPC角色信息...")
        user_id = "demo_user_001"  # 从CSV文件中获取的示例用户ID
        user_npc_personnas = supabase_service.get_npc_personna_by_user_id(user_id)
        print(f"   用户 {user_id} 有 {len(user_npc_personnas)} 个NPC角色")
        if user_npc_personnas:
            print(f"   第一个NPC角色ID: {user_npc_personnas[0].get('npc_id', 'N/A')}")
        
        # 测试根据NPC ID获取NPC角色信息
        print("\n3. 测试根据NPC ID获取NPC角色信息...")
        npc_id = "kexin"  # 从CSV文件中获取的示例NPC ID
        npc_personna = supabase_service.get_npc_personna_by_id(npc_id)
        if npc_personna:
            print(f"   找到NPC角色: {npc_personna.get('persona_data', {}).get('name', 'N/A') if isinstance(npc_personna.get('persona_data'), dict) else 'N/A'}")
            print(f"   NPC ID: {npc_personna.get('npc_id', 'N/A')}")
            print(f"   用户ID: {npc_personna.get('user_id', 'N/A')}")
        else:
            print(f"   未找到NPC ID为 {npc_id} 的角色")
        
        # 测试插入新的NPC角色信息
        print("\n4. 测试插入新的NPC角色信息...")
        new_npc_data = {
            "user_id": "test_user_001",
            "npc_id": "test_npc_001",
            "persona_data": {
                "name": "测试NPC",
                "age": 25,
                "gender": "男",
                "hobbies": ["阅读", "编程", "游戏"],
                "personality": "友好且乐于助人"
            },
            "learning_data": "这是一个测试NPC角色"
        }
        inserted_npc = supabase_service.insert_npc_personna(new_npc_data)
        if inserted_npc:
            print(f"   成功插入NPC角色: {inserted_npc.get('persona_data', {}).get('name', 'N/A')}")
            
            # 测试更新NPC角色信息
            print("\n5. 测试更新NPC角色信息...")
            update_data = {
                "learning_data": "这是一个更新后的测试NPC角色",
                "persona_data": {
                    "name": "测试NPC-更新版",
                    "age": 26,
                    "gender": "男",
                    "hobbies": ["阅读", "编程", "游戏", "旅行"],
                    "personality": "友好且乐于助人，喜欢旅行"
                }
            }
            updated_npc = supabase_service.update_npc_personna("test_npc_001", update_data)
            if updated_npc:
                print(f"   成功更新NPC角色: {updated_npc.get('persona_data', {}).get('name', 'N/A')}")
            else:
                print("   更新NPC角色失败")
            
            # 测试删除NPC角色信息
            print("\n6. 测试删除NPC角色信息...")
            delete_result = supabase_service.delete_npc_personna("test_npc_001")
            if delete_result:
                print("   成功删除NPC角色")
            else:
                print("   删除NPC角色失败")
        else:
            print("   插入NPC角色失败")
        
        print("\n✓ 所有测试完成")
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_supabase_service()
