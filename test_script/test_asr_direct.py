#!/usr/bin/env python3
"""
直接测试ASR服务器连接
"""

import sys
import os
import numpy as np
import librosa
from pathlib import Path

# 添加backend目录到路径
sys.path.append('backend')

from services.multimodal_asr_service import MultimodalASRService

def test_asr_direct():
    """直接测试ASR服务"""
    print("🎯 直接测试ASR服务连接...")
    
    # 初始化ASR服务
    asr_service = MultimodalASRService(
        model="Qwen2-Audio-7B-Instruct",
        api_key="EMPTY",
        api_base="http://172.16.1.151:20257/v1",
        timeout=60.0  # 更长的超时时间
    )
    
    # 加载音频文件
    audio_file = "开拓路-3.m4a"
    if not os.path.exists(audio_file):
        print(f"❌ 音频文件不存在: {audio_file}")
        return
    
    print(f"📁 加载音频文件: {audio_file}")
    audio, sr = librosa.load(audio_file, sr=16000, mono=True)
    print(f"✅ 音频加载成功: {len(audio)} 采样点, {sr}Hz, {len(audio)/sr:.2f}秒")
    
    # 重置服务器可用性缓存
    asr_service._server_available = None
    
    # 测试转录
    print("🚀 开始ASR转录...")
    result = asr_service.transcribe(audio)
    
    print(f"📝 ASR结果: {result}")
    
    if result.get("fallback"):
        print("❌ 使用了fallback响应，ASR服务器连接失败")
    else:
        print("✅ 使用了真实ASR服务器响应")
    
    return result

if __name__ == "__main__":
    test_asr_direct()