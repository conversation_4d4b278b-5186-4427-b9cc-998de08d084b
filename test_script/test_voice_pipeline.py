#!/usr/bin/env python3
"""
测试完整的语音管道 - 模拟前端发送音频数据到后端
"""
import asyncio
import websockets
import json
import base64
import numpy as np
import requests
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoicePipelineTester:
    def __init__(self, backend_url="http://localhost:8000", websocket_url="ws://localhost:8000"):
        self.backend_url = backend_url
        self.websocket_url = websocket_url
        self.user_id = "test_user_1"
        self.npc_id = 1
        self.session_id = None
        
    def generate_test_audio_data(self, duration_ms=1000, sample_rate=16000):
        """生成测试音频数据（模拟语音）"""
        samples = int(duration_ms * sample_rate / 1000)
        # 生成简单的正弦波作为测试音频
        t = np.linspace(0, duration_ms/1000, samples)
        frequency = 440  # A4音符
        audio = np.sin(2 * np.pi * frequency * t) * 0.5
        # 转换为16位整数
        audio_int16 = (audio * 32767).astype(np.int16)
        return audio_int16.tobytes()
    
    async def test_voice_pipeline(self):
        """测试完整的语音管道"""
        logger.info("🚀 开始测试完整的语音管道...")
        
        try:
            # 1. 用户登录
            logger.info("🔐 用户登录...")
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params={"username": "test_user", "password": "test_password"},
                timeout=10
            )
            
            if response.status_code != 200:
                logger.error(f"❌ 登录失败: {response.status_code}")
                return False
            
            user_data = response.json()
            user_id = user_data.get('user', {}).get('id', 1)
            logger.info(f"✅ 用户登录成功，ID: {user_id}")
            
            # 2. WebSocket连接
            uri = f"{self.websocket_url}/ws/{user_id}"
            logger.info(f"🔌 连接WebSocket: {uri}")
            
            async with websockets.connect(uri) as websocket:
                logger.info("✅ WebSocket连接成功")
                
                # 3. 启动会话
                start_msg = {"type": "start_session", "npc_id": self.npc_id}
                await websocket.send(json.dumps(start_msg))
                logger.info("📤 发送会话启动请求")
                
                # 等待会话启动响应
                response = await asyncio.wait_for(websocket.recv(), timeout=10)
                response_data = json.loads(response)
                
                if response_data.get("type") == "session_started":
                    self.session_id = response_data.get("session_id")
                    logger.info(f"✅ 会话启动成功，ID: {self.session_id}")
                else:
                    logger.error(f"❌ 会话启动失败: {response_data}")
                    return False
                
                # 4. 发送音频数据
                logger.info("🎵 发送测试音频数据...")
                audio_data = self.generate_test_audio_data(duration_ms=3000)  # 3秒音频
                chunk_size = 1024  # 每块1KB
                chunks = [audio_data[i:i+chunk_size] for i in range(0, len(audio_data), chunk_size)]
                
                logger.info(f"📦 将音频分为 {len(chunks)} 个块进行流式传输")
                
                for i, chunk in enumerate(chunks):
                    # 编码为base64
                    base64_chunk = base64.b64encode(chunk).decode('utf-8')
                    
                    # 发送音频块
                    audio_message = {
                        "type": "audio_chunk",
                        "data": base64_chunk
                    }
                    
                    await websocket.send(json.dumps(audio_message))
                    logger.info(f"📤 发送音频块 {i+1}/{len(chunks)} ({len(chunk)} 字节)")
                    
                    # 模拟实时流的延迟
                    await asyncio.sleep(0.1)
                
                # 5. 等待处理结果
                logger.info("⏳ 等待处理结果...")
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                        response_data = json.loads(response)
                        logger.info(f"📥 收到响应: {response_data.get('type', 'unknown')}")
                        
                        if response_data.get("type") == "transcription":
                            logger.info(f"🎯 转录结果: {response_data.get('text', 'N/A')}")
                        elif response_data.get("type") == "response_complete":
                            logger.info("✅ 响应完成")
                            break
                        elif response_data.get("type") == "error":
                            logger.error(f"❌ 错误: {response_data.get('message', 'Unknown error')}")
                            return False
                            
                except asyncio.TimeoutError:
                    logger.warning("⚠️ 等待响应超时")
                
                # 6. 结束会话
                end_msg = {"type": "end_session"}
                await websocket.send(json.dumps(end_msg))
                logger.info("📤 发送结束会话请求")
                
                # 等待结束响应
                try:
                    end_response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    end_data = json.loads(end_response)
                    if end_data.get("type") == "session_ended":
                        logger.info("✅ 会话正常结束")
                    else:
                        logger.warning(f"⚠️ 意外的结束响应: {end_data}")
                except asyncio.TimeoutError:
                    logger.warning("⚠️ 等待结束响应超时")
                
                logger.info("🎉 语音管道测试完成！")
                return True
                
        except Exception as e:
            logger.error(f"❌ 语音管道测试异常: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False

async def main():
    """主函数"""
    tester = VoicePipelineTester()
    success = await tester.test_voice_pipeline()
    
    if success:
        logger.info("✅ 语音管道测试成功！")
    else:
        logger.error("❌ 语音管道测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
