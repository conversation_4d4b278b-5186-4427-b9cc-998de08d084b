#!/usr/bin/env python3
"""
项目启动脚本 - 自动处理依赖安装和服务启动
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """运行命令并处理输出"""
    print(f"执行命令: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            raise
        return e

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")

def check_project_structure():
    """检查项目结构"""
    print("\n🔍 检查项目状态...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    print("✅ backend目录存在")
    
    requirements_file = backend_dir / "requirements.txt"
    if not requirements_file.exists():
        print("❌ requirements.txt不存在")
        return False
    print("✅ requirements.txt存在")
    
    return True

def install_backend_dependencies():
    """安装后端依赖"""
    print("\n📦 检查后端依赖...")
    backend_dir = Path("backend")
    
    # 检查是否已经安装了主要依赖
    try:
        import fastapi
        import uvicorn
        import supabase
        print("✅ 后端依赖已安装 (跳过安装)")
        return True
    except ImportError:
        pass
    
    # 安装依赖
    try:
        print("正在安装后端依赖...")
        run_command("pip install -r requirements.txt", cwd=backend_dir)
        print("✅ 后端依赖安装完成")
        return True
    except Exception as e:
        print(f"❌ 后端依赖安装失败: {e}")
        return False

def check_env_file():
    """检查环境变量文件"""
    print("\n🔧 检查环境配置...")
    
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("❌ .env文件不存在")
        return False
    print("✅ .env文件存在")
    
    # 读取并检查关键配置
    with open(env_file, 'r') as f:
        content = f.read()
    
    test_vars = []
    if "VOLCANO_API_KEY=test_" in content:
        test_vars.append("VOLCANO_API_KEY")
    if "MINIMAX_API_KEY=test_" in content:
        test_vars.append("MINIMAX_API_KEY")
    
    if test_vars:
        print(f"⚠️  以下环境变量使用测试值: {', '.join(test_vars)}")
        print("   当前使用测试值，某些功能可能无法正常工作")
    else:
        print("✅ 环境变量配置完整")
    
    return True

def check_model_files():
    """检查模型文件"""
    print("\n🔍 检查模型文件...")
    
    models_dir = Path("backend/models")
    if not models_dir.exists():
        models_dir.mkdir(parents=True)
        print("📁 创建models目录")
    
    # 检查Silero VAD模型
    vad_model = models_dir / "silero_vad.onnx"
    if vad_model.exists():
        print("✅ Silero VAD模型存在")
    else:
        print("⚠️  Silero VAD模型不存在，但会尝试自动下载")
    
    # FireRedASR模型不需要检查，使用远程服务
    print("✅ 使用远程Qwen2-Audio服务，无需本地ASR模型")
    
    return True

def test_external_connections():
    """测试外部服务连接"""
    print("\n🌐 测试连接...")
    
    # 测试Supabase连接
    try:
        supabase_url = "http://8.152.125.193:8000"
        response = requests.get(f"{supabase_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Supabase连接正常")
        else:
            print(f"⚠️  Supabase响应异常: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Supabase连接失败: {e}")
    
    # 测试Qwen2-Audio服务
    try:
        qwen_url = "http://172.16.1.151:20257/v1/models"
        response = requests.get(qwen_url, timeout=5)
        if response.status_code == 200:
            print("✅ Qwen2-Audio服务连接正常")
        else:
            print(f"⚠️  Qwen2-Audio服务响应异常: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Qwen2-Audio服务连接失败: {e}")

def start_backend():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    print("启动FastAPI服务器...")
    print("注意：某些外部服务可能不可用，但应用会使用本地Mock数据")
    print("服务将在 http://localhost:8000 启动")
    print("按 Ctrl+C 停止服务")
    
    backend_dir = Path("backend")
    try:
        os.chdir(backend_dir)
        run_command("python main.py")
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 语音聊天应用启动")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 检查项目结构
    if not check_project_structure():
        print("❌ 项目结构检查失败，退出")
        sys.exit(1)
    
    # 安装后端依赖
    if not install_backend_dependencies():
        print("❌ 依赖安装失败，退出")
        sys.exit(1)
    
    # 检查环境配置
    check_env_file()
    
    # 检查模型文件
    check_model_files()
    
    # 测试外部连接
    test_external_connections()
    
    print("\n" + "=" * 50)
    print("✅ 后端服务检查完成")
    print("=" * 50)
    
    # 启动后端
    start_backend()

if __name__ == "__main__":
    main()
