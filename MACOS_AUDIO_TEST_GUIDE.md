# 🎤 macOS音频测试指南

## 🚀 应用已成功启动！

现在请按以下步骤测试音频功能：

## 📱 测试步骤

### 1. 授权麦克风权限
当你第一次点击录音按钮时，系统会弹出权限请求对话框：
- 点击 **"好"** 或 **"允许"** 授予权限

### 2. 手动设置权限（如果自动授权失败）
1. 打开 **系统设置** (System Settings)
2. 点击 **隐私与安全性** (Privacy & Security)  
3. 点击 **麦克风** (Microphone)
4. 找到 **voice_chat_app** 或 **Runner** 并开启权限
5. 重启应用

### 3. 测试录音功能
1. 在应用中点击蓝色的麦克风按钮
2. 对着麦克风说话（比如："你好，测试一下"）
3. 松开按钮停止录音
4. 观察应用状态变化

## 🔍 预期行为

### 成功的日志输出应该显示：
```
🎤 startRecording() called - current state: VoiceChatState.connected
🎤 Proceeding with recording (Flutter Sound will handle permissions)
🔄 Creating new recorder...
🔄 Opening recorder...
✅ Recorder opened successfully
🎤 Starting Flutter Sound recording to: /path/to/temp/file.wav
✅ Flutter Sound recording started successfully
```

### 录音时应该看到：
- 麦克风按钮变成红色
- 状态显示 "Listening..." 或 "Recording..."
- 可能会有音频波形动画

### 停止录音后应该看到：
- 状态变为 "Processing..."
- 后端处理音频并返回转录结果
- 可能收到AI的语音回复

## ❌ 常见问题排查

### 如果看到 "Recorder is not open" 错误：
1. 确保已授予麦克风权限
2. 重启应用
3. 检查系统设置中的麦克风权限

### 如果权限被拒绝：
1. 点击应用右上角的 **?** 按钮查看详细权限设置指南
2. 或者按照上面的手动设置权限步骤操作

### 如果录音没有声音：
1. 检查系统音量设置
2. 确认麦克风硬件工作正常
3. 尝试在其他应用中测试麦克风

## 🎯 测试目标

成功的测试应该实现：
1. ✅ 麦克风权限正确授予
2. ✅ 录音器成功打开
3. ✅ 能够录制真实音频
4. ✅ 音频数据发送到后端
5. ✅ 收到ASR转录结果
6. ✅ 收到AI语音回复

## 📞 如果遇到问题

请提供以下信息：
1. 控制台日志输出
2. 具体的错误信息
3. 系统权限设置截图
4. macOS版本信息

现在开始测试吧！🎉