#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试真实MCP客户端的脚本
"""

import sys
import os
import asyncio
import logging

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_mcp_client():
    """测试MCP客户端"""
    try:
        from services.mcp_client import mcp_client
        from services.mcp_service import mcp_service
        from services.tool_manager_service import tool_manager_service
        
        logger.info("开始测试MCP客户端...")
        
        # 1. 测试获取所有工具
        logger.info("1. 测试获取所有工具...")
        try:
            tools = await tool_manager_service.enumerate_all_tools_async(use_cache=False)
            logger.info(f"获取到 {len(tools)} 个工具")
            
            # 显示前几个工具
            for i, tool in enumerate(tools[:5]):
                logger.info(f"  工具 {i+1}: {tool['name']} ({tool['server']}) - {tool['description'][:50]}...")
        
        except Exception as e:
            logger.error(f"获取工具列表失败: {e}")
        
        # 2. 测试执行内置工具
        logger.info("2. 测试执行内置工具...")
        try:
            result = await mcp_service.execute_tool_async("search_and_summarize", "builtin", query="AI发展趋势")
            logger.info(f"内置工具执行结果: {result['status']}")
            if result['success']:
                logger.info(f"结果内容: {str(result['result'])[:100]}...")
        except Exception as e:
            logger.error(f"执行内置工具失败: {e}")
        
        # 3. 测试MCP服务器工具（如果有的话）
        logger.info("3. 测试MCP服务器工具...")
        try:
            # 获取非内置工具
            all_tools = await tool_manager_service.enumerate_all_tools_async(use_cache=False)
            mcp_tools = [tool for tool in all_tools if tool['server'] != 'builtin']
            
            if mcp_tools:
                test_tool = mcp_tools[0]
                logger.info(f"测试MCP工具: {test_tool['name']} from {test_tool['server']}")
                
                # 尝试执行工具（使用简单参数）
                result = await mcp_service.execute_tool_async(
                    test_tool['name'], 
                    test_tool['server']
                )
                logger.info(f"MCP工具执行结果: {result['status']}")
                if result['success']:
                    logger.info(f"结果内容: {str(result['result'])[:100]}...")
            else:
                logger.info("没有找到MCP服务器工具")
        
        except Exception as e:
            logger.error(f"测试MCP服务器工具失败: {e}")
        
        # 4. 清理
        logger.info("4. 清理资源...")
        try:
            await mcp_client.cleanup()
            logger.info("清理完成")
        except Exception as e:
            logger.error(f"清理失败: {e}")
        
        logger.info("MCP客户端测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    logger.info("启动MCP客户端测试")
    asyncio.run(test_mcp_client())

if __name__ == "__main__":
    main()
