#!/bin/bash

echo "🚀 启动完整语音对话系统..."

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  端口 $1 已被占用"
        return 1
    else
        echo "✅ 端口 $1 可用"
        return 0
    fi
}

# 检查所需端口
echo "🔍 检查端口状态..."
check_port 8000
check_port 8001
check_port 8002

echo ""
echo "📋 需要启动的服务："
echo "1. 后端ASR服务 (端口8000) - Enhanced ASR + LLM"
echo "2. TTS API服务 (端口8001) - 语音合成"
echo "3. Web服务器 (端口8002) - 前端界面"
echo ""

echo "🎯 请在3个不同的终端窗口中运行以下命令："
echo ""
echo "终端1 - 后端ASR服务:"
echo "cd backend && python main.py"
echo ""
echo "终端2 - TTS API服务:"
echo "python start_real_voice_test.py"
echo ""
echo "终端3 - Web服务器:"
echo "python -m http.server 8002"
echo ""
echo "🌐 然后打开浏览器访问: http://localhost:8002/real_voice_pipeline_test.html"
echo ""
echo "📊 服务状态检查:"
echo "- 后端健康检查: http://localhost:8000/health"
echo "- TTS健康检查: http://localhost:8001/health"
echo "- 简化测试页面: http://localhost:8002/simple_recording_test.html"