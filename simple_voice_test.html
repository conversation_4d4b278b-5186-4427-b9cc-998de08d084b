<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 简化语音测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .record-btn {
            background: #dc3545;
            font-size: 18px;
            padding: 20px 40px;
        }
        .record-btn.recording {
            background: #28a745;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 简化语音测试</h1>
        
        <div>
            <button onclick="testServices()">测试服务连接</button>
            <button id="recordBtn" class="record-btn" onclick="toggleRecording()">开始录音</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="result">
            <h3>🎯 ASR识别结果</h3>
            <div id="asrResult">等待语音输入...</div>
        </div>
        
        <div class="result">
            <h3>🧠 LLM对话响应</h3>
            <div id="llmResult">等待对话生成...</div>
        </div>
        
        <div>
            <h3>🔊 音频播放</h3>
            <audio id="recordedAudio" controls style="display: none;"></audio>
            <audio id="responseAudio" controls style="display: none;"></audio>
        </div>
        
        <div class="log" id="logPanel">
            <div>🚀 简化语音测试工具已加载</div>
        </div>
    </div>

    <script>
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;

        const elements = {
            recordBtn: document.getElementById('recordBtn'),
            asrResult: document.getElementById('asrResult'),
            llmResult: document.getElementById('llmResult'),
            recordedAudio: document.getElementById('recordedAudio'),
            responseAudio: document.getElementById('responseAudio'),
            logPanel: document.getElementById('logPanel')
        };

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            elements.logPanel.appendChild(logEntry);
            elements.logPanel.scrollTop = elements.logPanel.scrollHeight;
            console.log(message);
        }

        async function testServices() {
            log('🔌 测试服务连接...');
            
            try {
                // 测试TTS服务
                const ttsResponse = await fetch('http://localhost:8001/health');
                if (ttsResponse.ok) {
                    log('✅ TTS服务连接成功');
                } else {
                    log('❌ TTS服务连接失败');
                }
            } catch (error) {
                log(`❌ TTS服务连接失败: ${error.message}`);
            }

            try {
                // 测试ASR服务
                const asrResponse = await fetch('http://localhost:8000/health');
                if (asrResponse.ok) {
                    log('✅ ASR服务连接成功');
                } else {
                    log('❌ ASR服务连接失败');
                }
            } catch (error) {
                log(`❌ ASR服务连接失败: ${error.message}`);
            }
        }

        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                stopRecording();
            }
        }

        async function startRecording() {
            try {
                log('🎤 请求麦克风权限...');

                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1
                    }
                });

                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = function() {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    log(`🎵 录音完成: ${audioBlob.size} bytes`);

                    // 保存录音用于播放
                    const recordedUrl = URL.createObjectURL(audioBlob);
                    elements.recordedAudio.src = recordedUrl;
                    elements.recordedAudio.style.display = 'block';

                    // 发送到后端处理
                    processRecordedAudio(audioBlob);

                    // 停止音频流
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.start();
                isRecording = true;

                elements.recordBtn.textContent = '停止录音';
                elements.recordBtn.className = 'record-btn recording';

                log('✅ 录音已开始');

            } catch (error) {
                log(`❌ 录音启动失败: ${error.message}`);
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                log('🛑 停止录音...');
                mediaRecorder.stop();
                isRecording = false;

                elements.recordBtn.textContent = '开始录音';
                elements.recordBtn.className = 'record-btn';
            }
        }

        async function processRecordedAudio(audioBlob) {
            try {
                log('🎤 开始处理录制的音频...');

                const formData = new FormData();
                formData.append('file', audioBlob, 'recording.wav');
                formData.append('user_id', '1');
                formData.append('npc_id', '1');

                log('📤 发送音频到ASR服务...');
                const response = await fetch('http://localhost:8000/process-audio', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.transcription) {
                        const asrText = result.transcription;
                        elements.asrResult.textContent = `识别文本: "${asrText}"`;
                        log(`🎯 ASR识别成功: "${asrText}"`);

                        if (result.response_text) {
                            const aiResponse = result.response_text;
                            elements.llmResult.textContent = `AI回复: "${aiResponse}"`;
                            log(`🧠 LLM回复: "${aiResponse}"`);

                            // 调用TTS
                            await performTTS(aiResponse);
                        }
                    } else {
                        throw new Error('ASR识别失败');
                    }
                } else {
                    throw new Error(`服务器错误: ${response.status}`);
                }

            } catch (error) {
                log(`❌ 音频处理失败: ${error.message}`);
                elements.asrResult.textContent = `错误: ${error.message}`;
            }
        }

        async function performTTS(text) {
            try {
                log(`🔊 开始TTS合成: "${text}"`);

                const response = await fetch('http://localhost:8001/api/tts/synthesize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        emotion: 'neutral',
                        speed: 1.0
                    })
                });

                if (response.ok) {
                    const audioBlob = await response.blob();
                    log(`🔊 TTS合成成功: ${audioBlob.size} 字节`);

                    const audioUrl = URL.createObjectURL(audioBlob);
                    elements.responseAudio.src = audioUrl;
                    elements.responseAudio.style.display = 'block';

                    try {
                        await elements.responseAudio.play();
                        log('🎵 音频播放成功');
                    } catch (e) {
                        log('🎵 音频已准备，请手动播放');
                    }
                } else {
                    throw new Error(`TTS合成失败: ${response.status}`);
                }

            } catch (error) {
                log(`❌ TTS合成失败: ${error.message}`);
            }
        }

        function clearResults() {
            elements.asrResult.textContent = '等待语音输入...';
            elements.llmResult.textContent = '等待对话生成...';
            elements.recordedAudio.style.display = 'none';
            elements.responseAudio.style.display = 'none';
            log('🗑️ 结果已清空');
        }

        // 页面加载完成
        window.onload = function() {
            log('🌐 页面加载完成');
            testServices(); // 自动测试服务连接
        };
    </script>
</body>
</html>