#!/usr/bin/env python3
"""
启动完整语音流水线测试环境
包含Web界面和后端测试功能
"""

import subprocess
import sys
import time
import webbrowser
import threading
import os
import signal
from pathlib import Path
import http.server
import socketserver
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('backend/.env')

class VoiceTestLauncher:
    def __init__(self):
        self.web_server_process = None
        self.backend_server_process = None
        self.running = True
        
    def check_environment(self):
        """检查环境配置"""
        print("🔍 检查环境配置...")
        
        # 检查必要文件
        required_files = [
            "complete_voice_pipeline_test.html",
            "test_complete_voice_pipeline.py",
            "backend/.env"
        ]
        
        missing_files = []
        for file in required_files:
            if not Path(file).exists():
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
            return False
        
        # 检查环境变量
        env_vars = {
            "VOLCANO_API_KEY": os.getenv("VOLCANO_API_KEY"),
            "VOLCANO_ENDPOINT": os.getenv("VOLCANO_ENDPOINT"),
            "MINIMAX_API_KEY": os.getenv("MINIMAX_API_KEY"),
            "MINIMAX_GROUP_ID": os.getenv("MINIMAX_GROUP_ID")
        }
        
        missing_vars = []
        for var, value in env_vars.items():
            if not value:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️ 缺少环境变量: {', '.join(missing_vars)}")
            print("   系统将使用mock模式运行")
        else:
            print("✅ 所有环境变量已配置")
        
        print("✅ 环境检查完成")
        return True
    
    def start_web_server(self):
        """启动Web服务器"""
        try:
            PORT = 8002
            Handler = http.server.SimpleHTTPRequestHandler
            
            # 切换到当前目录
            os.chdir(Path(__file__).parent)
            
            with socketserver.TCPServer(("", PORT), Handler) as httpd:
                print(f"✅ Web服务器已启动: http://localhost:{PORT}")
                print(f"📄 测试页面: http://localhost:{PORT}/complete_voice_pipeline_test.html")
                httpd.serve_forever()
        except Exception as e:
            print(f"❌ Web服务器启动失败: {e}")
    
    def start_backend_server(self):
        """启动后端服务器（可选）"""
        try:
            print("🚀 启动后端服务器...")
            backend_dir = Path("backend")
            if backend_dir.exists():
                self.backend_server_process = subprocess.Popen([
                    sys.executable, "-m", "uvicorn", "main:app", 
                    "--host", "0.0.0.0", "--port", "8000", "--reload"
                ], cwd=backend_dir)
                print("✅ 后端服务器已启动: http://localhost:8000")
            else:
                print("⚠️ backend目录不存在，跳过后端服务器启动")
        except Exception as e:
            print(f"❌ 后端服务器启动失败: {e}")
    
    def run_python_test(self):
        """运行Python测试"""
        try:
            print("\n🧪 运行Python测试...")
            result = subprocess.run([
                sys.executable, "test_complete_voice_pipeline.py"
            ], capture_output=False, text=True)
            
            if result.returncode == 0:
                print("✅ Python测试完成")
            else:
                print("❌ Python测试失败")
        except Exception as e:
            print(f"❌ Python测试执行失败: {e}")
    
    def signal_handler(self, signum, frame):
        """处理中断信号"""
        print("\n🛑 正在停止所有服务...")
        self.running = False
        
        if self.backend_server_process:
            self.backend_server_process.terminate()
            try:
                self.backend_server_process.wait(timeout=5)
                print("✅ 后端服务器已停止")
            except subprocess.TimeoutExpired:
                self.backend_server_process.kill()
                print("⚠️ 强制终止后端服务器")
        
        print("✅ 所有服务已停止")
        sys.exit(0)
    
    def show_menu(self):
        """显示菜单"""
        print("\n" + "="*60)
        print("🎤 完整语音流水线测试工具")
        print("="*60)
        print("请选择测试模式:")
        print("1. 启动Web界面测试 (推荐)")
        print("2. 运行Python命令行测试")
        print("3. 启动完整环境 (Web + 后端服务器)")
        print("4. 运行多个Python测试")
        print("5. 退出")
        print("="*60)
        
        while True:
            try:
                choice = input("请输入选择 (1-5): ").strip()
                if choice in ['1', '2', '3', '4', '5']:
                    return choice
                else:
                    print("❌ 无效选择，请输入1-5")
            except KeyboardInterrupt:
                print("\n👋 再见!")
                sys.exit(0)
    
    def run(self):
        """主运行函数"""
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("🎤 启动语音流水线测试环境")
        print("=" * 50)
        
        # 检查环境
        if not self.check_environment():
            return
        
        # 显示菜单
        choice = self.show_menu()
        
        if choice == '1':
            # Web界面测试
            print("\n🌐 启动Web界面测试...")
            
            # 在后台启动Web服务器
            server_thread = threading.Thread(target=self.start_web_server, daemon=True)
            server_thread.start()
            
            # 等待服务器启动
            time.sleep(2)
            
            # 自动打开浏览器
            print("🌍 正在打开浏览器...")
            webbrowser.open("http://localhost:8002/complete_voice_pipeline_test.html")
            
            print("\n" + "="*60)
            print("🎤 Web界面测试说明:")
            print("1. 浏览器会自动打开测试页面")
            print("2. 点击'开始录音'进行手动录音测试")
            print("3. 点击'开始完整测试'进行自动化测试")
            print("4. 页面会显示完整的流水线处理过程")
            print("5. 按 Ctrl+C 停止测试环境")
            print("="*60)
            
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.signal_handler(signal.SIGINT, None)
        
        elif choice == '2':
            # Python命令行测试
            self.run_python_test()
        
        elif choice == '3':
            # 完整环境
            print("\n🚀 启动完整测试环境...")
            
            # 启动后端服务器
            self.start_backend_server()
            time.sleep(3)
            
            # 启动Web服务器
            server_thread = threading.Thread(target=self.start_web_server, daemon=True)
            server_thread.start()
            time.sleep(2)
            
            # 打开浏览器
            webbrowser.open("http://localhost:8002/complete_voice_pipeline_test.html")
            
            print("\n" + "="*60)
            print("🎤 完整环境已启动:")
            print("- 后端API: http://localhost:8000")
            print("- Web界面: http://localhost:8002/complete_voice_pipeline_test.html")
            print("- API文档: http://localhost:8000/docs")
            print("- 按 Ctrl+C 停止所有服务")
            print("="*60)
            
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.signal_handler(signal.SIGINT, None)
        
        elif choice == '4':
            # 多个Python测试
            print("\n🧪 运行多个Python测试...")
            try:
                result = subprocess.run([
                    sys.executable, "test_complete_voice_pipeline.py", "--multiple"
                ], capture_output=False, text=True)
                
                if result.returncode == 0:
                    print("✅ 多个Python测试完成")
                else:
                    print("❌ 多个Python测试失败")
            except Exception as e:
                print(f"❌ 多个Python测试执行失败: {e}")
        
        elif choice == '5':
            print("👋 再见!")
            sys.exit(0)

def main():
    """主函数"""
    launcher = VoiceTestLauncher()
    launcher.run()

if __name__ == "__main__":
    main()