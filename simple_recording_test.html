<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 简单录音测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .recording {
            background: #dc3545;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        audio {
            width: 100%;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎤 简单录音测试</h1>
    
    <button id="recordBtn" onclick="toggleRecording()">开始录音</button>
    <button onclick="testBackend()">测试后端</button>
    <button onclick="clearLog()">清空日志</button>
    
    <div>
        <label>录制的音频:</label>
        <audio id="recordedAudio" controls style="display: none;"></audio>
    </div>
    
    <div class="log" id="logArea">点击"开始录音"测试录音功能...</div>

    <script>
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;
        
        const recordBtn = document.getElementById('recordBtn');
        const recordedAudio = document.getElementById('recordedAudio');
        const logArea = document.getElementById('logArea');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            logArea.textContent = '';
        }

        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                stopRecording();
            }
        }

        async function startRecording() {
            try {
                log('🎤 请求麦克风权限...');
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                log('✅ 麦克风权限获取成功');

                const options = {};
                if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
                    options.mimeType = 'audio/webm;codecs=opus';
                    log('📊 使用音频格式: audio/webm;codecs=opus');
                } else if (MediaRecorder.isTypeSupported('audio/wav')) {
                    options.mimeType = 'audio/wav';
                    log('📊 使用音频格式: audio/wav');
                } else {
                    log('⚠️ 使用默认音频格式');
                }

                mediaRecorder = new MediaRecorder(stream, options);
                audioChunks = [];
                
                const recordingStartTime = Date.now();

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                        log(`📦 收到音频数据: ${event.data.size} bytes`);
                    }
                };

                mediaRecorder.onstop = function() {
                    const recordingDuration = ((Date.now() - recordingStartTime) / 1000).toFixed(2);
                    
                    const audioBlob = new Blob(audioChunks, { type: options.mimeType || 'audio/webm' });
                    log(`🎵 录音完成: ${audioBlob.size} bytes, 时长: ${recordingDuration}秒`);
                    
                    // 保存录音用于播放
                    const recordedUrl = URL.createObjectURL(audioBlob);
                    recordedAudio.src = recordedUrl;
                    recordedAudio.style.display = 'block';
                    
                    // 测试发送到后端
                    testProcessAudio(audioBlob);
                    
                    // 停止音频流
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.onerror = function(event) {
                    log(`❌ 录音错误: ${event.error}`);
                };

                mediaRecorder.start(1000);
                isRecording = true;
                
                recordBtn.textContent = '停止录音';
                recordBtn.className = 'recording';
                
                log('✅ 录音已开始');

            } catch (error) {
                log(`❌ 录音启动失败: ${error.message}`);
                if (error.name === 'NotAllowedError') {
                    log('💡 请允许浏览器访问麦克风权限');
                } else if (error.name === 'NotFoundError') {
                    log('💡 未找到麦克风设备');
                }
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                log('🛑 停止录音...');
                mediaRecorder.stop();
                isRecording = false;
                
                recordBtn.textContent = '开始录音';
                recordBtn.className = '';
            }
        }

        async function testProcessAudio(audioBlob) {
            try {
                log('📤 发送音频到后端处理...');
                
                const formData = new FormData();
                formData.append('file', audioBlob, 'recording.wav');
                formData.append('user_id', '1');
                formData.append('npc_id', '1');
                
                const response = await fetch('http://localhost:8000/process-audio', {
                    method: 'POST',
                    body: formData
                });
                
                log(`📥 后端响应状态: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log('✅ 后端处理成功!');
                    log(`📝 ASR识别: "${result.transcription}"`);
                    log(`🔧 ASR提供商: ${result.asr_provider || 'N/A'}`);
                    log(`🧠 LLM回复: "${result.response_text}"`);
                    log(`🔊 TTS音频大小: ${result.audio_size} bytes`);
                } else {
                    const errorText = await response.text();
                    log(`❌ 后端处理失败: ${response.status} - ${errorText}`);
                }
                
            } catch (error) {
                log(`❌ 发送到后端失败: ${error.message}`);
                log('💡 请确保后端服务在 http://localhost:8000 运行');
            }
        }

        async function testBackend() {
            try {
                log('🔌 测试后端连接...');
                const response = await fetch('http://localhost:8000/health');
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ 后端连接成功');
                    log(`📊 服务状态: ${JSON.stringify(data.services)}`);
                } else {
                    log(`❌ 后端连接失败: ${response.status}`);
                }
            } catch (error) {
                log(`❌ 后端连接异常: ${error.message}`);
                log('💡 请确保后端服务在 http://localhost:8000 运行');
            }
        }

        // 页面加载时自动测试后端
        window.onload = function() {
            testBackend();
        };
    </script>
</body>
</html>