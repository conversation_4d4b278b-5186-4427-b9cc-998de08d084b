#!/usr/bin/env python3
"""
打开语音测试页面
"""

import webbrowser
import time

def main():
    print("🌐 正在打开语音对话测试页面...")
    
    # 打开浏览器
    url = "http://localhost:8002/real_voice_pipeline_test.html"
    webbrowser.open(url)
    
    print(f"✅ 已打开: {url}")
    print("\n📋 使用说明:")
    print("1. 点击'开始录音'按钮")
    print("2. 清晰地说话")
    print("3. 点击'停止录音'")
    print("4. 等待ASR识别结果")
    print("5. 查看LLM回复和TTS语音播放")
    print("\n🔧 如果页面无法加载，请运行: python3 quick_start.py")

if __name__ == "__main__":
    main()