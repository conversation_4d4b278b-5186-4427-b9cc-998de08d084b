#!/usr/bin/env python3
"""
录音测试服务器
专门用于测试录音功能，保存和分析录音文件
"""
import asyncio
import websockets
import json
import base64
import wave
import numpy as np
from pathlib import Path
from datetime import datetime
import logging
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="录音测试服务器", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建录音保存目录
RECORDINGS_DIR = Path("test_recordings")
RECORDINGS_DIR.mkdir(exist_ok=True)

# 全局变量
active_connections = {}
recording_counter = 0

class RecordingManager:
    def __init__(self):
        self.recordings = {}
        self.counter = 0
    
    def save_recording(self, audio_data: bytes, metadata: dict = None) -> str:
        """保存录音文件"""
        self.counter += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        filename = f"recording_{self.counter}_{timestamp}.wav"
        filepath = RECORDINGS_DIR / filename
        
        try:
            # 尝试解析音频数据
            if metadata and metadata.get('format') == 'raw_pcm':
                # 原始PCM数据，直接保存为WAV
                sample_rate = metadata.get('sample_rate', 16000)
                channels = metadata.get('channels', 1)
                
                # 转换为numpy数组
                if len(audio_data) % 2 == 0:
                    audio_array = np.frombuffer(audio_data, dtype=np.int16)
                else:
                    # 奇数长度，转换为uint8再处理
                    audio_uint8 = np.frombuffer(audio_data, dtype=np.uint8)
                    audio_array = ((audio_uint8.astype(np.float32) - 128.0) / 128.0 * 32767).astype(np.int16)
                
                # 保存为WAV文件
                with wave.open(str(filepath), 'wb') as wav_file:
                    wav_file.setnchannels(channels)
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(sample_rate)
                    wav_file.writeframes(audio_array.tobytes())
                
                logger.info(f"📁 保存PCM录音: {filename} ({len(audio_data)} bytes → {len(audio_array)} samples)")
                
            else:
                # 直接保存原始数据
                with open(filepath, 'wb') as f:
                    f.write(audio_data)
                
                logger.info(f"📁 保存原始录音: {filename} ({len(audio_data)} bytes)")
            
            # 记录录音信息
            recording_info = {
                'id': self.counter,
                'filename': filename,
                'filepath': str(filepath),
                'size': len(audio_data),
                'timestamp': datetime.now().isoformat(),
                'metadata': metadata or {}
            }
            
            self.recordings[self.counter] = recording_info
            
            return filename
            
        except Exception as e:
            logger.error(f"❌ 保存录音失败: {e}")
            return None
    
    def get_recording_info(self, recording_id: int = None):
        """获取录音信息"""
        if recording_id:
            return self.recordings.get(recording_id)
        return list(self.recordings.values())
    
    def analyze_recording(self, recording_id: int):
        """分析录音文件"""
        recording = self.recordings.get(recording_id)
        if not recording:
            return None
        
        filepath = Path(recording['filepath'])
        if not filepath.exists():
            return None
        
        try:
            # 尝试读取WAV文件
            with wave.open(str(filepath), 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                duration = frames / sample_rate
                
                # 读取音频数据进行分析
                audio_data = wav_file.readframes(frames)
                audio_array = np.frombuffer(audio_data, dtype=np.int16)
                
                # 计算音频统计信息
                max_amplitude = np.max(np.abs(audio_array))
                rms = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
                
                analysis = {
                    'filename': recording['filename'],
                    'duration': f"{duration:.2f}秒",
                    'sample_rate': f"{sample_rate} Hz",
                    'channels': channels,
                    'sample_width': f"{sample_width * 8} bit",
                    'frames': frames,
                    'max_amplitude': int(max_amplitude),
                    'rms': f"{rms:.2f}",
                    'file_size': f"{filepath.stat().st_size / 1024:.1f} KB",
                    'is_valid': True
                }
                
                logger.info(f"📊 录音分析完成: {recording['filename']}")
                return analysis
                
        except Exception as e:
            logger.error(f"❌ 录音分析失败: {e}")
            return {
                'filename': recording['filename'],
                'error': str(e),
                'is_valid': False
            }

# 创建录音管理器
recording_manager = RecordingManager()

@app.get("/")
async def root():
    """返回录音测试页面"""
    try:
        with open("test_recording.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return {"message": "录音测试服务器运行中", "status": "ready"}

@app.get("/recordings")
async def get_recordings():
    """获取所有录音列表"""
    recordings = recording_manager.get_recording_info()
    return {"recordings": recordings, "count": len(recordings)}

@app.get("/recordings/{recording_id}")
async def get_recording(recording_id: int):
    """获取特定录音信息"""
    recording = recording_manager.get_recording_info(recording_id)
    if not recording:
        return {"error": "录音不存在"}
    return recording

@app.get("/recordings/{recording_id}/analyze")
async def analyze_recording(recording_id: int):
    """分析录音文件"""
    analysis = recording_manager.analyze_recording(recording_id)
    if not analysis:
        return {"error": "录音不存在或分析失败"}
    return analysis

@app.get("/recordings/{recording_id}/download")
async def download_recording(recording_id: int):
    """下载录音文件"""
    recording = recording_manager.get_recording_info(recording_id)
    if not recording:
        return {"error": "录音不存在"}
    
    filepath = Path(recording['filepath'])
    if not filepath.exists():
        return {"error": "录音文件不存在"}
    
    return FileResponse(
        path=str(filepath),
        filename=recording['filename'],
        media_type='audio/wav'
    )

@app.post("/upload-recording")
async def upload_recording(
    file: UploadFile = File(...),
    metadata: str = Form(None)
):
    """上传录音文件"""
    try:
        # 读取文件数据
        audio_data = await file.read()
        
        # 解析元数据
        metadata_dict = {}
        if metadata:
            try:
                metadata_dict = json.loads(metadata)
            except:
                pass
        
        metadata_dict.update({
            'original_filename': file.filename,
            'content_type': file.content_type,
            'upload_method': 'http_upload'
        })
        
        # 保存录音
        filename = recording_manager.save_recording(audio_data, metadata_dict)
        
        if filename:
            return {
                "success": True,
                "filename": filename,
                "size": len(audio_data),
                "message": "录音上传成功"
            }
        else:
            return {"success": False, "message": "录音保存失败"}
            
    except Exception as e:
        logger.error(f"❌ 录音上传失败: {e}")
        return {"success": False, "message": f"上传失败: {str(e)}"}

@app.websocket("/ws/recording")
async def websocket_recording_endpoint(websocket: WebSocket):
    """WebSocket录音端点"""
    await websocket.accept()
    connection_id = f"conn_{datetime.now().timestamp()}"
    active_connections[connection_id] = websocket
    
    logger.info(f"🔌 录音WebSocket连接: {connection_id}")
    
    try:
        # 发送连接确认
        await websocket.send_text(json.dumps({
            "type": "connected",
            "connection_id": connection_id,
            "message": "录音WebSocket连接成功"
        }))
        
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "audio_data":
                # 处理音频数据
                audio_base64 = message.get("data", "")
                metadata = message.get("metadata", {})
                
                try:
                    # 解码音频数据
                    audio_data = base64.b64decode(audio_base64)
                    
                    # 添加WebSocket相关元数据
                    metadata.update({
                        'connection_id': connection_id,
                        'receive_method': 'websocket',
                        'data_size': len(audio_data)
                    })
                    
                    # 保存录音
                    filename = recording_manager.save_recording(audio_data, metadata)
                    
                    if filename:
                        # 分析录音
                        recording_id = recording_manager.counter
                        analysis = recording_manager.analyze_recording(recording_id)
                        
                        # 发送成功响应
                        await websocket.send_text(json.dumps({
                            "type": "recording_saved",
                            "filename": filename,
                            "recording_id": recording_id,
                            "size": len(audio_data),
                            "analysis": analysis,
                            "message": "录音保存成功"
                        }))
                    else:
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "message": "录音保存失败"
                        }))
                        
                except Exception as e:
                    logger.error(f"❌ 处理音频数据失败: {e}")
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": f"处理音频数据失败: {str(e)}"
                    }))
            
            elif message_type == "ping":
                # 心跳响应
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }))
            
            else:
                # 未知消息类型
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"未知消息类型: {message_type}"
                }))
    
    except WebSocketDisconnect:
        logger.info(f"🔌 录音WebSocket断开: {connection_id}")
        if connection_id in active_connections:
            del active_connections[connection_id]
    except Exception as e:
        logger.error(f"❌ WebSocket错误: {e}")
        if connection_id in active_connections:
            del active_connections[connection_id]

@app.get("/status")
async def get_status():
    """获取服务状态"""
    return {
        "status": "running",
        "active_connections": len(active_connections),
        "total_recordings": len(recording_manager.recordings),
        "recordings_dir": str(RECORDINGS_DIR),
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    logger.info("🚀 启动录音测试服务器...")
    logger.info(f"📁 录音保存目录: {RECORDINGS_DIR.absolute()}")
    logger.info("🌐 访问 http://localhost:8001 进行录音测试")
    
    uvicorn.run(app, host="0.0.0.0", port=8001)