# Real-time Voice Chat App

A real-time voice chat application similar to ChatGPT's voice interface, built with Flutter frontend and Supabase backend. Features continuous voice interaction with AI assistants, supporting user interruptions and natural conversation flow.

## ✨ Features

- 🎙️ **Real-time Voice Chat** - Continuous voice input and TTS output like ChatGPT
- 🔊 **Voice Activity Detection** - Using Silero-VAD-ONNX for accurate speech detection
- 🗣️ **Speech Recognition** - FireRedASR for high-quality Chinese/English ASR
- 🤖 **AI Streaming Responses** - Volcano Engine API with tool calling support
- 🎵 **Emotional TTS** - MiniMax WebSocket API with emotion and speed control
- ⚡ **User Interruption** - Natural conversation with interruption support
- 👥 **Multiple NPCs** - Different AI personalities and conversation styles
- 🔐 **Secure Authentication** - Username+password login system
- 📱 **Flutter Mobile App** - Beautiful voice-only interface
- 🗄️ **Supabase Backend** - Scalable database and real-time features

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │  Backend API    │    │   Supabase DB   │
│   (Frontend)    │◄──►│   (FastAPI)     │◄──►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Voice Recording │    │ VAD→ASR→LLM→TTS │    │ 4-Role Messages │
│ & Interruption  │    │ Processing      │    │ user/assistant/ │
│ Detection       │    │ Pipeline        │    │ developer/tool  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Flutter 3.0+
- Git (for model downloads)
- Supabase account

### 1. Clone and Setup

```bash
git clone <your-repo>
cd voice-chat-app

# Run automated setup
python setup.py
```

### 2. Download AI Models

```bash
# Silero VAD (auto-downloaded by setup script)
# FireRedASR - Download manually:
git clone https://huggingface.co/FireRedTeam/FireRedASR-AED-L backend/models/firered_asr
```

### 3. Configure Environment

Edit `backend/.env` with your API keys:

```env
# Supabase (already configured)
SUPABASE_URL=http://*************:8000
SUPABASE_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

# Add your API keys:
VOLCANO_API_KEY=your_volcano_api_key
VOLCANO_ENDPOINT=https://ark.cn-beijing.volces.com/api/v3/chat/completions

MINIMAX_API_KEY=your_minimax_api_key
MINIMAX_GROUP_ID=your_minimax_group_id
```

### 4. Setup Database

1. Go to your Supabase dashboard
2. Run the SQL migration from `supabase/migrations/001_initial_schema.sql`

### 5. Start the Application

```bash
# Backend
cd backend
python main.py

# Frontend (in another terminal)
cd frontend
flutter run
```

## 🎯 Voice Chat Flow

```
1. User speaks → VAD detects speech → ASR transcribes
2. Text sent to LLM → Volcano Engine generates response
3. Response parsed → Extract SPEAK content (emotion, speed, text)
4. TTS generates audio → Streamed to frontend
5. User can interrupt at any time → Natural conversation flow
```

## 🔧 API Keys Required

### Volcano Engine (火山方舟)
- **Purpose**: LLM streaming responses with tool calling
- **Get from**: https://www.volcengine.com/
- **Docs**: https://www.volcengine.com/docs/82379/1569618

### MiniMax TTS
- **Purpose**: Emotional text-to-speech synthesis
- **Get from**: https://platform.minimaxi.com/
- **Docs**: https://platform.minimaxi.com/document/同步语音合成

## 📱 Mobile App Features

- **Voice-only Interface** - No text display, pure voice interaction
- **Real-time Audio** - Continuous recording and playback
- **Visual Feedback** - Audio waveforms and speaking indicators
- **Interruption Support** - Tap to interrupt AI responses
- **NPC Selection** - Choose different conversation partners
- **Guest Mode** - Test without registration

## 🗄️ Database Schema

### 4-Role Message System

Based on SFT training data format:

- **user**: Human input messages
- **assistant**: AI responses with SPEAK formatting
- **developer**: System/debug messages
- **tool**: Tool call results and function outputs

### Tables

- `users` - Username+password authentication
- `npcs` - AI conversation partners with different prompts
- `conversation_sessions` - Individual chat sessions
- `conversation_messages` - Message history with 4-role support

## 🔄 Message Format

```json
{
  "role": "assistant",
  "content": "<turn>\n<THINK>\n## Analysis\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>Hello!</text></SPEAK>\n</turn>",
  "emotion": "neutral",
  "speed": 1.0,
  "tool_calls": [...]
}
```

## 🛠️ Development

### Backend Development

```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py
```

### Frontend Development

```bash
cd frontend
flutter pub get
flutter run
```

### Testing

```bash
# Test backend health
curl http://localhost:8000/health

# Test WebSocket
wscat -c ws://localhost:8000/ws/test-user

# Test authentication
curl -X POST http://localhost:8000/auth/login \
  -d "username=test&password=test123"
```

## 📦 Deployment

### Docker Deployment

```bash
docker-compose up -d
```

### Mobile App Build

```bash
# Android
flutter build apk --release

# iOS (requires macOS)
flutter build ios --release
```

See `DEPLOYMENT.md` for detailed production deployment guide.

## 🔍 Troubleshooting

### Common Issues

1. **Models not found**: Ensure FireRedASR is downloaded correctly
2. **API errors**: Check your API keys in `.env`
3. **WebSocket issues**: Verify backend is running on correct port
4. **Audio problems**: Test on physical device, not simulator

### Debug Commands

```bash
# Check backend logs
cd backend && python main.py

# Flutter debug
flutter logs

# Test endpoints
curl http://localhost:8000/npcs
```

## 📚 Documentation

- `QUICKSTART.md` - Quick setup guide
- `PROJECT_STRUCTURE.md` - Detailed project organization
- `DEPLOYMENT.md` - Production deployment guide

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **Silero Team** - VAD model
- **FireRed Team** - ASR model
- **Volcano Engine** - LLM API
- **MiniMax** - TTS API
- **Supabase** - Backend infrastructure

---

**Ready to chat with AI using just your voice? Get started now!** 🎉