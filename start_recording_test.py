#!/usr/bin/env python3
"""
启动录音测试环境
提供完整的录音测试界面
"""
import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def main():
    """启动录音测试环境"""
    print("🎤 启动录音测试环境")
    print("=" * 40)
    
    # 检查必要文件
    required_files = [
        "recording_test_server.py",
        "test_recording.html"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return
    
    print("✅ 所有必要文件存在")
    
    try:
        # 启动录音测试服务器
        print("🚀 启动录音测试服务器...")
        server_process = subprocess.Popen([
            sys.executable, "recording_test_server.py"
        ])
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 检查服务器是否运行
        if server_process.poll() is None:
            print("✅ 录音测试服务器已启动")
            print("🌐 服务器地址: http://localhost:8001")
            
            # 自动打开浏览器
            print("🌍 正在打开浏览器...")
            webbrowser.open("http://localhost:8001")
            
            print("\n" + "="*50)
            print("🎤 录音测试说明:")
            print("1. 浏览器会自动打开录音测试页面")
            print("2. 首次使用需要授权麦克风权限")
            print("3. 点击'开始录音'按钮进行录音")
            print("4. 录音完成后可以播放、下载和分析")
            print("5. 按 Ctrl+C 停止测试服务器")
            print("="*50)
            
            try:
                # 等待用户中断
                server_process.wait()
            except KeyboardInterrupt:
                print("\n🛑 正在停止录音测试服务器...")
                server_process.terminate()
                try:
                    server_process.wait(timeout=5)
                    print("✅ 录音测试服务器已停止")
                except subprocess.TimeoutExpired:
                    server_process.kill()
                    print("⚠️ 强制终止录音测试服务器")
        else:
            print("❌ 录音测试服务器启动失败")
            stdout, stderr = server_process.communicate()
            if stdout:
                print(f"输出: {stdout.decode()}")
            if stderr:
                print(f"错误: {stderr.decode()}")
    
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()