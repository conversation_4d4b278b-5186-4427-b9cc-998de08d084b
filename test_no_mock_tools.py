#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试确认所有mock工具已被删除的脚本
"""

import sys
import os
import asyncio
import logging

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_no_mock_tools():
    """测试确认没有mock工具"""
    try:
        from services.mcp_service import mcp_service
        from services.tool_manager_service import tool_manager_service
        
        logger.info("开始测试确认mock工具已删除...")
        
        # 1. 测试同步获取工具（应该没有内置工具）
        logger.info("1. 测试同步获取工具...")
        try:
            tools = tool_manager_service.enumerate_all_tools(use_cache=False)
            logger.info(f"同步获取到 {len(tools)} 个工具")
            
            # 检查是否有内置工具
            builtin_tools = [tool for tool in tools if tool.get('server') == 'builtin']
            if builtin_tools:
                logger.error(f"发现 {len(builtin_tools)} 个内置工具，应该已被删除！")
                for tool in builtin_tools:
                    logger.error(f"  - {tool['name']} ({tool['server']})")
            else:
                logger.info("✓ 没有发现内置工具，符合预期")
            
            # 显示所有工具
            for i, tool in enumerate(tools):
                logger.info(f"  工具 {i+1}: {tool['name']} ({tool['server']}) - {tool['description'][:50]}...")
        
        except Exception as e:
            logger.error(f"同步获取工具失败: {e}")
        
        # 2. 测试异步获取工具
        logger.info("2. 测试异步获取工具...")
        try:
            tools = await tool_manager_service.enumerate_all_tools_async(use_cache=False)
            logger.info(f"异步获取到 {len(tools)} 个工具")
            
            # 检查是否有内置工具
            builtin_tools = [tool for tool in tools if tool.get('server') == 'builtin']
            if builtin_tools:
                logger.error(f"发现 {len(builtin_tools)} 个内置工具，应该已被删除！")
                for tool in builtin_tools:
                    logger.error(f"  - {tool['name']} ({tool['server']})")
            else:
                logger.info("✓ 没有发现内置工具，符合预期")
        
        except Exception as e:
            logger.error(f"异步获取工具失败: {e}")
        
        # 3. 测试执行内置工具（应该失败）
        logger.info("3. 测试执行内置工具（应该失败）...")
        try:
            result = mcp_service.execute_tool("get-tickets", "builtin", from_station="北京", to_station="上海")
            if result.get('success'):
                logger.error("内置工具执行成功，应该已被删除！")
                logger.error(f"结果: {result}")
            else:
                logger.info("✓ 内置工具执行失败，符合预期")
                logger.info(f"错误信息: {result.get('error', 'Unknown error')}")
        except Exception as e:
            logger.info(f"✓ 内置工具执行抛出异常，符合预期: {e}")
        
        # 4. 测试异步执行内置工具（应该失败）
        logger.info("4. 测试异步执行内置工具（应该失败）...")
        try:
            result = await mcp_service.execute_tool_async("search_and_summarize", "builtin", query="AI发展趋势")
            if result.get('success'):
                logger.error("内置工具异步执行成功，应该已被删除！")
                logger.error(f"结果: {result}")
            else:
                logger.info("✓ 内置工具异步执行失败，符合预期")
                logger.info(f"错误信息: {result.get('error', 'Unknown error')}")
        except Exception as e:
            logger.info(f"✓ 内置工具异步执行抛出异常，符合预期: {e}")
        
        # 5. 检查MCP服务器配置
        logger.info("5. 检查MCP服务器配置...")
        try:
            from services.mcp_client import mcp_client
            logger.info(f"MCP客户端已加载，配置了 {len(mcp_client.server_configs)} 个服务器")
            for server_name in mcp_client.server_configs.keys():
                logger.info(f"  - {server_name}")
        except Exception as e:
            logger.error(f"检查MCP客户端失败: {e}")
        
        logger.info("Mock工具删除测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    logger.info("启动Mock工具删除验证测试")
    asyncio.run(test_no_mock_tools())

if __name__ == "__main__":
    main()
