# macOS 音频录制调试指南

## 快速检查清单

### 1. 权限配置
- ✅ Info.plist 包含 NSMicrophoneUsageDescription
- ✅ entitlements 包含 com.apple.security.device.microphone
- ✅ App Sandbox 启用且 Audio Input 勾选

### 2. 依赖检查
- ✅ flutter_sound: ^9.2.13
- ✅ permission_handler: ^11.3.1

### 3. 正确的调用顺序
```dart
// 1. 请求权限
final permission = await Permission.microphone.request();
if (!permission.isGranted) return;

// 2. 打开录音器
await recorder.openRecorder();

// 3. 检查状态
if (!recorder.isOpen || !recorder.isStopped) return;

// 4. 开始录音
await recorder.startRecorder(...);

// 5. 停止录音
await recorder.stopRecorder();

// 6. 关闭录音器
await recorder.closeRecorder();
```

### 4. 常见错误及解决方案

#### "Recorder is not open"
- 原因: 没有先调用 openRecorder() 或调用失败
- 解决: 确保 await recorder.openRecorder() 成功完成

#### 权限弹窗不出现
- 重置权限: `tccutil reset Microphone`
- 检查 entitlements 是否正确配置
- 确保应用已签名

#### 录音无声音
- 检查系统设置 > 安全性与隐私 > 麦克风
- 确认应用已获得麦克风权限
- 测试系统麦克风是否正常工作

### 5. 测试命令
```bash
# 重置权限
tccutil reset Microphone

# 运行测试应用
cd frontend
flutter run -d macos -t lib/main_test.dart

# 查看详细日志
flutter run -d macos -t lib/main_test.dart --verbose
```

### 6. 调试日志关键词
- ✅ "Recorder opened successfully"
- ✅ "recording started"
- ✅ "recording stopped"
- ❌ "Recorder is not open"
- ❌ "Permission denied"

### 7. 测试步骤
1. 运行测试应用: `flutter run -d macos -t lib/main_test.dart`
2. 应用启动后，检查是否显示 'Recorder: OPEN'
3. 点击 'Start Recording' 按钮
4. 如果弹出权限请求，点击 '允许'
5. 检查状态是否变为 'RECORDING'
6. 点击 'Stop Recording' 按钮
7. 检查是否显示录音文件路径

### 8. 如果测试应用工作正常
说明基础录音功能已修复，可以继续测试完整的语音聊天应用:
```bash
cd frontend
flutter run -d macos
```