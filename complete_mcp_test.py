#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整的MCP配置文件和服务器联通性测试脚本
"""

import sys
import os
import json
import glob
import logging

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mcp_config_files():
    """
    测试MCP配置文件
    """
    logger.info("=== 测试MCP配置文件 ===")
    
    try:
        # 1. 查找所有MCP配置文件
        logger.info("1. 查找MCP配置文件...")
        mcp_config_dir = os.path.join(os.path.dirname(__file__), 'backend', 'mcp_config')
        config_files = glob.glob(os.path.join(mcp_config_dir, "*.json"))
        
        logger.info(f"找到 {len(config_files)} 个配置文件")
        for config_file in config_files:
            logger.info(f"  - {os.path.basename(config_file)}")
        
        # 2. 加载并验证所有配置文件
        logger.info("2. 加载并验证MCP配置文件...")
        valid_configs = []
        invalid_configs = []
        
        for config_file in config_files:
            config_name = os.path.splitext(os.path.basename(config_file))[0]
            logger.info(f"处理配置文件: {config_name}")
            
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content:
                        logger.warning(f"配置文件 {config_name} 为空")
                        invalid_configs.append(config_name)
                        continue
                    
                    config_data = json.loads(content)
                    
                    # 验证基本结构
                    if "mcpServers" not in config_data:
                        logger.error(f"配置文件 {config_name} 缺少 'mcpServers' 字段")
                        invalid_configs.append(config_name)
                        continue
                    
                    valid_configs.append((config_name, config_data))
                    logger.info(f"  ✓ 配置文件 {config_name} 加载成功")
                    
            except json.JSONDecodeError as e:
                logger.error(f"配置文件 {config_name} JSON格式错误: {e}")
                invalid_configs.append(config_name)
            except Exception as e:
                logger.error(f"处理配置文件 {config_name} 时出现错误: {e}")
                invalid_configs.append(config_name)
        
        # 3. 显示验证结果
        logger.info("3. 配置文件验证结果:")
        logger.info(f"  有效配置文件: {len(valid_configs)}")
        logger.info(f"  无效配置文件: {len(invalid_configs)}")
        
        if valid_configs:
            logger.info("有效配置文件详情:")
            for config_name, config_data in valid_configs:
                logger.info(f"  - {config_name}:")
                mcp_servers = config_data.get("mcpServers", {})
                for server_name, server_config in mcp_servers.items():
                    command = server_config.get("command", "unknown")
                    args = server_config.get("args", [])
                    logger.info(f"    * {server_name}: {command} {' '.join(args)}")
        
        if invalid_configs:
            logger.warning("无效配置文件列表:")
            for config_name in invalid_configs:
                logger.warning(f"  - {config_name}")
        
        return valid_configs, invalid_configs
        
    except Exception as e:
        logger.error(f"测试MCP配置文件时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return [], []

def test_mcp_service_registration(valid_configs):
    """
    测试MCP服务注册功能
    """
    logger.info("=== 测试MCP服务注册功能 ===")
    
    try:
        # 导入服务
        from functions.master_service import master_service
        
        # 1. 测试注册有效的配置文件
        logger.info("1. 测试注册有效的配置文件...")
        registered_servers = []
        
        if valid_configs:
            for config_name, config_data in valid_configs:
                logger.info(f"注册配置文件: {config_name}")
                mcp_servers = config_data.get("mcpServers", {})
                
                for server_name, server_config in mcp_servers.items():
                    command = server_config.get("command")
                    args = server_config.get("args", [])
                    env = server_config.get("env", {})
                    
                    # 创建服务器URL表示
                    server_url = f"local://{command} {' '.join(args)}"
                    
                    # 注册服务器
                    result = master_service.call(
                        "register_server", 
                        server_name=f"{config_name}-{server_name}", 
                        server_url=server_url,
                        server_info={
                            "command": command,
                            "args": args,
                            "env": env,
                            "type": "local_command",
                            "source_config": config_name
                        }
                    )
                    
                    if result:
                        registered_servers.append(f"{config_name}-{server_name}")
                        logger.info(f"  ✓ 注册服务器: {config_name}-{server_name}")
                    else:
                        logger.error(f"  ✗ 注册服务器失败: {config_name}-{server_name}")
        else:
            logger.warning("没有有效的配置文件可以注册")
        
        # 2. 显示所有已注册的服务器
        logger.info("2. 显示所有已注册的服务器...")
        servers = master_service.call("list_servers")
        if servers:
            for server in servers:
                logger.info(f"  - {server['name']}: {server['url']}")
                if 'source_config' in server:
                    logger.info(f"    来源配置: {server['source_config']}")
            logger.info(f"总共注册了 {len(servers)} 个服务器")
        else:
            logger.info("没有服务器被注册")
        
        return registered_servers, len(servers)
        
    except Exception as e:
        logger.error(f"测试MCP服务注册功能时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return [], 0

def test_mcp_service_functionality():
    """
    测试MCP服务功能
    """
    logger.info("=== 测试MCP服务功能 ===")
    
    try:
        # 导入服务
        from functions.master_service import master_service
        
        # 1. 测试工具发现功能
        logger.info("1. 测试工具发现功能...")
        all_tools = master_service.get_all_tools_for_reranker()
        logger.info(f"发现 {len(all_tools)} 个工具用于reranker")
        if all_tools:
            for tool in all_tools[:5]:  # 只显示前5个工具
                logger.info(f"  - {tool['name']}: {tool['description'][:50]}...")
        else:
            logger.info("没有工具可发现")
        
        # 2. 测试工具执行功能
        logger.info("2. 测试工具执行功能...")
        test_tools = [
            {"name": "search_and_summarize", "params": {"query": "人工智能发展趋势"}},
            {"name": "fetch_news", "params": {"topic": "科技", "limit": 5}}
        ]
        
        for tool in test_tools:
            result = master_service.execute_mcp_tool(tool["name"], **tool["params"])
            if result["status"] == "success":
                logger.info(f"  ✓ 工具 {tool['name']} 执行成功")
            else:
                logger.error(f"  ✗ 工具 {tool['name']} 执行失败: {result.get('error', '未知错误')}")
        
        return len(all_tools)
        
    except Exception as e:
        logger.error(f"测试MCP服务功能时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 0

def cleanup_registered_servers(registered_servers):
    """
    清理注册的服务器
    """
    logger.info("=== 清理注册的服务器 ===")
    
    try:
        from functions.master_service import master_service
        
        logger.info("清理注册的服务器...")
        cleaned_count = 0
        for server_name in registered_servers:
            result = master_service.call("unregister_server", server_name=server_name)
            if result:
                logger.info(f"  ✓ 注销服务器: {server_name}")
                cleaned_count += 1
            else:
                logger.error(f"  ✗ 注销服务器失败: {server_name}")
        
        logger.info(f"成功清理 {cleaned_count}/{len(registered_servers)} 个服务器")
        return cleaned_count
        
    except Exception as e:
        logger.error(f"清理注册的服务器时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 0

def main():
    """
    主函数
    """
    logger.info("开始完整的MCP配置文件和服务器联通性测试")
    
    try:
        # 1. 测试MCP配置文件
        valid_configs, invalid_configs = test_mcp_config_files()
        
        # 2. 测试MCP服务注册
        registered_servers, total_servers = test_mcp_service_registration(valid_configs)
        
        # 3. 测试MCP服务功能
        tool_count = test_mcp_service_functionality()
        
        # 4. 清理注册的服务器
        cleaned_count = cleanup_registered_servers(registered_servers)
        
        # 5. 总结测试结果
        logger.info("=== 测试结果总结 ===")
        logger.info(f"有效配置文件: {len(valid_configs)}")
        logger.info(f"无效配置文件: {len(invalid_configs)}")
        logger.info(f"成功注册服务器: {len(registered_servers)}")
        logger.info(f"测试时总服务器数: {total_servers}")
        logger.info(f"发现工具数量: {tool_count}")
        logger.info(f"成功清理服务器: {cleaned_count}")
        
        if invalid_configs:
            logger.warning(f"无效配置文件列表: {', '.join(invalid_configs)}")
        
        # 判断测试是否成功
        overall_success = (
            len(invalid_configs) <= 2 and  # 允许最多2个无效配置文件（已知的空文件）
            len(registered_servers) > 0 and  # 至少成功注册一个服务器
            tool_count >= 0 and  # 工具发现功能正常
            cleaned_count == len(registered_servers)  # 所有服务器都成功清理
        )
        
        if overall_success:
            logger.info("✅ MCP配置文件和服务器联通性测试全部通过")
        else:
            logger.warning("⚠️  MCP配置文件和服务器联通性测试存在一些问题")
        
        return overall_success
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
