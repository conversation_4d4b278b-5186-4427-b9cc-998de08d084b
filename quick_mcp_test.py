#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速MCP配置文件和服务器联通性测试脚本
"""

import sys
import os
import json
import glob

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def quick_mcp_test():
    """
    快速测试MCP配置文件和服务器联通性
    """
    print("=== 快速MCP配置文件和服务器联通性测试 ===")
    
    # 1. 测试配置文件
    print("\n1. 测试MCP配置文件...")
    mcp_config_dir = os.path.join(os.path.dirname(__file__), 'backend', 'mcp_config')
    config_files = glob.glob(os.path.join(mcp_config_dir, "*.json"))
    
    print(f"找到 {len(config_files)} 个配置文件")
    
    valid_configs = []
    invalid_configs = []
    
    for config_file in config_files:
        config_name = os.path.splitext(os.path.basename(config_file))[0]
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    print(f"  ⚠️  {config_name}: 空文件")
                    invalid_configs.append(config_name)
                    continue
                
                config_data = json.loads(content)
                
                # 检查是否为MCP服务器配置
                if "mcpServers" in config_data:
                    valid_configs.append((config_name, config_data))
                    print(f"  ✅ {config_name}: 有效MCP服务器配置")
                else:
                    # 检查是否为多服务器配置
                    if "servers" in config_data:
                        print(f"  ℹ️  {config_name}: 多服务器配置文件")
                        invalid_configs.append(config_name)
                    else:
                        print(f"  ❌ {config_name}: 无效配置格式")
                        invalid_configs.append(config_name)
                        
        except json.JSONDecodeError as e:
            print(f"  ❌ {config_name}: JSON格式错误 - {e}")
            invalid_configs.append(config_name)
        except Exception as e:
            print(f"  ❌ {config_name}: 错误 - {e}")
            invalid_configs.append(config_name)
    
    # 2. 测试MCP服务
    print("\n2. 测试MCP服务...")
    try:
        from functions.master_service import master_service
        
        # 注册一个测试服务器
        result = master_service.call("register_server", 
                                   server_name="quick_test_server", 
                                   server_url="http://test:8000")
        print(f"服务器注册: {'✅ 成功' if result else '❌ 失败'}")
        
        # 列出服务器
        servers = master_service.call("list_servers")
        print(f"服务器列表: {len(servers)} 个服务器")
        
        # 注销测试服务器
        result = master_service.call("unregister_server", server_name="quick_test_server")
        print(f"服务器注销: {'✅ 成功' if result else '❌ 失败'}")
        
        # 测试工具执行
        result = master_service.execute_mcp_tool("search_and_summarize", query="AI发展趋势")
        print(f"工具执行: {'✅ 成功' if result['status'] == 'success' else '❌ 失败'}")
        
        service_test_passed = True
        
    except Exception as e:
        print(f"  ❌ MCP服务测试失败: {e}")
        service_test_passed = False
    
    # 3. 测试总结
    print("\n=== 测试总结 ===")
    print(f"有效配置文件: {len(valid_configs)}")
    print(f"无效配置文件: {len(invalid_configs)}")
    print(f"MCP服务测试: {'通过' if service_test_passed else '失败'}")
    
    if valid_configs:
        print("\n有效配置文件详情:")
        for config_name, config_data in valid_configs:
            mcp_servers = config_data.get("mcpServers", {})
            for server_name, server_config in mcp_servers.items():
                command = server_config.get("command", "unknown")
                args = server_config.get("args", [])
                print(f"  - {config_name}::{server_name}: {command} {' '.join(args)}")
    
    overall_success = (len(valid_configs) >= 5 and service_test_passed)
    print(f"\n总体测试结果: {'✅ 通过' if overall_success else '❌ 失败'}")
    
    return overall_success

if __name__ == "__main__":
    success = quick_mcp_test()
    sys.exit(0 if success else 1)
