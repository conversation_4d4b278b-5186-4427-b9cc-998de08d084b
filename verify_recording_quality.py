#!/usr/bin/env python3
"""
录音质量验证脚本
分析生成的录音文件质量
"""
import wave
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
from datetime import datetime

def analyze_audio_file(filepath):
    """分析音频文件"""
    print(f"🔍 分析音频文件: {filepath}")
    
    try:
        with wave.open(str(filepath), 'rb') as wav_file:
            # 获取基本信息
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            sample_width = wav_file.getsampwidth()
            duration = frames / sample_rate
            
            print(f"📊 基本信息:")
            print(f"   - 时长: {duration:.2f}秒")
            print(f"   - 采样率: {sample_rate} Hz")
            print(f"   - 声道数: {channels}")
            print(f"   - 位深: {sample_width * 8} bit")
            print(f"   - 总帧数: {frames}")
            
            # 读取音频数据
            audio_data = wav_file.readframes(frames)
            if sample_width == 2:
                audio_array = np.frombuffer(audio_data, dtype=np.int16)
            elif sample_width == 1:
                audio_array = np.frombuffer(audio_data, dtype=np.uint8)
            else:
                audio_array = np.frombuffer(audio_data, dtype=np.float32)
            
            # 音频质量分析
            max_amplitude = np.max(np.abs(audio_array))
            min_amplitude = np.min(audio_array)
            rms = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
            
            # 检测静音
            silence_threshold = max_amplitude * 0.01  # 1%的最大振幅作为静音阈值
            silence_samples = np.sum(np.abs(audio_array) < silence_threshold)
            silence_percentage = (silence_samples / len(audio_array)) * 100
            
            # 频率分析 (简单的FFT)
            fft = np.fft.fft(audio_array[:min(len(audio_array), 8192)])  # 取前8192个样本
            freqs = np.fft.fftfreq(len(fft), 1/sample_rate)
            magnitude = np.abs(fft)
            
            # 找到主要频率
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude[:len(magnitude)//2]
            dominant_freq_idx = np.argmax(positive_magnitude[1:]) + 1  # 跳过DC分量
            dominant_freq = positive_freqs[dominant_freq_idx]
            
            print(f"📈 质量分析:")
            print(f"   - 最大振幅: {max_amplitude}")
            print(f"   - 最小振幅: {min_amplitude}")
            print(f"   - RMS值: {rms:.2f}")
            print(f"   - 静音比例: {silence_percentage:.1f}%")
            print(f"   - 主要频率: {dominant_freq:.1f} Hz")
            
            # 质量评估
            quality_score = 0
            quality_issues = []
            
            # 检查振幅范围
            if max_amplitude > 1000:  # 对于16bit音频
                quality_score += 25
            else:
                quality_issues.append("振幅过低，可能录音音量不足")
            
            # 检查静音比例
            if silence_percentage < 50:
                quality_score += 25
            else:
                quality_issues.append("静音比例过高")
            
            # 检查频率范围
            if 100 <= dominant_freq <= 8000:  # 人声频率范围
                quality_score += 25
            else:
                quality_issues.append(f"主要频率异常: {dominant_freq:.1f} Hz")
            
            # 检查RMS值
            if rms > 100:
                quality_score += 25
            else:
                quality_issues.append("RMS值过低，信号强度不足")
            
            print(f"🎯 质量评分: {quality_score}/100")
            
            if quality_issues:
                print(f"⚠️ 发现的问题:")
                for issue in quality_issues:
                    print(f"   - {issue}")
            else:
                print("✅ 音频质量良好")
            
            return {
                'filepath': str(filepath),
                'duration': duration,
                'sample_rate': sample_rate,
                'channels': channels,
                'sample_width': sample_width,
                'frames': frames,
                'max_amplitude': int(max_amplitude),
                'min_amplitude': int(min_amplitude),
                'rms': float(rms),
                'silence_percentage': float(silence_percentage),
                'dominant_freq': float(dominant_freq),
                'quality_score': quality_score,
                'quality_issues': quality_issues,
                'analysis_time': datetime.now().isoformat()
            }
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def main():
    """主函数"""
    print("🎵 录音质量验证")
    print("=" * 40)
    
    # 查找录音文件
    recordings_dir = Path("test_recordings")
    if not recordings_dir.exists():
        print("❌ 录音目录不存在")
        return
    
    wav_files = list(recordings_dir.glob("*.wav"))
    if not wav_files:
        print("❌ 没有找到WAV文件")
        return
    
    print(f"📁 找到 {len(wav_files)} 个录音文件")
    
    results = []
    for wav_file in wav_files:
        print(f"\n{'='*50}")
        result = analyze_audio_file(wav_file)
        if result:
            results.append(result)
    
    # 保存分析结果
    if results:
        report_file = f"recording_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 质量分析报告已保存: {report_file}")
        
        # 总结
        avg_quality = sum(r['quality_score'] for r in results) / len(results)
        print(f"\n📊 总体质量评分: {avg_quality:.1f}/100")
        
        if avg_quality >= 80:
            print("✅ 录音质量优秀")
        elif avg_quality >= 60:
            print("⚠️ 录音质量一般")
        else:
            print("❌ 录音质量需要改进")

if __name__ == "__main__":
    main()