import urllib.request
import json

def test_endpoint(url):
    try:
        response = urllib.request.urlopen(url)
        data = response.read().decode('utf-8')
        print(f"Status code: {response.getcode()}")
        print(f"Response: {data}")
        return json.loads(data)
    except Exception as e:
        print(f"Error accessing {url}: {e}")
        return None

def test_backend_connectivity():
    print("Testing backend connectivity...")
    
    # Test the root endpoint
    print("\n1. Testing root endpoint:")
    test_endpoint("http://localhost:8000/")
    
    # Test the test endpoint
    print("\n2. Testing test endpoint:")
    test_endpoint("http://localhost:8000/test")
    
    # Test the health endpoint
    print("\n3. Testing health endpoint:")
    test_endpoint("http://localhost:8000/health")

if __name__ == "__main__":
    test_backend_connectivity()
