# 语音聊天应用启动指南

## 应用概述
本应用是一个实时语音聊天系统，包含后端服务和前端应用两部分：
- **后端**: 基于 FastAPI 的 Python 服务，提供 REST API 和 WebSocket 支持
- **前端**: 基于 Flutter 的跨平台移动应用，支持 Android、iOS 和 Web

## 项目结构
```
.
├── backend/                 # 后端服务
│   ├── main.py             # 主服务入口
│   ├── services/           # 业务逻辑服务
│   ├── models/             # 数据模型
│   ├── utils/              # 工具函数
│   └── ...
├── frontend/               # 前端应用
│   ├── lib/                # Dart 源代码
│   │   ├── main.dart       # 应用入口
│   │   ├── screens/        # 页面组件
│   │   ├── services/       # 业务逻辑
│   │   └── models/         # 数据模型
│   ├── test/               # 测试文件
│   └── ...
├── start_app.py            # 启动脚本
├── start_backend.py        # 后端启动脚本
└── start_frontend.py       # 前端启动说明脚本
```

## 启动步骤

### 方法一：使用启动脚本（推荐）

#### 1. 启动完整应用
```bash
python start_app.py
```

此脚本将：
- 自动启动后端服务（监听端口 8000）
- 显示前端应用的启动说明

#### 2. 仅启动后端服务
```bash
python start_backend.py
```

#### 3. 获取前端启动说明
```bash
python start_frontend.py
```

### 方法二：手动启动

#### 启动后端服务
```bash
cd backend
python main.py
```

后端服务将在 `http://localhost:8000` 启动

#### 启动前端应用
```bash
cd frontend
flutter pub get    # 安装依赖
flutter run        # 启动应用
```

## 服务端点

### 后端 API
- **基础URL**: `http://localhost:8000`
- **API文档**: `http://localhost:8000/docs`
- **健康检查**: `http://localhost:8000/health`
- **测试端点**: `http://localhost:8000/test`
- **WebSocket**: `ws://localhost:8000/ws/{user_id}`

### 主要API端点
- `GET /` - 根路径
- `GET /health` - 健康检查
- `GET /test` - 测试端点
- `GET /npcs` - 获取NPC列表
- `POST /auth/register` - 用户注册
- `POST /auth/login` - 用户登录
- `POST /process-audio` - 音频处理
- `GET /mcp/servers` - 获取MCP服务器列表
- `GET /mcp/tools` - 获取MCP工具列表
- `POST /mcp/tools/execute` - 执行MCP工具

## 配置文件

### 后端配置
- `.env` - 环境变量配置文件
- `supabase.md` - Supabase 配置说明

### 前端配置
- `lib/config/app_config.dart` - 应用配置
- `pubspec.yaml` - 依赖配置

## 测试

### 后端测试
```bash
cd backend
python -m pytest
```

### 前端测试
```bash
cd frontend
flutter test                    # 运行所有测试
flutter test --coverage         # 运行测试并生成覆盖率报告
./test/run_tests.sh             # 使用测试运行脚本
```

## 依赖要求

### 后端依赖
- Python 3.8+
- FastAPI
- Uvicorn
- Supabase
- 其他Python库（详见 backend/requirements.txt）

### 前端依赖
- Flutter SDK 3.0+
- Dart 2.17+
- 其他Flutter包（详见 frontend/pubspec.yaml）

## 故障排除

### 后端问题
1. **端口被占用**: 更改 `main.py` 中的端口号
2. **数据库连接失败**: 检查 `.env` 文件中的数据库配置
3. **服务启动失败**: 查看控制台错误信息

### 前端问题
1. **Flutter未安装**: 按照官方文档安装Flutter SDK
2. **依赖安装失败**: 检查网络连接或使用国内镜像源
3. **应用启动失败**: 查看设备连接状态或日志信息

## 开发工具

### API测试
- 使用 `curl` 命令测试API
- 使用 Postman 或 Insomnia 等图形化工具
- 查看 FastAPI 自动生成的交互式文档

### 调试
- 后端: 使用日志和断点调试
- 前端: 使用 Flutter DevTools
- 数据库: 使用 Supabase 控制台

## 部署

### 后端部署
1. 安装依赖: `pip install -r requirements.txt`
2. 配置环境变量
3. 启动服务: `uvicorn main:app --host 0.0.0.0 --port 8000`

### 前端部署
1. 构建Web版本: `flutter build web`
2. 构建移动版本: `flutter build apk` 或 `flutter build ios`
3. 部署到相应平台

## 维护

### 代码更新
1. 拉取最新代码: `git pull`
2. 更新后端依赖: `pip install -r requirements.txt`
3. 更新前端依赖: `flutter pub get`

### 数据库迁移
1. 检查数据库结构: `python check_table_structure.py`
2. 初始化数据库: `python init_db.py`
3. 修复数据库结构: `python fix_database_structure.py`

## 支持与反馈

如有问题，请联系开发团队或查看相关文档。
