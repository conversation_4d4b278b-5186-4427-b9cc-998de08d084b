#!/bin/bash

echo "🎯 Flutter前端启动脚本"
echo "=" * 50

# 检查Flutter是否安装
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter未安装，请先安装Flutter"
    echo "   安装指南: https://flutter.dev/docs/get-started/install"
    exit 1
fi

echo "✅ Flutter已安装"
flutter --version

# 进入前端目录
cd frontend

# 检查pubspec.yaml
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ pubspec.yaml不存在"
    exit 1
fi

echo "📦 安装Flutter依赖..."
flutter pub get

if [ $? -ne 0 ]; then
    echo "❌ Flutter依赖安装失败"
    exit 1
fi

echo "✅ Flutter依赖安装完成"

# 检查后端连接
echo "🌐 检查后端连接..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ 后端服务正在运行"
else
    echo "⚠️  后端服务未运行，请先启动后端服务"
    echo "   运行: python start_project.py"
fi

echo ""
echo "🚀 启动Flutter应用..."
echo "选择运行平台:"
echo "1) Web (Chrome)"
echo "2) Desktop (当前平台)"
echo "3) Android (需要连接设备或模拟器)"
echo "4) iOS (仅限macOS)"

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo "🌐 在Chrome中启动Web版本..."
        flutter run -d chrome
        ;;
    2)
        echo "🖥️  启动桌面版本..."
        flutter run -d desktop
        ;;
    3)
        echo "📱 启动Android版本..."
        flutter run -d android
        ;;
    4)
        if [[ "$OSTYPE" == "darwin"* ]]; then
            echo "📱 启动iOS版本..."
            flutter run -d ios
        else
            echo "❌ iOS只能在macOS上运行"
            exit 1
        fi
        ;;
    *)
        echo "❌ 无效选择，启动默认平台..."
        flutter run
        ;;
esac
