#!/usr/bin/env python3
import requests

# 测试POST请求
try:
    response = requests.post(
        "http://localhost:8000/auth/login",
        params={
            "username": "test_user",
            "password": "test_password"
        },
        timeout=5
    )
    print(f"POST Status: {response.status_code}")
    print(f"POST Response: {response.text}")
except Exception as e:
    print(f"POST Error: {e}")

# 测试GET请求
try:
    response = requests.get(
        "http://localhost:8000/auth/login",
        params={
            "username": "test_user", 
            "password": "test_password"
        },
        timeout=5
    )
    print(f"GET Status: {response.status_code}")
    print(f"GET Response: {response.text}")
except Exception as e:
    print(f"GET Error: {e}")