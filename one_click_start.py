#!/usr/bin/env python3
"""
一键启动语音对话系统
"""

import webbrowser
import subprocess
import time
import sys
import os

def main():
    print("🚀 一键启动语音对话系统")
    print("=" * 50)
    
    # 杀死可能存在的Web服务器进程
    os.system("pkill -f 'python.*http.server.*8002' 2>/dev/null")
    time.sleep(1)
    
    # 启动Web服务器
    print("🌐 启动Web服务器 (端口8002)...")
    subprocess.Popen([sys.executable, '-m', 'http.server', '8002'], 
                    stdout=subprocess.DEVNULL, 
                    stderr=subprocess.DEVNULL)
    
    time.sleep(2)
    
    # 打开原始测试页面
    url = "http://localhost:8002/real_voice_pipeline_test.html"
    print(f"🌐 打开完整测试页面: {url}")
    webbrowser.open(url)
    
    print("\n✅ 系统启动完成！")
    print("\n📋 使用说明:")
    print("1. 页面会自动测试TTS连接")
    print("2. 点击'测试ASR连接'测试语音识别")
    print("3. 点击'开始录音'进行语音对话")
    print("4. 或点击'测试真实服务'测试LLM和TTS")
    
    print("\n🔧 确保以下服务正在运行:")
    print("终端1: cd backend && python main.py  (ASR + LLM服务)")
    print("终端2: python start_real_voice_test.py  (TTS服务)")
    
    print("\n🌐 如果页面无法加载，请手动访问:")
    print(f"   {url}")
    
    print("\n🔍 备用简化版本:")
    print("   http://localhost:8002/simple_voice_test.html")

if __name__ == "__main__":
    main()