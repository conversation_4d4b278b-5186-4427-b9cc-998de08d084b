#!/usr/bin/env python3
"""
语音对话端到端测试脚本
测试完整的语音对话流水线：录音 -> ASR -> LLM -> TTS -> 播放
"""

import asyncio
import json
import requests
import websockets
import base64
import numpy as np
import wave
import tempfile
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加backend目录到路径
sys.path.append('backend')

class VoicePipelineTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.tts_url = "http://localhost:8001"
        self.websocket_url = "ws://localhost:8000/ws"
        self.test_results = []
        
    def log_test(self, test_name, success, details="", error=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": str(error) if error else None,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {details}")
        if error:
            print(f"   Error: {error}")
    
    def create_test_audio_with_speech(self, text="Hello AI assistant", duration=3.0):
        """创建包含语音内容的测试音频"""
        sample_rate = 16000
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # 创建更复杂的音频信号来模拟语音
        # 使用多个频率组合来模拟语音的复杂性
        frequencies = [200, 400, 800, 1600]  # 模拟语音的基频和谐波
        audio_data = np.zeros_like(t)
        
        for i, freq in enumerate(frequencies):
            # 添加一些变化来模拟语音的动态特性
            amplitude = 0.1 / (i + 1)  # 高频成分幅度递减
            phase_mod = np.sin(2 * np.pi * 2 * t)  # 频率调制
            audio_data += amplitude * np.sin(2 * np.pi * freq * t + 0.1 * phase_mod)
        
        # 添加一些噪声来模拟真实录音环境
        noise = np.random.normal(0, 0.01, len(audio_data))
        audio_data += noise
        
        # 应用包络来模拟语音的起伏
        envelope = np.exp(-((t - duration/2) / (duration/4))**2)  # 高斯包络
        audio_data *= envelope
        
        # 确保音频在合理范围内
        audio_data = np.clip(audio_data, -0.8, 0.8)
        
        # 转换为16位整数
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        # 创建WAV文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            with wave.open(temp_file.name, 'wb') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_int16.tobytes())
            
            return temp_file.name, len(audio_data)
    
    def test_services_availability(self):
        """测试所有服务的可用性"""
        services = [
            ("Backend Main", f"{self.backend_url}/health"),
            ("TTS Service", f"{self.tts_url}/health"),
            ("Backend Root", f"{self.backend_url}/"),
            ("TTS Root", f"{self.tts_url}/")
        ]
        
        all_available = True
        
        for service_name, url in services:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    self.log_test(f"Service - {service_name}", True, f"Available at {url}")
                else:
                    self.log_test(f"Service - {service_name}", False, f"HTTP {response.status_code}")
                    all_available = False
            except Exception as e:
                self.log_test(f"Service - {service_name}", False, error=e)
                all_available = False
        
        return all_available
    
    def test_asr_service(self):
        """测试ASR服务"""
        try:
            # 创建测试音频
            audio_file, audio_length = self.create_test_audio_with_speech("测试语音识别功能")
            
            try:
                # 测试ASR连接
                response = requests.get(f"{self.backend_url}/api/asr/test", timeout=10)
                if response.status_code == 200:
                    asr_status = response.json()
                    available_services = asr_status.get("asr_services", {})
                    
                    self.log_test(
                        "ASR Connection Test", 
                        True, 
                        f"Available services: {available_services}"
                    )
                else:
                    self.log_test("ASR Connection Test", False, f"HTTP {response.status_code}")
                
                # 测试ASR转录
                with open(audio_file, 'rb') as f:
                    files = {'file': ('test_speech.wav', f, 'audio/wav')}
                    response = requests.post(
                        f"{self.backend_url}/api/asr/transcribe",
                        files=files,
                        timeout=30
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    success = result.get("success", False)
                    transcription = result.get("transcription", "")
                    provider = result.get("provider", "unknown")
                    confidence = result.get("confidence", 0.0)
                    
                    self.log_test(
                        "ASR Transcription", 
                        success, 
                        f"Provider: {provider}, Confidence: {confidence:.2f}, Text: '{transcription}'"
                    )
                    
                    return success, transcription
                else:
                    self.log_test("ASR Transcription", False, f"HTTP {response.status_code}: {response.text}")
                    return False, ""
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(audio_file)
                except:
                    pass
            
        except Exception as e:
            self.log_test("ASR Service Test", False, error=e)
            return False, ""
    
    def test_llm_service(self):
        """测试LLM服务"""
        try:
            # 测试后端的LLM端点
            response = requests.get(f"{self.backend_url}/test", timeout=15)
            if response.status_code == 200:
                result = response.json()
                if "error" not in result:
                    question = result.get("question", "")
                    response_text = result.get("response", "")
                    npc_name = result.get("npc_name", "unknown")
                    
                    self.log_test(
                        "LLM Service Test", 
                        True, 
                        f"Q: '{question}' -> A: '{response_text}' (NPC: {npc_name})"
                    )
                    return True, response_text
                else:
                    self.log_test("LLM Service Test", False, result.get("error"))
                    return False, ""
            else:
                self.log_test("LLM Service Test", False, f"HTTP {response.status_code}")
                return False, ""
                
        except Exception as e:
            self.log_test("LLM Service Test", False, error=e)
            return False, ""
    
    def test_tts_service(self):
        """测试TTS服务"""
        try:
            # 测试TTS健康检查
            response = requests.get(f"{self.tts_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                services = health_data.get("services", {})
                
                self.log_test(
                    "TTS Health Check", 
                    True, 
                    f"TTS: {services.get('tts', 'unknown')}, LLM: {services.get('llm', 'unknown')}"
                )
            else:
                self.log_test("TTS Health Check", False, f"HTTP {response.status_code}")
            
            # 测试TTS合成
            test_text = "这是一个TTS语音合成测试。"
            tts_data = {
                "text": test_text,
                "emotion": "neutral",
                "speed": 1.0
            }
            
            response = requests.post(
                f"{self.tts_url}/api/tts/synthesize",
                json=tts_data,
                timeout=20
            )
            
            if response.status_code == 200:
                audio_data = response.content
                audio_size = len(audio_data)
                
                self.log_test(
                    "TTS Synthesis", 
                    True, 
                    f"Generated {audio_size} bytes of audio for text: '{test_text}'"
                )
                
                # 保存音频文件用于验证
                output_file = f"tts_test_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
                with open(output_file, 'wb') as f:
                    f.write(audio_data)
                
                print(f"   Audio saved to: {output_file}")
                return True, audio_size
            else:
                self.log_test("TTS Synthesis", False, f"HTTP {response.status_code}: {response.text}")
                return False, 0
                
        except Exception as e:
            self.log_test("TTS Service Test", False, error=e)
            return False, 0
    
    def test_complete_pipeline(self):
        """测试完整的语音处理流水线"""
        try:
            # 创建测试音频
            audio_file, audio_length = self.create_test_audio_with_speech("你好，请介绍一下人工智能")
            
            try:
                # 测试完整流水线
                with open(audio_file, 'rb') as f:
                    files = {'file': ('test_conversation.wav', f, 'audio/wav')}
                    data = {'user_id': 1, 'npc_id': 1}
                    response = requests.post(
                        f"{self.backend_url}/process-audio",
                        files=files,
                        data=data,
                        timeout=60
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    if "error" not in result:
                        transcription = result.get("transcription", "")
                        response_text = result.get("response_text", "")
                        asr_provider = result.get("asr_provider", "unknown")
                        asr_confidence = result.get("asr_confidence", 0.0)
                        emotion = result.get("emotion", "neutral")
                        speed = result.get("speed", 1.0)
                        audio_size = result.get("audio_size", 0)
                        
                        pipeline_details = (
                            f"ASR({asr_provider}, {asr_confidence:.2f}): '{transcription}' -> "
                            f"LLM: '{response_text[:50]}...' -> "
                            f"TTS({emotion}, {speed}x): {audio_size} bytes"
                        )
                        
                        self.log_test(
                            "Complete Voice Pipeline", 
                            True, 
                            pipeline_details
                        )
                        
                        return True, result
                    else:
                        self.log_test("Complete Voice Pipeline", False, result.get("error"))
                        return False, {}
                else:
                    self.log_test("Complete Voice Pipeline", False, f"HTTP {response.status_code}: {response.text}")
                    return False, {}
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(audio_file)
                except:
                    pass
            
        except Exception as e:
            self.log_test("Complete Voice Pipeline", False, error=e)
            return False, {}
    
    async def test_websocket_voice_flow(self):
        """测试WebSocket语音流"""
        try:
            user_id = "voice_test_user"
            uri = f"{self.websocket_url}/{user_id}"
            
            async with websockets.connect(uri) as websocket:
                self.log_test("WebSocket Voice Connection", True, f"Connected to {uri}")
                
                # 启动会话
                start_msg = {"type": "start_session", "npc_id": 1}
                await websocket.send(json.dumps(start_msg))
                
                # 等待会话启动确认
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    data = json.loads(response)
                    
                    if data.get("type") == "session_started":
                        session_id = data.get("session_id")
                        self.log_test("WebSocket Session Start", True, f"Session: {session_id}")
                        
                        # 模拟发送音频数据
                        for i in range(5):  # 发送5个音频块
                            # 创建模拟音频数据
                            audio_chunk = np.random.randint(0, 256, 1024, dtype=np.uint8)
                            audio_msg = {
                                "type": "audio_chunk",
                                "data": base64.b64encode(audio_chunk).decode()
                            }
                            
                            await websocket.send(json.dumps(audio_msg))
                            await asyncio.sleep(0.1)  # 模拟实时音频流
                        
                        self.log_test("WebSocket Audio Streaming", True, "Sent 5 audio chunks")
                        
                        # 等待可能的响应
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=5)
                            data = json.loads(response)
                            self.log_test("WebSocket Response", True, f"Received: {data.get('type', 'unknown')}")
                        except asyncio.TimeoutError:
                            self.log_test("WebSocket Response", True, "No immediate response (expected)")
                        
                        # 结束会话
                        end_msg = {"type": "end_session"}
                        await websocket.send(json.dumps(end_msg))
                        
                        return True
                    else:
                        self.log_test("WebSocket Session Start", False, f"Unexpected response: {data}")
                        return False
                        
                except asyncio.TimeoutError:
                    self.log_test("WebSocket Session Start", False, "Timeout waiting for session start")
                    return False
                
        except Exception as e:
            self.log_test("WebSocket Voice Flow", False, error=e)
            return False
    
    def generate_pipeline_report(self):
        """生成语音流水线测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        report = {
            "voice_pipeline_summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
            },
            "test_results": self.test_results,
            "generated_at": datetime.now().isoformat()
        }
        
        # 保存报告
        report_file = f"voice_pipeline_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n{'='*60}")
        print("🎤 VOICE PIPELINE TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {report['voice_pipeline_summary']['success_rate']}")
        print(f"Report saved to: {report_file}")
        
        return report

async def main():
    """主测试函数"""
    print("🎤 Starting Voice Pipeline End-to-End Test")
    print("="*60)
    
    tester = VoicePipelineTester()
    
    # 1. 服务可用性检查
    print("\n1. Services Availability Check")
    services_ok = tester.test_services_availability()
    
    if not services_ok:
        print("⚠️ Some services are not available. Continuing with available tests...")
    
    # 2. ASR服务测试
    print("\n2. ASR Service Test")
    asr_success, transcription = tester.test_asr_service()
    
    # 3. LLM服务测试
    print("\n3. LLM Service Test")
    llm_success, llm_response = tester.test_llm_service()
    
    # 4. TTS服务测试
    print("\n4. TTS Service Test")
    tts_success, audio_size = tester.test_tts_service()
    
    # 5. 完整流水线测试
    print("\n5. Complete Pipeline Test")
    pipeline_success, pipeline_result = tester.test_complete_pipeline()
    
    # 6. WebSocket语音流测试
    print("\n6. WebSocket Voice Flow Test")
    await tester.test_websocket_voice_flow()
    
    # 生成测试报告
    print("\n7. Generating Pipeline Test Report")
    report = tester.generate_pipeline_report()
    
    return report

if __name__ == "__main__":
    asyncio.run(main())
