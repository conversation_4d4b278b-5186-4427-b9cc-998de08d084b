import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../models/user_model.dart';
import '../config/app_config.dart';

class AuthService extends StateNotifier<AsyncValue<UserModel?>> {
  AuthService() : super(const AsyncValue.loading()) {
    _init();
  }

  static const _storage = FlutterSecureStorage();
  
  // Fallback to SharedPreferences if secure storage fails
  Future<void> _secureWrite(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Secure storage failed, using SharedPreferences: $e');
      }
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(key, value);
    }
  }
  
  Future<String?> _secureRead(String key) async {
    try {
      return await _storage.read(key: key);
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Secure storage failed, using SharedPreferences: $e');
      }
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(key);
    }
  }
  
  Future<void> _secureDelete(String key) async {
    try {
      await _storage.delete(key: key);
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Secure storage failed, using SharedPreferences: $e');
      }
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(key);
    }
  }
  UserModel? _currentUser;

  UserModel? get currentUser => _currentUser;

  Future<void> _init() async {
    try {
      final userData = await _secureRead('user_data');
      final token = await _secureRead('user_token');
      
      if (userData != null && token != null) {
        _currentUser = UserModel.fromJson(json.decode(userData));
        state = AsyncValue.data(_currentUser);
      } else {
        state = const AsyncValue.data(null);
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<UserModel?> register({
    required String username,
    required String password,
    String? email,
    String? nickname,
  }) async {
    try {
      state = const AsyncValue.loading();
      
      final queryParams = <String, String>{
        'username': username,
        'password': password,
      };
      if (email != null) queryParams['email'] = email;
      if (nickname != null) queryParams['nickname'] = nickname;
      
      final uri = Uri.parse('${AppConfig.backendUrl}/auth/register').replace(
        queryParameters: queryParams,
      );
      
      final response = await http.post(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final user = UserModel.fromJson(data['user']);
        final token = data['token'];
        
        // Store user data and token
        await _storage.write(key: 'user_token', value: token);
        await _storage.write(key: 'user_data', value: json.encode(user.toJson()));
        
        _currentUser = user;
        state = AsyncValue.data(user);
        return user;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? 'Registration failed');
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }

  Future<UserModel?> login({
    required String username,
    required String password,
  }) async {
    try {
      state = const AsyncValue.loading();
      
      final uri = Uri.parse('${AppConfig.backendUrl}/auth/login').replace(
        queryParameters: {
          'username': username,
          'password': password,
        },
      );
      
      if (kDebugMode) {
        print('🔍 Login request URL: $uri');
      }
      
      final response = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Login request timeout');
        },
      );

      if (kDebugMode) {
        print('🔍 Login response status: ${response.statusCode}');
        print('🔍 Login response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final user = UserModel.fromJson(data['user']);
        final token = data['token'];
        
        if (kDebugMode) {
          print('✅ Login successful: ${user.username}');
        }
        
        // Store user data and token
        await _storage.write(key: 'user_token', value: token);
        await _storage.write(key: 'user_data', value: json.encode(user.toJson()));
        
        _currentUser = user;
        state = AsyncValue.data(user);
        return user;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['detail'] ?? 'Login failed';
        if (kDebugMode) {
          print('❌ Login failed: $errorMessage');
        }
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Login error: $e');
      }
      state = AsyncValue.error(e, StackTrace.current);
      throw e; // 重新抛出异常，确保UI能捕获到
    }
  }

  Future<void> logout() async {
    try {
      await _storage.delete(key: 'user_token');
      await _storage.delete(key: 'user_data');
      
      _currentUser = null;
      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<String?> getToken() async {
    return await _storage.read(key: 'user_token');
  }

  Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && _currentUser != null;
  }

  Future<UserModel?> getCurrentUser() async {
    if (_currentUser != null) {
      return _currentUser;
    }
    
    final userData = await _storage.read(key: 'user_data');
    if (userData != null) {
      _currentUser = UserModel.fromJson(json.decode(userData));
      return _currentUser;
    }
    
    return null;
  }

  // Guest login for testing purposes
  Future<UserModel?> loginAsGuest() async {
    try {
      state = const AsyncValue.loading();
      
      final guestUser = UserModel(
        id: 999,
        uuid: 'guest_${DateTime.now().millisecondsSinceEpoch}',
        username: 'guest_${DateTime.now().millisecondsSinceEpoch}',
        nickname: 'Guest User',
        email: null,
        avatarUrl: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // Store guest data
      await _storage.write(key: 'user_token', value: 'guest_token');
      await _storage.write(key: 'user_data', value: json.encode(guestUser.toJson()));
      
      _currentUser = guestUser;
      state = AsyncValue.data(guestUser);
      return guestUser;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }
}

// Provider for AuthService
final authServiceProvider = StateNotifierProvider<AuthService, AsyncValue<UserModel?>>((ref) {
  return AuthService();
});

// Helper provider to get current user
final currentUserProvider = Provider<UserModel?>((ref) {
  final authState = ref.watch(authServiceProvider);
  return authState.when(
    data: (user) => user,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Helper provider to check if user is logged in
final isLoggedInProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user != null;
});