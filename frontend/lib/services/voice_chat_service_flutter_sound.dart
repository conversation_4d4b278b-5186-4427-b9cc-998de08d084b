import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';
import '../config/app_config.dart';
import '../models/user_model.dart';

enum VoiceChatState {
  idle,
  connecting,
  connected,
  recording,
  processing,
  playing,
  error,
}

class VoiceChatMessage {
  final String type;
  final dynamic data;
  final DateTime timestamp;

  VoiceChatMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory VoiceChatMessage.fromJson(Map<String, dynamic> json) {
    return VoiceChatMessage(
      type: json['type']?.toString() ?? 'unknown',
      data: json['data'] ?? json,
      timestamp: DateTime.now(),
    );
  }
}

final voiceChatServiceProvider = StateNotifierProvider<VoiceChatService, VoiceChatState>((ref) {
  return VoiceChatService();
});

class VoiceChatService extends StateNotifier<VoiceChatState> {
  VoiceChatService() : super(VoiceChatState.idle) {
    _initAudio();
  }

  WebSocketChannel? _channel;
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  
  Timer? _recordingTimer;
  String? _currentSessionId;
  int? _currentNpcId;
  String? _tempAudioPath;
  StreamSubscription? _recordingSubscription;

  final StreamController<VoiceChatMessage> _messageController = StreamController.broadcast();
  final StreamController<String> _transcriptionController = StreamController.broadcast();
  final StreamController<Uint8List> _audioChunkController = StreamController.broadcast();

  Stream<VoiceChatMessage> get messageStream => _messageController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;

  bool get isConnected => _channel != null;
  bool get isRecording => state == VoiceChatState.recording;
  bool get isPlaying => state == VoiceChatState.playing;
  String? get currentSessionId => _currentSessionId;

  Future<void> _initAudio() async {
    try {
      print('🎵 Initializing Flutter Sound for macOS...');
      
      _recorder = FlutterSoundRecorder();
      _player = FlutterSoundPlayer();
      
      // 预先打开录音器和播放器，避免后续重复打开
      try {
        await _recorder!.openRecorder();
        print('✅ Recorder pre-opened successfully');
      } catch (e) {
        print('⚠️ Recorder pre-open failed: $e');
        // 不设置为null，稍后再试
      }
      
      try {
        await _player!.openPlayer();
        print('✅ Player pre-opened successfully');
      } catch (e) {
        print('⚠️ Player pre-open failed: $e');
        // 不设置为null，稍后再试
      }
      
      print('✅ Flutter Sound initialization completed');
    } catch (e) {
      print('❌ Error initializing Flutter Sound: $e');
    }
  }

  Future<bool> requestPermissions() async {
    try {
      print('🎤 Flutter Sound will handle permissions automatically');
      return true; // Flutter Sound handles permissions internally on macOS
    } catch (e) {
      print('⚠️ Permission check failed: $e');
      return false;
    }
  }

  Future<void> connect(UserModel user) async {
    if (state == VoiceChatState.connecting || isConnected) return;

    state = VoiceChatState.connecting;

    try {
      final uri = Uri.parse('${AppConfig.websocketUrl}/${user.id}');
      print('🔌 Connecting to WebSocket: $uri');
      
      _channel = WebSocketChannel.connect(uri);

      _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );

      state = VoiceChatState.connected;
      _addMessage('system', 'Connected to voice chat service');
      print('✅ WebSocket connected successfully');

    } catch (e) {
      print('❌ WebSocket connection failed: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to connect: $e');
    }
  }

  Future<void> startSession(int npcId) async {
    if (!isConnected) {
      print('❌ Cannot start session - not connected');
      return;
    }

    _currentNpcId = npcId;

    final message = {
      'type': 'start_session',
      'npc_id': npcId,
    };

    _channel!.sink.add(json.encode(message));
    _addMessage('system', 'Starting conversation session...');
    print('📤 Session start message sent for NPC: $npcId');
  }

  Future<void> startRecording() async {
    print('🎤 startRecording() called - current state: $state');

    if (state == VoiceChatState.error) {
      print('🔄 Resetting from error state to idle');
      state = VoiceChatState.idle;
    }

    if (state != VoiceChatState.connected && state != VoiceChatState.idle) {
      print('❌ Cannot start recording - invalid state: $state');
      return;
    }

    try {
      // 1. 首先请求权限
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        print('❌ Microphone permission denied');
        state = VoiceChatState.error;
        _addMessage('error', 'Microphone permission required');
        return;
      }

      // 2. 确保录音器已创建
      if (_recorder == null) {
        _recorder = FlutterSoundRecorder();
        print('🎤 Created new recorder instance');
      }

      // 3. 打开录音器（如果还没有打开）
      try {
        await _recorder!.openRecorder();
        print('✅ Recorder opened successfully');
      } catch (e) {
        print('⚠️ Recorder may already be open: $e');
      }

      // 4. 检查录音器是否正在录音
      if (_recorder!.isRecording) {
        print('❌ Recorder is already recording');
        state = VoiceChatState.error;
        _addMessage('error', 'Recorder is already recording');
        return;
      }

      // 5. 获取临时文件路径
      final tempDir = await getTemporaryDirectory();
      _tempAudioPath = '${tempDir.path}/recording_${DateTime.now().millisecondsSinceEpoch}.wav';

      print('🎤 Starting Flutter Sound recording to: $_tempAudioPath');
      print('🎤 Recorder state - isRecording: ${_recorder!.isRecording}');
      
      // 6. 开始录音到文件（必须 await 完成）
      await _recorder!.startRecorder(
        toFile: _tempAudioPath,
        codec: Codec.pcm16WAV,
        sampleRate: 16000,
        numChannels: 1,
      );
      
      // 7. 确认录音已开始
      if (!_recorder!.isRecording) {
        print('❌ Recording failed to start');
        state = VoiceChatState.error;
        _addMessage('error', 'Recording failed to start');
        return;
      }
      
      state = VoiceChatState.recording;
      _addMessage('system', 'Real recording started with Flutter Sound');
      print('✅ Flutter Sound recording started successfully');

      // 8. 开始实时音频流处理
      _startRealTimeAudioStreaming();

    } catch (e) {
      print('❌ Error in startRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to start recording: $e');
      
      // 不要重置录音器，保持其状态
      print('🔄 Keeping recorder instance for next attempt');
    }
  }

  void _startRealTimeAudioStreaming() {
    print('🎤 Starting real-time audio streaming with Flutter Sound...');
    _recordingTimer?.cancel();

    // 每200ms检查一次录音文件并发送数据
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 200), (timer) async {
      if (state != VoiceChatState.recording || !isConnected) {
        timer.cancel();
        print('🛑 Real-time streaming stopped - state: $state, connected: $isConnected');
        return;
      }

      // 读取当前录音文件的最新数据
      try {
        if (_tempAudioPath != null && File(_tempAudioPath!).existsSync()) {
          final audioFile = File(_tempAudioPath!);
          final audioBytes = await audioFile.readAsBytes();
          
          if (audioBytes.length > 44) { // WAV文件头是44字节
            // 只发送音频数据部分（跳过WAV头）
            final audioData = audioBytes.sublist(44);
            
            // 发送最新的音频块（避免重复发送）
            final chunkSize = 6400; // 200ms at 16kHz, 16-bit = 6400 bytes
            if (audioData.length >= chunkSize) {
              final startIndex = audioData.length - chunkSize;
              final audioChunk = audioData.sublist(startIndex);
              
              print('🎵 Sending real audio chunk: ${audioChunk.length} bytes');
              _sendRealAudioData(audioChunk);
            }
          }
        }
      } catch (e) {
        print('⚠️ Error reading audio file during streaming: $e');
      }
    });

    print('✅ Real-time audio streaming started');
  }

  void _sendRealAudioData(Uint8List audioData) {
    if (!isConnected) {
      print('❌ Cannot send audio data - WebSocket not connected');
      return;
    }

    try {
      final base64Audio = base64.encode(audioData);
      final message = {
        'type': 'audio_chunk',
        'data': base64Audio,
        'format': 'pcm16',
        'sample_rate': 16000,
        'channels': 1,
        'source': 'flutter_sound_real',
      };

      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);

      print('✅ Real audio chunk sent: ${audioData.length} bytes');
    } catch (e) {
      print('❌ Error sending audio data: $e');
    }
  }

  Future<void> stopRecording() async {
    print('🛑 stopRecording() called - current state: $state');

    if (state != VoiceChatState.recording) {
      print('❌ Cannot stop recording - not in recording state: $state');
      return;
    }

    try {
      // 1. 停止实时流
      _recordingTimer?.cancel();
      _recordingTimer = null;

      // 2. 检查录音器状态并停止录音
      if (_recorder != null && _recorder!.isRecording) {
        print('🛑 Stopping Flutter Sound recorder...');
        final filePath = await _recorder!.stopRecorder();
        print('✅ Flutter Sound recording stopped, file: $filePath');
        
        // 3. 发送完整的录音文件
        if (_tempAudioPath != null && File(_tempAudioPath!).existsSync()) {
          final audioFile = File(_tempAudioPath!);
          final audioBytes = await audioFile.readAsBytes();
          
          if (audioBytes.length > 44) {
            // 跳过WAV文件头，只发送音频数据
            final audioData = audioBytes.sublist(44);
            print('📤 Sending complete recording: ${audioData.length} bytes');
            
            final base64Audio = base64.encode(audioData);
            final message = {
              'type': 'audio_complete',
              'data': base64Audio,
              'format': 'pcm16',
              'sample_rate': 16000,
              'channels': 1,
              'source': 'flutter_sound_real',
            };

            if (isConnected) {
              _channel!.sink.add(json.encode(message));
            }
          }
          
          // 4. 清理临时文件
          try {
            await audioFile.delete();
            print('🗑️ Temporary audio file deleted');
          } catch (e) {
            print('⚠️ Failed to delete temporary file: $e');
          }
        }
      } else {
        print('⚠️ Recorder is null or not recording - state: ${_recorder?.isRecording}');
      }

      state = VoiceChatState.processing;
      _addMessage('system', 'Processing real audio...');
      print('🔄 State set to processing');

    } catch (e) {
      print('❌ Error in stopRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to stop recording: $e');
    }
  }

  void interrupt() {
    if (!isConnected) return;

    final message = {'type': 'interrupt'};
    _channel!.sink.add(json.encode(message));

    state = VoiceChatState.connected;
    _addMessage('system', 'Assistant interrupted');
    print('⏸️ Interrupt signal sent');
  }

  Future<void> endSession() async {
    if (!isConnected || _currentSessionId == null) return;

    final message = {'type': 'end_session'};
    _channel!.sink.add(json.encode(message));

    _currentSessionId = null;
    _currentNpcId = null;

    _addMessage('system', 'Session ended');
    print('🔚 Session ended');
  }

  void _handleWebSocketMessage(dynamic message) {
    try {
      final data = message is String ? json.decode(message) : message;
      final Map<String, dynamic> messageData;
      
      if (data is Map<String, dynamic>) {
        messageData = data;
      } else if (data is Map) {
        messageData = Map<String, dynamic>.from(data);
      } else {
        messageData = {
          'type': 'unknown',
          'data': data?.toString() ?? 'Unknown data',
        };
      }

      final chatMessage = VoiceChatMessage.fromJson(messageData);
      print('📥 WebSocket message received: ${chatMessage.type}');

      switch (chatMessage.type) {
        case 'connected':
          print('✅ WebSocket connection confirmed');
          break;
          
        case 'session_started':
          _currentSessionId = messageData['session_id']?.toString();
          _addMessage('system', 'Session started successfully');
          print('✅ Session started: $_currentSessionId');
          break;

        case 'transcription':
          final text = messageData['text'];
          final confidence = messageData['confidence'] ?? 1.0;
          _transcriptionController.add('$text (${(confidence * 100).toInt()}%)');
          _addMessage('transcription', text);
          print('📝 Transcription: $text');
          break;

        case 'audio_chunk':
          _handleAudioChunk(messageData['data']);
          break;

        case 'response_complete':
          state = VoiceChatState.connected;
          _addMessage('system', 'Response complete');
          print('✅ Response complete');
          break;

        case 'interrupted':
          state = VoiceChatState.connected;
          _addMessage('system', 'Assistant was interrupted');
          break;

        case 'session_ended':
          _currentSessionId = null;
          _addMessage('system', 'Session ended');
          break;

        case 'error':
          state = VoiceChatState.error;
          final errorMsg = messageData['message'] ?? 'Unknown error';
          _addMessage('error', errorMsg);
          print('❌ Server error: $errorMsg');
          break;

        case 'heartbeat':
          print('💓 Heartbeat received');
          break;

        default:
          _addMessage('unknown', messageData);
          print('❓ Unknown message type: ${chatMessage.type}');
      }

      _messageController.add(chatMessage);

    } catch (e) {
      print('❌ Error handling WebSocket message: $e');
    }
  }

  void _handleAudioChunk(String base64Audio) {
    try {
      final audioData = base64.decode(base64Audio);
      _audioChunkController.add(audioData);
      print('🎵 Audio chunk received: ${audioData.length} bytes');

      // 播放音频
      _playRealAudio(audioData);

    } catch (e) {
      print('❌ Error handling audio chunk: $e');
    }
  }

  Future<void> _playRealAudio(Uint8List audioData) async {
    try {
      state = VoiceChatState.playing;
      print('🔊 Playing real audio: ${audioData.length} bytes');
      
      if (_player != null) {
        // 确保播放器已打开
        if (!_player!.isOpen) {
          await _player!.openPlayer();
          print('✅ Player opened for playback');
        }
        
        // 保存音频数据到临时文件
        final tempDir = await getTemporaryDirectory();
        final tempAudioFile = File('${tempDir.path}/playback_${DateTime.now().millisecondsSinceEpoch}.wav');
        
        // 创建WAV文件头
        final wavHeader = _createWavHeader(audioData.length);
        final wavData = Uint8List.fromList([...wavHeader, ...audioData]);
        await tempAudioFile.writeAsBytes(wavData);
        
        // 播放音频文件
        await _player!.startPlayer(
          fromURI: tempAudioFile.path,
          codec: Codec.pcm16WAV,
        );
        
        // 监听播放完成
        _player!.onProgress!.listen((event) {
          if (event.position >= event.duration) {
            if (state == VoiceChatState.playing) {
              state = VoiceChatState.connected;
            }
            // 清理临时文件
            tempAudioFile.delete().catchError((e) {
              print('⚠️ Failed to delete playback temp file: $e');
            });
            print('✅ Real audio playback completed');
          }
        });
        
        print('🎵 Real audio playback started');
      } else {
        // 如果播放器不可用，模拟播放时间
        final playbackDuration = Duration(milliseconds: (audioData.length / 32).round());
        await Future.delayed(playbackDuration);
        
        if (state == VoiceChatState.playing) {
          state = VoiceChatState.connected;
        }
        
        print('✅ Audio playback simulation completed');
      }

    } catch (e) {
      print('❌ Error playing real audio: $e');
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
    }
  }

  List<int> _createWavHeader(int dataLength) {
    // 创建WAV文件头
    final header = <int>[];
    
    // RIFF header
    header.addAll('RIFF'.codeUnits);
    header.addAll(_intToBytes(36 + dataLength, 4)); // File size - 8
    header.addAll('WAVE'.codeUnits);
    
    // fmt chunk
    header.addAll('fmt '.codeUnits);
    header.addAll(_intToBytes(16, 4)); // fmt chunk size
    header.addAll(_intToBytes(1, 2));  // PCM format
    header.addAll(_intToBytes(1, 2));  // mono
    header.addAll(_intToBytes(16000, 4)); // sample rate
    header.addAll(_intToBytes(32000, 4)); // byte rate
    header.addAll(_intToBytes(2, 2));  // block align
    header.addAll(_intToBytes(16, 2)); // bits per sample
    
    // data chunk
    header.addAll('data'.codeUnits);
    header.addAll(_intToBytes(dataLength, 4));
    
    return header;
  }

  List<int> _intToBytes(int value, int bytes) {
    final result = <int>[];
    for (int i = 0; i < bytes; i++) {
      result.add((value >> (i * 8)) & 0xFF);
    }
    return result;
  }

  void _handleWebSocketError(error) {
    print('❌ WebSocket error: $error');
    state = VoiceChatState.error;
    _addMessage('error', 'WebSocket error: $error');
  }

  void _handleWebSocketClosed() {
    print('🔌 WebSocket connection closed');
    state = VoiceChatState.idle;
    _addMessage('system', 'Connection closed');
    _cleanup();
  }

  void _addMessage(String type, dynamic data) {
    final message = VoiceChatMessage(
      type: type,
      data: data,
      timestamp: DateTime.now(),
    );
    _messageController.add(message);
  }

  void _cleanup() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
    _recordingSubscription?.cancel();
    _recordingSubscription = null;
    
    // 清理临时文件
    if (_tempAudioPath != null) {
      try {
        File(_tempAudioPath!).delete();
      } catch (e) {
        print('⚠️ Failed to cleanup temp audio file: $e');
      }
      _tempAudioPath = null;
    }
    
    _currentSessionId = null;
    _currentNpcId = null;
    print('🧹 Cleanup completed');
  }

  void disconnect() {
    print('🔌 Disconnecting WebSocket...');
    _channel?.sink.close();
    _channel = null;
    _cleanup();
    state = VoiceChatState.idle;
  }

  @override
  void dispose() {
    print('🗑️ Disposing VoiceChatService...');
    disconnect();
    _messageController.close();
    _transcriptionController.close();
    _audioChunkController.close();
    
    // 安全关闭Flutter Sound
    _disposeAudio();
    
    super.dispose();
  }

  Future<void> _disposeAudio() async {
    try {
      // 停止录音（如果正在录音）
      if (_recorder != null && _recorder!.isRecording) {
        await _recorder!.stopRecorder();
        print('🛑 Recording stopped during dispose');
      }
      
      // 关闭录音器
      if (_recorder != null && _recorder!.isOpen) {
        await _recorder!.closeRecorder();
        print('🔒 Recorder closed');
      }
      
      // 关闭播放器
      if (_player != null && _player!.isOpen) {
        await _player!.closePlayer();
        print('🔒 Player closed');
      }
      
    } catch (e) {
      print('⚠️ Error disposing audio: $e');
    }
  }
}