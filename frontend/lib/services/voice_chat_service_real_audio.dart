import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';  // 使用record插件替代flutter_sound
import 'package:audioplayers/audioplayers.dart';  // 用于音频播放
import '../config/app_config.dart';
import '../models/user_model.dart';

enum VoiceChatState {
  idle,
  connecting,
  connected,
  recording,
  processing,
  playing,
  error,
}

class VoiceChatMessage {
  final String type;
  final dynamic data;
  final DateTime timestamp;

  VoiceChatMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory VoiceChatMessage.fromJson(Map<String, dynamic> json) {
    return VoiceChatMessage(
      type: json['type']?.toString() ?? 'unknown',
      data: json['data'] ?? json,
      timestamp: DateTime.now(),
    );
  }
}

final voiceChatServiceProvider = StateNotifierProvider<VoiceChatService, VoiceChatState>((ref) {
  return VoiceChatService();
});

class VoiceChatService extends StateNotifier<VoiceChatState> {
  VoiceChatService() : super(VoiceChatState.idle) {
    _initAudio();
  }

  WebSocketChannel? _channel;
  AudioRecorder? _recorder;  // 使用record插件
  AudioPlayer? _player;      // 使用audioplayers插件
  
  Timer? _recordingTimer;
  String? _currentSessionId;
  int? _currentNpcId;
  List<int> _audioBuffer = [];

  final StreamController<VoiceChatMessage> _messageController = StreamController.broadcast();
  final StreamController<String> _transcriptionController = StreamController.broadcast();
  final StreamController<Uint8List> _audioChunkController = StreamController.broadcast();

  Stream<VoiceChatMessage> get messageStream => _messageController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;

  bool get isConnected => _channel != null;
  bool get isRecording => state == VoiceChatState.recording;
  bool get isPlaying => state == VoiceChatState.playing;
  String? get currentSessionId => _currentSessionId;

  Future<void> _initAudio() async {
    try {
      print('🎵 Initializing real audio services for macOS...');
      
      // 初始化record插件（更好的macOS支持）
      _recorder = AudioRecorder();
      _player = AudioPlayer();
      
      // 检查录音权限
      final hasPermission = await _recorder!.hasPermission();
      print('🎤 Recording permission status: $hasPermission');
      
      print('✅ Real audio services initialized successfully');
    } catch (e) {
      print('❌ Error initializing audio services: $e');
      _recorder = null;
      _player = null;
    }
  }

  Future<bool> requestPermissions() async {
    try {
      // 使用record插件请求权限
      if (_recorder != null) {
        final hasPermission = await _recorder!.hasPermission();
        if (!hasPermission) {
          print('🎤 Requesting microphone permission...');
          // record插件会自动处理权限请求
          return await _recorder!.hasPermission();
        }
        return hasPermission;
      }
      
      // 备用权限请求
      final microphoneStatus = await Permission.microphone.request();
      return microphoneStatus == PermissionStatus.granted;
    } catch (e) {
      print('⚠️ Permission request failed: $e');
      return false;
    }
  }

  Future<void> connect(UserModel user) async {
    if (state == VoiceChatState.connecting || isConnected) return;

    state = VoiceChatState.connecting;

    try {
      final uri = Uri.parse('${AppConfig.websocketUrl}/${user.id}');
      print('🔌 Connecting to WebSocket: $uri');
      
      _channel = WebSocketChannel.connect(uri);

      // Listen to WebSocket messages
      _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );

      state = VoiceChatState.connected;
      _addMessage('system', 'Connected to voice chat service');
      print('✅ WebSocket connected successfully');

    } catch (e) {
      print('❌ WebSocket connection failed: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to connect: $e');
    }
  }

  Future<void> startSession(int npcId) async {
    if (!isConnected) {
      print('❌ Cannot start session - not connected');
      return;
    }

    _currentNpcId = npcId;

    final message = {
      'type': 'start_session',
      'npc_id': npcId,
    };

    _channel!.sink.add(json.encode(message));
    _addMessage('system', 'Starting conversation session...');
    print('📤 Session start message sent for NPC: $npcId');
  }

  Future<void> startRecording() async {
    print('🎤 startRecording() called - current state: $state');

    if (state == VoiceChatState.error) {
      print('🔄 Resetting from error state to idle');
      state = VoiceChatState.idle;
    }

    if (state != VoiceChatState.connected && state != VoiceChatState.idle) {
      print('❌ Cannot start recording - invalid state: $state');
      return;
    }

    try {
      // 请求麦克风权限
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        print('❌ Microphone permission denied');
        _addMessage('error', 'Microphone permission required');
        return;
      }

      // 检查录音器是否可用
      if (_recorder == null) {
        print('❌ Audio recorder not available');
        _addMessage('error', 'Audio recorder not available');
        return;
      }

      // 开始真实录音
      print('🎤 Starting real audio recording...');
      
      // 配置录音参数
      const config = RecordConfig(
        encoder: AudioEncoder.pcm16bits,  // 16位PCM
        sampleRate: 16000,                // 16kHz采样率
        numChannels: 1,                   // 单声道
        autoGain: true,                   // 自动增益
        echoCancel: true,                 // 回声消除
        noiseSuppress: true,              // 噪声抑制
      );

      // 开始录音到流
      final stream = await _recorder!.startStream(config);
      
      state = VoiceChatState.recording;
      _addMessage('system', 'Real recording started');
      print('✅ Real recording started successfully');

      // 监听音频流
      stream.listen(
        (audioData) {
          _handleRealAudioData(audioData);
        },
        onError: (error) {
          print('❌ Audio stream error: $error');
          state = VoiceChatState.error;
          _addMessage('error', 'Audio stream error: $error');
        },
        onDone: () {
          print('🔚 Audio stream ended');
        },
      );

    } catch (e) {
      print('❌ Error in startRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to start recording: $e');
    }
  }

  void _handleRealAudioData(Uint8List audioData) {
    try {
      print('🎵 Received real audio data: ${audioData.length} bytes');
      
      // 添加到缓冲区
      _audioBuffer.addAll(audioData);
      
      // 当缓冲区达到一定大小时发送（约100ms的音频）
      const int targetChunkSize = 3200; // 16kHz * 0.1s * 2 bytes = 3200 bytes
      
      if (_audioBuffer.length >= targetChunkSize) {
        // 提取音频块
        final chunk = Uint8List.fromList(_audioBuffer.take(targetChunkSize).toList());
        _audioBuffer.removeRange(0, targetChunkSize);
        
        // 发送真实音频数据
        _sendRealAudioChunk(chunk);
      }
    } catch (e) {
      print('❌ Error handling real audio data: $e');
    }
  }

  void _sendRealAudioChunk(Uint8List audioData) {
    if (!isConnected) {
      print('❌ Cannot send audio chunk - WebSocket not connected');
      return;
    }

    try {
      final base64Audio = base64.encode(audioData);
      final message = {
        'type': 'audio_chunk',
        'data': base64Audio,
        'format': 'pcm16',
        'sample_rate': 16000,
        'channels': 1,
        'source': 'real_microphone',  // 标记这是真实麦克风数据
      };

      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);

      print('✅ Real audio chunk sent: ${audioData.length} bytes');
    } catch (e) {
      print('❌ Error sending real audio chunk: $e');
    }
  }

  Future<void> stopRecording() async {
    print('🛑 stopRecording() called - current state: $state');

    if (state != VoiceChatState.recording) {
      print('❌ Cannot stop recording - not in recording state: $state');
      return;
    }

    try {
      // 停止真实录音
      if (_recorder != null) {
        await _recorder!.stop();
        print('✅ Real recording stopped');
      }

      // 发送剩余的音频数据
      if (_audioBuffer.isNotEmpty) {
        final remainingData = Uint8List.fromList(_audioBuffer);
        _audioBuffer.clear();
        _sendRealAudioChunk(remainingData);
        print('📤 Sent remaining audio data: ${remainingData.length} bytes');
      }

      state = VoiceChatState.processing;
      _addMessage('system', 'Processing real audio...');
      print('🔄 State set to processing');

    } catch (e) {
      print('❌ Error in stopRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to stop recording: $e');
    }
  }

  void interrupt() {
    if (!isConnected) return;

    final message = {'type': 'interrupt'};
    _channel!.sink.add(json.encode(message));

    state = VoiceChatState.connected;
    _addMessage('system', 'Assistant interrupted');
    print('⏸️ Interrupt signal sent');
  }

  Future<void> endSession() async {
    if (!isConnected || _currentSessionId == null) return;

    final message = {'type': 'end_session'};
    _channel!.sink.add(json.encode(message));

    _currentSessionId = null;
    _currentNpcId = null;

    _addMessage('system', 'Session ended');
    print('🔚 Session ended');
  }

  void _handleWebSocketMessage(dynamic message) {
    try {
      final data = message is String ? json.decode(message) : message;
      final Map<String, dynamic> messageData;
      
      if (data is Map<String, dynamic>) {
        messageData = data;
      } else if (data is Map) {
        messageData = Map<String, dynamic>.from(data);
      } else {
        messageData = {
          'type': 'unknown',
          'data': data?.toString() ?? 'Unknown data',
        };
      }

      final chatMessage = VoiceChatMessage.fromJson(messageData);
      print('📥 WebSocket message received: ${chatMessage.type}');

      switch (chatMessage.type) {
        case 'connected':
          print('✅ WebSocket connection confirmed');
          break;
          
        case 'session_started':
          _currentSessionId = messageData['session_id']?.toString();
          _addMessage('system', 'Session started successfully');
          print('✅ Session started: $_currentSessionId');
          break;

        case 'transcription':
          final text = messageData['text'];
          final confidence = messageData['confidence'] ?? 1.0;
          _transcriptionController.add('$text (${(confidence * 100).toInt()}%)');
          _addMessage('transcription', text);
          print('📝 Transcription: $text');
          break;

        case 'audio_chunk':
          _handleAudioChunk(messageData['data']);
          break;

        case 'response_complete':
          state = VoiceChatState.connected;
          _addMessage('system', 'Response complete');
          print('✅ Response complete');
          break;

        case 'interrupted':
          state = VoiceChatState.connected;
          _addMessage('system', 'Assistant was interrupted');
          break;

        case 'session_ended':
          _currentSessionId = null;
          _addMessage('system', 'Session ended');
          break;

        case 'error':
          state = VoiceChatState.error;
          final errorMsg = messageData['message'] ?? 'Unknown error';
          _addMessage('error', errorMsg);
          print('❌ Server error: $errorMsg');
          break;

        case 'heartbeat':
          print('💓 Heartbeat received');
          break;

        default:
          _addMessage('unknown', messageData);
          print('❓ Unknown message type: ${chatMessage.type}');
      }

      _messageController.add(chatMessage);

    } catch (e) {
      print('❌ Error handling WebSocket message: $e');
    }
  }

  void _handleAudioChunk(String base64Audio) {
    try {
      final audioData = base64.decode(base64Audio);
      _audioChunkController.add(audioData);
      print('🎵 Audio chunk received: ${audioData.length} bytes');

      // 播放音频
      _playAudioChunk(audioData);

    } catch (e) {
      print('❌ Error handling audio chunk: $e');
    }
  }

  Future<void> _playAudioChunk(Uint8List audioData) async {
    try {
      state = VoiceChatState.playing;
      print('🔊 Playing audio chunk: ${audioData.length} bytes');
      
      if (_player != null) {
        // 使用audioplayers播放音频
        // 注意：这里需要将PCM数据转换为可播放的格式
        // 或者保存为临时文件后播放
        
        // 简化实现：模拟播放时间
        await Future.delayed(Duration(milliseconds: (audioData.length / 32).round()));
      }
      
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
      
      print('✅ Audio playback completed');

    } catch (e) {
      print('❌ Error playing audio chunk: $e');
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
    }
  }

  void _handleWebSocketError(error) {
    print('❌ WebSocket error: $error');
    state = VoiceChatState.error;
    _addMessage('error', 'WebSocket error: $error');
  }

  void _handleWebSocketClosed() {
    print('🔌 WebSocket connection closed');
    state = VoiceChatState.idle;
    _addMessage('system', 'Connection closed');
    _cleanup();
  }

  void _addMessage(String type, dynamic data) {
    final message = VoiceChatMessage(
      type: type,
      data: data,
      timestamp: DateTime.now(),
    );
    _messageController.add(message);
  }

  void _cleanup() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
    _audioBuffer.clear();
    
    if (_recorder != null) {
      try {
        _recorder!.stop();
      } catch (e) {
        print('⚠️ Error stopping recorder: $e');
      }
    }
    
    _currentSessionId = null;
    _currentNpcId = null;
    print('🧹 Cleanup completed');
  }

  void disconnect() {
    print('🔌 Disconnecting WebSocket...');
    _channel?.sink.close();
    _channel = null;
    _cleanup();
    state = VoiceChatState.idle;
  }

  @override
  void dispose() {
    print('🗑️ Disposing VoiceChatService...');
    disconnect();
    _messageController.close();
    _transcriptionController.close();
    _audioChunkController.close();
    
    if (_player != null) {
      _player!.dispose();
    }
    
    super.dispose();
  }
}