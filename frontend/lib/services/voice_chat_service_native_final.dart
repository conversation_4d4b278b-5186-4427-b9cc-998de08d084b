import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:path_provider/path_provider.dart';
import '../config/app_config.dart';
import '../models/user_model.dart';

enum VoiceChatState {
  idle,
  connecting,
  connected,
  recording,
  processing,
  playing,
  error,
}

class VoiceChatMessage {
  final String type;
  final dynamic data;
  final DateTime timestamp;

  VoiceChatMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory VoiceChatMessage.fromJson(Map<String, dynamic> json) {
    return VoiceChatMessage(
      type: json['type']?.toString() ?? 'unknown',
      data: json['data'] ?? json,
      timestamp: DateTime.now(),
    );
  }
}

final voiceChatServiceProvider = StateNotifierProvider<VoiceChatServiceNative, VoiceChatState>((ref) {
  return VoiceChatServiceNative();
});

class VoiceChatServiceNative extends StateNotifier<VoiceChatState> {
  VoiceChatServiceNative() : super(VoiceChatState.idle) {
    _initAudio();
  }

  // 原生音频平台通道
  static const platform = MethodChannel('com.example.voice_chat_app/audio');
  
  WebSocketChannel? _channel;
  Timer? _recordingTimer;
  String? _currentSessionId;
  int? _currentNpcId;
  String? _currentRecordingPath;
  bool _isRecording = false;
  bool _hasPermission = false;

  final StreamController<VoiceChatMessage> _messageController = StreamController.broadcast();
  final StreamController<String> _transcriptionController = StreamController.broadcast();
  final StreamController<Uint8List> _audioChunkController = StreamController.broadcast();

  Stream<VoiceChatMessage> get messageStream => _messageController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;

  bool get isConnected => _channel != null;
  bool get isRecording => _isRecording;
  bool get isPlaying => state == VoiceChatState.playing;
  String? get currentSessionId => _currentSessionId;

  Future<void> _initAudio() async {
    try {
      print('🎵 Initializing native macOS audio...');
      
      // 检查权限
      _hasPermission = await platform.invokeMethod('hasPermission');
      
      if (!_hasPermission) {
        print('🎤 Requesting microphone permission...');
        _hasPermission = await platform.invokeMethod('requestPermission');
      }
      
      if (_hasPermission) {
        print('✅ Native audio initialized with permissions');
      } else {
        print('❌ Microphone permission denied');
      }
      
    } catch (e) {
      print('❌ Error initializing native audio: $e');
    }
  }

  Future<void> connect(UserModel user) async {
    if (state == VoiceChatState.connecting || isConnected) return;

    state = VoiceChatState.connecting;

    try {
      final uri = Uri.parse('${AppConfig.websocketUrl}/${user.id}');
      print('🔌 Connecting to WebSocket: $uri');
      
      _channel = WebSocketChannel.connect(uri);

      _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );

      state = VoiceChatState.connected;
      _addMessage('system', 'Connected to voice chat service');
      print('✅ WebSocket connected successfully');

    } catch (e) {
      print('❌ WebSocket connection failed: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to connect: $e');
    }
  }

  Future<void> startSession(int npcId) async {
    if (!isConnected) {
      print('❌ Cannot start session - not connected');
      return;
    }

    _currentNpcId = npcId;

    final message = {
      'type': 'start_session',
      'npc_id': npcId,
    };

    _channel!.sink.add(json.encode(message));
    _addMessage('system', 'Starting conversation session...');
    print('📤 Session start message sent for NPC: $npcId');
  }

  Future<void> startRecording() async {
    print('🎤 startRecording() called - current state: $state');

    if (state == VoiceChatState.error) {
      print('🔄 Resetting from error state to idle');
      state = VoiceChatState.idle;
    }

    if (state != VoiceChatState.connected && state != VoiceChatState.idle) {
      print('❌ Cannot start recording - invalid state: $state');
      return;
    }

    if (_isRecording) {
      print('❌ Already recording');
      return;
    }

    try {
      // 检查权限
      if (!_hasPermission) {
        print('🎤 Requesting permission...');
        _hasPermission = await platform.invokeMethod('requestPermission');
        if (!_hasPermission) {
          print('❌ Microphone permission denied');
          state = VoiceChatState.error;
          _addMessage('error', 'Microphone permission required');
          return;
        }
      }

      // 开始录音
      final fileName = 'recording_${DateTime.now().millisecondsSinceEpoch}.wav';
      final success = await platform.invokeMethod('startRecording', {
        'fileName': fileName,
        'sampleRate': 16000,
        'channels': 1,
      });

      if (success) {
        _isRecording = true;
        _currentRecordingPath = fileName;
        state = VoiceChatState.recording;
        _addMessage('system', 'Recording started with native macOS audio');
        print('✅ Native recording started: $fileName');

        // 注释掉实时音频流，只在停止录音时发送完整音频
        // _startRealTimeAudioStreaming();
      } else {
        print('❌ Failed to start recording');
        state = VoiceChatState.error;
        _addMessage('error', 'Failed to start recording');
      }

    } catch (e) {
      print('❌ Error in startRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to start recording: $e');
    }
  }

  void _startRealTimeAudioStreaming() {
    print('🎤 Starting real-time audio streaming simulation...');
    _recordingTimer?.cancel();

    // 每200ms发送模拟音频数据
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 200), (timer) async {
      if (!_isRecording || !isConnected) {
        timer.cancel();
        print('🛑 Real-time streaming stopped - recording: $_isRecording, connected: $isConnected');
        return;
      }

      // 发送模拟音频块
      try {
        final simulatedAudioData = Uint8List.fromList(List.generate(6400, (index) => 0)); // 静音数据
        _sendRealAudioData(simulatedAudioData);
      } catch (e) {
        print('⚠️ Error sending simulated audio data: $e');
      }
    });

    print('✅ Real-time audio streaming simulation started');
  }

  void _sendRealAudioData(Uint8List audioData) {
    if (!isConnected) {
      print('❌ Cannot send audio data - WebSocket not connected');
      return;
    }

    try {
      final base64Audio = base64.encode(audioData);
      final message = {
        'type': 'audio_chunk',
        'data': base64Audio,
        'format': 'pcm16',
        'sample_rate': 16000,
        'channels': 1,
        'source': 'native_macos',
      };

      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);

      print('✅ Audio chunk sent: ${audioData.length} bytes');
    } catch (e) {
      print('❌ Error sending audio data: $e');
    }
  }

  Future<void> stopRecording() async {
    print('🛑 stopRecording() called - current state: $state');

    if (!_isRecording) {
      print('❌ Cannot stop recording - not recording');
      return;
    }

    try {
      // 停止实时流
      _recordingTimer?.cancel();
      _recordingTimer = null;

      // 停止原生录音
      final result = await platform.invokeMethod('stopRecording');
      _isRecording = false;

      if (result != null && result is Map) {
        final filePath = result['path'] as String?;
        final fileSize = result['size'] as int?;
        
        print('✅ Native recording stopped: $filePath, size: ${fileSize ?? 0} bytes');

        // 发送完整的录音文件
        if (filePath != null && fileSize != null && fileSize > 0) {
          await _sendCompleteRecording(filePath);
        }
      }

      state = VoiceChatState.processing;
      _addMessage('system', 'Processing native audio...');
      print('🔄 State set to processing');

    } catch (e) {
      print('❌ Error in stopRecording: $e');
      _isRecording = false;
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to stop recording: $e');
    }
  }

  Future<void> _sendCompleteRecording(String filePath) async {
    try {
      final audioFile = File(filePath);
      if (await audioFile.exists()) {
        final audioBytes = await audioFile.readAsBytes();
        
        // 跳过WAV文件头（44字节），只发送音频数据
        final audioData = audioBytes.length > 44 ? audioBytes.sublist(44) : audioBytes;
        
        print('📤 Sending complete recording: ${audioData.length} bytes');
        
        final base64Audio = base64.encode(audioData);
        final message = {
          'type': 'audio_complete',
          'data': base64Audio,
          'format': 'pcm16',
          'sample_rate': 16000,
          'channels': 1,
          'source': 'native_macos',
        };

        if (isConnected) {
          _channel!.sink.add(json.encode(message));
        }
        
        // 清理临时文件
        try {
          await audioFile.delete();
          print('🗑️ Temporary audio file deleted');
        } catch (e) {
          print('⚠️ Failed to delete temporary file: $e');
        }
      }
    } catch (e) {
      print('❌ Error sending complete recording: $e');
    }
  }

  void interrupt() {
    if (!isConnected) return;

    final message = {'type': 'interrupt'};
    _channel!.sink.add(json.encode(message));

    state = VoiceChatState.connected;
    _addMessage('system', 'Assistant interrupted');
    print('⏸️ Interrupt signal sent');
  }

  Future<void> endSession() async {
    if (!isConnected || _currentSessionId == null) return;

    final message = {'type': 'end_session'};
    _channel!.sink.add(json.encode(message));

    _currentSessionId = null;
    _currentNpcId = null;

    _addMessage('system', 'Session ended');
    print('🔚 Session ended');
  }

  void _handleWebSocketMessage(dynamic message) {
    try {
      final data = message is String ? json.decode(message) : message;
      final Map<String, dynamic> messageData;
      
      if (data is Map<String, dynamic>) {
        messageData = data;
      } else if (data is Map) {
        messageData = Map<String, dynamic>.from(data);
      } else {
        messageData = {
          'type': 'unknown',
          'data': data?.toString() ?? 'Unknown data',
        };
      }

      final chatMessage = VoiceChatMessage.fromJson(messageData);
      print('📥 WebSocket message received: ${chatMessage.type}');

      switch (chatMessage.type) {
        case 'connected':
          print('✅ WebSocket connection confirmed');
          break;
          
        case 'session_started':
          _currentSessionId = messageData['session_id']?.toString();
          _addMessage('system', 'Session started successfully');
          print('✅ Session started: $_currentSessionId');
          break;

        case 'transcription':
          final text = messageData['text'];
          final confidence = messageData['confidence'] ?? 1.0;
          _transcriptionController.add('$text (${(confidence * 100).toInt()}%)');
          _addMessage('transcription', text);
          print('📝 Transcription: $text');
          break;

        case 'audio_chunk':
          _handleAudioChunk(messageData['data']);
          break;

        case 'response_complete':
          state = VoiceChatState.connected;
          _addMessage('system', 'Response complete');
          print('✅ Response complete');
          break;

        case 'interrupted':
          state = VoiceChatState.connected;
          _addMessage('system', 'Assistant was interrupted');
          break;

        case 'session_ended':
          _currentSessionId = null;
          _addMessage('system', 'Session ended');
          break;

        case 'error':
          state = VoiceChatState.error;
          final errorMsg = messageData['message'] ?? 'Unknown error';
          _addMessage('error', errorMsg);
          print('❌ Server error: $errorMsg');
          break;

        case 'heartbeat':
          print('💓 Heartbeat received');
          break;

        default:
          _addMessage('unknown', messageData);
          print('❓ Unknown message type: ${chatMessage.type}');
      }

      _messageController.add(chatMessage);

    } catch (e) {
      print('❌ Error handling WebSocket message: $e');
    }
  }

  void _handleAudioChunk(String base64Audio) {
    try {
      final audioData = base64.decode(base64Audio);
      _audioChunkController.add(audioData);
      print('🎵 Audio chunk received: ${audioData.length} bytes');

      // 播放音频
      _playRealAudio(audioData);

    } catch (e) {
      print('❌ Error handling audio chunk: $e');
    }
  }

  Future<void> _playRealAudio(Uint8List audioData) async {
    try {
      state = VoiceChatState.playing;
      print('🔊 Playing real audio: ${audioData.length} bytes');
      
      // 使用原生 macOS 播放音频
      try {
        // 创建临时文件来播放音频
        final tempDir = await getTemporaryDirectory();
        final tempAudioFile = File('${tempDir.path}/playback_${DateTime.now().millisecondsSinceEpoch}.wav');
        
        // 写入音频数据（假设后端发送的是完整的 WAV 文件）
        await tempAudioFile.writeAsBytes(audioData);
        
        // 使用原生平台通道播放音频
        final success = await platform.invokeMethod('playAudio', {
          'filePath': tempAudioFile.path,
        });
        
        if (success) {
          print('🎵 Native audio playback started');
          
          // 估算播放时间（基于数据大小）
          final playbackDuration = Duration(milliseconds: (audioData.length / 32).round());
          await Future.delayed(playbackDuration);
          
          // 清理临时文件
          try {
            await tempAudioFile.delete();
            print('🗑️ Playback temp file deleted');
          } catch (e) {
            print('⚠️ Failed to delete playback temp file: $e');
          }
        } else {
          print('❌ Native audio playback failed, using simulation');
          await _playAudioSimulation(audioData);
        }
        
      } catch (e) {
        print('❌ Native audio playback error: $e, using simulation');
        await _playAudioSimulation(audioData);
      }
      
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
      
      print('✅ Audio playback completed');

    } catch (e) {
      print('❌ Error in audio playback: $e');
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
    }
  }

  Future<void> _playAudioSimulation(Uint8List audioData) async {
    try {
      print('🔊 Simulating audio playback: ${audioData.length} bytes');
      
      // 模拟播放时间（基于数据大小）
      final playbackDuration = Duration(milliseconds: (audioData.length / 32).round());
      await Future.delayed(playbackDuration);
      
      print('✅ Audio playback simulation completed');

    } catch (e) {
      print('❌ Error in audio playback simulation: $e');
    }
  }

  void _handleWebSocketError(error) {
    print('❌ WebSocket error: $error');
    state = VoiceChatState.error;
    _addMessage('error', 'WebSocket error: $error');
  }

  void _handleWebSocketClosed() {
    print('🔌 WebSocket connection closed');
    state = VoiceChatState.idle;
    _addMessage('system', 'Connection closed');
    _cleanup();
  }

  void _addMessage(String type, dynamic data) {
    final message = VoiceChatMessage(
      type: type,
      data: data,
      timestamp: DateTime.now(),
    );
    _messageController.add(message);
  }

  void _cleanup() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
    
    // 停止录音（如果正在录音）
    if (_isRecording) {
      try {
        platform.invokeMethod('stopRecording');
        _isRecording = false;
      } catch (e) {
        print('⚠️ Error stopping recording during cleanup: $e');
      }
    }
    
    _currentSessionId = null;
    _currentNpcId = null;
    _currentRecordingPath = null;
    print('🧹 Cleanup completed');
  }

  void disconnect() {
    print('🔌 Disconnecting WebSocket...');
    _channel?.sink.close();
    _channel = null;
    _cleanup();
    state = VoiceChatState.idle;
  }

  @override
  void dispose() {
    print('🗑️ Disposing VoiceChatServiceNative...');
    disconnect();
    _messageController.close();
    _transcriptionController.close();
    _audioChunkController.close();
    
    super.dispose();
  }
}