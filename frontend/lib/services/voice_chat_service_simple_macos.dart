import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:permission_handler/permission_handler.dart';
import '../config/app_config.dart';
import '../models/user_model.dart';

enum VoiceChatState {
  idle,
  connecting,
  connected,
  recording,
  processing,
  playing,
  error,
}

class VoiceChatMessage {
  final String type;
  final dynamic data;
  final DateTime timestamp;

  VoiceChatMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory VoiceChatMessage.fromJson(Map<String, dynamic> json) {
    return VoiceChatMessage(
      type: json['type']?.toString() ?? 'unknown',
      data: json['data'] ?? json,
      timestamp: DateTime.now(),
    );
  }
}

final voiceChatServiceProvider = StateNotifierProvider<VoiceChatService, VoiceChatState>((ref) {
  return VoiceChatService();
});

class VoiceChatService extends StateNotifier<VoiceChatState> {
  VoiceChatService() : super(VoiceChatState.idle) {
    _initAudio();
  }

  WebSocketChannel? _channel;
  Timer? _recordingTimer;
  String? _currentSessionId;
  int? _currentNpcId;

  final StreamController<VoiceChatMessage> _messageController = StreamController.broadcast();
  final StreamController<String> _transcriptionController = StreamController.broadcast();
  final StreamController<Uint8List> _audioChunkController = StreamController.broadcast();

  Stream<VoiceChatMessage> get messageStream => _messageController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;

  bool get isConnected => _channel != null;
  bool get isRecording => state == VoiceChatState.recording;
  bool get isPlaying => state == VoiceChatState.playing;
  String? get currentSessionId => _currentSessionId;

  Future<void> _initAudio() async {
    try {
      print('🎵 Initializing simplified audio services for macOS...');
      print('✅ Audio services initialized (using enhanced simulation)');
    } catch (e) {
      print('❌ Error initializing audio services: $e');
    }
  }

  Future<bool> requestPermissions() async {
    try {
      print('🎤 Requesting microphone permission...');
      final microphoneStatus = await Permission.microphone.request();
      final granted = microphoneStatus == PermissionStatus.granted;
      print('🎤 Microphone permission: ${granted ? "granted" : "denied"}');
      return granted;
    } catch (e) {
      print('⚠️ Permission request failed: $e');
      return false;
    }
  }

  Future<void> connect(UserModel user) async {
    if (state == VoiceChatState.connecting || isConnected) return;

    state = VoiceChatState.connecting;

    try {
      final uri = Uri.parse('${AppConfig.websocketUrl}/${user.id}');
      print('🔌 Connecting to WebSocket: $uri');
      
      _channel = WebSocketChannel.connect(uri);

      // Listen to WebSocket messages
      _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );

      state = VoiceChatState.connected;
      _addMessage('system', 'Connected to voice chat service');
      print('✅ WebSocket connected successfully');

    } catch (e) {
      print('❌ WebSocket connection failed: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to connect: $e');
    }
  }

  Future<void> startSession(int npcId) async {
    if (!isConnected) {
      print('❌ Cannot start session - not connected');
      return;
    }

    _currentNpcId = npcId;

    final message = {
      'type': 'start_session',
      'npc_id': npcId,
    };

    _channel!.sink.add(json.encode(message));
    _addMessage('system', 'Starting conversation session...');
    print('📤 Session start message sent for NPC: $npcId');
  }

  Future<void> startRecording() async {
    print('🎤 startRecording() called - current state: $state');

    if (state == VoiceChatState.error) {
      print('🔄 Resetting from error state to idle');
      state = VoiceChatState.idle;
    }

    if (state != VoiceChatState.connected && state != VoiceChatState.idle) {
      print('❌ Cannot start recording - invalid state: $state');
      return;
    }

    try {
      // 请求麦克风权限
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        print('⚠️ Microphone permission denied, using enhanced simulation');
      }

      state = VoiceChatState.recording;
      _addMessage('system', 'Recording started (enhanced simulation for macOS)');
      print('✅ Recording started with enhanced simulation');

      // 开始增强的音频流传输
      _startEnhancedAudioStreaming();

    } catch (e) {
      print('❌ Error in startRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to start recording: $e');
    }
  }

  void _startEnhancedAudioStreaming() {
    print('🎤 Starting enhanced audio streaming with speech-like patterns...');
    _recordingTimer?.cancel();

    int chunkCount = 0;
    
    // 生成更真实的语音模式音频数据
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (state != VoiceChatState.recording || !isConnected) {
        timer.cancel();
        print('🛑 Audio streaming stopped - state: $state, connected: $isConnected');
        return;
      }

      // 生成包含语音特征的音频数据
      final audioData = _generateSpeechLikeAudio(chunkCount);
      _sendAudioChunk(audioData);
      
      chunkCount++;
      
      // 模拟说话停顿 - 每20个块（2秒）后停止
      if (chunkCount >= 20) {
        print('🔄 Simulated speech complete, stopping recording');
        stopRecording();
      }
    });

    print('✅ Enhanced audio streaming started');
  }

  Uint8List _generateSpeechLikeAudio(int chunkIndex) {
    // 生成更像真实语音的音频数据
    const int sampleRate = 16000;
    const int chunkDurationMs = 100;
    const int samplesPerChunk = (sampleRate * chunkDurationMs) ~/ 1000; // 1600 samples
    const int bytesPerSample = 2; // 16-bit
    const int chunkSize = samplesPerChunk * bytesPerSample; // 3200 bytes

    final random = math.Random(chunkIndex); // 使用chunkIndex作为种子，确保可重现
    final audioData = Uint8List(chunkSize);

    // 模拟不同的语音模式
    for (int i = 0; i < samplesPerChunk; i++) {
      final double time = (chunkIndex * samplesPerChunk + i) / sampleRate;
      
      // 基础语音频率 (100-300Hz)
      final double fundamentalFreq = 150 + 50 * math.sin(time * 2);
      
      // 添加谐波 (语音的特征)
      double signal = 0.0;
      signal += 0.6 * math.sin(2 * math.pi * fundamentalFreq * time);           // 基频
      signal += 0.3 * math.sin(2 * math.pi * fundamentalFreq * 2 * time);       // 二次谐波
      signal += 0.2 * math.sin(2 * math.pi * fundamentalFreq * 3 * time);       // 三次谐波
      
      // 添加共振峰 (元音特征)
      final double formant1 = 800 + 200 * math.sin(time * 3);  // 第一共振峰
      final double formant2 = 1200 + 300 * math.sin(time * 5); // 第二共振峰
      signal += 0.15 * math.sin(2 * math.pi * formant1 * time);
      signal += 0.1 * math.sin(2 * math.pi * formant2 * time);
      
      // 添加语音包络 (音量变化)
      final double envelope = 0.5 + 0.3 * math.sin(time * 8) + 0.2 * math.sin(time * 12);
      signal *= envelope;
      
      // 添加少量噪声 (呼吸音等)
      final double noise = (random.nextDouble() - 0.5) * 0.05;
      signal += noise;
      
      // 限制幅度并转换为16位整数
      signal = signal.clamp(-0.8, 0.8);
      final int sampleInt16 = (signal * 32767).round();
      
      // 存储为小端字节序
      audioData[i * 2] = sampleInt16 & 0xFF;
      audioData[i * 2 + 1] = (sampleInt16 >> 8) & 0xFF;
    }

    return audioData;
  }

  void _sendAudioChunk(Uint8List audioData) {
    if (!isConnected) {
      print('❌ Cannot send audio chunk - WebSocket not connected');
      return;
    }

    try {
      final base64Audio = base64.encode(audioData);
      final message = {
        'type': 'audio_chunk',
        'data': base64Audio,
        'format': 'pcm16',
        'sample_rate': 16000,
        'channels': 1,
        'source': 'enhanced_simulation', // 标记为增强模拟
      };

      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);

      print('✅ Enhanced audio chunk sent: ${audioData.length} bytes');
    } catch (e) {
      print('❌ Error sending audio chunk: $e');
    }
  }

  Future<void> stopRecording() async {
    print('🛑 stopRecording() called - current state: $state');

    if (state != VoiceChatState.recording) {
      print('❌ Cannot stop recording - not in recording state: $state');
      return;
    }

    try {
      // 停止音频流传输
      _recordingTimer?.cancel();
      _recordingTimer = null;
      print('✅ Audio streaming stopped');

      state = VoiceChatState.processing;
      _addMessage('system', 'Processing enhanced audio...');
      print('🔄 State set to processing');

    } catch (e) {
      print('❌ Error in stopRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to stop recording: $e');
    }
  }

  void interrupt() {
    if (!isConnected) return;

    final message = {'type': 'interrupt'};
    _channel!.sink.add(json.encode(message));

    state = VoiceChatState.connected;
    _addMessage('system', 'Assistant interrupted');
    print('⏸️ Interrupt signal sent');
  }

  Future<void> endSession() async {
    if (!isConnected || _currentSessionId == null) return;

    final message = {'type': 'end_session'};
    _channel!.sink.add(json.encode(message));

    _currentSessionId = null;
    _currentNpcId = null;

    _addMessage('system', 'Session ended');
    print('🔚 Session ended');
  }

  void _handleWebSocketMessage(dynamic message) {
    try {
      final data = message is String ? json.decode(message) : message;
      final Map<String, dynamic> messageData;
      
      if (data is Map<String, dynamic>) {
        messageData = data;
      } else if (data is Map) {
        messageData = Map<String, dynamic>.from(data);
      } else {
        messageData = {
          'type': 'unknown',
          'data': data?.toString() ?? 'Unknown data',
        };
      }

      final chatMessage = VoiceChatMessage.fromJson(messageData);
      print('📥 WebSocket message received: ${chatMessage.type}');

      switch (chatMessage.type) {
        case 'connected':
          print('✅ WebSocket connection confirmed');
          break;
          
        case 'session_started':
          _currentSessionId = messageData['session_id']?.toString();
          _addMessage('system', 'Session started successfully');
          print('✅ Session started: $_currentSessionId');
          break;

        case 'transcription':
          final text = messageData['text'];
          final confidence = messageData['confidence'] ?? 1.0;
          _transcriptionController.add('$text (${(confidence * 100).toInt()}%)');
          _addMessage('transcription', text);
          print('📝 Transcription: $text');
          break;

        case 'audio_chunk':
          _handleAudioChunk(messageData['data']);
          break;

        case 'response_complete':
          state = VoiceChatState.connected;
          _addMessage('system', 'Response complete');
          print('✅ Response complete');
          break;

        case 'interrupted':
          state = VoiceChatState.connected;
          _addMessage('system', 'Assistant was interrupted');
          break;

        case 'session_ended':
          _currentSessionId = null;
          _addMessage('system', 'Session ended');
          break;

        case 'error':
          state = VoiceChatState.error;
          final errorMsg = messageData['message'] ?? 'Unknown error';
          _addMessage('error', errorMsg);
          print('❌ Server error: $errorMsg');
          break;

        case 'heartbeat':
          print('💓 Heartbeat received');
          break;

        default:
          _addMessage('unknown', messageData);
          print('❓ Unknown message type: ${chatMessage.type}');
      }

      _messageController.add(chatMessage);

    } catch (e) {
      print('❌ Error handling WebSocket message: $e');
    }
  }

  void _handleAudioChunk(String base64Audio) {
    try {
      final audioData = base64.decode(base64Audio);
      _audioChunkController.add(audioData);
      print('🎵 Audio chunk received: ${audioData.length} bytes');

      // 模拟音频播放
      _simulateAudioPlayback(audioData);

    } catch (e) {
      print('❌ Error handling audio chunk: $e');
    }
  }

  Future<void> _simulateAudioPlayback(Uint8List audioData) async {
    try {
      state = VoiceChatState.playing;
      print('🔊 Simulating audio playback: ${audioData.length} bytes');
      
      // 根据音频数据大小计算播放时间
      final playbackDuration = Duration(milliseconds: (audioData.length / 32).round());
      await Future.delayed(playbackDuration);
      
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
      
      print('✅ Audio playback simulation completed');

    } catch (e) {
      print('❌ Error simulating audio playback: $e');
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
    }
  }

  void _handleWebSocketError(error) {
    print('❌ WebSocket error: $error');
    state = VoiceChatState.error;
    _addMessage('error', 'WebSocket error: $error');
  }

  void _handleWebSocketClosed() {
    print('🔌 WebSocket connection closed');
    state = VoiceChatState.idle;
    _addMessage('system', 'Connection closed');
    _cleanup();
  }

  void _addMessage(String type, dynamic data) {
    final message = VoiceChatMessage(
      type: type,
      data: data,
      timestamp: DateTime.now(),
    );
    _messageController.add(message);
  }

  void _cleanup() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
    _currentSessionId = null;
    _currentNpcId = null;
    print('🧹 Cleanup completed');
  }

  void disconnect() {
    print('🔌 Disconnecting WebSocket...');
    _channel?.sink.close();
    _channel = null;
    _cleanup();
    state = VoiceChatState.idle;
  }

  @override
  void dispose() {
    print('🗑️ Disposing VoiceChatService...');
    disconnect();
    _messageController.close();
    _transcriptionController.close();
    _audioChunkController.close();
    super.dispose();
  }
}