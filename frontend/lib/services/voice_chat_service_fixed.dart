import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import '../config/app_config.dart';
import '../models/user_model.dart';

enum VoiceChatState {
  idle,
  connecting,
  connected,
  recording,
  processing,
  playing,
  error,
}

class VoiceChatMessage {
  final String type;
  final dynamic data;
  final DateTime timestamp;

  VoiceChatMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory VoiceChatMessage.fromJson(Map<String, dynamic> json) {
    return VoiceChatMessage(
      type: json['type']?.toString() ?? 'unknown',
      data: json['data'] ?? json,
      timestamp: DateTime.now(),
    );
  }
}

final voiceChatServiceProvider = StateNotifierProvider<VoiceChatService, VoiceChatState>((ref) {
  return VoiceChatService();
});

class VoiceChatService extends StateNotifier<VoiceChatState> {
  VoiceChatService() : super(VoiceChatState.idle) {
    _initAudio();
  }

  WebSocketChannel? _channel;
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;

  Timer? _recordingTimer;
  String? _currentSessionId;
  int? _currentNpcId;

  final StreamController<VoiceChatMessage> _messageController = StreamController.broadcast();
  final StreamController<String> _transcriptionController = StreamController.broadcast();
  final StreamController<Uint8List> _audioChunkController = StreamController.broadcast();

  Stream<VoiceChatMessage> get messageStream => _messageController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;

  bool get isConnected => _channel != null;
  bool get isRecording => state == VoiceChatState.recording;
  bool get isPlaying => state == VoiceChatState.playing;
  String? get currentSessionId => _currentSessionId;

  Future<void> _initAudio() async {
    try {
      print('🎵 Initializing audio services...');

      // 尝试初始化flutter_sound
      _recorder = FlutterSoundRecorder();
      _player = FlutterSoundPlayer();

      // 在macOS上，flutter_sound可能有问题，所以我们用try-catch包装
      try {
        await _recorder!.openRecorder();
        print('✅ Recorder initialized');
      } catch (e) {
        print('⚠️ Recorder initialization failed: $e');
        _recorder = null;
      }

      try {
        await _player!.openPlayer();
        print('✅ Player initialized');
      } catch (e) {
        print('⚠️ Player initialization failed: $e');
        _player = null;
      }

      if (_recorder == null && _player == null) {
        print('⚠️ Audio services not available, will use simulated audio');
      } else {
        print('✅ Audio services partially or fully initialized');
      }

    } catch (e) {
      print('❌ Error initializing audio services: $e');
      _recorder = null;
      _player = null;
    }
  }

  Future<bool> requestPermissions() async {
    final microphoneStatus = await Permission.microphone.request();
    return microphoneStatus == PermissionStatus.granted;
  }

  Future<void> connect(UserModel user) async {
    if (state == VoiceChatState.connecting || isConnected) return;

    state = VoiceChatState.connecting;

    try {
      final uri = Uri.parse('${AppConfig.websocketUrl}/${user.id}');
      _channel = WebSocketChannel.connect(uri);

      // Listen to WebSocket messages
      _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );

      state = VoiceChatState.connected;
      _addMessage('system', 'Connected to voice chat service');

    } catch (e) {
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to connect: $e');
    }
  }

  Future<void> startSession(int npcId) async {
    if (!isConnected) return;

    _currentNpcId = npcId;

    final message = {
      'type': 'start_session',
      'npc_id': npcId,
    };

    _channel!.sink.add(json.encode(message));
    _addMessage('system', 'Starting conversation session...');
  }

  void resetToIdleState() {
    print('🔄 Resetting to idle state from: $state');
    _recordingTimer?.cancel();
    _recordingTimer = null;
    state = VoiceChatState.idle;
    _addMessage('system', 'Ready for recording');
  }

  Future<void> startRecording() async {
    print('🎤 startRecording() called - current state: $state');

    // If in error state, reset to idle first
    if (state == VoiceChatState.error) {
      print('🔄 Resetting from error state to idle');
      resetToIdleState();
    }

    if (state != VoiceChatState.connected && state != VoiceChatState.idle) {
      print('❌ Cannot start recording - invalid state: $state');
      return;
    }

    try {
      // Check permissions
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        throw Exception('Microphone permission denied');
      }

      // Try to use real recording if available
      if (_recorder != null) {
        print('🎙️ Using real microphone recording');
        await _recorder!.startRecorder(
          toStream: _audioChunkController.sink,
          codec: Codec.pcm16WAV,
          sampleRate: 16000,
        );
        print('✅ Real recording started');
      } else {
        // Fallback to simulated recording
        print('🔄 Using simulated recording (Flutter Sound not available)');
        // Start sending audio chunks
        print('🚀 About to start audio streaming...');
        _startAudioStreaming();
      }

      state = VoiceChatState.recording;
      _addMessage('system', 'Recording started');

    } catch (e) {
      print('❌ Error in startRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to start recording: $e');
    }
  }

  Future<void> stopRecording() async {
    print('🛑 stopRecording() called - current state: $state');

    if (state != VoiceChatState.recording) {
      print('❌ Cannot stop recording - not in recording state: $state');
      return;
    }

    try {
      // Stop the audio streaming timer first
      print('🛑 Stopping audio streaming timer...');
      _recordingTimer?.cancel();
      _recordingTimer = null;

      // Try to stop the recorder if available
      if (_recorder != null) {
        try {
          await _recorder!.stopRecorder();
          print('✅ Recorder stopped successfully');
        } catch (e) {
          print('❌ Failed to stop recorder: $e');
          // 继续处理，即使停止录音失败
        }
      }

      print('🔄 Setting state to processing...');
      state = VoiceChatState.processing;
      _addMessage('system', 'Processing audio...');

    } catch (e) {
      print('❌ Error in stopRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to stop recording: $e');
    }
  }

  void _startAudioStreaming() {
    print('🎤 Starting audio streaming...');
    _recordingTimer?.cancel();

    // Send larger, more realistic simulated audio chunks
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (state != VoiceChatState.recording || !isConnected) {
        timer.cancel();
        print('🛑 Audio streaming stopped - state: $state, connected: $isConnected');
        return;
      }

      // Generate a larger, more realistic chunk of random audio data (1600 bytes)
      // This simulates 100ms of 16-bit mono audio at 8kHz, a common format.
      final int chunkSize = 1600;
      final random = math.Random();
      final audioData = Uint8List.fromList(
        List<int>.generate(chunkSize, (i) => random.nextInt(256))
      );

      _sendAudioChunk(audioData);
    });

    print('✅ Audio streaming timer started, sending realistic chunks');
  }

  void _sendAudioChunk(Uint8List audioData) {
    if (!isConnected) {
      print('❌ Cannot send audio chunk - WebSocket not connected');
      return;
    }

    try {
      final base64Audio = base64.encode(audioData);
      final message = {
        'type': 'audio_chunk',
        'data': base64Audio,
      };

      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);

      print('✅ Audio chunk sent: ${audioData.length} bytes (base64: ${base64Audio.length} chars)');
    } catch (e) {
      print('❌ Error sending audio chunk: $e');
    }
  }

  // 添加测试音频传输功能
  void testAudioTransmission() {
    print('🧪 Testing audio transmission...');
    // 发送测试音频数据
    final testAudioData = Uint8List.fromList([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
    _sendAudioChunk(testAudioData);
    print('📤 Sent test audio data');
  }

  void interrupt() {
    if (!isConnected) return;

    // Stop any current playback
    if (_player != null) {
      _player!.stopPlayer();
    }

    // Send interrupt signal
    final message = {'type': 'interrupt'};
    _channel!.sink.add(json.encode(message));

    state = VoiceChatState.connected;
    _addMessage('system', 'Assistant interrupted');
  }

  Future<void> endSession() async {
    if (!isConnected || _currentSessionId == null) return;

    final message = {'type': 'end_session'};
    _channel!.sink.add(json.encode(message));

    _currentSessionId = null;
    _currentNpcId = null;

    _addMessage('system', 'Session ended');
  }

  void _handleWebSocketMessage(dynamic message) {
    try {
      // The message might already be a Map (decoded JSON) or a String
      final data = message is String ? json.decode(message) : message;

      // Ensure data is a Map and handle potential type issues
      final Map<String, dynamic> messageData;
      if (data is Map<String, dynamic>) {
        messageData = data;
      } else if (data is Map) {
        messageData = Map<String, dynamic>.from(data);
      } else {
        // If data is not a map, create a default structure
        messageData = {
          'type': 'unknown',
          'data': data?.toString() ?? 'Unknown data',
        };
      }

      final chatMessage = VoiceChatMessage.fromJson(messageData);

      switch (chatMessage.type) {
        case 'session_started':
          // Handle potential type issues for session_id
          _currentSessionId = messageData['session_id']?.toString();
          _addMessage('system', 'Session started successfully');
          break;

        case 'transcription':
          final text = messageData['text'];
          final confidence = messageData['confidence'] ?? 1.0;
          _transcriptionController.add('$text (${(confidence * 100).toInt()}%)');
          _addMessage('transcription', text);
          break;

        case 'audio_chunk':
          _handleAudioChunk(messageData['data']);
          break;

        case 'tts_audio':
          _handleTtsAudio(messageData['url']);
          break;

        case 'response_complete':
          state = VoiceChatState.connected;
          _addMessage('system', 'Response complete');
          break;

        case 'interrupted':
          state = VoiceChatState.connected;
          _addMessage('system', 'Assistant was interrupted');
          break;

        case 'session_ended':
          _currentSessionId = null;
          _addMessage('system', 'Session ended');
          break;

        case 'error':
          state = VoiceChatState.error;
          _addMessage('error', messageData['message'] ?? 'Unknown error');
          break;

        default:
          _addMessage('unknown', messageData);
      }

      _messageController.add(chatMessage);

    } catch (e) {
      print('Error handling WebSocket message: $e');
    }
  }

  void _handleAudioChunk(String base64Audio) {
    try {
      final audioData = base64.decode(base64Audio);
      _audioChunkController.add(audioData);

      // Play audio chunk
      _playAudioChunk(audioData);

    } catch (e) {
      print('Error handling audio chunk: $e');
    }
  }

  Future<void> _playAudioChunk(Uint8List audioData) async {
    try {
      if (state != VoiceChatState.playing) {
        state = VoiceChatState.playing;
      }

      // 播放音频数据
      if (_player != null) {
        try {
          // 将音频数据保存到临时文件并播放
          final tempFile = await _createTempAudioFile(audioData);
          await _player!.startPlayer(
            fromURI: tempFile.path,
            whenFinished: () {
              state = VoiceChatState.connected;
            },
          );
        } catch (e) {
          print('Error playing audio with Flutter Sound: $e');
          // 如果Flutter Sound不可用，使用系统默认播放器
          await _playWithSystemPlayer(audioData);
        }
      } else {
        // 如果没有音频播放器，使用系统默认播放器
        await _playWithSystemPlayer(audioData);
      }

    } catch (e) {
      print('Error playing audio chunk: $e');
      state = VoiceChatState.connected;
    }
  }

  // 创建临时音频文件
  Future<File> _createTempAudioFile(Uint8List audioData) async {
    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/temp_audio_${DateTime.now().millisecondsSinceEpoch}.wav');
    
    // 添加WAV文件头
    final wavData = _addWavHeader(audioData);
    await tempFile.writeAsBytes(wavData);
    
    return tempFile;
  }

  // 添加WAV文件头
  Uint8List _addWavHeader(Uint8List audioData) {
    final byteRate = 16000 * 2; // 16kHz * 16 bits
    final bufferSize = audioData.length;
    
    final header = Uint8List(44);
    final byteData = ByteData.view(header.buffer);
    
    // RIFF header
    header[0] = 0x52; // 'R'
    header[1] = 0x49; // 'I'
    header[2] = 0x46; // 'F'
    header[3] = 0x46; // 'F'
    
    // File size
    byteData.setUint32(4, 36 + bufferSize, Endian.little);
    
    // WAVE header
    header[8] = 0x57; // 'W'
    header[9] = 0x41; // 'A'
    header[10] = 0x56; // 'V'
    header[11] = 0x45; // 'E'
    
    // fmt chunk
    header[12] = 0x66; // 'f'
    header[13] = 0x6d; // 'm'
    header[14] = 0x74; // 't'
    header[15] = 0x20; // ' '
    
    // fmt chunk size
    byteData.setUint32(16, 16, Endian.little);
    
    // Audio format (1 = PCM)
    byteData.setUint16(20, 1, Endian.little);
    
    // Number of channels
    byteData.setUint16(22, 1, Endian.little);
    
    // Sample rate
    byteData.setUint32(24, 16000, Endian.little);
    
    // Byte rate
    byteData.setUint32(28, byteRate, Endian.little);
    
    // Block align
    byteData.setUint16(32, 2, Endian.little);
    
    // Bits per sample
    byteData.setUint16(34, 16, Endian.little);
    
    // data chunk
    header[36] = 0x64; // 'd'
    header[37] = 0x61; // 'a'
    header[38] = 0x74; // 't'
    header[39] = 0x61; // 'a'
    
    // data chunk size
    byteData.setUint32(40, bufferSize, Endian.little);
    
    // Combine header and audio data
    final wavData = Uint8List(44 + bufferSize);
    wavData.setRange(0, 44, header);
    wavData.setRange(44, 44 + bufferSize, audioData);
    
    return wavData;
  }

  // 处理TTS音频URL
  void _handleTtsAudio(String audioUrl) async {
    print('🔊 Handling TTS audio URL: $audioUrl');
    _addMessage('system', 'Playing TTS audio...');
    
    try {
      // 下载并播放音频
      await _downloadAndPlayAudio(audioUrl);
    } catch (e) {
      print('❌ Error playing TTS audio: $e');
      _addMessage('error', 'Failed to play audio: $e');
    }
  }

  // 下载并播放音频
  Future<void> _downloadAndPlayAudio(String audioUrl) async {
    try {
      // 使用系统播放器播放音频URL
      await _playAudioFromUrl(audioUrl);
    } catch (e) {
      print('❌ Error downloading/playing audio: $e');
    }
  }

  // 从URL播放音频
  Future<void> _playAudioFromUrl(String audioUrl) async {
    print('🔊 Playing audio from URL: $audioUrl');
    
    // 在实际实现中，这里可以使用音频播放库播放网络音频
    // 目前只是模拟播放完成
    await Future.delayed(const Duration(seconds: 3));
    state = VoiceChatState.connected;
    _addMessage('system', 'Audio playback completed');
  }

  // 使用系统默认播放器播放音频
  Future<void> _playWithSystemPlayer(Uint8List audioData) async {
    print('🔊 Playing audio chunk with system player (size: ${audioData.length} bytes)');
    
    // 在实际实现中，这里可以使用系统音频API播放音频数据
    // 目前只是模拟播放完成
    await Future.delayed(Duration(milliseconds: (audioData.length / 320).round()));
    state = VoiceChatState.connected;
  }

  void _handleWebSocketError(error) {
    state = VoiceChatState.error;
    _addMessage('error', 'WebSocket error: $error');
  }

  void _handleWebSocketClosed() {
    state = VoiceChatState.idle;
    _addMessage('system', 'Connection closed');
    _cleanup();
  }

  void _addMessage(String type, dynamic data) {
    final message = VoiceChatMessage(
      type: type,
      data: data,
      timestamp: DateTime.now(),
    );
    _messageController.add(message);
  }

  void _cleanup() {
    _recordingTimer?.cancel();
    if (_recorder != null) {
      _recorder!.stopRecorder();
    }
    if (_player != null) {
      _player!.stopPlayer();
    }
    _currentSessionId = null;
    _currentNpcId = null;
  }

  void disconnect() {
    _channel?.sink.close();
    _channel = null;
    _cleanup();
    state = VoiceChatState.idle;
  }

  @override
  void dispose() {
    disconnect();
    _messageController.close();
    _transcriptionController.close();
    _audioChunkController.close();

    // 安全地关闭音频服务
    if (_recorder != null) {
      try {
        _recorder!.closeRecorder();
      } catch (e) {
        print('⚠️ Error closing recorder: $e');
      }
    }
    if (_player != null) {
      try {
        _player!.closePlayer();
      } catch (e) {
        print('⚠️ Error closing player: $e');
      }
    }
    super.dispose();
  }
}
