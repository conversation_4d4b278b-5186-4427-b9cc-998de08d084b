import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import '../models/user_model.dart';
import '../config/app_config.dart';

class MemoryAuthService extends StateNotifier<AsyncValue<UserModel?>> {
  MemoryAuthService() : super(const AsyncValue.data(null));

  UserModel? _currentUser;
  String? _currentToken;

  UserModel? get currentUser => _currentUser;
  String? get currentToken => _currentToken;

  Future<UserModel?> login({
    required String username,
    required String password,
  }) async {
    try {
      state = const AsyncValue.loading();
      
      final uri = Uri.parse('${AppConfig.backendUrl}/auth/login').replace(
        queryParameters: {
          'username': username,
          'password': password,
        },
      );
      
      if (kDebugMode) {
        print('🔍 Login request URL: $uri');
      }
      
      final response = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Login request timeout');
        },
      );

      if (kDebugMode) {
        print('🔍 Login response status: ${response.statusCode}');
        print('🔍 Login response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        // Handle potential type issues in the response
        final userData = data['user'] is Map<String, dynamic> 
            ? data['user'] 
            : (data['user'] is Map ? Map<String, dynamic>.from(data['user']) : {});
        
        if (userData.isEmpty) {
          throw Exception('Invalid user data in response');
        }
        
        final user = UserModel.fromJson(userData);
        final token = data['token']?.toString() ?? '';

        if (kDebugMode) {
          print('✅ Login successful: ${user.username}');
          print('✅ User data: ${user.toJson()}');
        }

        // Store in memory only
        _currentUser = user;
        _currentToken = token;

        state = AsyncValue.data(user);
        return user;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['detail'] ?? 'Login failed';
        if (kDebugMode) {
          print('❌ Login failed: $errorMessage');
        }
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Login error: $e');
      }
      state = AsyncValue.error(e, StackTrace.current);
      throw e;
    }
  }

  Future<UserModel?> register({
    required String username,
    required String password,
    String? email,
    String? nickname,
  }) async {
    try {
      state = const AsyncValue.loading();
      
      final queryParams = <String, String>{
        'username': username,
        'password': password,
      };
      if (email != null) queryParams['email'] = email;
      if (nickname != null) queryParams['nickname'] = nickname;
      
      final uri = Uri.parse('${AppConfig.backendUrl}/auth/register').replace(
        queryParameters: queryParams,
      );
      
      final response = await http.post(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final user = UserModel.fromJson(data['user']);
        final token = data['token'];
        
        // Store in memory only
        _currentUser = user;
        _currentToken = token;
        
        state = AsyncValue.data(user);
        return user;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? 'Registration failed');
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      throw e;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    _currentToken = null;
    state = const AsyncValue.data(null);
  }

  Future<UserModel?> loginAsGuest() async {
    try {
      state = const AsyncValue.loading();
      
      final guestUser = UserModel(
        id: 999,
        uuid: 'guest_${DateTime.now().millisecondsSinceEpoch}',
        username: 'guest_${DateTime.now().millisecondsSinceEpoch}',
        nickname: 'Guest User',
        email: null,
        avatarUrl: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // Store in memory only
      _currentUser = guestUser;
      _currentToken = 'guest_token';
      
      state = AsyncValue.data(guestUser);
      return guestUser;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }
}

// Provider for MemoryAuthService
final memoryAuthServiceProvider = StateNotifierProvider<MemoryAuthService, AsyncValue<UserModel?>>((ref) {
  return MemoryAuthService();
});

// Helper provider to get current user
final currentUserProvider = Provider<UserModel?>((ref) {
  final authState = ref.watch(memoryAuthServiceProvider);
  return authState.when(
    data: (user) => user,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Helper provider to check if user is logged in
final isLoggedInProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user != null;
});
