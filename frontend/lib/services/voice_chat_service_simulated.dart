import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:permission_handler/permission_handler.dart';
import '../config/app_config.dart';
import '../models/user_model.dart';

enum VoiceChatState {
  idle,
  connecting,
  connected,
  recording,
  processing,
  playing,
  error,
}

class VoiceChatMessage {
  final String type;
  final dynamic data;
  final DateTime timestamp;

  VoiceChatMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory VoiceChatMessage.fromJson(Map<String, dynamic> json) {
    return VoiceChatMessage(
      type: json['type']?.toString() ?? 'unknown',
      data: json['data'] ?? json,
      timestamp: DateTime.now(),
    );
  }
}

final voiceChatServiceProvider = StateNotifierProvider<VoiceChatService, VoiceChatState>((ref) {
  return VoiceChatService();
});

class VoiceChatService extends StateNotifier<VoiceChatState> {
  VoiceChatService() : super(VoiceChatState.idle) {
    _initAudio();
  }

  WebSocketChannel? _channel;
  Timer? _recordingTimer;
  String? _currentSessionId;
  int? _currentNpcId;

  final StreamController<VoiceChatMessage> _messageController = StreamController.broadcast();
  final StreamController<String> _transcriptionController = StreamController.broadcast();
  final StreamController<Uint8List> _audioChunkController = StreamController.broadcast();

  Stream<VoiceChatMessage> get messageStream => _messageController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;

  bool get isConnected => _channel != null;
  bool get isRecording => state == VoiceChatState.recording;
  bool get isPlaying => state == VoiceChatState.playing;
  String? get currentSessionId => _currentSessionId;

  Future<void> _initAudio() async {
    try {
      print('🎵 Initializing macOS-compatible audio services...');
      // 在macOS上，我们使用系统级的音频API
      print('✅ macOS audio services initialized');
    } catch (e) {
      print('❌ Error initializing audio services: $e');
    }
  }

  Future<bool> requestPermissions() async {
    try {
      final microphoneStatus = await Permission.microphone.request();
      print('🎤 Microphone permission: $microphoneStatus');
      return microphoneStatus == PermissionStatus.granted;
    } catch (e) {
      print('⚠️ Permission request failed: $e');
      return false; // 假设权限已授予，继续执行
    }
  }

  Future<void> connect(UserModel user) async {
    if (state == VoiceChatState.connecting || isConnected) return;

    state = VoiceChatState.connecting;

    try {
      final uri = Uri.parse('${AppConfig.websocketUrl}/${user.id}');
      print('🔌 Connecting to WebSocket: $uri');
      
      _channel = WebSocketChannel.connect(uri);

      // Listen to WebSocket messages
      _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );

      state = VoiceChatState.connected;
      _addMessage('system', 'Connected to voice chat service');
      print('✅ WebSocket connected successfully');

    } catch (e) {
      print('❌ WebSocket connection failed: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to connect: $e');
    }
  }

  Future<void> startSession(int npcId) async {
    if (!isConnected) {
      print('❌ Cannot start session - not connected');
      return;
    }

    _currentNpcId = npcId;

    final message = {
      'type': 'start_session',
      'npc_id': npcId,
    };

    _channel!.sink.add(json.encode(message));
    _addMessage('system', 'Starting conversation session...');
    print('📤 Session start message sent for NPC: $npcId');
  }

  Future<void> startRecording() async {
    print('🎤 startRecording() called - current state: $state');

    if (state == VoiceChatState.error) {
      print('🔄 Resetting from error state to idle');
      state = VoiceChatState.idle;
    }

    if (state != VoiceChatState.connected && state != VoiceChatState.idle) {
      print('❌ Cannot start recording - invalid state: $state');
      return;
    }

    try {
      // 请求麦克风权限
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        print('⚠️ Microphone permission not granted, using simulated audio');
      }

      state = VoiceChatState.recording;
      _addMessage('system', 'Recording started');
      print('✅ Recording state set, starting audio streaming...');

      // 开始音频流传输
      _startAudioStreaming();

    } catch (e) {
      print('❌ Error in startRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to start recording: $e');
    }
  }

  Future<void> stopRecording() async {
    print('🛑 stopRecording() called - current state: $state');

    if (state != VoiceChatState.recording) {
      print('❌ Cannot stop recording - not in recording state: $state');
      return;
    }

    try {
      // 停止音频流传输
      _recordingTimer?.cancel();
      _recordingTimer = null;
      print('✅ Audio streaming stopped');

      state = VoiceChatState.processing;
      _addMessage('system', 'Processing audio...');
      print('🔄 State set to processing');

    } catch (e) {
      print('❌ Error in stopRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to stop recording: $e');
    }
  }

  void _startAudioStreaming() {
    print('🎤 Starting enhanced audio streaming...');
    _recordingTimer?.cancel();

    // 生成更真实的音频数据
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (state != VoiceChatState.recording || !isConnected) {
        timer.cancel();
        print('🛑 Audio streaming stopped - state: $state, connected: $isConnected');
        return;
      }

      // 生成1600字节的音频数据（100ms的16位单声道音频，16kHz采样率）
      final audioData = _generateRealisticAudioChunk();
      _sendAudioChunk(audioData);
    });

    print('✅ Enhanced audio streaming timer started');
  }

  Uint8List _generateRealisticAudioChunk() {
    // 生成更真实的音频数据，模拟语音信号
    const int sampleRate = 16000;
    const int chunkDurationMs = 100;
    const int samplesPerChunk = (sampleRate * chunkDurationMs) ~/ 1000; // 1600 samples
    const int bytesPerSample = 2; // 16-bit
    const int chunkSize = samplesPerChunk * bytesPerSample; // 3200 bytes

    final random = math.Random();
    final audioData = Uint8List(chunkSize);

    // 生成模拟的语音信号（带有一些周期性和随机性）
    for (int i = 0; i < samplesPerChunk; i++) {
      // 生成一个简单的正弦波加噪声，模拟语音
      final double time = i / sampleRate;
      final double frequency = 200 + random.nextDouble() * 300; // 200-500Hz
      final double amplitude = 0.3 + random.nextDouble() * 0.4; // 0.3-0.7
      
      final double signal = amplitude * math.sin(2 * math.pi * frequency * time);
      final double noise = (random.nextDouble() - 0.5) * 0.1; // 小量噪声
      final double sample = signal + noise;
      
      // 转换为16位整数
      final int sampleInt16 = (sample * 32767).clamp(-32768, 32767).round();
      
      // 存储为小端字节序
      audioData[i * 2] = sampleInt16 & 0xFF;
      audioData[i * 2 + 1] = (sampleInt16 >> 8) & 0xFF;
    }

    return audioData;
  }

  void _sendAudioChunk(Uint8List audioData) {
    if (!isConnected) {
      print('❌ Cannot send audio chunk - WebSocket not connected');
      return;
    }

    try {
      final base64Audio = base64.encode(audioData);
      final message = {
        'type': 'audio_chunk',
        'data': base64Audio,
        'format': 'pcm16',
        'sample_rate': 16000,
        'channels': 1,
      };

      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);

      print('✅ Audio chunk sent: ${audioData.length} bytes (${audioData.length ~/ 2} samples)');
    } catch (e) {
      print('❌ Error sending audio chunk: $e');
    }
  }

  void interrupt() {
    if (!isConnected) return;

    final message = {'type': 'interrupt'};
    _channel!.sink.add(json.encode(message));

    state = VoiceChatState.connected;
    _addMessage('system', 'Assistant interrupted');
    print('⏸️ Interrupt signal sent');
  }

  Future<void> endSession() async {
    if (!isConnected || _currentSessionId == null) return;

    final message = {'type': 'end_session'};
    _channel!.sink.add(json.encode(message));

    _currentSessionId = null;
    _currentNpcId = null;

    _addMessage('system', 'Session ended');
    print('🔚 Session ended');
  }

  void _handleWebSocketMessage(dynamic message) {
    try {
      final data = message is String ? json.decode(message) : message;
      final Map<String, dynamic> messageData;
      
      if (data is Map<String, dynamic>) {
        messageData = data;
      } else if (data is Map) {
        messageData = Map<String, dynamic>.from(data);
      } else {
        messageData = {
          'type': 'unknown',
          'data': data?.toString() ?? 'Unknown data',
        };
      }

      final chatMessage = VoiceChatMessage.fromJson(messageData);
      print('📥 WebSocket message received: ${chatMessage.type}');

      switch (chatMessage.type) {
        case 'session_started':
          _currentSessionId = messageData['session_id']?.toString();
          _addMessage('system', 'Session started successfully');
          print('✅ Session started: $_currentSessionId');
          break;

        case 'transcription':
          final text = messageData['text'];
          final confidence = messageData['confidence'] ?? 1.0;
          _transcriptionController.add('$text (${(confidence * 100).toInt()}%)');
          _addMessage('transcription', text);
          print('📝 Transcription: $text');
          break;

        case 'audio_chunk':
          _handleAudioChunk(messageData['data']);
          break;

        case 'response_complete':
          state = VoiceChatState.connected;
          _addMessage('system', 'Response complete');
          print('✅ Response complete');
          break;

        case 'interrupted':
          state = VoiceChatState.connected;
          _addMessage('system', 'Assistant was interrupted');
          break;

        case 'session_ended':
          _currentSessionId = null;
          _addMessage('system', 'Session ended');
          break;

        case 'error':
          state = VoiceChatState.error;
          final errorMsg = messageData['message'] ?? 'Unknown error';
          _addMessage('error', errorMsg);
          print('❌ Server error: $errorMsg');
          break;

        default:
          _addMessage('unknown', messageData);
          print('❓ Unknown message type: ${chatMessage.type}');
      }

      _messageController.add(chatMessage);

    } catch (e) {
      print('❌ Error handling WebSocket message: $e');
    }
  }

  void _handleAudioChunk(String base64Audio) {
    try {
      final audioData = base64.decode(base64Audio);
      _audioChunkController.add(audioData);
      print('🎵 Audio chunk received: ${audioData.length} bytes');

      // 播放音频
      _playAudioChunk(audioData);

    } catch (e) {
      print('❌ Error handling audio chunk: $e');
    }
  }

  Future<void> _playAudioChunk(Uint8List audioData) async {
    try {
      state = VoiceChatState.playing;
      print('🔊 Playing audio chunk: ${audioData.length} bytes');
      
      // 在macOS上，我们可以使用系统的音频播放功能
      // 这里先模拟播放，实际项目中可以集成macOS音频API
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
      
      print('✅ Audio playback completed');

    } catch (e) {
      print('❌ Error playing audio chunk: $e');
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
    }
  }

  void _handleWebSocketError(error) {
    print('❌ WebSocket error: $error');
    state = VoiceChatState.error;
    _addMessage('error', 'WebSocket error: $error');
  }

  void _handleWebSocketClosed() {
    print('🔌 WebSocket connection closed');
    state = VoiceChatState.idle;
    _addMessage('system', 'Connection closed');
    _cleanup();
  }

  void _addMessage(String type, dynamic data) {
    final message = VoiceChatMessage(
      type: type,
      data: data,
      timestamp: DateTime.now(),
    );
    _messageController.add(message);
  }

  void _cleanup() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
    _currentSessionId = null;
    _currentNpcId = null;
    print('🧹 Cleanup completed');
  }

  void disconnect() {
    print('🔌 Disconnecting WebSocket...');
    _channel?.sink.close();
    _channel = null;
    _cleanup();
    state = VoiceChatState.idle;
  }

  @override
  void dispose() {
    print('🗑️ Disposing VoiceChatService...');
    disconnect();
    _messageController.close();
    _transcriptionController.close();
    _audioChunkController.close();
    super.dispose();
  }
}