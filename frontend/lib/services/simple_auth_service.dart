import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../models/user_model.dart';
import '../config/app_config.dart';

class SimpleAuthService extends StateNotifier<AsyncValue<UserModel?>> {
  SimpleAuthService() : super(const AsyncValue.loading()) {
    _init();
  }

  UserModel? _currentUser;

  UserModel? get currentUser => _currentUser;

  Future<void> _init() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString('user_data');
      final token = prefs.getString('user_token');
      
      if (userData != null && token != null) {
        _currentUser = UserModel.fromJson(json.decode(userData));
        state = AsyncValue.data(_currentUser);
      } else {
        state = const AsyncValue.data(null);
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<UserModel?> login({
    required String username,
    required String password,
  }) async {
    try {
      state = const AsyncValue.loading();
      
      final uri = Uri.parse('${AppConfig.backendUrl}/auth/login').replace(
        queryParameters: {
          'username': username,
          'password': password,
        },
      );
      
      if (kDebugMode) {
        print('🔍 Login request URL: $uri');
      }
      
      final response = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Login request timeout');
        },
      );

      if (kDebugMode) {
        print('🔍 Login response status: ${response.statusCode}');
        print('🔍 Login response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final user = UserModel.fromJson(data['user']);
        final token = data['token'];
        
        if (kDebugMode) {
          print('✅ Login successful: ${user.username}');
        }
        
        // Store user data and token using SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_token', token);
        await prefs.setString('user_data', json.encode(user.toJson()));
        
        _currentUser = user;
        state = AsyncValue.data(user);
        return user;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['detail'] ?? 'Login failed';
        if (kDebugMode) {
          print('❌ Login failed: $errorMessage');
        }
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Login error: $e');
      }
      state = AsyncValue.error(e, StackTrace.current);
      throw e;
    }
  }

  Future<UserModel?> register({
    required String username,
    required String password,
    String? email,
    String? nickname,
  }) async {
    try {
      state = const AsyncValue.loading();
      
      final queryParams = <String, String>{
        'username': username,
        'password': password,
      };
      if (email != null) queryParams['email'] = email;
      if (nickname != null) queryParams['nickname'] = nickname;
      
      final uri = Uri.parse('${AppConfig.backendUrl}/auth/register').replace(
        queryParameters: queryParams,
      );
      
      final response = await http.post(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final user = UserModel.fromJson(data['user']);
        final token = data['token'];
        
        // Store user data and token
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_token', token);
        await prefs.setString('user_data', json.encode(user.toJson()));
        
        _currentUser = user;
        state = AsyncValue.data(user);
        return user;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? 'Registration failed');
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      throw e;
    }
  }

  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_token');
      await prefs.remove('user_data');
      
      _currentUser = null;
      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<UserModel?> loginAsGuest() async {
    try {
      state = const AsyncValue.loading();
      
      final guestUser = UserModel(
        id: 999,
        uuid: 'guest_${DateTime.now().millisecondsSinceEpoch}',
        username: 'guest_${DateTime.now().millisecondsSinceEpoch}',
        nickname: 'Guest User',
        email: null,
        avatarUrl: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // Store guest data
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_token', 'guest_token');
      await prefs.setString('user_data', json.encode(guestUser.toJson()));
      
      _currentUser = guestUser;
      state = AsyncValue.data(guestUser);
      return guestUser;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }
}

// Provider for SimpleAuthService
final simpleAuthServiceProvider = StateNotifierProvider<SimpleAuthService, AsyncValue<UserModel?>>((ref) {
  return SimpleAuthService();
});

// Helper provider to get current user
final currentUserProvider = Provider<UserModel?>((ref) {
  final authState = ref.watch(simpleAuthServiceProvider);
  return authState.when(
    data: (user) => user,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Helper provider to check if user is logged in
final isLoggedInProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user != null;
});