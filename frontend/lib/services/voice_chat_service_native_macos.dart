import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';
import '../config/app_config.dart';
import '../models/user_model.dart';

enum VoiceChatState {
  idle,
  connecting,
  connected,
  recording,
  processing,
  playing,
  error,
}

class VoiceChatMessage {
  final String type;
  final dynamic data;
  final DateTime timestamp;

  VoiceChatMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory VoiceChatMessage.fromJson(Map<String, dynamic> json) {
    return VoiceChatMessage(
      type: json['type']?.toString() ?? 'unknown',
      data: json['data'] ?? json,
      timestamp: DateTime.now(),
    );
  }
}

final voiceChatServiceProvider = StateNotifierProvider<VoiceChatService, VoiceChatState>((ref) {
  return VoiceChatService();
});

class VoiceChatService extends StateNotifier<VoiceChatState> {
  VoiceChatService() : super(VoiceChatState.idle) {
    _initAudio();
  }

  WebSocketChannel? _channel;
  AudioRecorder? _recorder;
  AudioPlayer? _player;
  
  Timer? _recordingTimer;
  String? _currentSessionId;
  int? _currentNpcId;
  String? _tempAudioPath;

  final StreamController<VoiceChatMessage> _messageController = StreamController.broadcast();
  final StreamController<String> _transcriptionController = StreamController.broadcast();
  final StreamController<Uint8List> _audioChunkController = StreamController.broadcast();

  Stream<VoiceChatMessage> get messageStream => _messageController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;

  bool get isConnected => _channel != null;
  bool get isRecording => state == VoiceChatState.recording;
  bool get isPlaying => state == VoiceChatState.playing;
  String? get currentSessionId => _currentSessionId;

  Future<void> _initAudio() async {
    try {
      print('🎵 Initializing native macOS audio services...');
      
      _recorder = AudioRecorder();
      _player = AudioPlayer();
      
      print('✅ Native audio services initialized');
    } catch (e) {
      print('❌ Error initializing audio services: $e');
      _recorder = null;
      _player = null;
    }
  }

  Future<bool> requestPermissions() async {
    try {
      print('🎤 Requesting microphone permission...');
      
      // 使用record插件检查权限
      if (_recorder != null) {
        final hasPermission = await _recorder!.hasPermission();
        print('🎤 Microphone permission: $hasPermission');
        return hasPermission;
      }
      
      // 备用权限检查
      final microphoneStatus = await Permission.microphone.request();
      final granted = microphoneStatus == PermissionStatus.granted;
      print('🎤 Microphone permission (fallback): $granted');
      return granted;
    } catch (e) {
      print('⚠️ Permission request failed: $e');
      return false;
    }
  }

  Future<void> connect(UserModel user) async {
    if (state == VoiceChatState.connecting || isConnected) return;

    state = VoiceChatState.connecting;

    try {
      final uri = Uri.parse('${AppConfig.websocketUrl}/${user.id}');
      print('🔌 Connecting to WebSocket: $uri');
      
      _channel = WebSocketChannel.connect(uri);

      _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );

      state = VoiceChatState.connected;
      _addMessage('system', 'Connected to voice chat service');
      print('✅ WebSocket connected successfully');

    } catch (e) {
      print('❌ WebSocket connection failed: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to connect: $e');
    }
  }

  Future<void> startSession(int npcId) async {
    if (!isConnected) {
      print('❌ Cannot start session - not connected');
      return;
    }

    _currentNpcId = npcId;

    final message = {
      'type': 'start_session',
      'npc_id': npcId,
    };

    _channel!.sink.add(json.encode(message));
    _addMessage('system', 'Starting conversation session...');
    print('📤 Session start message sent for NPC: $npcId');
  }

  Future<void> startRecording() async {
    print('🎤 startRecording() called - current state: $state');

    if (state == VoiceChatState.error) {
      print('🔄 Resetting from error state to idle');
      state = VoiceChatState.idle;
    }

    if (state != VoiceChatState.connected && state != VoiceChatState.idle) {
      print('❌ Cannot start recording - invalid state: $state');
      return;
    }

    try {
      // 请求麦克风权限
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        print('❌ Microphone permission denied');
        _addMessage('error', 'Microphone permission required');
        return;
      }

      if (_recorder == null) {
        print('❌ Audio recorder not available');
        _addMessage('error', 'Audio recorder not available');
        return;
      }

      // 获取临时文件路径
      final tempDir = await getTemporaryDirectory();
      _tempAudioPath = '${tempDir.path}/recording_${DateTime.now().millisecondsSinceEpoch}.wav';

      print('🎤 Starting native recording to: $_tempAudioPath');
      
      // 配置录音参数
      const config = RecordConfig(
        encoder: AudioEncoder.wav,
        sampleRate: 16000,
        numChannels: 1,
        bitRate: 256000,
      );

      // 开始录音
      await _recorder!.start(config, path: _tempAudioPath);
      
      state = VoiceChatState.recording;
      _addMessage('system', 'Native recording started');
      print('✅ Native recording started successfully');

      // 开始实时音频流处理
      _startRealTimeAudioStreaming();

    } catch (e) {
      print('❌ Error in startRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to start recording: $e');
    }
  }

  void _startRealTimeAudioStreaming() {
    print('🎤 Starting real-time audio streaming...');
    _recordingTimer?.cancel();

    // 每100ms检查一次录音文件并发送数据
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) async {
      if (state != VoiceChatState.recording || !isConnected) {
        timer.cancel();
        print('🛑 Real-time streaming stopped - state: $state, connected: $isConnected');
        return;
      }

      // 这里我们暂时使用定期发送完整文件的方式
      // 在实际应用中，可以使用startStream方法获取实时数据
      try {
        if (_tempAudioPath != null && File(_tempAudioPath!).existsSync()) {
          final audioFile = File(_tempAudioPath!);
          final audioBytes = await audioFile.readAsBytes();
          
          if (audioBytes.isNotEmpty) {
            print('🎵 Sending real audio data: ${audioBytes.length} bytes');
            _sendRealAudioData(audioBytes);
          }
        }
      } catch (e) {
        print('⚠️ Error reading audio file: $e');
      }
    });

    print('✅ Real-time audio streaming started');
  }

  void _sendRealAudioData(Uint8List audioData) {
    if (!isConnected) {
      print('❌ Cannot send audio data - WebSocket not connected');
      return;
    }

    try {
      // 只发送最新的音频数据块（避免发送整个文件）
      final chunkSize = 3200; // 100ms at 16kHz, 16-bit
      final startIndex = audioData.length > chunkSize ? audioData.length - chunkSize : 0;
      final audioChunk = audioData.sublist(startIndex);
      
      final base64Audio = base64.encode(audioChunk);
      final message = {
        'type': 'audio_chunk',
        'data': base64Audio,
        'format': 'wav',
        'sample_rate': 16000,
        'channels': 1,
        'source': 'native_macos_recorder',
      };

      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);

      print('✅ Native audio chunk sent: ${audioChunk.length} bytes');
    } catch (e) {
      print('❌ Error sending audio data: $e');
    }
  }

  Future<void> stopRecording() async {
    print('🛑 stopRecording() called - current state: $state');

    if (state != VoiceChatState.recording) {
      print('❌ Cannot stop recording - not in recording state: $state');
      return;
    }

    try {
      // 停止实时流
      _recordingTimer?.cancel();
      _recordingTimer = null;

      // 停止录音
      if (_recorder != null) {
        final audioPath = await _recorder!.stop();
        print('✅ Native recording stopped, saved to: $audioPath');
        
        // 发送完整的录音文件
        if (audioPath != null && File(audioPath).existsSync()) {
          final audioFile = File(audioPath);
          final audioBytes = await audioFile.readAsBytes();
          print('📤 Sending complete recording: ${audioBytes.length} bytes');
          
          final base64Audio = base64.encode(audioBytes);
          final message = {
            'type': 'audio_complete',
            'data': base64Audio,
            'format': 'wav',
            'sample_rate': 16000,
            'channels': 1,
            'source': 'native_macos_recorder',
          };

          _channel!.sink.add(json.encode(message));
          
          // 清理临时文件
          try {
            await audioFile.delete();
            print('🗑️ Temporary audio file deleted');
          } catch (e) {
            print('⚠️ Failed to delete temporary file: $e');
          }
        }
      }

      state = VoiceChatState.processing;
      _addMessage('system', 'Processing native audio...');
      print('🔄 State set to processing');

    } catch (e) {
      print('❌ Error in stopRecording: $e');
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to stop recording: $e');
    }
  }

  void interrupt() {
    if (!isConnected) return;

    final message = {'type': 'interrupt'};
    _channel!.sink.add(json.encode(message));

    state = VoiceChatState.connected;
    _addMessage('system', 'Assistant interrupted');
    print('⏸️ Interrupt signal sent');
  }

  Future<void> endSession() async {
    if (!isConnected || _currentSessionId == null) return;

    final message = {'type': 'end_session'};
    _channel!.sink.add(json.encode(message));

    _currentSessionId = null;
    _currentNpcId = null;

    _addMessage('system', 'Session ended');
    print('🔚 Session ended');
  }

  void _handleWebSocketMessage(dynamic message) {
    try {
      final data = message is String ? json.decode(message) : message;
      final Map<String, dynamic> messageData;
      
      if (data is Map<String, dynamic>) {
        messageData = data;
      } else if (data is Map) {
        messageData = Map<String, dynamic>.from(data);
      } else {
        messageData = {
          'type': 'unknown',
          'data': data?.toString() ?? 'Unknown data',
        };
      }

      final chatMessage = VoiceChatMessage.fromJson(messageData);
      print('📥 WebSocket message received: ${chatMessage.type}');

      switch (chatMessage.type) {
        case 'connected':
          print('✅ WebSocket connection confirmed');
          break;
          
        case 'session_started':
          _currentSessionId = messageData['session_id']?.toString();
          _addMessage('system', 'Session started successfully');
          print('✅ Session started: $_currentSessionId');
          break;

        case 'transcription':
          final text = messageData['text'];
          final confidence = messageData['confidence'] ?? 1.0;
          _transcriptionController.add('$text (${(confidence * 100).toInt()}%)');
          _addMessage('transcription', text);
          print('📝 Transcription: $text');
          break;

        case 'audio_chunk':
          _handleAudioChunk(messageData['data']);
          break;

        case 'response_complete':
          state = VoiceChatState.connected;
          _addMessage('system', 'Response complete');
          print('✅ Response complete');
          break;

        case 'interrupted':
          state = VoiceChatState.connected;
          _addMessage('system', 'Assistant was interrupted');
          break;

        case 'session_ended':
          _currentSessionId = null;
          _addMessage('system', 'Session ended');
          break;

        case 'error':
          state = VoiceChatState.error;
          final errorMsg = messageData['message'] ?? 'Unknown error';
          _addMessage('error', errorMsg);
          print('❌ Server error: $errorMsg');
          break;

        case 'heartbeat':
          print('💓 Heartbeat received');
          break;

        default:
          _addMessage('unknown', messageData);
          print('❓ Unknown message type: ${chatMessage.type}');
      }

      _messageController.add(chatMessage);

    } catch (e) {
      print('❌ Error handling WebSocket message: $e');
    }
  }

  void _handleAudioChunk(String base64Audio) {
    try {
      final audioData = base64.decode(base64Audio);
      _audioChunkController.add(audioData);
      print('🎵 Audio chunk received: ${audioData.length} bytes');

      // 播放音频
      _playNativeAudio(audioData);

    } catch (e) {
      print('❌ Error handling audio chunk: $e');
    }
  }

  Future<void> _playNativeAudio(Uint8List audioData) async {
    try {
      state = VoiceChatState.playing;
      print('🔊 Playing native audio: ${audioData.length} bytes');
      
      if (_player != null) {
        // 保存音频数据到临时文件
        final tempDir = await getTemporaryDirectory();
        final tempAudioFile = File('${tempDir.path}/playback_${DateTime.now().millisecondsSinceEpoch}.wav');
        await tempAudioFile.writeAsBytes(audioData);
        
        // 播放音频文件
        await _player!.play(DeviceFileSource(tempAudioFile.path));
        
        // 监听播放完成
        _player!.onPlayerComplete.listen((_) {
          if (state == VoiceChatState.playing) {
            state = VoiceChatState.connected;
          }
          // 清理临时文件
          tempAudioFile.delete().catchError((e) {
            print('⚠️ Failed to delete playback temp file: $e');
          });
          print('✅ Native audio playback completed');
        });
        
        print('🎵 Native audio playback started');
      } else {
        // 如果播放器不可用，模拟播放时间
        final playbackDuration = Duration(milliseconds: (audioData.length / 32).round());
        await Future.delayed(playbackDuration);
        
        if (state == VoiceChatState.playing) {
          state = VoiceChatState.connected;
        }
        
        print('✅ Audio playback simulation completed');
      }

    } catch (e) {
      print('❌ Error playing native audio: $e');
      if (state == VoiceChatState.playing) {
        state = VoiceChatState.connected;
      }
    }
  }

  void _handleWebSocketError(error) {
    print('❌ WebSocket error: $error');
    state = VoiceChatState.error;
    _addMessage('error', 'WebSocket error: $error');
  }

  void _handleWebSocketClosed() {
    print('🔌 WebSocket connection closed');
    state = VoiceChatState.idle;
    _addMessage('system', 'Connection closed');
    _cleanup();
  }

  void _addMessage(String type, dynamic data) {
    final message = VoiceChatMessage(
      type: type,
      data: data,
      timestamp: DateTime.now(),
    );
    _messageController.add(message);
  }

  void _cleanup() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
    
    // 清理临时文件
    if (_tempAudioPath != null) {
      try {
        File(_tempAudioPath!).delete();
      } catch (e) {
        print('⚠️ Failed to cleanup temp audio file: $e');
      }
      _tempAudioPath = null;
    }
    
    _currentSessionId = null;
    _currentNpcId = null;
    print('🧹 Cleanup completed');
  }

  void disconnect() {
    print('🔌 Disconnecting WebSocket...');
    _channel?.sink.close();
    _channel = null;
    _cleanup();
    state = VoiceChatState.idle;
  }

  @override
  void dispose() {
    print('🗑️ Disposing VoiceChatService...');
    disconnect();
    _messageController.close();
    _transcriptionController.close();
    _audioChunkController.close();
    
    _player?.dispose();
    
    super.dispose();
  }
}