import 'package:flutter/material.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';

void main() => runApp(const MyApp());

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    return const MaterialApp(home: MicDemo());
  }
}

class MicDemo extends StatefulWidget {
  const MicDemo({super.key});
  @override
  State<MicDemo> createState() => _MicDemoState();
}

class _MicDemoState extends State<MicDemo> {
  final FlutterSoundRecorder _rec = FlutterSoundRecorder();
  bool _opened = false;
  bool _recording = false;
  String? _path;
  String _status = 'Initializing...';

  @override
  void initState() {
    super.initState();
    _init();
  }

  Future<void> _init() async {
    setState(() => _status = 'Requesting microphone permission...');
    
    // 1) 请求权限
    final st = await Permission.microphone.request();
    if (!st.isGranted) {
      setState(() => _status = '❌ Microphone permission not granted');
      debugPrint('❌ mic permission not granted');
      return;
    }
    
    setState(() => _status = 'Permission granted, opening recorder...');
    
    // 2) 检查录音器状态，只有在未打开时才打开
    try {
      if (!_rec.isOpen) {
        await _rec.openRecorder();
        _opened = true;
        setState(() => _status = '✅ Recorder opened successfully');
        debugPrint('✅ recorder opened');
      } else {
        _opened = true;
        setState(() => _status = '✅ Recorder already open');
        debugPrint('✅ recorder already open');
      }
    } catch (e) {
      setState(() => _status = '❌ Failed to open recorder: $e');
      debugPrint('❌ openRecorder failed: $e');
    }
  }

  Future<void> _start() async {
    // 检查录音器状态
    if (!_rec.isOpen) {
      setState(() => _status = '❌ Recorder not open, cannot start recording');
      debugPrint('❌ Recorder not open');
      return;
    }
    
    if (_recording) {
      setState(() => _status = '⚠️ Already recording');
      return;
    }

    // 确保录音器处于停止状态
    if (!_rec.isStopped) {
      setState(() => _status = '⚠️ Recorder not in stopped state');
      debugPrint('⚠️ Recorder not stopped: isRecording=${_rec.isRecording}, isPaused=${_rec.isPaused}');
      return;
    }

    try {
      setState(() => _status = 'Starting recording...');
      
      // macOS 先用 WAV，稳定；需要 AAC 再换 Codec.aacADTS / aacMP4
      final path = 'record_${DateTime.now().millisecondsSinceEpoch}.wav';
      await _rec.startRecorder(
        toFile: path,
        codec: Codec.pcm16WAV,
        sampleRate: 44100,
        numChannels: 1,
      );
      _path = path;
      _recording = true;
      setState(() => _status = '🎙️ Recording started -> $path');
      debugPrint('🎙️ recording started -> $path');
    } catch (e) {
      setState(() => _status = '❌ Start recording error: $e');
      debugPrint('❌ startRecorder error: $e');
    }
  }

  Future<void> _stop() async {
    if (!_recording) return;
    
    try {
      setState(() => _status = 'Stopping recording...');
      
      final filePath = await _rec.stopRecorder();
      _recording = false;
      setState(() => _status = '✅ Recording stopped: $filePath');
      debugPrint('✅ recording stopped: $filePath');
    } catch (e) {
      setState(() => _status = '❌ Stop recording error: $e');
      debugPrint('❌ stopRecorder error: $e');
    }
  }

  @override
  void dispose() {
    _rec.closeRecorder();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('macOS Mic Test (flutter_sound)')),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _opened ? 'Recorder: OPEN' : 'Recorder: CLOSED',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _opened ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _status,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _recording ? null : _start,
                child: const Text('Start Recording'),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: _recording ? _stop : null,
                child: const Text('Stop Recording'),
              ),
              const SizedBox(height: 8),
              if (_path != null) 
                Text(
                  'Last file: $_path',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              const SizedBox(height: 20),
              Text(
                'Recording state: ${_recording ? "RECORDING" : "STOPPED"}',
                style: TextStyle(
                  fontSize: 16,
                  color: _recording ? Colors.red : Colors.grey,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}