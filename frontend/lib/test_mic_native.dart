import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';

void main() => runApp(const NativeTestApp());

class NativeTestApp extends StatelessWidget {
  const NativeTestApp({super.key});
  @override
  Widget build(BuildContext context) {
    return const MaterialApp(home: NativeMicTest());
  }
}

class NativeMicTest extends StatefulWidget {
  const NativeMicTest({super.key});
  @override
  State<NativeMicTest> createState() => _NativeMicTestState();
}

class _NativeMicTestState extends State<NativeMicTest> {
  static const platform = MethodChannel('com.example.voice_chat_app/audio');
  
  String _status = 'Not initialized';
  String? _lastFile;
  bool _isInitialized = false;
  bool _isRecording = false;

  @override
  void initState() {
    super.initState();
    _initRecorder();
  }

  Future<void> _initRecorder() async {
    setState(() => _status = 'Checking permissions...');
    
    try {
      final bool hasPermission = await platform.invokeMethod('hasPermission');
      
      if (hasPermission) {
        _isInitialized = true;
        setState(() => _status = 'Ready to record!');
        print('✅ Native recorder initialized with permissions');
      } else {
        // 请求权限
        final bool granted = await platform.invokeMethod('requestPermission');
        if (granted) {
          _isInitialized = true;
          setState(() => _status = 'Ready to record!');
          print('✅ Permission granted');
        } else {
          setState(() => _status = 'Permission denied - Please allow microphone access in System Preferences');
          print('❌ Permission denied');
        }
      }
      
    } catch (e) {
      setState(() => _status = 'Failed to initialize: $e');
      print('❌ Init error: $e');
      
      // 如果是平台通道不存在，提供备用方案
      if (e.toString().contains('MissingPluginException')) {
        setState(() => _status = 'Native audio not implemented yet - This is a demo UI');
        _isInitialized = true; // 允许测试UI
      }
    }
  }

  Future<void> _startRecording() async {
    if (!_isInitialized) {
      setState(() => _status = 'Recorder not ready');
      return;
    }

    if (_isRecording) {
      setState(() => _status = 'Already recording');
      return;
    }

    try {
      setState(() => _status = 'Starting recording...');
      
      final fileName = 'test_${DateTime.now().millisecondsSinceEpoch}.wav';
      
      // 开始录音
      final bool success = await platform.invokeMethod('startRecording', {
        'fileName': fileName,
        'sampleRate': 16000,
        'channels': 1,
      });
      
      if (success) {
        _isRecording = true;
        _lastFile = fileName;
        setState(() => _status = '🎙️ Recording: $fileName');
        print('🎙️ Recording started: $fileName');
      } else {
        setState(() => _status = 'Failed to start recording');
      }
      
    } catch (e) {
      setState(() => _status = 'Recording failed: $e');
      print('❌ Recording error: $e');
      
      // 如果是平台通道不存在，模拟录音
      if (e.toString().contains('MissingPluginException')) {
        _isRecording = true;
        _lastFile = 'demo_${DateTime.now().millisecondsSinceEpoch}.wav';
        setState(() => _status = '🎙️ Demo Recording: ${_lastFile!}');
      }
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) {
      setState(() => _status = 'Not recording');
      return;
    }

    try {
      setState(() => _status = 'Stopping recording...');
      
      final Map<dynamic, dynamic>? result = await platform.invokeMethod('stopRecording');
      _isRecording = false;
      
      if (result != null) {
        final String? path = result['path'];
        final int? size = result['size'];
        setState(() => _status = '✅ Recording saved: $path (${size ?? 0} bytes)');
        print('✅ Recording stopped: $path, size: ${size ?? 0} bytes');
      } else {
        setState(() => _status = '⚠️ Recording stopped but no result returned');
      }
      
    } catch (e) {
      _isRecording = false;
      setState(() => _status = 'Stop failed: $e');
      print('❌ Stop error: $e');
      
      // 如果是平台通道不存在，模拟停止
      if (e.toString().contains('MissingPluginException')) {
        setState(() => _status = '✅ Demo Recording stopped: ${_lastFile!}');
      }
    }
  }

  Future<void> _resetRecorder() async {
    setState(() => _status = 'Resetting...');
    
    try {
      if (_isRecording) {
        await platform.invokeMethod('stopRecording');
        _isRecording = false;
      }
      
      _lastFile = null;
      
      // 重新检查权限
      await _initRecorder();
      
    } catch (e) {
      setState(() => _status = 'Reset failed: $e');
      print('❌ Reset error: $e');
      
      // 如果是平台通道不存在，重置状态
      if (e.toString().contains('MissingPluginException')) {
        _isRecording = false;
        _lastFile = null;
        setState(() => _status = 'Demo reset complete');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Native macOS Audio Test'),
        backgroundColor: Colors.purple,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 状态图标
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _isRecording ? Colors.red.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                ),
                child: Icon(
                  _isRecording ? Icons.mic : Icons.mic_none,
                  size: 64,
                  color: _isRecording ? Colors.red : Colors.grey,
                ),
              ),
              
              const SizedBox(height: 30),
              
              // 状态文本
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _status,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // 录音器状态
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _isInitialized ? Icons.check_circle : Icons.error,
                    color: _isInitialized ? Colors.green : Colors.red,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Recorder: ${_isInitialized ? "Ready" : "Not Ready"}',
                    style: TextStyle(
                      color: _isInitialized ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 10),
              
              // 录音状态
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _isRecording ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                    color: _isRecording ? Colors.red : Colors.grey,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Recording: ${_isRecording ? "Active" : "Stopped"}',
                    style: TextStyle(
                      color: _isRecording ? Colors.red : Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 30),
              
              // 控制按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: _isInitialized && !_isRecording ? _startRecording : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Start'),
                  ),
                  ElevatedButton(
                    onPressed: _isRecording ? _stopRecording : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Stop'),
                  ),
                  ElevatedButton(
                    onPressed: !_isRecording ? _resetRecorder : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Reset'),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // 文件信息
              if (_lastFile != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'Last Recording:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _lastFile!,
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 30),
              
              // 帮助信息
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Native macOS Audio:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text('• Uses macOS AVAudioEngine directly'),
                      Text('• Handles permissions automatically'),
                      Text('• No third-party plugin dependencies'),
                      Text('• Currently shows demo UI'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}