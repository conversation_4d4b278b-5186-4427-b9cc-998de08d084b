import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';

class PermissionHelper {
  static void showMicrophonePermissionDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.mic_off, color: Colors.red, size: 24),
              SizedBox(width: 8),
              Text('需要麦克风权限'),
            ],
          ),
          content: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '为了使用语音功能，请按以下步骤授权：',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16),
                Text('📱 方法1: 系统设置'),
                SizedBox(height: 8),
                Text('1. 打开"系统设置" (System Settings)'),
                Text('2. 点击"隐私与安全性" (Privacy & Security)'),
                Text('3. 点击"麦克风" (Microphone)'),
                Text('4. 找到本应用并开启权限开关'),
                Text('5. 重启应用'),
                SizedBox(height: 16),
                Text('🔄 方法2: 重启应用'),
                SizedBox(height: 8),
                Text('1. 完全关闭当前应用'),
                Text('2. 重新启动应用'),
                Text('3. 点击录音按钮'),
                Text('4. 在弹出的权限对话框中点击"允许"'),
                SizedBox(height: 16),
                Text(
                  '💡 提示：应用名称可能显示为 "Runner" 或 "voice_chat_app"',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('我知道了'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _openSystemPreferences();
              },
              child: const Text('打开系统设置'),
            ),
          ],
        );
      },
    );
  }

  static void _openSystemPreferences() {
    try {
      // 尝试打开系统设置的隐私页面
      Process.run('open', ['x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone']);
    } catch (e) {
      print('无法自动打开系统设置: $e');
    }
  }

  static void showPermissionInstructions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          maxChildSize: 0.9,
          minChildSize: 0.5,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 拖拽指示器
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  
                  // 标题
                  const Row(
                    children: [
                      Icon(Icons.settings, color: Colors.blue, size: 28),
                      SizedBox(width: 12),
                      Text(
                        'macOS 麦克风权限设置',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // 内容
                  Expanded(
                    child: ListView(
                      controller: scrollController,
                      children: [
                        _buildInstructionCard(
                          '方法1: 系统设置 (推荐)',
                          Icons.settings,
                          Colors.blue,
                          [
                            '1. 点击屏幕左上角的苹果图标 🍎',
                            '2. 选择"系统设置" (System Settings)',
                            '3. 点击"隐私与安全性" (Privacy & Security)',
                            '4. 点击"麦克风" (Microphone)',
                            '5. 找到本应用并开启权限开关',
                            '6. 重启应用',
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        _buildInstructionCard(
                          '方法2: 应用运行时授权',
                          Icons.refresh,
                          Colors.green,
                          [
                            '1. 完全关闭当前应用',
                            '2. 重新启动应用',
                            '3. 点击录音按钮',
                            '4. 在弹出的权限对话框中点击"允许"',
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        _buildInstructionCard(
                          '故障排除',
                          Icons.help_outline,
                          Colors.orange,
                          [
                            '• 应用名称可能显示为 "Runner" 或 "voice_chat_app"',
                            '• 如果找不到应用，请先尝试录音触发权限请求',
                            '• 权限设置后需要重启应用才能生效',
                            '• 如果仍有问题，请重启系统',
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // 底部按钮
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('关闭'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _openSystemPreferences();
                          },
                          child: const Text('打开系统设置'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  static Widget _buildInstructionCard(
    String title,
    IconData icon,
    Color color,
    List<String> steps,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...steps.map((step) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                step,
                style: const TextStyle(fontSize: 14),
              ),
            )),
          ],
        ),
      ),
    );
  }
}