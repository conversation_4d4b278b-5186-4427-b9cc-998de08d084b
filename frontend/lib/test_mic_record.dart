import 'package:flutter/material.dart';
import 'package:record/record.dart';
import 'dart:io';

void main() => runApp(const RecordTestApp());

class RecordTestApp extends StatelessWidget {
  const RecordTestApp({super.key});
  @override
  Widget build(BuildContext context) {
    return const MaterialApp(home: RecordMicTest());
  }
}

class RecordMicTest extends StatefulWidget {
  const RecordMicTest({super.key});
  @override
  State<RecordMicTest> createState() => _RecordMicTestState();
}

class _RecordMicTestState extends State<RecordMicTest> {
  final AudioRecorder _recorder = AudioRecorder();
  String _status = 'Not initialized';
  String? _lastFile;
  bool _isInitialized = false;
  bool _isRecording = false;

  @override
  void initState() {
    super.initState();
    _initRecorder();
  }

  Future<void> _initRecorder() async {
    setState(() => _status = 'Checking permissions...');
    
    try {
      // 检查权限
      if (await _recorder.hasPermission()) {
        _isInitialized = true;
        setState(() => _status = 'Ready to record!');
        print('✅ Recorder initialized with permissions');
      } else {
        setState(() => _status = 'Permission denied - Please allow microphone access');
        print('❌ No microphone permission');
      }
      
    } catch (e) {
      setState(() => _status = 'Failed to initialize: $e');
      print('❌ Init error: $e');
    }
  }

  Future<void> _startRecording() async {
    if (!_isInitialized) {
      setState(() => _status = 'Recorder not ready');
      return;
    }

    if (_isRecording) {
      setState(() => _status = 'Already recording');
      return;
    }

    try {
      setState(() => _status = 'Starting recording...');
      
      final fileName = 'test_${DateTime.now().millisecondsSinceEpoch}.wav';
      
      // 开始录音
      await _recorder.start(
        const RecordConfig(
          encoder: AudioEncoder.wav,
          sampleRate: 16000,
          numChannels: 1,
        ),
        path: fileName,
      );
      
      _isRecording = true;
      _lastFile = fileName;
      setState(() => _status = '🎙️ Recording: $fileName');
      print('🎙️ Recording started: $fileName');
      
    } catch (e) {
      setState(() => _status = 'Recording failed: $e');
      print('❌ Recording error: $e');
      
      // 权限相关错误的特殊处理
      if (e.toString().toLowerCase().contains('permission')) {
        setState(() => _status = 'Permission denied - Please go to System Preferences > Security & Privacy > Microphone');
      }
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) {
      setState(() => _status = 'Not recording');
      return;
    }

    try {
      setState(() => _status = 'Stopping recording...');
      
      final path = await _recorder.stop();
      _isRecording = false;
      
      if (path != null) {
        final file = File(path);
        final size = await file.length();
        setState(() => _status = '✅ Recording saved: $path (${size} bytes)');
        print('✅ Recording stopped: $path, size: $size bytes');
      } else {
        setState(() => _status = '⚠️ Recording stopped but no file returned');
      }
      
    } catch (e) {
      _isRecording = false;
      setState(() => _status = 'Stop failed: $e');
      print('❌ Stop error: $e');
    }
  }

  Future<void> _resetRecorder() async {
    setState(() => _status = 'Resetting...');
    
    try {
      if (_isRecording) {
        await _recorder.stop();
        _isRecording = false;
      }
      
      _lastFile = null;
      
      // 重新检查权限
      await _initRecorder();
      
    } catch (e) {
      setState(() => _status = 'Reset failed: $e');
      print('❌ Reset error: $e');
    }
  }

  @override
  void dispose() {
    if (_isRecording) {
      _recorder.stop();
    }
    _recorder.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Record Plugin Test'),
        backgroundColor: Colors.blue,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 状态图标
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _isRecording ? Colors.red.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                ),
                child: Icon(
                  _isRecording ? Icons.mic : Icons.mic_none,
                  size: 64,
                  color: _isRecording ? Colors.red : Colors.grey,
                ),
              ),
              
              const SizedBox(height: 30),
              
              // 状态文本
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _status,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // 录音器状态
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _isInitialized ? Icons.check_circle : Icons.error,
                    color: _isInitialized ? Colors.green : Colors.red,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Recorder: ${_isInitialized ? "Ready" : "Not Ready"}',
                    style: TextStyle(
                      color: _isInitialized ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 10),
              
              // 录音状态
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _isRecording ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                    color: _isRecording ? Colors.red : Colors.grey,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Recording: ${_isRecording ? "Active" : "Stopped"}',
                    style: TextStyle(
                      color: _isRecording ? Colors.red : Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 30),
              
              // 控制按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: _isInitialized && !_isRecording ? _startRecording : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Start'),
                  ),
                  ElevatedButton(
                    onPressed: _isRecording ? _stopRecording : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Stop'),
                  ),
                  ElevatedButton(
                    onPressed: !_isRecording ? _resetRecorder : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Reset'),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // 文件信息
              if (_lastFile != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'Last Recording:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _lastFile!,
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 30),
              
              // 帮助信息
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Instructions:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text('1. App will request microphone permission automatically'),
                      Text('2. Click "Start" to begin recording'),
                      Text('3. Click "Stop" to finish recording'),
                      Text('4. Use "Reset" if there are issues'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}