import 'package:flutter/material.dart';
import 'package:flutter_sound/flutter_sound.dart';

void main() => runApp(const SimpleTestApp());

class SimpleTestApp extends StatelessWidget {
  const SimpleTestApp({super.key});
  @override
  Widget build(BuildContext context) {
    return const MaterialApp(home: SimpleMicTest());
  }
}

class SimpleMicTest extends StatefulWidget {
  const SimpleMicTest({super.key});
  @override
  State<SimpleMicTest> createState() => _SimpleMicTestState();
}

class _SimpleMicTestState extends State<SimpleMicTest> {
  FlutterSoundRecorder? _recorder;
  String _status = 'Not initialized';
  String? _lastFile;

  @override
  void initState() {
    super.initState();
    _initRecorder();
  }

  Future<void> _initRecorder() async {
    setState(() => _status = 'Initializing...');
    
    try {
      // 创建并打开录音器（Flutter Sound 会自动处理权限）
      _recorder = FlutterSoundRecorder();
      await _recorder!.openRecorder();
      
      setState(() => _status = 'Ready to record');
      print('✅ Recorder initialized and opened');
      
    } catch (e) {
      setState(() => _status = 'Init failed: $e');
      print('❌ Init error: $e');
      
      // 如果是权限问题，给出提示
      if (e.toString().contains('permission') || e.toString().contains('Permission')) {
        setState(() => _status = 'Permission denied - Please allow microphone access in System Preferences');
      }
    }
  }

  Future<void> _startRecording() async {
    if (_recorder == null) {
      setState(() => _status = 'Recorder not ready');
      return;
    }

    if (_recorder!.isRecording) {
      setState(() => _status = 'Already recording');
      return;
    }

    try {
      setState(() => _status = 'Starting...');
      
      final fileName = 'test_${DateTime.now().millisecondsSinceEpoch}.wav';
      await _recorder!.startRecorder(
        toFile: fileName,
        codec: Codec.pcm16WAV,
        sampleRate: 16000,
        numChannels: 1,
      );
      
      _lastFile = fileName;
      setState(() => _status = 'Recording: $fileName');
      print('🎙️ Recording started: $fileName');
      
    } catch (e) {
      setState(() => _status = 'Start failed: $e');
      print('❌ Start error: $e');
    }
  }

  Future<void> _stopRecording() async {
    if (_recorder == null || !_recorder!.isRecording) {
      setState(() => _status = 'Not recording');
      return;
    }

    try {
      setState(() => _status = 'Stopping...');
      
      final path = await _recorder!.stopRecorder();
      setState(() => _status = 'Stopped. File: $path');
      print('✅ Recording stopped: $path');
      
    } catch (e) {
      setState(() => _status = 'Stop failed: $e');
      print('❌ Stop error: $e');
    }
  }

  @override
  void dispose() {
    _recorder?.closeRecorder();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isRecording = _recorder?.isRecording ?? false;
    final isReady = _recorder != null;
    
    return Scaffold(
      appBar: AppBar(title: const Text('Simple Mic Test')),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isRecording ? Icons.mic : Icons.mic_none,
                size: 64,
                color: isRecording ? Colors.red : Colors.grey,
              ),
              const SizedBox(height: 20),
              Text(
                'Status: $_status',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 20),
              Text(
                'Recorder Ready: $isReady',
                style: TextStyle(
                  color: isReady ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: isReady && !isRecording ? _startRecording : null,
                child: const Text('Start Recording'),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: isRecording ? _stopRecording : null,
                child: const Text('Stop Recording'),
              ),
              const SizedBox(height: 20),
              if (_lastFile != null)
                Text(
                  'Last file: $_lastFile',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
            ],
          ),
        ),
      ),
    );
  }
}