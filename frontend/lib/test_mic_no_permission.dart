import 'package:flutter/material.dart';
import 'package:flutter_sound/flutter_sound.dart';

void main() => runApp(const NoPermissionTestApp());

class NoPermissionTestApp extends StatelessWidget {
  const NoPermissionTestApp({super.key});
  @override
  Widget build(BuildContext context) {
    return const MaterialApp(home: NoPermissionMicTest());
  }
}

class NoPermissionMicTest extends StatefulWidget {
  const NoPermissionMicTest({super.key});
  @override
  State<NoPermissionMicTest> createState() => _NoPermissionMicTestState();
}

class _NoPermissionMicTestState extends State<NoPermissionMicTest> {
  FlutterSoundRecorder? _recorder;
  String _status = 'Not initialized';
  String? _lastFile;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initRecorder();
  }

  Future<void> _initRecorder() async {
    setState(() => _status = 'Initializing recorder...');
    
    try {
      // 直接创建并打开录音器，让 Flutter Sound 处理权限
      _recorder = FlutterSoundRecorder();
      
      setState(() => _status = 'Opening recorder...');
      await _recorder!.openRecorder();
      
      _isInitialized = true;
      setState(() => _status = 'Ready to record! (Permission will be requested on first recording)');
      print('✅ Recorder initialized successfully');
      
    } catch (e) {
      setState(() => _status = 'Failed to initialize: $e');
      print('❌ Init error: $e');
      
      // 提供用户指导
      if (e.toString().toLowerCase().contains('permission')) {
        setState(() => _status = 'Permission needed - Please allow microphone access when prompted');
      }
    }
  }

  Future<void> _startRecording() async {
    if (!_isInitialized || _recorder == null) {
      setState(() => _status = 'Recorder not ready');
      return;
    }

    if (_recorder!.isRecording) {
      setState(() => _status = 'Already recording');
      return;
    }

    try {
      setState(() => _status = 'Starting recording...');
      
      final fileName = 'test_${DateTime.now().millisecondsSinceEpoch}.wav';
      
      // 开始录音 - 这里会触发权限请求
      await _recorder!.startRecorder(
        toFile: fileName,
        codec: Codec.pcm16WAV,
        sampleRate: 16000,
        numChannels: 1,
      );
      
      _lastFile = fileName;
      setState(() => _status = '🎙️ Recording: $fileName');
      print('🎙️ Recording started: $fileName');
      
    } catch (e) {
      setState(() => _status = 'Recording failed: $e');
      print('❌ Recording error: $e');
      
      // 权限相关错误的特殊处理
      if (e.toString().toLowerCase().contains('permission')) {
        setState(() => _status = 'Permission denied - Please go to System Preferences > Security & Privacy > Microphone and allow access');
      }
    }
  }

  Future<void> _stopRecording() async {
    if (!_recorder!.isRecording) {
      setState(() => _status = 'Not recording');
      return;
    }

    try {
      setState(() => _status = 'Stopping recording...');
      
      final path = await _recorder!.stopRecorder();
      setState(() => _status = '✅ Recording saved: ${path ?? _lastFile}');
      print('✅ Recording stopped: $path');
      
    } catch (e) {
      setState(() => _status = 'Stop failed: $e');
      print('❌ Stop error: $e');
    }
  }

  Future<void> _resetRecorder() async {
    setState(() => _status = 'Resetting...');
    
    try {
      if (_recorder != null) {
        if (_recorder!.isRecording) {
          await _recorder!.stopRecorder();
        }
        await _recorder!.closeRecorder();
      }
      
      _recorder = null;
      _isInitialized = false;
      _lastFile = null;
      
      // 重新初始化
      await _initRecorder();
      
    } catch (e) {
      setState(() => _status = 'Reset failed: $e');
      print('❌ Reset error: $e');
    }
  }

  @override
  void dispose() {
    if (_recorder != null) {
      if (_recorder!.isRecording) {
        _recorder!.stopRecorder();
      }
      _recorder!.closeRecorder();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isRecording = _recorder?.isRecording ?? false;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('No Permission Plugin Test'),
        backgroundColor: Colors.blue,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 状态图标
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isRecording ? Colors.red.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                ),
                child: Icon(
                  isRecording ? Icons.mic : Icons.mic_none,
                  size: 64,
                  color: isRecording ? Colors.red : Colors.grey,
                ),
              ),
              
              const SizedBox(height: 30),
              
              // 状态文本
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _status,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // 录音器状态
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _isInitialized ? Icons.check_circle : Icons.error,
                    color: _isInitialized ? Colors.green : Colors.red,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Recorder: ${_isInitialized ? "Ready" : "Not Ready"}',
                    style: TextStyle(
                      color: _isInitialized ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 30),
              
              // 控制按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: _isInitialized && !isRecording ? _startRecording : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Start'),
                  ),
                  ElevatedButton(
                    onPressed: isRecording ? _stopRecording : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Stop'),
                  ),
                  ElevatedButton(
                    onPressed: !isRecording ? _resetRecorder : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Reset'),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // 文件信息
              if (_lastFile != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'Last Recording:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _lastFile!,
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 30),
              
              // 帮助信息
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Instructions:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text('1. Click "Start" to begin recording'),
                      Text('2. Allow microphone access when prompted'),
                      Text('3. Click "Stop" to finish recording'),
                      Text('4. Use "Reset" if there are issues'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}