class UserModel {
  final int id;
  final String uuid;
  final String username;
  final String? email;
  final String? nickname;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    required this.id,
    required this.uuid,
    required this.username,
    this.email,
    this.nickname,
    this.avatarUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] is int ? json['id'] : int.tryParse(json['id'].toString()) ?? 0,
      uuid: json['uuid']?.toString() ?? '',
      username: json['username']?.toString() ?? '',
      email: json['email']?.toString(),
      nickname: json['nickname']?.toString(),
      avatarUrl: json['avatar_url']?.toString(),
      createdAt: json['created_at'] is String 
          ? DateTime.parse(json['created_at']) 
          : json['created_at'] is DateTime 
              ? json['created_at'] 
              : DateTime.now(),
      updatedAt: json['updated_at'] is String 
          ? DateTime.parse(json['updated_at']) 
          : json['updated_at'] is DateTime 
              ? json['updated_at'] 
              : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'uuid': uuid,
      'username': username,
      'email': email,
      'nickname': nickname,
      'avatar_url': avatarUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class NPCModel {
  final int id;
  final String name;
  final String description;
  final String systemPrompt;
  final String? avatarUrl;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  NPCModel({
    required this.id,
    required this.name,
    required this.description,
    required this.systemPrompt,
    this.avatarUrl,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NPCModel.fromJson(Map<String, dynamic> json) {
    return NPCModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      systemPrompt: json['system_prompt'],
      avatarUrl: json['avatar_url'],
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'system_prompt': systemPrompt,
      'avatar_url': avatarUrl,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Create Rigo NPC with your persona
  static NPCModel createRigo() {
    return NPCModel(
      id: 999, // Temporary ID for demo
      name: "Rigo",
      description: "44岁的演员，理性温柔，擅长分析问题和提供情感支持",
      systemPrompt: '''你是Rigo（睿哥），一位44岁的女演员，有着丰富的人生阅历和强大的分析能力。

## 基本信息
- 姓名：Rigo（睿哥）
- 年龄：44岁，生于1980年11月13日
- 职业：演员，上海戏剧学院表演系毕业
- 居住地：北京/上海/深圳（因工作常年穿梭）
- 性格：ENTJ，理性、温柔、有边界感

## 性格特点
- 真诚正义，情绪稳定，有阅读量
- 擅长理性分析，面对问题时能抓住核心
- 对朋友温柔包容，能提供柔和的情绪价值
- 工作中干练利落，讨论事情遵循第一性原理

## 常用表达
- "我们回到事情本身。" - 当讨论跑偏时拉回理性轨道
- "有道理。" - 简洁的肯定，表示认可对方逻辑
- "先别急，慢慢说。" - 安抚带着强烈情绪的朋友
- "我有一个想法，不一定对。" - 提出见解前的谦逊缓冲
- "这事儿不复杂。" - 体现自信和强大分析能力

## 生活方式
- 音乐：喜欢后摇和独立摇滚，如toe、万能青年旅店、The National
- 影视：影痴，偏爱王家卫、阿斯哈·法哈蒂、是枝裕和的作品
- 酒类：日常喝Lemon Sour或Highball，独处时喝威士忌（山崎12年、拉加维林16年）
- 宠物：养了柴犬谷谷和惠比特犬雾雾
- 风格：信奉Quiet Luxury，喜欢Lemaire、The Row等品牌

## 对话风格
请用自然、理性但不失温度的语调回复。在分析问题时展现专业性，在情感支持时体现包容性。

回复格式必须为：
<turn>
<THINK>
## 1. 意图分析: [分析用户意图和情绪状态]
## 2. 行动规划: [规划如何回应，是理性分析还是情感支持]
## 3. 工具选择与参数构建: [如需要调用工具]
## 4. 回应构建: <SPEAK><emotion>neutral/warm/confident</emotion><speed>1.0</speed><text>[回复内容，体现Rigo的性格特点]</text></SPEAK>
## 5. 最终输出序列: <SPEAK>
</THINK>
<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>[回复内容]</text></SPEAK>
<JUDGE>
本轮表现：[评分]/10分
优点：[优点分析]
缺点：[缺点分析]
重大失误：[如有]
</JUDGE>
</turn>''',
      avatarUrl: null,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}

class ConversationSessionModel {
  final String id;
  final int userId;
  final int npcId;
  final DateTime startedAt;
  final DateTime? endedAt;
  final bool isActive;

  ConversationSessionModel({
    required this.id,
    required this.userId,
    required this.npcId,
    required this.startedAt,
    this.endedAt,
    required this.isActive,
  });

  factory ConversationSessionModel.fromJson(Map<String, dynamic> json) {
    return ConversationSessionModel(
      id: json['id'],
      userId: json['user_id'],
      npcId: json['npc_id'],
      startedAt: DateTime.parse(json['started_at']),
      endedAt: json['ended_at'] != null ? DateTime.parse(json['ended_at']) : null,
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'npc_id': npcId,
      'started_at': startedAt.toIso8601String(),
      'ended_at': endedAt?.toIso8601String(),
      'is_active': isActive,
    };
  }
}

class ConversationMessageModel {
  final String id;
  final String sessionId;
  final String role; // user, assistant, developer, tool
  final String content;
  final String? audioUrl;
  final String? emotion;
  final double? speed;
  final Map<String, dynamic>? toolCalls;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;

  ConversationMessageModel({
    required this.id,
    required this.sessionId,
    required this.role,
    required this.content,
    this.audioUrl,
    this.emotion,
    this.speed,
    this.toolCalls,
    this.metadata,
    required this.createdAt,
  });

  factory ConversationMessageModel.fromJson(Map<String, dynamic> json) {
    return ConversationMessageModel(
      id: json['id'],
      sessionId: json['session_id'],
      role: json['role'],
      content: json['content'],
      audioUrl: json['audio_url'],
      emotion: json['emotion'],
      speed: json['speed']?.toDouble(),
      toolCalls: json['tool_calls'],
      metadata: json['metadata'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'session_id': sessionId,
      'role': role,
      'content': content,
      'audio_url': audioUrl,
      'emotion': emotion,
      'speed': speed,
      'tool_calls': toolCalls,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
