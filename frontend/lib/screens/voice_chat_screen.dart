import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/user_model.dart';
import '../services/memory_auth_service.dart';
import '../services/voice_chat_service_native_final.dart';
import '../utils/permission_helper.dart';
import '../config/app_config.dart';

class VoiceChatScreen extends ConsumerStatefulWidget {
  final NPCModel npc;

  const VoiceChatScreen({
    super.key,
    required this.npc,
  });

  @override
  ConsumerState<VoiceChatScreen> createState() => _VoiceChatScreenState();
}

class _VoiceChatScreenState extends ConsumerState<VoiceChatScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  StreamSubscription? _messageSubscription;
  StreamSubscription? _transcriptionSubscription;
  StreamSubscription? _audioChunkSubscription;

  String _currentTranscription = '';
  String _assistantText = '';
  String _statusMessage = 'Tap to start talking';

  // Tools info chip state
  bool _showToolsChip = false;
  String _toolsChipText = '';
  Timer? _toolsChipTimer;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeVoiceChat();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeOut,
    ));
  }

  Future<void> _initializeVoiceChat() async {
    // 延迟到下一帧执行，避免在initState中直接调用provider
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final voiceChatService = ref.read(voiceChatServiceProvider.notifier);
        final authState = ref.read(memoryAuthServiceProvider);

        await authState.when(
          data: (user) async {
            if (user != null) {
              setState(() {
                _statusMessage = 'Connecting...';
              });

              try {
                await voiceChatService.connect(user);
                print('✅ WebSocket connected for user: ${user.id}');

                await voiceChatService.startSession(widget.npc.id);
                print('✅ Session started for NPC: ${widget.npc.id}');

                if (mounted) {
                  setState(() {
                    _statusMessage = 'Ready to chat';
                  });
                }
              } catch (e) {
                print('❌ Connection error: $e');
                if (mounted) {
                  setState(() {
                    _statusMessage = 'Connection error: ${e.toString()}';
                  });
                }
              }
            }
          },
          loading: () {
            setState(() {
              _statusMessage = 'Loading...';
            });
          },
          error: (error, stack) {
            setState(() {
              _statusMessage = 'Connection failed';
            });
          },
        );

        // Listen to voice chat messages
        _messageSubscription = voiceChatService.messageStream.listen((message) {
          if (mounted) {
            setState(() {
              // 根据消息类型更新状态
              switch (message.type) {
                case 'response_complete':
                  _statusMessage = 'Tap to start talking';
                  _fetchLatestAssistantText();
                  break;
                case 'tools_info':
                  // Show chip for tools usage and auto-hide
                  final toolsUsed = (message.data['tools_used'] ?? []) as List;
                  final enhanced = message.data['enhanced_mode'] == true;
                  _toolsChipText = enhanced
                      ? (toolsUsed.isNotEmpty ? '使用了工具: ${toolsUsed.join(', ')}' : '增强模式处理')
                      : '工具信息';
                  _showToolsChip = true;
                  _toolsChipTimer?.cancel();
                  _toolsChipTimer = Timer(const Duration(seconds: 3), () {
                    if (mounted) setState(() => _showToolsChip = false);
                  });
                  break;
                case 'system':
                  if (message.data.toString().contains('Session started')) {
                    _statusMessage = 'Ready to chat';
                  }
                  break;
                case 'error':
                  _statusMessage = 'Error: ${message.data}';
                  // 如果是权限错误，显示权限设置指南
                  if (message.data.toString().contains('麦克风权限') ||
                      message.data.toString().contains('permission')) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      PermissionHelper.showPermissionInstructions(context);
                    });
                  }
                  break;
              }
            });
          }
        });

        // Listen to transcriptions
        _transcriptionSubscription = voiceChatService.transcriptionStream.listen((text) {
          if (mounted) {
            setState(() {
              _currentTranscription = text;
            });
          }
        });

        // Listen to audio chunks
        _audioChunkSubscription = voiceChatService.audioChunkStream.listen((audioData) {
          print('🎵 Received audio chunk: ${audioData.length} bytes');
          // Audio playback is handled in the service
        });
      } catch (e) {
        if (mounted) {
          setState(() {
            _statusMessage = 'Failed to initialize: ${e.toString()}';
          });
        }
      }
    });
  }

  Future<void> _fetchLatestAssistantText() async {
    try {
      final voiceChatService = ref.read(voiceChatServiceProvider.notifier);
      final sessionId = voiceChatService.currentSessionId;
      if (sessionId == null) return;

      final uri = Uri.parse('${AppConfig.backendUrl}/sessions/$sessionId/context?limit=5');
      final resp = await http.get(uri);
      if (resp.statusCode == 200) {
        final data = json.decode(resp.body) as Map<String, dynamic>;
        final List messages = (data['messages'] as List?) ?? [];
        // 找到最后一条assistant消息
        for (var i = messages.length - 1; i >= 0; i--) {
          final msg = messages[i] as Map<String, dynamic>;
          if (msg['role'] == 'assistant') {
            setState(() {
              _assistantText = (msg['content'] ?? '').toString();
            });
            break;
          }
        }
      }
    } catch (_) {
      // ignore fetch errors for now
    }
  }

  void _startRecording() {
    try {
      print('🎤 Starting native recording...');
      final voiceChatService = ref.read(voiceChatServiceProvider.notifier);
      voiceChatService.startRecording();

      _pulseController.repeat(reverse: true);
      _waveController.repeat();

      setState(() {
        _statusMessage = 'Listening with native macOS audio...';
        _currentTranscription = '';
      });
      print('✅ Native recording started');
    } catch (e) {
      print('❌ Failed to start native recording: $e');
      setState(() {
        _statusMessage = 'Failed to start recording: ${e.toString()}';
      });
    }
  }

  void _stopRecording() {
    try {
      print('🛑 Stopping native recording...');
      final voiceChatService = ref.read(voiceChatServiceProvider.notifier);
      voiceChatService.stopRecording();

      _pulseController.stop();
      _waveController.stop();

      setState(() {
        _statusMessage = 'Processing native audio...';
      });
      print('✅ Native recording stopped, processing...');

      // 设置一个超时，如果120秒后还在Processing状态，就重置
      Timer(const Duration(seconds: 120), () {
        if (mounted && _statusMessage.contains('Processing')) {
          // 显示超时对话框
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('Processing Timeout'),
                content: const Text('No response from server. Please try again.'),
                actions: <Widget>[
                  TextButton(
                    child: const Text('OK'),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              );
            },
          );
          print('⏰ Processing timeout, resetting to ready state');
          setState(() {
            _statusMessage = 'Tap to start talking';
          });
        }
      });
    } catch (e) {
      print('❌ Failed to stop native recording: $e');
      setState(() {
        _statusMessage = 'Failed to stop recording: ${e.toString()}';
      });
    }
  }

  void _endSession() async {
    final voiceChatService = ref.read(voiceChatServiceProvider.notifier);
    await voiceChatService.endSession();
    voiceChatService.disconnect();

    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _messageSubscription?.cancel();
    _transcriptionSubscription?.cancel();
    _audioChunkSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final voiceChatState = ref.watch(voiceChatServiceProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      body: SafeArea(
        child: Column(
          children: [
            // Custom App Bar
            _buildAppBar(),

            // Main Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height -
                                 MediaQuery.of(context).padding.top -
                                 MediaQuery.of(context).padding.bottom - 60, // AppBar height
                    ),
                    child: IntrinsicHeight(
                      child: Column(
                        children: [
                          const SizedBox(height: 20),

                          // NPC Avatar Section
                          _buildNPCSection(),

                          const SizedBox(height: 40),

                          // Voice Visualization
                          _buildVoiceVisualization(voiceChatState),

                          const SizedBox(height: 20),

                          // Status Message
                          _buildStatusMessage(),

                          // Transcription Section
                          if (_currentTranscription.isNotEmpty)
                            _buildTranscriptionSection(),

                          // Assistant Text Section (LLM)
                          if (_assistantText.isNotEmpty)
                            _buildAssistantSection(),

                          if (_currentTranscription.isEmpty && _assistantText.isEmpty)
                            const SizedBox(height: 60), // 减少固定空间

                          const Spacer(),

                          // Control Button
                          _buildControlButton(voiceChatState),

                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
            onPressed: _endSession,
          ),
          Expanded(
            child: Text(
              widget.npc.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.help_outline, color: Colors.white, size: 20),
                onPressed: () => PermissionHelper.showPermissionInstructions(context),
                tooltip: '权限设置帮助',
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: const Text(
                  'BETA',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNPCSection() {
    return Column(
      mainAxisSize: MainAxisSize.min, // 使用最小空间
      children: [
        // NPC Avatar with glow effect
        Container(
          width: 80, // 减小头像尺寸
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(40),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.3),
                blurRadius: 15,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFF1A1A1A),
              borderRadius: BorderRadius.circular(40),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: widget.npc.avatarUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(38),
                    child: Image.network(
                      widget.npc.avatarUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                        const Icon(Icons.smart_toy, size: 32, color: Colors.white),
                    ),
                  )
                : const Icon(Icons.smart_toy, size: 32, color: Colors.white),
          ),
        ),

        const SizedBox(height: 12), // 减少间距

        // NPC Name
        Text(
          widget.npc.name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20, // 稍微减小字体
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildVoiceVisualization(VoiceChatState state) {
    // Define a flag to disable interaction when processing
    final bool isProcessing = state == VoiceChatState.processing;

    // 根据屏幕尺寸调整大小
    final screenHeight = MediaQuery.of(context).size.height;
    final visualizationSize = screenHeight < 700 ? 160.0 : 180.0;

    return Center(
      child: SizedBox(
        width: visualizationSize,
        height: visualizationSize,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Outer ripple effect
            if (state == VoiceChatState.recording)
              AnimatedBuilder(
                animation: _waveAnimation,
                builder: (context, child) {
                  return Container(
                    width: visualizationSize + (_waveAnimation.value * 60),
                    height: visualizationSize + (_waveAnimation.value * 60),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(visualizationSize),
                      border: Border.all(
                        color: Colors.blue.withOpacity(0.3 - (_waveAnimation.value * 0.3)),
                        width: 2,
                      ),
                    ),
                  );
                },
              ),

            // Middle ripple
            if (state == VoiceChatState.recording)
              AnimatedBuilder(
                animation: _waveAnimation,
                builder: (context, child) {
                  return Container(
                    width: (visualizationSize * 0.8) + (_waveAnimation.value * 40),
                    height: (visualizationSize * 0.8) + (_waveAnimation.value * 40),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(visualizationSize * 0.8),
                      border: Border.all(
                        color: Colors.blue.withOpacity(0.5 - (_waveAnimation.value * 0.4)),
                        width: 1,
                      ),
                    ),
                  );
                },
              ),

            // Main voice button
            GestureDetector(
              // Disable gestures when processing
              onTapDown: isProcessing ? null : (_) => _startRecording(),
              onTapUp: isProcessing ? null : (_) => _stopRecording(),
              onTapCancel: isProcessing ? null : () => _stopRecording(),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: state == VoiceChatState.recording ? (visualizationSize * 0.7) : (visualizationSize * 0.6),
                height: state == VoiceChatState.recording ? (visualizationSize * 0.7) : (visualizationSize * 0.6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: state == VoiceChatState.recording
                        ? [Colors.red.withOpacity(0.8), Colors.red.withOpacity(0.6)]
                        : (isProcessing
                            ? [Colors.grey.withOpacity(0.5), Colors.grey.withOpacity(0.4)]
                            : [Colors.blue.withOpacity(0.8), Colors.blue.withOpacity(0.6)]),
                  ),
                  borderRadius: BorderRadius.circular(70),
                  boxShadow: [
                    BoxShadow(
                      color: (state == VoiceChatState.recording
                              ? Colors.red
                              : (isProcessing ? Colors.grey : Colors.blue))
                          .withOpacity(0.4),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: isProcessing
                    ? const SizedBox(
                        width: 40,
                        height: 40,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          strokeWidth: 3,
                        ),
                      )
                    : Icon(
                        state == VoiceChatState.recording ? Icons.stop : Icons.mic,
                        size: state == VoiceChatState.recording ? 50 : 45,
                        color: Colors.white,
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusMessage() {
    return SizedBox(
      height: 40,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Status text
          Text(
            _statusMessage,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
          // Tools chip overlay (top-right)
          Positioned(
            right: 0,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 250),
              opacity: _showToolsChip ? 1.0 : 0.0,
              child: _showToolsChip
                  ? Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.white.withOpacity(0.12)),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.build_circle_outlined, color: Colors.lightBlueAccent, size: 16),
                          const SizedBox(width: 6),
                          Text(
                            _toolsChipText,
                            style: const TextStyle(color: Colors.white, fontSize: 12),
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTranscriptionSection() {
    return Flexible(
      child: Container(
        constraints: const BoxConstraints(
          maxHeight: 60, // 减少最大高度
          minHeight: 40,
        ),
        margin: const EdgeInsets.only(top: 10),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
          ),
        ),
        child: SingleChildScrollView(
          child: Text(
            _currentTranscription,
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 13, // 稍微减小字体
              height: 1.3,
            ),
            textAlign: TextAlign.center,
            maxLines: 3, // 限制最大行数
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }
  Widget _buildControlButton(VoiceChatState state) {
    bool isRecording = state == VoiceChatState.recording;
    bool isProcessing = state == VoiceChatState.processing;

    return GestureDetector(
      // Disable gestures when processing
      onTapDown: isProcessing ? null : (_) => _startRecording(),
      onTapUp: isProcessing ? null : (_) => _stopRecording(),
      onTapCancel: isProcessing ? null : () => _stopRecording(),
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isRecording
                ? [Colors.red.withOpacity(0.8), Colors.red.withOpacity(0.6)]
                : (isProcessing
                    ? [Colors.grey.withOpacity(0.5), Colors.grey.withOpacity(0.4)]
                    : [Colors.blue.withOpacity(0.8), Colors.blue.withOpacity(0.6)]),
          ),
          boxShadow: [
            BoxShadow(
              color: (isRecording
                      ? Colors.red
                      : (isProcessing ? Colors.grey : Colors.blue))
                  .withOpacity(0.4),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: isProcessing
            ? const SizedBox(
                width: 30,
                height: 30,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 2,
                ),
              )
            : Icon(
                isRecording ? Icons.stop : Icons.mic,
                size: 35,
                color: Colors.white,
              ),
      ),
    );
  }

  Widget _buildAssistantSection() {
    return Flexible(
      child: Container(
        constraints: const BoxConstraints(
          maxHeight: 140,
          minHeight: 40,
        ),
        margin: const EdgeInsets.only(top: 10),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.06),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withOpacity(0.12),
          ),
        ),
        child: SingleChildScrollView(
          child: Text(
            _assistantText,
            style: TextStyle(
              color: Colors.white.withOpacity(0.95),
              fontSize: 13,
              height: 1.35,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

