import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/voice_chat_screen.dart';
import 'models/user_model.dart';

void main() {
  runApp(const ProviderScope(child: LayoutTestApp()));
}

class LayoutTestApp extends StatelessWidget {
  const LayoutTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Layout Fix Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const LayoutTestScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class LayoutTestScreen extends StatelessWidget {
  const LayoutTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Layout Fix Test'),
        backgroundColor: Colors.blue,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Test different screen sizes:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => VoiceChatScreen(
                      npc: NPCModel(
                        id: 1,
                        name: 'Test NPC',
                        description: 'Test NPC for layout testing',
                        systemPrompt: 'You are a test NPC',
                        voiceId: 'test',
                        isActive: true,
                        avatarUrl: null,
                      ),
                    ),
                  ),
                );
              },
              child: const Text('Test Voice Chat Layout'),
            ),
            const SizedBox(height: 10),
            const Text(
              'This will test the fixed layout on your current screen size.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}