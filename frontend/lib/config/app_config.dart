class AppConfig {
  // Supabase Configuration
  static const String supabaseUrl = 'http://*************:8000';
  static const String supabaseAnonKey = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiYW5vbiIsInJlZiI6InNicC13bGp2ajh6bWkxcXFvdmhoIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NTMxOTAzNjQsImV4cCI6MjA2ODc2NjM2NH0.A3O1gtaZRdTnpe1T7wHJzYSlw9UCqACVFFM35ju1J5Y';

  // Backend API Configuration
  static const String backendUrl = 'http://localhost:8000';
  static const String websocketUrl = 'ws://localhost:8000/ws';
  
  // WeChat Configuration
  static const String wechatAppId = 'YOUR_WECHAT_APP_ID';
  
  // Audio Configuration
  static const int sampleRate = 16000;
  static const int audioChunkSize = 1024;
  static const Duration recordingInterval = Duration(milliseconds: 100);
  
  // UI Configuration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const double borderRadius = 12.0;
  static const double padding = 16.0;
  
  // Voice Activity Detection
  static const double vadThreshold = 0.5;
  static const Duration minSpeechDuration = Duration(milliseconds: 500);
  static const Duration maxSilenceDuration = Duration(seconds: 2);
  
  // Audio Playback
  static const double defaultVolume = 1.0;
  static const Duration audioBufferSize = Duration(milliseconds: 200);
}
