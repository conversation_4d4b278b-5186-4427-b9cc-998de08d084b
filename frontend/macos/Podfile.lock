PODS:
  - app_links (1.0.0):
    - FlutterMacOS
  - flutter_secure_storage_macos (6.1.3):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `Flutter/ephemeral/.symlinks/plugins/app_links/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `Flutter/ephemeral/.symlinks/plugins/sign_in_with_apple/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - webview_flutter_wkwebview (from `Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin`)

EXTERNAL SOURCES:
  app_links:
    :path: Flutter/ephemeral/.symlinks/plugins/app_links/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sign_in_with_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/sign_in_with_apple/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  webview_flutter_wkwebview:
    :path: Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin

SPEC CHECKSUMS:
  app_links: 9028728e32c83a0831d9db8cf91c526d16cc5468
  flutter_secure_storage_macos: 7f45e30f838cf2659862a4e4e3ee1c347c2b3b54
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: 6673c03c9e3643f6c8d33601943fbfa9ae99f94e
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 54d867c82ac51cbd61b565781b9fada492027009

COCOAPODS: 1.16.2
