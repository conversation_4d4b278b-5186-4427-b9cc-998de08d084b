import Cocoa
import FlutterMacOS
import AVFoundation

class MainFlutterWindow: NSWindow {
  override func awakeFromNib() {
    let flutterViewController = FlutterViewController()
    let windowFrame = self.frame
    self.contentViewController = flutterViewController
    self.setFrame(windowFrame, display: true)

    RegisterGeneratedPlugins(registry: flutterViewController)
    
    // 注册自定义音频录制插件
    let audioPlugin = AudioRecorderPlugin()
    let channel = FlutterMethodChannel(name: "com.example.voice_chat_app/audio", binaryMessenger: flutterViewController.engine.binaryMessenger)
    channel.setMethodCallHandler(audioPlugin.handle)

    super.awakeFromNib()
  }
}

// 音频录制插件实现
class AudioRecorderPlugin: NSObject {
    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?
    private var isRecording = false
    private var currentFilePath: String?
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "hasPermission":
            hasPermission(result: result)
        case "requestPermission":
            requestPermission(result: result)
        case "startRecording":
            startRecording(call: call, result: result)
        case "stopRecording":
            stopRecording(result: result)
        case "playAudio":
            playAudio(call: call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func hasPermission(result: @escaping FlutterResult) {
        let status = AVCaptureDevice.authorizationStatus(for: .audio)
        result(status == .authorized)
    }
    
    private func requestPermission(result: @escaping FlutterResult) {
        AVCaptureDevice.requestAccess(for: .audio) { granted in
            DispatchQueue.main.async {
                result(granted)
            }
        }
    }
    
    private func startRecording(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard !isRecording else {
            result(FlutterError(code: "ALREADY_RECORDING", message: "Already recording", details: nil))
            return
        }
        
        let arguments = call.arguments as? [String: Any]
        let fileName = arguments?["fileName"] as? String ?? "recording.wav"
        let sampleRate = arguments?["sampleRate"] as? Double ?? 16000.0
        let channels = arguments?["channels"] as? Int ?? 1
        
        // 获取文档目录
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = documentsPath.appendingPathComponent(fileName)
        currentFilePath = audioFilename.path
        
        // 设置录音设置
        let settings = [
            AVFormatIDKey: Int(kAudioFormatLinearPCM),
            AVSampleRateKey: sampleRate,
            AVNumberOfChannelsKey: channels,
            AVLinearPCMBitDepthKey: 16,
            AVLinearPCMIsBigEndianKey: false,
            AVLinearPCMIsFloatKey: false
        ] as [String : Any]
        
        do {
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.isMeteringEnabled = true
            
            let success = audioRecorder?.record() ?? false
            if success {
                isRecording = true
                result(true)
            } else {
                result(FlutterError(code: "RECORDING_FAILED", message: "Failed to start recording", details: nil))
            }
        } catch {
            result(FlutterError(code: "RECORDING_ERROR", message: error.localizedDescription, details: nil))
        }
    }
    
    private func stopRecording(result: @escaping FlutterResult) {
        guard isRecording else {
            result(FlutterError(code: "NOT_RECORDING", message: "Not currently recording", details: nil))
            return
        }
        
        audioRecorder?.stop()
        isRecording = false
        
        if let filePath = currentFilePath {
            do {
                let fileAttributes = try FileManager.default.attributesOfItem(atPath: filePath)
                let fileSize = fileAttributes[.size] as? Int64 ?? 0
                
                let resultData: [String: Any] = [
                    "path": filePath,
                    "size": fileSize
                ]
                result(resultData)
            } catch {
                result(["path": filePath, "size": 0])
            }
        } else {
            result(FlutterError(code: "NO_FILE", message: "No recording file found", details: nil))
        }
        
        currentFilePath = nil
        audioRecorder = nil
    }
    
    private func playAudio(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any],
              let filePath = arguments["filePath"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "File path is required", details: nil))
            return
        }
        
        let fileURL = URL(fileURLWithPath: filePath)
        
        do {
            // 停止当前播放（如果有）
            audioPlayer?.stop()
            
            // 创建新的播放器
            audioPlayer = try AVAudioPlayer(contentsOf: fileURL)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            
            let success = audioPlayer?.play() ?? false
            if success {
                print("🎵 Audio playback started: \(filePath)")
                result(true)
            } else {
                result(FlutterError(code: "PLAYBACK_FAILED", message: "Failed to start audio playback", details: nil))
            }
            
        } catch {
            result(FlutterError(code: "PLAYBACK_ERROR", message: error.localizedDescription, details: nil))
        }
    }
}

extension AudioRecorderPlugin: AVAudioRecorderDelegate {
    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        isRecording = false
        print("Recording finished successfully: \(flag)")
    }
    
    func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        isRecording = false
        print("Recording error: \(error?.localizedDescription ?? "Unknown error")")
    }
}

extension AudioRecorderPlugin: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        print("Audio playback finished successfully: \(flag)")
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        print("Audio playback error: \(error?.localizedDescription ?? "Unknown error")")
    }
}
