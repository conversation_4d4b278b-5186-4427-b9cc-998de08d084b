// 真实的Web Audio API录音实现
class RealAudioRecorder {
    constructor() {
        this.mediaRecorder = null;
        this.audioStream = null;
        this.audioChunks = [];
        this.isRecording = false;
        this.onDataAvailable = null;
        this.onError = null;
    }

    async initialize() {
        try {
            console.log('🎤 Initializing real audio recorder...');
            
            // 请求麦克风权限
            this.audioStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            
            console.log('✅ Microphone access granted');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize audio recorder:', error);
            if (this.onError) {
                this.onError('Microphone access denied: ' + error.message);
            }
            return false;
        }
    }

    async startRecording() {
        if (!this.audioStream) {
            const initialized = await this.initialize();
            if (!initialized) return false;
        }

        try {
            console.log('🎤 Starting real recording...');
            
            this.audioChunks = [];
            this.mediaRecorder = new MediaRecorder(this.audioStream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                    console.log('🎵 Real audio chunk received:', event.data.size, 'bytes');
                    
                    // 转换为PCM并发送
                    this.convertAndSendChunk(event.data);
                }
            };

            this.mediaRecorder.onerror = (event) => {
                console.error('❌ MediaRecorder error:', event.error);
                if (this.onError) {
                    this.onError('Recording error: ' + event.error);
                }
            };

            this.mediaRecorder.start(100); // 每100ms一个chunk
            this.isRecording = true;
            console.log('✅ Real recording started');
            return true;
        } catch (error) {
            console.error('❌ Failed to start recording:', error);
            if (this.onError) {
                this.onError('Failed to start recording: ' + error.message);
            }
            return false;
        }
    }

    async convertAndSendChunk(audioBlob) {
        try {
            // 将WebM音频转换为PCM数据
            const arrayBuffer = await audioBlob.arrayBuffer();
            const audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000
            });
            
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            const pcmData = audioBuffer.getChannelData(0); // 获取单声道数据
            
            // 转换为16位PCM
            const pcm16 = new Int16Array(pcmData.length);
            for (let i = 0; i < pcmData.length; i++) {
                pcm16[i] = Math.max(-32768, Math.min(32767, pcmData[i] * 32767));
            }
            
            // 转换为字节数组
            const bytes = new Uint8Array(pcm16.buffer);
            
            console.log('🔄 Converted audio chunk:', bytes.length, 'bytes PCM');
            
            if (this.onDataAvailable) {
                this.onDataAvailable(bytes);
            }
            
        } catch (error) {
            console.error('❌ Failed to convert audio chunk:', error);
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            console.log('🛑 Stopping real recording...');
            this.mediaRecorder.stop();
            this.isRecording = false;
            console.log('✅ Real recording stopped');
        }
    }

    cleanup() {
        if (this.audioStream) {
            this.audioStream.getTracks().forEach(track => track.stop());
            this.audioStream = null;
        }
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.isRecording = false;
        console.log('🧹 Audio recorder cleaned up');
    }
}

// 真实的音频播放器
class RealAudioPlayer {
    constructor() {
        this.audioContext = null;
        this.isPlaying = false;
    }

    async initialize() {
        try {
            console.log('🔊 Initializing real audio player...');
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000
            });
            console.log('✅ Audio player initialized');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize audio player:', error);
            return false;
        }
    }

    async playPCMData(pcmBytes) {
        if (!this.audioContext) {
            const initialized = await this.initialize();
            if (!initialized) return false;
        }

        try {
            console.log('🔊 Playing real audio:', pcmBytes.length, 'bytes');
            
            // 将字节数组转换为Float32Array
            const pcm16 = new Int16Array(pcmBytes.buffer);
            const floatData = new Float32Array(pcm16.length);
            
            for (let i = 0; i < pcm16.length; i++) {
                floatData[i] = pcm16[i] / 32768.0;
            }
            
            // 创建音频缓冲区
            const audioBuffer = this.audioContext.createBuffer(1, floatData.length, 16000);
            audioBuffer.getChannelData(0).set(floatData);
            
            // 播放音频
            const source = this.audioContext.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(this.audioContext.destination);
            
            this.isPlaying = true;
            source.onended = () => {
                this.isPlaying = false;
                console.log('✅ Audio playback completed');
            };
            
            source.start();
            console.log('🎵 Real audio playback started');
            return true;
            
        } catch (error) {
            console.error('❌ Failed to play audio:', error);
            this.isPlaying = false;
            return false;
        }
    }

    stop() {
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        this.isPlaying = false;
        console.log('🛑 Audio player stopped');
    }
}

// 全局实例
window.realAudioRecorder = new RealAudioRecorder();
window.realAudioPlayer = new RealAudioPlayer();

console.log('🎵 Real audio system loaded');