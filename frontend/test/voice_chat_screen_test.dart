import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voice_chat_app/screens/voice_chat_screen.dart';
import 'package:voice_chat_app/models/user_model.dart';

void main() {
  group('VoiceChatScreen', () {
    late Widget testWidget;
    late NPCModel testNPC;

    setUp(() {
      testNPC = NPCModel.createRigo();

      testWidget = MaterialApp(
        home: Scaffold(
          body: ProviderScope(
            child: VoiceChatScreen(npc: testNPC),
          ),
        ),
      );
    });

    testWidgets('should display NPC information', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Rigo'), findsOneWidget);
      expect(find.text('44岁的演员，理性温柔，擅长分析问题和提供情感支持'), findsOneWidget);
    });

    testWidgets('should display voice chat controls', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Assert
      expect(find.byIcon(Icons.mic), findsOneWidget); // Microphone button
      expect(find.text('Tap to start talking'), findsOneWidget);
    });

    testWidgets('should show status messages', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Tap to start talking'), findsOneWidget);
    });

    testWidgets('should display visualization elements', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Assert
      // Check for the main visualization circle
      expect(find.byType(Container), findsWidgets); // Multiple containers including visualization
    });

    testWidgets('should have back button', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Assert
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('should display transcription section', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Your voice will appear here'), findsOneWidget);
    });
  });
}
