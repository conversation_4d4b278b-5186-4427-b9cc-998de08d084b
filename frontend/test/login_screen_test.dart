import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voice_chat_app/screens/login_screen.dart';
import 'package:voice_chat_app/services/auth_service.dart';

void main() {
  group('LoginScreen', () {
    late Widget testWidget;

    setUp(() {
      testWidget = MaterialApp(
        home: Scaffold(
          body: ProviderScope(
            child: LoginScreen(),
          ),
        ),
      );
    });

    testWidgets('should display login form by default', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);

      // Act
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Voice Chat'), findsOneWidget);
      expect(find.text('Username'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Login'), findsOneWidget);
      expect(find.text("Don't have an account? Register"), findsOneWidget);
    });

    testWidgets('should toggle between login and register modes', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text("Don't have an account? Register"));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Register'), findsOneWidget);
      expect(find.text('Already have an account? Login'), findsOneWidget);

      // Act - toggle back
      await tester.tap(find.text('Already have an account? Login'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Login'), findsOneWidget);
      expect(find.text("Don't have an account? Register"), findsOneWidget);
    });

    testWidgets('should show additional fields in register mode', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text("Don't have an account? Register"));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Email (optional)'), findsOneWidget);
      expect(find.text('Nickname (optional)'), findsOneWidget);
    });

    testWidgets('should validate username field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Act
      await tester.enterText(find.byType(TextFormField).at(0), ''); // Username field
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Username is required'), findsOneWidget);

      // Act - enter short username
      await tester.enterText(find.byType(TextFormField).at(0), 'ab');
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Username must be at least 3 characters'), findsOneWidget);
    });

    testWidgets('should validate password field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Act
      await tester.enterText(find.byType(TextFormField).at(1), ''); // Password field
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Password is required'), findsOneWidget);

      // Act - enter short password
      await tester.enterText(find.byType(TextFormField).at(1), '123');
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Password must be at least 6 characters'), findsOneWidget);
    });

    testWidgets('should validate email field in register mode', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Act - switch to register mode
      await tester.tap(find.text("Don't have an account? Register"));
      await tester.pumpAndSettle();

      // Act - enter invalid email
      await tester.enterText(find.byType(TextFormField).at(2), 'invalid-email'); // Email field
      await tester.tap(find.text('Register'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('should toggle password visibility', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      final passwordField = find.byType(TextFormField).at(1);
      final visibilityIcon = find.byIcon(Icons.visibility);

      // Act
      await tester.tap(visibilityIcon);
      await tester.pumpAndSettle();

      // Assert
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);

      // Act - toggle back
      await tester.tap(find.byIcon(Icons.visibility_off));
      await tester.pumpAndSettle();

      // Assert
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });

    testWidgets('should show guest login option', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Continue as Guest'), findsOneWidget);
    });
  });
}
