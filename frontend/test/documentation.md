# Frontend Testing Documentation

## Overview

This document provides comprehensive information about the frontend testing strategy for the Voice Chat App. The tests are designed to ensure the reliability and functionality of all core features including authentication, context handling, and API interactions.

## Test Structure

The frontend tests are organized into the following categories:

### 1. Unit Tests
- **Authentication Service Tests** (`auth_service_test.dart`)
- **Voice Chat Service Tests** (`voice_chat_service_test.dart`)

### 2. Widget Tests
- **Login Screen Tests** (`login_screen_test.dart`)
- **NPC Selection Screen Tests** (`npc_selection_screen_test.dart`)
- **Voice Chat Screen Tests** (`voice_chat_screen_test.dart`)

### 3. Integration Tests
- **App Flow Tests** (`widget_test.dart`)

## Running Tests

### Prerequisites

Before running tests, ensure you have:
1. Flutter SDK installed
2. All dependencies installed (`flutter pub get`)

### Running All Tests

```bash
cd frontend
flutter test
```

### Running Specific Test Files

```bash
# Run authentication service tests
flutter test test/auth_service_test.dart

# Run voice chat service tests
flutter test test/voice_chat_service_test.dart

# Run login screen tests
flutter test test/login_screen_test.dart

# Run NPC selection screen tests
flutter test test/npc_selection_screen_test.dart

# Run voice chat screen tests
flutter test test/voice_chat_screen_test.dart

# Run integration tests
flutter test test/widget_test.dart
```

### Running Tests with Coverage

```bash
flutter test --coverage
```

This generates a coverage report in the `coverage/` directory.

### Using the Test Runner Script

A convenient test runner script is available:

```bash
cd frontend
./test/run_tests.sh
```

This script:
1. Runs all tests
2. Generates test results in JSON format
3. Creates coverage reports
4. Provides a summary of test results

## Test Coverage Details

### Authentication Functionality

#### Login
- Valid credentials login
- Invalid credentials handling
- Error response handling
- User data storage

#### Registration
- New user registration
- Duplicate username handling
- Optional field handling (email, nickname)
- Success and error flows

#### Guest Login
- Guest user creation
- Guest session initialization
- Guest user data structure

#### Token Management
- Token storage and retrieval
- Token validation
- Session persistence

#### Logout
- User data cleanup
- Token removal
- Session termination

### Context Handling

#### WebSocket Connection
- Connection establishment
- Error handling
- Reconnection logic
- Connection state management

#### Session Management
- Session initialization
- Session termination
- NPC selection
- Session state tracking

#### Message Handling
- Transcription messages
- Audio chunk processing
- Error messages
- System messages

#### Recording Functionality
- Recording start/stop
- Audio streaming
- Recording state management
- Permission handling

### UI Components

#### Login Screen
- Form field validation
- Login/register mode switching
- Password visibility toggle
- Guest login option
- Loading states
- Error display

#### NPC Selection Screen
- NPC list display
- NPC selection
- User information display
- Loading and error states
- Logout functionality

#### Voice Chat Screen
- NPC information display
- Voice visualization
- Transcription display
- Control button states
- Status messaging
- Navigation controls

### Integration Flows

#### Complete User Journey
- App startup
- Login flow
- NPC selection
- Voice chat initiation
- Session management
- Logout flow

## Mocking Strategy

### HTTP Requests
- Mocked using `mockito`
- Realistic response simulation
- Error condition testing

### WebSocket Connections
- Mocked using `web_socket_channel/testing`
- Message sending/receiving simulation
- Connection state simulation

### Secure Storage
- Mocked using `mockito`
- Token storage simulation
- User data persistence simulation

## Test Data

### User Models
- Valid user data structures
- Edge cases (null values, empty strings)
- Different user types (regular, guest)

### NPC Models
- Rigo NPC with full persona
- Demo NPCs with minimal data
- Edge cases

### Message Models
- Various message types
- Different content formats
- Error message structures

## Continuous Integration

The test suite is designed to be run in CI/CD pipelines. Key considerations:

### Test Execution Time
- Tests are optimized for fast execution
- Parallel test execution support
- Minimal external dependencies

### Environment Independence
- All external services are mocked
- No network calls in tests
- Deterministic test results

### Reporting
- Machine-readable output (JSON)
- Human-readable reports (HTML)
- Coverage reports

## Adding New Tests

### Creating Test Files
1. Create a new file in the `test/` directory
2. Follow the naming convention: `[feature]_test.dart`
3. Use appropriate test groupings
4. Include both positive and negative test cases

### Writing Effective Tests
1. Follow the Arrange-Act-Assert pattern
2. Use descriptive test names
3. Test one behavior per test
4. Include edge cases
5. Mock external dependencies

### Test Maintenance
1. Update tests when functionality changes
2. Remove obsolete tests
3. Refactor tests for clarity
4. Keep test data up to date

## Troubleshooting

### Common Issues

#### Tests Failing Due to Unmocked Dependencies
- Ensure all external services are properly mocked
- Check for missing mock setup

#### Test Performance Issues
- Review test setup/teardown
- Check for unnecessary network calls
- Optimize mock implementations

#### Coverage Gaps
- Identify untested code paths
- Add tests for edge cases
- Review integration test coverage

### Debugging Tests
1. Run individual tests to isolate issues
2. Use `print` statements for debugging
3. Check mock setup
4. Verify test data

## Best Practices

### Test Design
- Write tests before implementation when possible
- Focus on user-facing behavior
- Test both success and failure cases
- Keep tests independent

### Code Quality
- Use meaningful test descriptions
- Maintain consistent naming conventions
- Keep tests DRY (Don't Repeat Yourself)
- Remove dead code

### Maintenance
- Regular test review
- Update tests with feature changes
- Monitor test execution times
- Refactor tests as needed

## Future Improvements

### Planned Enhancements
1. Performance testing
2. Accessibility testing
3. Internationalization testing
4. Device-specific testing

### Coverage Expansion
1. Additional edge cases
2. Error recovery scenarios
3. Network condition simulation
4. Concurrent user testing

This testing framework provides a solid foundation for ensuring the quality and reliability of the Voice Chat App frontend.
