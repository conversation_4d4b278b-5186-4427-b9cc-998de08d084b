import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voice_chat_app/main.dart';
import 'package:voice_chat_app/screens/login_screen.dart';
import 'package:voice_chat_app/screens/npc_selection_screen.dart';
import 'package:voice_chat_app/models/user_model.dart';

void main() {
  group('App Integration Tests', () {
    testWidgets('App should start with splash screen and navigate to login', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const ProviderScope(child: VoiceChatApp()));
      
      // Act
      await tester.pumpAndSettle();

      // Assert
      // Should show login screen since user is not authenticated
      expect(find.text('Voice Chat'), findsOneWidget);
      expect(find.text('Username'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
    });

    testWidgets('App should navigate to NPC selection after login', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const ProviderScope(child: VoiceChatApp()));
      await tester.pumpAndSettle();

      // Act - Fill in login form
      await tester.enterText(find.byType(TextFormField).at(0), 'testuser');
      await tester.enterText(find.byType(TextFormField).at(1), 'password123');
      
      // Tap login button
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Assert
      // Should show NPC selection screen
      expect(find.text('Choose Your Assistant'), findsOneWidget);
      expect(find.text('Rigo'), findsOneWidget);
    });

    testWidgets('App should allow selecting NPC and starting chat', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const ProviderScope(child: VoiceChatApp()));
      await tester.pumpAndSettle();

      // Act - Select NPC
      await tester.tap(find.text('Rigo'));
      await tester.pumpAndSettle();

      // Assert
      // Should show NPC selection with selection indicator
      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets('App should show voice chat screen when starting chat', (WidgetTester tester) async {
      // Arrange
      final npc = NPCModel.createRigo();
      
      await tester.pumpWidget(
        MaterialApp(
          home: ProviderScope(
            child: VoiceChatScreen(npc: npc),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Rigo'), findsOneWidget);
      expect(find.byIcon(Icons.mic), findsOneWidget);
    });
  });
}
