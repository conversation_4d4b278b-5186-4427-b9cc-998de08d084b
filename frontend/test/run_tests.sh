#!/bin/bash

# Frontend Test Runner Script

echo "========================================"
echo "  Voice Chat App Frontend Test Runner   "
echo "========================================"

# Navigate to frontend directory
cd "$(dirname "$0")"/..

# Check if Flutter is installed
if ! command -v flutter &> /dev/null
then
    echo "Error: Flutter is not installed or not in PATH"
    exit 1
fi

echo "Flutter version: $(flutter --version | head -n 1)"

# Get current timestamp
timestamp=$(date +"%Y-%m-%d %H:%M:%S")

echo "Starting tests at: $timestamp"

# Create test results directory
mkdir -p test_results

# Run all tests
echo "Running all frontend tests..."
flutter test --machine > test_results/test_results.json

# Check if tests passed
if [ $? -eq 0 ]; then
    echo "✅ All tests passed!"
    
    # Generate coverage report if coverage package is available
    echo "Generating coverage report..."
    if flutter test --coverage &> /dev/null; then
        echo "✅ Coverage report generated"
        echo "📊 Coverage report location: coverage/lcov.info"
    else
        echo "⚠️  Coverage report generation failed"
    fi
else
    echo "❌ Some tests failed!"
    
    # Show detailed test results
    echo "📄 Test results saved to: test_results/test_results.json"
fi

# Run individual test suites and show results
echo ""
echo "Running individual test suites..."

echo "1. Authentication Service Tests"
flutter test test/auth_service_test.dart

echo ""
echo "2. Voice Chat Service Tests"
flutter test test/voice_chat_service_test.dart

echo ""
echo "3. Login Screen Tests"
flutter test test/login_screen_test.dart

echo ""
echo "4. NPC Selection Screen Tests"
flutter test test/npc_selection_screen_test.dart

echo ""
echo "5. Voice Chat Screen Tests"
flutter test test/voice_chat_screen_test.dart

echo ""
echo "6. Integration Tests"
flutter test test/widget_test.dart

# Summary
echo ""
echo "========================================"
echo "  Test Run Summary                      "
echo "========================================"
echo "Tests completed at: $(date +"%Y-%m-%d %H:%M:%S")"

# Count test files
test_files=$(find test -name "*.dart" -not -name "*test.mocks.dart" | wc -l)
echo "Total test files: $test_files"

# Count total tests (approximate)
total_tests=$(grep -r "test(" test/ | wc -l)
echo "Approximate total tests: $total_tests"

echo ""
echo "For detailed test results, check the test_results directory."
echo "For coverage reports, check the coverage directory."

echo ""
echo "🎉 Test run completed!"
