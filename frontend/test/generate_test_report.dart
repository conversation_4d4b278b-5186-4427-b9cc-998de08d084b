import 'dart:io';
import 'dart:convert';

void main() async {
  print('Generating test report...');

  // Create test results directory if it doesn't exist
  final testResultsDir = Directory('test_results');
  if (!await testResultsDir.exists()) {
    await testResultsDir.create();
  }

  // Run tests and capture output
  final process = await Process.run('flutter', ['test', '--machine']);
  
  // Save raw results
  final rawResultsFile = File('test_results/raw_results.json');
  await rawResultsFile.writeAsString(process.stdout);
  
  // Parse results
  final results = json.decode(process.stdout);
  
  // Generate HTML report
  final htmlReport = generateHtmlReport(results);
  final htmlReportFile = File('test_results/test_report.html');
  await htmlReportFile.writeAsString(htmlReport);
  
  // Generate summary
  final summary = generateSummary(results);
  final summaryFile = File('test_results/summary.txt');
  await summaryFile.writeAsString(summary);
  
  print('Test report generated successfully!');
  print('HTML report: test_results/test_report.html');
  print('Summary: test_results/summary.txt');
}

String generateHtmlReport(dynamic results) {
  final buffer = StringBuffer();
  
  buffer.writeln('''
<!DOCTYPE html>
<html>
<head>
    <title>Frontend Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { display: flex; justify-content: space-around; margin: 20px 0; }
        .metric { text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; }
        .passed { color: #4CAF50; }
        .failed { color: #F44336; }
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .table th { background-color: #f2f2f2; }
        .test-passed { background-color: #e8f5e8; }
        .test-failed { background-color: #ffe8e8; }
        .status-passed { color: #4CAF50; font-weight: bold; }
        .status-failed { color: #F44336; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Frontend Test Report</h1>
        <p>Generated on: ${DateTime.now()}</p>
    </div>
  ''');
  
  // Summary section
  final totalTests = results.length;
  final passedTests = results.where((test) => test['result'] == 'success').length;
  final failedTests = totalTests - passedTests;
  
  buffer.writeln('''
    <div class="summary">
        <div class="metric">
            <div class="metric-value">$totalTests</div>
            <div>Total Tests</div>
        </div>
        <div class="metric">
            <div class="metric-value passed">$passedTests</div>
            <div>Passed</div>
        </div>
        <div class="metric">
            <div class="metric-value failed">$failedTests</div>
            <div>Failed</div>
        </div>
        <div class="metric">
            <div class="metric-value">${totalTests > 0 ? (passedTests / totalTests * 100).toStringAsFixed(1) : '0'}%</div>
            <div>Success Rate</div>
        </div>
    </div>
  ''');
  
  // Test results table
  buffer.writeln('''
    <table class="table">
        <thead>
            <tr>
                <th>Test Name</th>
                <th>Status</th>
                <th>Duration (ms)</th>
                <th>Details</th>
            </tr>
        </thead>
        <tbody>
  ''');
  
  for (final test in results) {
    final testName = test['name'] ?? 'Unknown Test';
    final status = test['result'] ?? 'unknown';
    final duration = test['time'] ?? 0;
    final errorMessage = test['error'] ?? '';
    
    final statusClass = status == 'success' ? 'test-passed' : 'test-failed';
    final statusText = status == 'success' ? 'PASSED' : 'FAILED';
    final statusDisplayClass = status == 'success' ? 'status-passed' : 'status-failed';
    
    buffer.writeln('''
            <tr class="$statusClass">
                <td>$testName</td>
                <td class="$statusDisplayClass">$statusText</td>
                <td>$duration</td>
                <td>${errorMessage.isNotEmpty ? errorMessage : 'N/A'}</td>
            </tr>
    ''');
  }
  
  buffer.writeln('''
        </tbody>
    </table>
</body>
</html>
  ''');
  
  return buffer.toString();
}

String generateSummary(dynamic results) {
  final buffer = StringBuffer();
  
  buffer.writeln('Frontend Test Report Summary');
  buffer.writeln('==========================');
  buffer.writeln('Generated on: ${DateTime.now()}');
  buffer.writeln('');
  
  final totalTests = results.length;
  final passedTests = results.where((test) => test['result'] == 'success').length;
  final failedTests = totalTests - passedTests;
  
  buffer.writeln('Test Summary:');
  buffer.writeln('-------------');
  buffer.writeln('Total Tests: $totalTests');
  buffer.writeln('Passed: $passedTests');
  buffer.writeln('Failed: $failedTests');
  buffer.writeln('Success Rate: ${totalTests > 0 ? (passedTests / totalTests * 100).toStringAsFixed(1) : '0'}%');
  buffer.writeln('');
  
  if (failedTests > 0) {
    buffer.writeln('Failed Tests:');
    buffer.writeln('-------------');
    for (final test in results) {
      if (test['result'] != 'success') {
        buffer.writeln('- ${test['name'] ?? 'Unknown Test'}');
        if (test['error'] != null) {
          buffer.writeln('  Error: ${test['error']}');
        }
      }
    }
    buffer.writeln('');
  }
  
  buffer.writeln('All tests completed.');
  
  return buffer.toString();
}
