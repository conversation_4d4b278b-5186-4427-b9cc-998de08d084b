import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:voice_chat_app/models/user_model.dart';
import 'package:voice_chat_app/services/auth_service.dart';
import 'dart:convert';

// Generate mocks
@GenerateMocks([http.Client, FlutterSecureStorage])
import 'auth_service_test.mocks.dart';

void main() {
  group('AuthService', () {
    late AuthService authService;
    late MockClient mockClient;
    late MockFlutterSecureStorage mockStorage;

    setUp(() {
      mockClient = MockClient();
      mockStorage = MockFlutterSecureStorage();
      authService = AuthService();
    });

    tearDown(() {
      // Reset mocks
    });

    group('Login', () {
      test('should login successfully with valid credentials', () async {
        // Arrange
        final user = UserModel(
          id: 1,
          uuid: 'test-uuid',
          username: 'testuser',
          email: '<EMAIL>',
          nickname: 'Test User',
          avatarUrl: null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final responseJson = {
          'user': user.toJson(),
          'token': 'test-token',
        };

        // Mock HTTP response
        when(mockClient.post(
          Uri.parse('http://localhost:8000/auth/login'),
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(json.encode(responseJson), 200));

        // Act
        final result = await authService.login(
          username: 'testuser',
          password: 'password123',
        );

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals(1));
        expect(result.username, equals('testuser'));
      });

      test('should fail login with invalid credentials', () async {
        // Arrange
        final errorResponse = {'detail': 'Invalid credentials'};

        when(mockClient.post(
          Uri.parse('http://localhost:8000/auth/login'),
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(json.encode(errorResponse), 401));

        // Act
        final result = await authService.login(
          username: 'invaliduser',
          password: 'wrongpassword',
        );

        // Assert
        expect(result, isNull);
      });
    });

    group('Registration', () {
      test('should register successfully with valid data', () async {
        // Arrange
        final user = UserModel(
          id: 2,
          uuid: 'new-uuid',
          username: 'newuser',
          email: '<EMAIL>',
          nickname: 'New User',
          avatarUrl: null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final responseJson = {
          'user': user.toJson(),
          'token': 'new-token',
        };

        when(mockClient.post(
          Uri.parse('http://localhost:8000/auth/register'),
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(json.encode(responseJson), 200));

        // Act
        final result = await authService.register(
          username: 'newuser',
          password: 'password123',
          email: '<EMAIL>',
          nickname: 'New User',
        );

        // Assert
        expect(result, isNotNull);
        expect(result!.username, equals('newuser'));
      });

      test('should fail registration with existing username', () async {
        // Arrange
        final errorResponse = {'detail': 'Username already exists'};

        when(mockClient.post(
          Uri.parse('http://localhost:8000/auth/register'),
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(json.encode(errorResponse), 400));

        // Act
        final result = await authService.register(
          username: 'existinguser',
          password: 'password123',
        );

        // Assert
        expect(result, isNull);
      });
    });

    group('Guest Login', () {
      test('should login as guest successfully', () async {
        // Act
        final result = await authService.loginAsGuest();

        // Assert
        expect(result, isNotNull);
        expect(result!.username, contains('guest_'));
        expect(result.id, equals(999));
      });
    });

    group('Logout', () {
      test('should logout and clear user data', () async {
        // Arrange
        // First login to have user data
        final user = UserModel(
          id: 1,
          uuid: 'test-uuid',
          username: 'testuser',
          email: '<EMAIL>',
          nickname: 'Test User',
          avatarUrl: null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act
        await authService.logout();

        // Assert
        expect(authService.currentUser, isNull);
      });
    });

    group('Token Management', () {
      test('should get token when user is logged in', () async {
        // Arrange
        when(mockStorage.read(key: 'user_token'))
            .thenAnswer((_) async => 'test-token');

        // Act
        final token = await authService.getToken();

        // Assert
        expect(token, equals('test-token'));
      });

      test('should return null token when user is not logged in', () async {
        // Arrange
        when(mockStorage.read(key: 'user_token')).thenAnswer((_) async => null);

        // Act
        final token = await authService.getToken();

        // Assert
        expect(token, isNull);
      });
    });
  });
}
