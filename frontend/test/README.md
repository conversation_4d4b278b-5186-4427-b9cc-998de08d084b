# Frontend Test Suite

This directory contains comprehensive tests for the Voice Chat App frontend functionality.

## Test Structure

- `auth_service_test.dart` - Tests for authentication service (login, registration, guest login)
- `voice_chat_service_test.dart` - Tests for voice chat service (WebSocket connection, session management)
- `login_screen_test.dart` - Tests for login screen UI components and validation
- `npc_selection_screen_test.dart` - Tests for NPC selection screen
- `voice_chat_screen_test.dart` - Tests for voice chat screen UI
- `widget_test.dart` - Integration tests for the entire app flow
- `all_tests.dart` - Test suite that runs all tests together

## Running Tests

To run all tests:

```bash
cd frontend
flutter test
```

To run a specific test file:

```bash
flutter test test/auth_service_test.dart
```

To run tests with coverage:

```bash
flutter test --coverage
```

## Test Coverage

### Authentication Service
- Login with valid/invalid credentials
- User registration
- Guest login functionality
- Token management
- Logout functionality

### Voice Chat Service
- WebSocket connection handling
- Session management (start/end sessions)
- Recording functionality
- Message handling (transcription, errors, etc.)
- Interrupt functionality

### UI Components

#### Login Screen
- Default login form display
- Toggle between login/register modes
- Form validation (username, password, email)
- Password visibility toggle
- Guest login option

#### NPC Selection Screen
- User information display
- NPC list loading and display
- NPC selection functionality
- Logout functionality

#### Voice Chat Screen
- NPC information display
- Voice chat controls
- Status message display
- Visualization elements
- Navigation controls

### Integration Tests
- App startup flow
- Navigation between screens
- End-to-end user flow from login to voice chat

## Mocking

The tests use `mockito` for mocking HTTP requests, WebSocket connections, and secure storage. Mocks are generated automatically using the `@GenerateMocks` annotation.

To regenerate mocks:

```bash
flutter pub run build_runner build
```

## Test Data

Tests use the following test data:
- Test users with predefined credentials
- Mock HTTP responses for API calls
- Mock WebSocket messages for real-time communication
- Demo NPCs including the Rigo character

## Continuous Integration

These tests should be run as part of the CI/CD pipeline to ensure that new changes don't break existing functionality.
