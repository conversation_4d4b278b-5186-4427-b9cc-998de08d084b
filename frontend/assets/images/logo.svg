<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="60" cy="60" r="55" fill="url(#gradient)" stroke="#2196F3" stroke-width="2"/>
  
  <!-- Voice Wave Animation -->
  <g transform="translate(60, 60)">
    <!-- Center Microphone -->
    <circle cx="0" cy="0" r="8" fill="#FFFFFF"/>
    <rect x="-3" y="-12" width="6" height="16" rx="3" fill="#2196F3"/>
    <path d="M-8 -2 Q-8 8 0 8 Q8 8 8 -2" stroke="#2196F3" stroke-width="2" fill="none"/>
    <line x1="0" y1="8" x2="0" y2="16" stroke="#2196F3" stroke-width="2"/>
    <line x1="-6" y1="16" x2="6" y2="16" stroke="#2196F3" stroke-width="2"/>
    
    <!-- Voice Waves -->
    <circle cx="0" cy="0" r="20" fill="none" stroke="#2196F3" stroke-width="2" opacity="0.6">
      <animate attributeName="r" values="20;30;20" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="30" fill="none" stroke="#2196F3" stroke-width="1.5" opacity="0.4">
      <animate attributeName="r" values="30;40;30" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.1;0.4" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="40" fill="none" stroke="#2196F3" stroke-width="1" opacity="0.2">
      <animate attributeName="r" values="40;50;40" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.05;0.2" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E3F2FD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BBDEFB;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>