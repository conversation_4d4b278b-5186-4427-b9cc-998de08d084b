#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的MCP服务测试脚本
"""

import sys
import os
import logging

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mcp_basic_functionality():
    """
    测试MCP基本功能
    """
    logger.info("开始测试MCP基本功能...")
    
    try:
        # 导入服务
        from functions.master_service import master_service
        from services.mcp_service import mcp_service
        
        # 1. 测试服务器注册
        logger.info("1. 测试服务器注册...")
        result = master_service.call("register_server", server_name="test_server", server_url="http://test:8000")
        logger.info(f"服务器注册结果: {'成功' if result else '失败'}")
        
        # 2. 测试列出服务器
        logger.info("2. 测试列出服务器...")
        servers = master_service.call("list_servers")
        logger.info(f"当前服务器数量: {len(servers)}")
        for server in servers:
            logger.info(f"  - {server['name']}: {server['url']}")
        
        # 3. 测试获取服务器信息
        logger.info("3. 测试获取服务器信息...")
        server_info = master_service.call("get_server_info", server_name="test_server")
        if server_info:
            logger.info(f"服务器信息获取成功: {server_info['name']}")
        else:
            logger.error("服务器信息获取失败")
        
        # 4. 测试工具执行
        logger.info("4. 测试工具执行...")
        result = master_service.execute_mcp_tool("search_and_summarize", query="人工智能发展趋势")
        if result["status"] == "success":
            logger.info(f"工具执行成功: {result['tool_name']}")
        else:
            logger.error(f"工具执行失败: {result.get('error', '未知错误')}")
        
        # 5. 测试注销服务器
        logger.info("5. 测试注销服务器...")
        result = master_service.call("unregister_server", server_name="test_server")
        logger.info(f"服务器注销结果: {'成功' if result else '失败'}")
        
        # 6. 再次列出服务器
        logger.info("6. 再次列出服务器...")
        servers = master_service.call("list_servers")
        logger.info(f"注销后服务器数量: {len(servers)}")
        
        logger.info("MCP基本功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"测试MCP基本功能时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数
    """
    logger.info("开始MCP基本功能测试")
    
    success = test_mcp_basic_functionality()
    
    if success:
        logger.info("MCP基本功能测试全部完成")
    else:
        logger.error("MCP基本功能测试失败")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
