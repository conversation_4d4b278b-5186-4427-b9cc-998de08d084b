#!/usr/bin/env python3
"""
最简单的服务器测试
"""
from fastapi import FastAPI
import uvicorn

app = FastAPI()

@app.get("/")
def root():
    return {"message": "Simple test server"}

@app.post("/auth/login")
@app.get("/auth/login") 
def login(username: str, password: str):
    print(f"Login request: {username} / {password}")
    if username == "test_user" and password == "test_password":
        return {"user": {"id": 1, "username": username, "nickname": "Test User"}, "token": "token_1"}
    return {"error": "Invalid credentials"}

if __name__ == "__main__":
    print("Starting simple test server on port 8001...")
    uvicorn.run(app, host="0.0.0.0", port=8001)