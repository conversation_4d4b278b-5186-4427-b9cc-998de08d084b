#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MCP服务状态检查脚本
可以独立运行来检查MCP服务器和工具的状态
"""

import sys
import os
import asyncio
import logging
import requests
import json
from pathlib import Path
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_backend_service():
    """检查后端服务是否运行"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            logger.info("✅ 后端服务正常运行")
            return True
        else:
            logger.warning(f"⚠️ 后端服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        logger.error("❌ 后端服务未运行，请先启动: python backend/main.py")
        return False

def check_mcp_tools_api():
    """检查MCP工具API"""
    try:
        response = requests.get("http://localhost:8000/api/tools/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            tools = data.get('tools', [])
            logger.info(f"✅ MCP工具API正常，共 {len(tools)} 个工具")
            
            # 按服务器分组统计
            servers = {}
            for tool in tools:
                server = tool.get('server', 'unknown')
                if server not in servers:
                    servers[server] = []
                servers[server].append(tool['name'])
            
            for server, tool_names in servers.items():
                logger.info(f"  📦 {server}: {len(tool_names)} 个工具")
                for tool_name in tool_names[:3]:  # 只显示前3个
                    logger.info(f"    - {tool_name}")
                if len(tool_names) > 3:
                    logger.info(f"    ... 还有 {len(tool_names) - 3} 个工具")
            
            return True, data
        else:
            logger.error(f"❌ MCP工具API响应异常: {response.status_code}")
            return False, None
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ 无法访问MCP工具API: {e}")
        return False, None

def check_mcp_configs():
    """检查MCP配置文件"""
    config_dir = Path("backend/mcp_config")
    if not config_dir.exists():
        logger.error("❌ MCP配置目录不存在")
        return False
    
    config_files = list(config_dir.glob("*.json"))
    logger.info(f"📁 找到 {len(config_files)} 个MCP配置文件:")
    
    for config_file in config_files:
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            server_name = config.get('name', config_file.stem)
            command = config.get('command', 'unknown')
            args = config.get('args', [])
            
            logger.info(f"  ✅ {server_name}: {command} {' '.join(args)}")
        except Exception as e:
            logger.error(f"  ❌ {config_file.name}: 配置文件错误 - {e}")
    
    return True

async def test_mcp_client_direct():
    """直接测试MCP客户端"""
    try:
        from services.mcp_client import mcp_client
        from services.tool_manager_service import tool_manager_service
        
        logger.info("🔧 直接测试MCP客户端...")
        
        # 获取所有工具
        tools = await tool_manager_service.enumerate_all_tools_async(use_cache=False)
        logger.info(f"✅ 直接获取到 {len(tools)} 个工具")
        
        # 测试一个简单的工具调用（如果有的话）
        if tools:
            test_tool = tools[0]
            tool_name = test_tool['name']
            server_name = test_tool['server']
            
            logger.info(f"🧪 测试工具调用: {tool_name} ({server_name})")
            
            # 这里可以添加具体的工具测试逻辑
            # result = await mcp_client.execute_tool(tool_name, server_name, **test_params)
            
        return True
    except Exception as e:
        logger.error(f"❌ 直接MCP客户端测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 MCP服务状态检查")
    print("=" * 50)
    
    # 1. 检查后端服务
    print("\n1. 检查后端服务...")
    backend_ok = check_backend_service()
    
    # 2. 检查MCP配置文件
    print("\n2. 检查MCP配置文件...")
    config_ok = check_mcp_configs()
    
    # 3. 检查MCP工具API
    print("\n3. 检查MCP工具API...")
    if backend_ok:
        api_ok, tools_data = check_mcp_tools_api()
    else:
        api_ok = False
        tools_data = None
    
    # 4. 直接测试MCP客户端（如果后端未运行）
    if not backend_ok:
        print("\n4. 直接测试MCP客户端...")
        try:
            direct_ok = asyncio.run(test_mcp_client_direct())
        except Exception as e:
            logger.error(f"直接测试失败: {e}")
            direct_ok = False
    else:
        direct_ok = True
    
    # 5. 总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    print(f"  后端服务: {'✅ 正常' if backend_ok else '❌ 异常'}")
    print(f"  MCP配置: {'✅ 正常' if config_ok else '❌ 异常'}")
    print(f"  工具API: {'✅ 正常' if api_ok else '❌ 异常'}")
    print(f"  直接测试: {'✅ 正常' if direct_ok else '❌ 异常'}")
    
    if tools_data:
        total_tools = len(tools_data.get('tools', []))
        print(f"  总工具数: {total_tools}")
    
    if all([backend_ok, config_ok, api_ok]):
        print("\n🎉 所有MCP服务检查通过！")
        return 0
    else:
        print("\n⚠️ 部分MCP服务存在问题，请检查上述输出")
        return 1

if __name__ == "__main__":
    sys.exit(main())
