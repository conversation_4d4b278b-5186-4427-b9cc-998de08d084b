{"test_time": "2025-08-07T15:39:40.945526", "system_info": {"platform": "<PERSON>", "platform_version": "Darwin Kernel Version 25.0.0: <PERSON><PERSON> Jun 17 00:06:42 PDT 2025; root:xnu-12377.0.122.0.1~120/RELEASE_ARM64_T6000", "python_version": "3.12.2", "architecture": "64bit"}, "tests": {"server_startup": {"status": "PASS", "timestamp": "2025-08-07T15:39:43.979134", "details": {"pid": 2053}, "error": null}, "server_health": {"status": "PASS", "timestamp": "2025-08-07T15:39:46.008035", "details": {"status": "running", "active_connections": 0, "total_recordings": 0, "recordings_dir": "test_recordings", "timestamp": "2025-08-07T15:39:46.006699"}, "error": null}, "html_page_access": {"status": "PASS", "timestamp": "2025-08-07T15:39:46.011311", "details": {"content_length": 18276, "has_recording_ui": true}, "error": null}, "websocket_connection": {"status": "PASS", "timestamp": "2025-08-07T15:39:46.053604", "details": {"type": "connected", "connection_id": "conn_1754552386.052448", "message": "录音WebSocket连接成功"}, "error": null}, "websocket_heartbeat": {"status": "PASS", "timestamp": "2025-08-07T15:39:46.054196", "details": {"type": "pong", "timestamp": "2025-08-07T15:39:46.053983"}, "error": null}, "audio_upload": {"status": "PASS", "timestamp": "2025-08-07T15:39:46.156098", "details": {"filename": "recording_1_20250807_153946_153.wav", "recording_id": 1, "size": 32000, "analysis": {"filename": "recording_1_20250807_153946_153.wav", "duration": "1.00秒", "sample_rate": "16000 Hz", "channels": 1, "sample_width": "16 bit", "frames": 16000, "max_amplitude": 32767, "rms": "23169.27", "file_size": "31.3 KB", "is_valid": true}}, "error": null}, "recording_analysis": {"status": "PASS", "timestamp": "2025-08-07T15:39:46.160246", "details": {"filename": "recording_1_20250807_153946_153.wav", "duration": "1.00秒", "sample_rate": "16000 Hz", "channels": 1, "sample_width": "16 bit", "frames": 16000, "max_amplitude": 32767, "rms": "23169.27", "file_size": "31.3 KB", "is_valid": true}, "error": null}, "recording_download": {"status": "PASS", "timestamp": "2025-08-07T15:39:46.168237", "details": {"content_length": 32044, "content_type": "audio/wav"}, "error": null}, "recordings_list": {"status": "PASS", "timestamp": "2025-08-07T15:39:46.170920", "details": {"count": 1, "recordings_found": true}, "error": null}, "file_system_access": {"status": "PASS", "timestamp": "2025-08-07T15:39:46.171534", "details": {"recordings_dir_exists": true, "wav_files_count": 1, "directory_path": "/Users/<USER>/LocalRepo/20250729t163822/test_recordings"}, "error": null}}, "issues": [], "recommendations": [{"category": "系统优化", "issue": "所有测试通过", "solution": "系统录音功能正常，建议进行实际录音测试验证用户体验", "priority": "LOW"}], "summary": {"total_tests": 10, "passed_tests": 10, "failed_tests": 0, "success_rate": "100.0%"}}