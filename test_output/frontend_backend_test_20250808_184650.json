{"timestamp": "2025-08-08T18:46:41.347518", "steps": [{"step": "开始测试", "timestamp": "2025-08-08T18:46:41.347669", "details": "启动前端-后端连接测试"}, {"step": "连接WebSocket", "timestamp": "2025-08-08T18:46:41.347685", "details": "尝试连接到 ws://localhost:8000/ws/999"}, {"step": "WebSocket连接", "timestamp": "2025-08-08T18:46:41.413017", "details": "连接成功"}, {"step": "开始会话", "timestamp": "2025-08-08T18:46:41.413264", "details": "已发送开始会话请求 (NPC ID: 1)"}, {"step": "监听消息", "timestamp": "2025-08-08T18:46:41.413356", "details": "开始监听服务器消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:46:42.165741", "details": "收到消息类型: session_started"}, {"step": "会话开始", "timestamp": "2025-08-08T18:46:42.165815", "details": "会话已开始, Session ID: dfbc3e0e-7608-4227-9719-1458829b0d92"}, {"step": "发送测试音频", "timestamp": "2025-08-08T18:46:42.413980", "details": "生成并发送测试音频数据"}, {"step": "生成测试音频", "timestamp": "2025-08-08T18:46:42.414038", "details": "创建测试音频数据"}, {"step": "保存测试音频", "timestamp": "2025-08-08T18:46:42.415360", "details": "测试音频已保存到: test_recordings/test_tone_20250808_184642.wav"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:42.415499", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:42.516967", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:42.618271", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:42.719672", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:42.821088", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:42.922376", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.023686", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.124998", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.226479", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.327885", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.428449", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.529366", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.630148", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.731378", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.832650", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:43.933559", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:44.036795", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:44.138228", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:44.238895", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:46:44.340161", "details": "发送音频块: 3200 字节"}, {"step": "等待处理", "timestamp": "2025-08-08T18:46:44.440614", "details": "等待服务器处理完成..."}, {"step": "结束会话", "timestamp": "2025-08-08T18:46:49.442225", "details": "已发送结束会话消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:46:50.956430", "details": "收到消息类型: transcription"}, {"step": "转录结果", "timestamp": "2025-08-08T18:46:50.956485", "details": "转录文本: 好的正在转录 (置信度: 0.8)"}, {"step": "关闭连接", "timestamp": "2025-08-08T18:46:50.956887", "details": "WebSocket连接已关闭"}], "errors": [], "success": false}