{"timestamp": "2025-08-08T18:30:30.042352", "steps": [{"step": "开始测试", "timestamp": "2025-08-08T18:30:30.042531", "details": "启动前端-后端连接测试"}, {"step": "连接WebSocket", "timestamp": "2025-08-08T18:30:30.042563", "details": "尝试连接到 ws://localhost:8000/ws/999"}, {"step": "WebSocket连接", "timestamp": "2025-08-08T18:30:30.120181", "details": "连接成功"}, {"step": "开始会话", "timestamp": "2025-08-08T18:30:30.120395", "details": "已发送开始会话请求 (NPC ID: 1)"}, {"step": "监听消息", "timestamp": "2025-08-08T18:30:30.120512", "details": "开始监听服务器消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:30:30.779206", "details": "收到消息类型: session_started"}, {"step": "会话开始", "timestamp": "2025-08-08T18:30:30.779255", "details": "会话已开始, Session ID: 17cdbd56-48cd-4493-a1c4-ad6c4c6f6586"}, {"step": "发送测试音频", "timestamp": "2025-08-08T18:30:31.121574", "details": "生成并发送测试音频数据"}, {"step": "生成测试音频", "timestamp": "2025-08-08T18:30:31.121628", "details": "创建测试音频数据"}, {"step": "保存测试音频", "timestamp": "2025-08-08T18:30:31.122892", "details": "测试音频已保存到: test_recordings/test_tone_20250808_183031.wav"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:31.123037", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:31.224292", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:31.325575", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:31.425878", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:31.527153", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:31.628443", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:31.729040", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:31.830033", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:31.934395", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.035991", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.137333", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.238705", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.339988", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.441248", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.541526", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.643052", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.744450", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.846137", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:32.949564", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:30:33.054715", "details": "发送音频块: 3200 字节"}, {"step": "等待处理", "timestamp": "2025-08-08T18:30:33.156065", "details": "等待服务器处理完成..."}, {"step": "结束会话", "timestamp": "2025-08-08T18:30:38.157367", "details": "已发送结束会话消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:30:39.324609", "details": "收到消息类型: transcription"}, {"step": "转录结果", "timestamp": "2025-08-08T18:30:39.324660", "details": "转录文本: 喂喂喂 (置信度: 0.8)"}, {"step": "关闭连接", "timestamp": "2025-08-08T18:30:39.325210", "details": "WebSocket连接已关闭"}], "errors": [], "success": false}