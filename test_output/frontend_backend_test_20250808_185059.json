{"timestamp": "2025-08-08T18:50:50.725881", "steps": [{"step": "开始测试", "timestamp": "2025-08-08T18:50:50.726022", "details": "启动前端-后端连接测试"}, {"step": "连接WebSocket", "timestamp": "2025-08-08T18:50:50.726035", "details": "尝试连接到 ws://localhost:8000/ws/999"}, {"step": "WebSocket连接", "timestamp": "2025-08-08T18:50:50.795660", "details": "连接成功"}, {"step": "开始会话", "timestamp": "2025-08-08T18:50:50.795862", "details": "已发送开始会话请求 (NPC ID: 1)"}, {"step": "监听消息", "timestamp": "2025-08-08T18:50:50.795954", "details": "开始监听服务器消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:50:51.604071", "details": "收到消息类型: session_started"}, {"step": "会话开始", "timestamp": "2025-08-08T18:50:51.604113", "details": "会话已开始, Session ID: d4ad1df5-be2b-4bef-b146-60715a83df60"}, {"step": "发送测试音频", "timestamp": "2025-08-08T18:50:51.797024", "details": "生成并发送测试音频数据"}, {"step": "生成测试音频", "timestamp": "2025-08-08T18:50:51.797075", "details": "创建测试音频数据"}, {"step": "保存测试音频", "timestamp": "2025-08-08T18:50:51.798279", "details": "测试音频已保存到: test_recordings/test_tone_20250808_185051.wav"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:51.798419", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:51.900900", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.004500", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.105787", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.207073", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.307649", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.408255", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.508867", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.610212", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.711891", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.813296", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:52.914212", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:53.015560", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:53.116177", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:53.217504", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:53.318873", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:53.420650", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:53.522017", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:53.623342", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:50:53.724703", "details": "发送音频块: 3200 字节"}, {"step": "等待处理", "timestamp": "2025-08-08T18:50:53.825830", "details": "等待服务器处理完成..."}, {"step": "结束会话", "timestamp": "2025-08-08T18:50:58.827106", "details": "已发送结束会话消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:50:59.362980", "details": "收到消息类型: transcription"}, {"step": "转录结果", "timestamp": "2025-08-08T18:50:59.363034", "details": "转录文本: 喂你好请问哪位 (置信度: 0.8)"}, {"step": "关闭连接", "timestamp": "2025-08-08T18:50:59.363525", "details": "WebSocket连接已关闭"}], "errors": [], "success": false}