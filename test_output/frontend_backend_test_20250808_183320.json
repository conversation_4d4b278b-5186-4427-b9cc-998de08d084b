{"timestamp": "2025-08-08T18:33:12.411319", "steps": [{"step": "开始测试", "timestamp": "2025-08-08T18:33:12.411462", "details": "启动前端-后端连接测试"}, {"step": "连接WebSocket", "timestamp": "2025-08-08T18:33:12.411486", "details": "尝试连接到 ws://localhost:8000/ws/999"}, {"step": "WebSocket连接", "timestamp": "2025-08-08T18:33:12.488087", "details": "连接成功"}, {"step": "开始会话", "timestamp": "2025-08-08T18:33:12.488468", "details": "已发送开始会话请求 (NPC ID: 1)"}, {"step": "监听消息", "timestamp": "2025-08-08T18:33:12.488684", "details": "开始监听服务器消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:33:12.994568", "details": "收到消息类型: session_started"}, {"step": "会话开始", "timestamp": "2025-08-08T18:33:12.994623", "details": "会话已开始, Session ID: 0bc3d52a-b940-4669-839d-198886fb5054"}, {"step": "发送测试音频", "timestamp": "2025-08-08T18:33:13.490601", "details": "生成并发送测试音频数据"}, {"step": "生成测试音频", "timestamp": "2025-08-08T18:33:13.490719", "details": "创建测试音频数据"}, {"step": "保存测试音频", "timestamp": "2025-08-08T18:33:13.494422", "details": "测试音频已保存到: test_recordings/test_tone_20250808_183313.wav"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:13.494640", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:13.595895", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:13.696804", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:13.797456", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:13.897927", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:13.999198", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:14.100086", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:14.200512", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:14.301785", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:14.403044", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:14.504306", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:14.605050", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:14.706314", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:14.807596", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:14.908889", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:15.010536", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:15.112231", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:15.213780", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:15.315510", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:33:15.418240", "details": "发送音频块: 3200 字节"}, {"step": "等待处理", "timestamp": "2025-08-08T18:33:15.521146", "details": "等待服务器处理完成..."}, {"step": "接收消息", "timestamp": "2025-08-08T18:33:20.022477", "details": "收到消息类型: transcription"}, {"step": "转录结果", "timestamp": "2025-08-08T18:33:20.022522", "details": "转录文本: 喂 (置信度: 0.8)"}, {"step": "结束会话", "timestamp": "2025-08-08T18:33:20.523286", "details": "已发送结束会话消息"}, {"step": "关闭连接", "timestamp": "2025-08-08T18:33:20.527057", "details": "WebSocket连接已关闭"}], "errors": [], "success": false}