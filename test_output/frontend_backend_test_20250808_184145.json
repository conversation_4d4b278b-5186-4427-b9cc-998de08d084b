{"timestamp": "2025-08-08T18:41:34.175428", "steps": [{"step": "开始测试", "timestamp": "2025-08-08T18:41:34.175607", "details": "启动前端-后端连接测试"}, {"step": "连接WebSocket", "timestamp": "2025-08-08T18:41:34.175637", "details": "尝试连接到 ws://localhost:8000/ws/999"}, {"step": "WebSocket连接", "timestamp": "2025-08-08T18:41:34.246111", "details": "连接成功"}, {"step": "开始会话", "timestamp": "2025-08-08T18:41:34.246283", "details": "已发送开始会话请求 (NPC ID: 1)"}, {"step": "监听消息", "timestamp": "2025-08-08T18:41:34.246356", "details": "开始监听服务器消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:41:35.121907", "details": "收到消息类型: session_started"}, {"step": "会话开始", "timestamp": "2025-08-08T18:41:35.121979", "details": "会话已开始, Session ID: 3f652fbc-f0fe-41bb-943e-d29a512e6ba4"}, {"step": "发送测试音频", "timestamp": "2025-08-08T18:41:35.247354", "details": "生成并发送测试音频数据"}, {"step": "生成测试音频", "timestamp": "2025-08-08T18:41:35.247411", "details": "创建测试音频数据"}, {"step": "保存测试音频", "timestamp": "2025-08-08T18:41:35.248599", "details": "测试音频已保存到: test_recordings/test_tone_20250808_184135.wav"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:35.248723", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:35.349961", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:35.451333", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:35.552672", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:35.653550", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:35.754819", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:35.855460", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:35.956751", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.058046", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.158890", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.260168", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.361424", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.462675", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.563953", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.665720", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.767494", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.868883", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:36.970136", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:37.071469", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:41:37.172471", "details": "发送音频块: 3200 字节"}, {"step": "等待处理", "timestamp": "2025-08-08T18:41:37.273655", "details": "等待服务器处理完成..."}, {"step": "结束会话", "timestamp": "2025-08-08T18:41:42.274999", "details": "已发送结束会话消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:41:45.908827", "details": "收到消息类型: transcription"}, {"step": "转录结果", "timestamp": "2025-08-08T18:41:45.908873", "details": "转录文本: 喂 (置信度: 0.8)"}, {"step": "关闭连接", "timestamp": "2025-08-08T18:41:45.909384", "details": "WebSocket连接已关闭"}], "errors": [], "success": false}