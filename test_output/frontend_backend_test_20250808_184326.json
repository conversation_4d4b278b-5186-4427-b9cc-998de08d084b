{"timestamp": "2025-08-08T18:43:16.504961", "steps": [{"step": "开始测试", "timestamp": "2025-08-08T18:43:16.505078", "details": "启动前端-后端连接测试"}, {"step": "连接WebSocket", "timestamp": "2025-08-08T18:43:16.505090", "details": "尝试连接到 ws://localhost:8000/ws/999"}, {"step": "WebSocket连接", "timestamp": "2025-08-08T18:43:16.561639", "details": "连接成功"}, {"step": "开始会话", "timestamp": "2025-08-08T18:43:16.561851", "details": "已发送开始会话请求 (NPC ID: 1)"}, {"step": "监听消息", "timestamp": "2025-08-08T18:43:16.561994", "details": "开始监听服务器消息"}, {"step": "发送测试音频", "timestamp": "2025-08-08T18:43:17.563140", "details": "生成并发送测试音频数据"}, {"step": "生成测试音频", "timestamp": "2025-08-08T18:43:17.563213", "details": "创建测试音频数据"}, {"step": "保存测试音频", "timestamp": "2025-08-08T18:43:17.564521", "details": "测试音频已保存到: test_recordings/test_tone_20250808_184317.wav"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:17.564688", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:17.665771", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:17.766704", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:17.867171", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:17.968455", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.069717", "details": "发送音频块: 3200 字节"}, {"step": "接收消息", "timestamp": "2025-08-08T18:43:18.143269", "details": "收到消息类型: session_started"}, {"step": "会话开始", "timestamp": "2025-08-08T18:43:18.143331", "details": "会话已开始, Session ID: d2315eae-6b61-4dd0-ac33-eb75883163ec"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.170719", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.272027", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.373314", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.474612", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.575218", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.676477", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.777738", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.879015", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:18.980610", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:19.081635", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:19.182900", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:19.284194", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:19.384767", "details": "发送音频块: 3200 字节"}, {"step": "发送音频块", "timestamp": "2025-08-08T18:43:19.485249", "details": "发送音频块: 3200 字节"}, {"step": "等待处理", "timestamp": "2025-08-08T18:43:19.586203", "details": "等待服务器处理完成..."}, {"step": "结束会话", "timestamp": "2025-08-08T18:43:24.587545", "details": "已发送结束会话消息"}, {"step": "接收消息", "timestamp": "2025-08-08T18:43:26.350913", "details": "收到消息类型: transcription"}, {"step": "转录结果", "timestamp": "2025-08-08T18:43:26.350978", "details": "转录文本: 喂你好喂你好请问是张先生吗我是请问您是哪位我是京东快递的您有个快递到了您现在方便签收吗方便的你现在在家吗在家好的那我稍后给您送过去吧谢谢 (置信度: 0.8)"}, {"step": "关闭连接", "timestamp": "2025-08-08T18:43:26.351728", "details": "WebSocket连接已关闭"}], "errors": [], "success": false}