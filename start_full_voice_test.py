#!/usr/bin/env python3
"""
启动完整语音流水线测试
自动启动后端服务并打开测试页面
"""
import subprocess
import sys
import time
import webbrowser
import requests
from pathlib import Path
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceTestLauncher:
    def __init__(self):
        self.backend_process = None
        self.backend_url = "http://localhost:8000"
        self.test_page = "test_full_voice_pipeline.html"
        
    def check_backend_health(self, timeout=30):
        """检查后端服务健康状态"""
        logger.info("🏥 检查后端服务健康状态...")
        
        for attempt in range(timeout):
            try:
                response = requests.get(f"{self.backend_url}/health", timeout=2)
                if response.status_code == 200:
                    health_data = response.json()
                    logger.info("✅ 后端服务健康检查通过")
                    
                    # 显示服务状态
                    services = health_data.get('services', {})
                    for service, status in services.items():
                        status_icon = "✅" if "error" not in str(status).lower() else "⚠️"
                        logger.info(f"   {service}: {status_icon} {status}")
                    
                    return True
                    
            except requests.exceptions.ConnectionError:
                if attempt == 0:
                    logger.info(f"⏳ 等待后端服务启动... (尝试 {attempt + 1}/{timeout})")
                time.sleep(1)
                continue
            except Exception as e:
                logger.warning(f"⚠️ 健康检查异常: {e}")
                time.sleep(1)
                continue
        
        logger.error("❌ 后端服务健康检查失败")
        return False
    
    def start_backend(self):
        """启动后端服务"""
        logger.info("🚀 启动后端服务...")
        
        # 检查后端文件是否存在
        backend_main = Path("backend/main.py")
        if not backend_main.exists():
            logger.error("❌ 后端文件不存在: backend/main.py")
            return False
        
        try:
            # 启动后端服务
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", "backend.main:app",
                "--host", "0.0.0.0",
                "--port", "8000",
                "--reload"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            logger.info(f"📡 后端服务已启动 (PID: {self.backend_process.pid})")
            
            # 等待服务启动并检查健康状态
            if self.check_backend_health():
                logger.info("✅ 后端服务启动成功")
                return True
            else:
                logger.error("❌ 后端服务启动失败")
                self.stop_backend()
                return False
                
        except Exception as e:
            logger.error(f"❌ 启动后端服务异常: {e}")
            return False
    
    def stop_backend(self):
        """停止后端服务"""
        if self.backend_process:
            logger.info("🛑 停止后端服务...")
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                logger.info("✅ 后端服务已停止")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ 强制终止后端服务")
                self.backend_process.kill()
            except Exception as e:
                logger.error(f"❌ 停止后端服务异常: {e}")
    
    def open_test_page(self):
        """打开测试页面"""
        logger.info("🌐 打开测试页面...")
        
        # 检查测试页面是否存在
        if not Path(self.test_page).exists():
            logger.error(f"❌ 测试页面不存在: {self.test_page}")
            return False
        
        try:
            # 获取测试页面的完整路径
            test_page_path = Path(self.test_page).absolute()
            test_page_url = f"file://{test_page_path}"
            
            logger.info(f"📄 测试页面路径: {test_page_url}")
            
            # 打开浏览器
            webbrowser.open(test_page_url)
            logger.info("✅ 测试页面已在浏览器中打开")
            return True
            
        except Exception as e:
            logger.error(f"❌ 打开测试页面失败: {e}")
            return False
    
    def show_instructions(self):
        """显示使用说明"""
        logger.info("=" * 60)
        logger.info("🎤 完整语音流水线测试说明")
        logger.info("=" * 60)
        logger.info("1. 后端服务已启动在 http://localhost:8000")
        logger.info("2. 测试页面已在浏览器中打开")
        logger.info("3. 测试流程:")
        logger.info("   📱 点击'连接服务器'建立WebSocket连接")
        logger.info("   🎤 点击'开始录音'进行语音输入")
        logger.info("   🎯 系统会自动进行ASR语音识别")
        logger.info("   🧠 LLM会生成对话响应")
        logger.info("   🔊 TTS会合成语音并播放")
        logger.info("4. 每个环节都有详细的状态显示和日志")
        logger.info("5. 可以查看性能指标和中间结果")
        logger.info("6. 按 Ctrl+C 停止所有服务")
        logger.info("=" * 60)
    
    def run(self):
        """运行完整测试"""
        logger.info("🚀 启动完整语音流水线测试环境")
        
        try:
            # 1. 启动后端服务
            if not self.start_backend():
                logger.error("❌ 后端服务启动失败，退出")
                return
            
            # 2. 打开测试页面
            if not self.open_test_page():
                logger.error("❌ 测试页面打开失败")
                self.stop_backend()
                return
            
            # 3. 显示使用说明
            self.show_instructions()
            
            # 4. 等待用户中断
            try:
                logger.info("⏳ 测试环境运行中，按 Ctrl+C 停止...")
                while True:
                    time.sleep(1)
                    
                    # 检查后端进程是否还在运行
                    if self.backend_process and self.backend_process.poll() is not None:
                        logger.error("❌ 后端服务意外停止")
                        break
                        
            except KeyboardInterrupt:
                logger.info("🛑 收到停止信号")
            
        except Exception as e:
            logger.error(f"❌ 运行异常: {e}")
        
        finally:
            # 清理资源
            self.stop_backend()
            logger.info("✅ 测试环境已停止")

def main():
    """主函数"""
    # 检查必要文件
    required_files = [
        "backend/main.py",
        "test_full_voice_pipeline.html"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return
    
    # 检查环境变量
    required_env_vars = [
        "VOLCANO_API_KEY",
        "VOLCANO_ENDPOINT", 
        "MINIMAX_API_KEY",
        "MINIMAX_GROUP_ID"
    ]
    
    missing_env_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_env_vars.append(var)
    
    if missing_env_vars:
        logger.warning(f"⚠️ 缺少环境变量: {', '.join(missing_env_vars)}")
        logger.warning("某些功能可能无法正常工作")
    
    # 启动测试环境
    launcher = VoiceTestLauncher()
    launcher.run()

if __name__ == "__main__":
    main()