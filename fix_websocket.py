#!/usr/bin/env python3
"""
修复WebSocket端点的脚本
"""
import re

def fix_websocket_endpoint():
    """修复WebSocket端点"""
    
    # 读取原文件
    with open('backend/main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 新的WebSocket端点实现
    new_websocket_code = '''# WebSocket endpoint for real-time communication - FIXED VERSION
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    
    try:
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                message_type = message.get("type")
                
                if message_type == "start_session":
                    # Start new conversation session
                    npc_id = message.get("npc_id", 1)
                    logger.info(f"Starting session for user {user_id} with NPC {npc_id}")
                    
                    session_id = await create_conversation_session(int(user_id), npc_id)
                    logger.info(f"Session creation result: {session_id}")
                    
                    if session_id:
                        manager.user_sessions[user_id]["session_id"] = session_id
                        manager.user_sessions[user_id]["npc_id"] = npc_id
                        
                        logger.info(f"Session started successfully: {session_id}")
                        await manager.send_message(user_id, {
                            "type": "session_started",
                            "session_id": session_id,
                            "npc_id": npc_id
                        })
                    else:
                        logger.error(f"Failed to create session for user {user_id}")
                        await manager.send_message(user_id, {
                            "type": "error",
                            "message": "Failed to start session"
                        })
                
                elif message_type == "audio_chunk":
                    # Process audio chunk
                    audio_data = base64.b64decode(message["data"])
                    await process_audio_chunk(user_id, audio_data)
                
                elif message_type == "interrupt":
                    # Handle user interruption
                    if user_id in manager.user_sessions:
                        manager.user_sessions[user_id]["is_speaking"] = False
                    await manager.send_message(user_id, {
                        "type": "interrupted",
                        "message": "Assistant interrupted"
                    })
                
                elif message_type == "end_session":
                    # End conversation session
                    if user_id in manager.user_sessions:
                        session_id = manager.user_sessions[user_id].get("session_id")
                        if session_id:
                            try:
                                supabase.table("conversation_sessions").update({
                                    "ended_at": datetime.now().isoformat(),
                                    "is_active": False
                                }).eq("id", session_id).execute()
                            except Exception as e:
                                logger.error(f"Error ending session: {e}")
                    
                    await manager.send_message(user_id, {
                        "type": "session_ended"
                    })
                    break
                    
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received from user {user_id}")
                await manager.send_message(user_id, {
                    "type": "error",
                    "message": "Invalid message format"
                })
            except Exception as e:
                logger.error(f"Error processing message from user {user_id}: {e}")
                break
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for user {user_id}")
        manager.disconnect(user_id)
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
        manager.disconnect(user_id)'''
    
    # 找到WebSocket端点的开始和结束位置
    websocket_start = content.find('@app.websocket("/ws/{user_id}")')
    if websocket_start == -1:
        print("❌ 找不到WebSocket端点")
        return False
    
    # 找到下一个函数或类的开始位置作为结束点
    # 寻找 "async def process_audio_chunk" 作为结束标记
    end_marker = "async def process_audio_chunk"
    websocket_end = content.find(end_marker, websocket_start)
    
    if websocket_end == -1:
        print("❌ 找不到WebSocket端点的结束位置")
        return False
    
    # 替换WebSocket端点
    new_content = content[:websocket_start] + new_websocket_code + '\n\n' + content[websocket_end:]
    
    # 写入修复后的文件
    with open('backend/main.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ WebSocket端点已修复")
    return True

if __name__ == "__main__":
    fix_websocket_endpoint()