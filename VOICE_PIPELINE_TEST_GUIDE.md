# 完整语音流水线测试指南

## 概述

本测试套件提供了完整的语音流水线测试，包含录音→ASR→LLM→TTS→播放的每个环节，并提供详细的中间结果便于排查问题。

## 测试流程

```
🎤 录音 → 🎯 ASR识别 → 🧠 LLM对话 → 🔊 TTS合成 → ▶️ 音频播放
```

## 测试工具

### 1. 快速启动器 - `quick_voice_test.py`

**用途**: 一键启动各种测试模式

**使用方法**:
```bash
python quick_voice_test.py
```

**功能**:
- 选项1: 启动完整测试环境 (推荐)
- 选项2: 运行自动化测试脚本
- 选项3: 仅启动后端服务
- 选项4: 退出

### 2. 完整测试环境 - `start_full_voice_test.py`

**用途**: 启动后端服务并打开可视化测试页面

**使用方法**:
```bash
python start_full_voice_test.py
```

**功能**:
- 自动启动后端服务 (http://localhost:8000)
- 自动打开测试页面 (test_full_voice_pipeline.html)
- 提供完整的使用说明
- 按Ctrl+C停止所有服务

### 3. 可视化测试页面 - `test_full_voice_pipeline.html`

**用途**: 浏览器端的完整语音流水线测试界面

**功能**:
- 🎤 实时录音功能
- 📊 流水线状态可视化
- 📈 性能指标监控
- 📝 详细日志显示
- 🔊 音频播放验证

**使用步骤**:
1. 点击"连接服务器"建立WebSocket连接
2. 点击"开始录音"进行语音输入
3. 观察各阶段状态变化
4. 查看识别结果和AI响应
5. 播放合成的语音

### 4. 自动化测试脚本 - `test_voice_pipeline_stages.py`

**用途**: 各阶段独立的自动化测试

**使用方法**:
```bash
python test_voice_pipeline_stages.py
```

**测试内容**:
- ✅ 后端健康检查
- ✅ 用户认证测试
- ✅ WebSocket连接测试
- ✅ 会话启动测试
- ✅ ASR语音识别测试
- ✅ LLM对话生成测试
- ✅ TTS语音合成测试
- ✅ 音频播放验证测试

**输出**:
- 详细的测试日志
- 性能指标统计
- JSON格式测试报告

## 测试结果解读

### 流水线状态

| 阶段 | 图标 | 说明 |
|------|------|------|
| 录音 | 🎤 | 麦克风录音，生成音频数据 |
| ASR识别 | 🎯 | 语音转文字，输出识别结果 |
| LLM对话 | 🧠 | 生成AI对话响应 |
| TTS合成 | 🔊 | 文字转语音，生成音频 |
| 音频播放 | ▶️ | 播放合成的语音 |

### 状态颜色

- **灰色**: 等待中
- **蓝色**: 进行中 (active)
- **绿色**: 成功完成 (success)
- **红色**: 失败 (error)

### 性能指标

- **录音时长**: 用户录音的实际时长
- **ASR耗时**: 语音识别处理时间
- **LLM耗时**: 对话生成处理时间
- **TTS耗时**: 语音合成处理时间

## 问题排查

### 常见问题

1. **连接失败**
   - 检查后端服务是否启动 (http://localhost:8000/health)
   - 确认端口8000未被占用
   - 检查防火墙设置

2. **录音失败**
   - 检查麦克风权限
   - 确认浏览器支持MediaRecorder API
   - 检查音频设备是否正常

3. **ASR识别失败**
   - 检查ASR服务配置
   - 验证API密钥和端点
   - 查看音频格式是否支持

4. **LLM响应失败**
   - 检查LLM服务配置
   - 验证API密钥
   - 检查网络连接

5. **TTS合成失败**
   - 检查TTS服务配置
   - 验证API密钥
   - 确认文本内容格式

### 调试方法

1. **查看浏览器控制台**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息

2. **查看后端日志**
   - 后端服务会输出详细的处理日志
   - 关注ERROR和WARNING级别的消息

3. **检查测试报告**
   - 自动化测试会生成JSON格式的详细报告
   - 包含每个阶段的执行时间和错误信息

4. **使用健康检查**
   - 访问 http://localhost:8000/health
   - 查看各服务的状态

## 环境要求

### 后端要求

- Python 3.8+
- 必要的Python包 (requirements.txt)
- 环境变量配置:
  - `VOLCANO_API_KEY`: ASR服务API密钥
  - `VOLCANO_ENDPOINT`: ASR服务端点
  - `MINIMAX_API_KEY`: TTS服务API密钥
  - `MINIMAX_GROUP_ID`: TTS服务组ID

### 前端要求

- 现代浏览器 (Chrome, Firefox, Safari, Edge)
- 支持WebRTC和MediaRecorder API
- 麦克风权限
- 音频播放权限

### 网络要求

- 稳定的网络连接
- 能够访问ASR和TTS服务端点
- WebSocket连接支持

## 最佳实践

### 测试建议

1. **首次测试**
   - 使用 `python quick_voice_test.py` 选择选项1
   - 在安静环境中进行录音测试
   - 说话清晰，语速适中

2. **性能测试**
   - 使用自动化测试脚本进行批量测试
   - 记录各阶段的处理时间
   - 对比不同条件下的性能表现

3. **问题诊断**
   - 逐个阶段进行测试
   - 查看详细的日志输出
   - 保存测试报告用于分析

### 优化建议

1. **录音质量**
   - 使用高质量麦克风
   - 在安静环境中录音
   - 保持适当的录音音量

2. **网络优化**
   - 确保稳定的网络连接
   - 考虑使用CDN加速
   - 优化音频数据传输格式

3. **服务配置**
   - 根据实际需求调整超时时间
   - 优化API调用频率
   - 监控服务响应时间

## 总结

这套测试工具提供了完整的语音流水线测试能力，可以帮助你：

- ✅ 验证整个语音对话系统的功能
- ✅ 监控各阶段的性能表现
- ✅ 快速定位和解决问题
- ✅ 优化用户体验

建议从快速启动器开始，选择适合的测试模式，逐步验证系统的各个功能。

---

**快速开始**: `python quick_voice_test.py`

**问题反馈**: 查看生成的测试报告和日志文件