#!/usr/bin/env python3
"""
全面的系统功能测试脚本
测试整个语音对话系统的各个组件和功能
"""

import asyncio
import json
import requests
import websockets
import base64
import numpy as np
import wave
import tempfile
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加backend目录到路径
sys.path.append('backend')

class SystemTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.websocket_url = "ws://localhost:8000/ws"
        self.test_results = []
        
    def log_test(self, test_name, success, details="", error=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": str(error) if error else None,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {details}")
        if error:
            print(f"   Error: {error}")
    
    def test_backend_health(self):
        """测试后端健康状态"""
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                services = data.get("services", {})
                
                # 检查各个服务状态
                service_status = []
                for service, status in services.items():
                    if isinstance(status, dict):
                        service_status.append(f"{service}: {status}")
                    else:
                        service_status.append(f"{service}: {status}")
                
                self.log_test(
                    "Backend Health Check", 
                    True, 
                    f"All services checked. Status: {', '.join(service_status)}"
                )
                return True
            else:
                self.log_test("Backend Health Check", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Backend Health Check", False, error=e)
            return False
    
    def test_authentication(self):
        """测试用户认证功能"""
        try:
            # 测试登录
            login_data = {
                "username": "test_user",
                "password": "test_password"
            }
            
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                user = data.get("user", {})
                token = data.get("token", "")
                
                self.log_test(
                    "User Authentication", 
                    True, 
                    f"Login successful for user: {user.get('username', 'unknown')}"
                )
                return True, token
            else:
                self.log_test("User Authentication", False, f"Login failed: {response.text}")
                return False, None
                
        except Exception as e:
            self.log_test("User Authentication", False, error=e)
            return False, None
    
    def test_npc_endpoints(self):
        """测试NPC相关端点"""
        try:
            response = requests.get(f"{self.backend_url}/npcs", timeout=10)
            if response.status_code == 200:
                data = response.json()
                npcs = data.get("npcs", [])
                source = data.get("source", "unknown")
                
                self.log_test(
                    "NPC Endpoints", 
                    True, 
                    f"Retrieved {len(npcs)} NPCs from {source}"
                )
                return True, npcs
            else:
                self.log_test("NPC Endpoints", False, f"HTTP {response.status_code}")
                return False, []
        except Exception as e:
            self.log_test("NPC Endpoints", False, error=e)
            return False, []
    
    def test_mcp_services(self):
        """测试MCP服务功能"""
        try:
            # 测试MCP服务器列表
            response = requests.get(f"{self.backend_url}/mcp/servers", timeout=10)
            if response.status_code == 200:
                servers_data = response.json()
                servers = servers_data.get("servers", [])
                
                self.log_test(
                    "MCP Servers List", 
                    True, 
                    f"Found {len(servers)} registered MCP servers"
                )
            else:
                self.log_test("MCP Servers List", False, f"HTTP {response.status_code}")
                return False
            
            # 测试MCP工具列表
            response = requests.get(f"{self.backend_url}/mcp/tools", timeout=10)
            if response.status_code == 200:
                tools_data = response.json()
                tools = tools_data.get("tools", [])
                
                # 按类别统计工具
                categories = {}
                for tool in tools:
                    category = tool.get("category", "unknown")
                    if category not in categories:
                        categories[category] = 0
                    categories[category] += 1
                
                category_summary = ", ".join([f"{cat}: {count}" for cat, count in categories.items()])
                
                self.log_test(
                    "MCP Tools List", 
                    True, 
                    f"Found {len(tools)} tools. Categories: {category_summary}"
                )
            else:
                self.log_test("MCP Tools List", False, f"HTTP {response.status_code}")
                return False
            
            # 测试MCP工具执行
            test_params = {
                "tool_name": "search_and_summarize",
                "parameters": {"query": "AI发展趋势测试"}
            }
            
            response = requests.post(
                f"{self.backend_url}/mcp/tools/execute",
                params=test_params,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                success = result.get("success", False)
                
                self.log_test(
                    "MCP Tool Execution", 
                    success, 
                    f"Tool executed: {result.get('tool_name', 'unknown')}"
                )
            else:
                self.log_test("MCP Tool Execution", False, f"HTTP {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("MCP Services", False, error=e)
            return False
    
    def create_test_audio(self, duration=2.0, sample_rate=16000):
        """创建测试音频数据"""
        # 生成简单的正弦波作为测试音频
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        frequency = 440  # A4音符
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
        
        # 转换为16位整数
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        # 创建WAV文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            with wave.open(temp_file.name, 'wb') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_int16.tobytes())
            
            return temp_file.name
    
    def test_audio_processing(self):
        """测试音频处理功能"""
        try:
            # 创建测试音频文件
            audio_file = self.create_test_audio()
            
            try:
                # 测试ASR端点
                with open(audio_file, 'rb') as f:
                    files = {'file': ('test_audio.wav', f, 'audio/wav')}
                    response = requests.post(
                        f"{self.backend_url}/api/asr/transcribe",
                        files=files,
                        timeout=30
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    success = result.get("success", False)
                    transcription = result.get("transcription", "")
                    provider = result.get("provider", "unknown")
                    
                    self.log_test(
                        "ASR Transcription", 
                        success, 
                        f"Provider: {provider}, Result: '{transcription}'"
                    )
                else:
                    self.log_test("ASR Transcription", False, f"HTTP {response.status_code}")
                
                # 测试完整音频处理流水线
                with open(audio_file, 'rb') as f:
                    files = {'file': ('test_audio.wav', f, 'audio/wav')}
                    data = {'user_id': 1, 'npc_id': 1}
                    response = requests.post(
                        f"{self.backend_url}/process-audio",
                        files=files,
                        data=data,
                        timeout=60
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    if "error" not in result:
                        transcription = result.get("transcription", "")
                        response_text = result.get("response_text", "")
                        asr_provider = result.get("asr_provider", "unknown")
                        
                        self.log_test(
                            "Audio Processing Pipeline", 
                            True, 
                            f"ASR: '{transcription}' -> LLM: '{response_text[:50]}...' (Provider: {asr_provider})"
                        )
                    else:
                        self.log_test("Audio Processing Pipeline", False, result.get("error"))
                else:
                    self.log_test("Audio Processing Pipeline", False, f"HTTP {response.status_code}")
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(audio_file)
                except:
                    pass
            
            return True
            
        except Exception as e:
            self.log_test("Audio Processing", False, error=e)
            return False
    
    async def test_websocket_connection(self):
        """测试WebSocket连接和实时通信"""
        try:
            user_id = "test_user_123"
            uri = f"{self.websocket_url}/{user_id}"
            
            async with websockets.connect(uri) as websocket:
                self.log_test("WebSocket Connection", True, f"Connected to {uri}")
                
                # 测试会话启动
                start_session_msg = {
                    "type": "start_session",
                    "npc_id": 1
                }
                
                await websocket.send(json.dumps(start_session_msg))
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    data = json.loads(response)
                    
                    if data.get("type") == "session_started":
                        session_id = data.get("session_id")
                        self.log_test(
                            "WebSocket Session Start", 
                            True, 
                            f"Session started: {session_id}"
                        )
                    else:
                        self.log_test("WebSocket Session Start", False, f"Unexpected response: {data}")
                
                except asyncio.TimeoutError:
                    self.log_test("WebSocket Session Start", False, "Timeout waiting for response")
                
                # 测试音频数据发送
                test_audio_data = np.random.randint(0, 256, 1024, dtype=np.uint8)
                audio_chunk_msg = {
                    "type": "audio_chunk",
                    "data": base64.b64encode(test_audio_data).decode()
                }
                
                await websocket.send(json.dumps(audio_chunk_msg))
                self.log_test("WebSocket Audio Chunk", True, f"Sent {len(test_audio_data)} bytes")
                
                # 测试会话结束
                end_session_msg = {"type": "end_session"}
                await websocket.send(json.dumps(end_session_msg))
                
                self.log_test("WebSocket Session End", True, "Session end message sent")
                
            return True
            
        except Exception as e:
            self.log_test("WebSocket Connection", False, error=e)
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
            },
            "test_results": self.test_results,
            "generated_at": datetime.now().isoformat()
        }
        
        # 保存报告
        report_file = f"system_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n{'='*60}")
        print("📊 SYSTEM TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {report['test_summary']['success_rate']}")
        print(f"Report saved to: {report_file}")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result.get('error', 'Unknown error')}")
        
        return report

async def main():
    """主测试函数"""
    print("🚀 Starting Comprehensive System Test")
    print("="*60)
    
    tester = SystemTester()
    
    # 1. 后端健康检查
    print("\n1. Backend Health Check")
    tester.test_backend_health()
    
    # 2. 认证测试
    print("\n2. Authentication Test")
    auth_success, token = tester.test_authentication()
    
    # 3. NPC端点测试
    print("\n3. NPC Endpoints Test")
    npc_success, npcs = tester.test_npc_endpoints()
    
    # 4. MCP服务测试
    print("\n4. MCP Services Test")
    tester.test_mcp_services()
    
    # 5. 音频处理测试
    print("\n5. Audio Processing Test")
    tester.test_audio_processing()
    
    # 6. WebSocket测试
    print("\n6. WebSocket Connection Test")
    await tester.test_websocket_connection()
    
    # 生成测试报告
    print("\n7. Generating Test Report")
    report = tester.generate_test_report()
    
    return report

if __name__ == "__main__":
    asyncio.run(main())
