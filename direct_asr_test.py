import requests
import os

# --- Configuration ---
BACKEND_URL = "http://localhost:8000/process-audio"
# Use the provided audio file
AUDIO_FILE_PATH = os.path.join(os.path.dirname(__file__), '开拓路-3.m4a') 
# You can change this to '录音测试工具.webm' if you want to test the other file
# AUDIO_FILE_PATH = os.path.join(os.path.dirname(__file__), '录音测试工具.webm') 

def run_test():
    """
    Sends a real audio file directly to the backend for a full pipeline test.
    """
    print(f"🚀 Starting direct ASR test...")
    print(f"📢 Audio File: {AUDIO_FILE_PATH}")
    print(f"🔗 Backend URL: {BACKEND_URL}")

    if not os.path.exists(AUDIO_FILE_PATH):
        print(f"❌ ERROR: Audio file not found at '{AUDIO_FILE_PATH}'")
        return

    try:
        with open(AUDIO_FILE_PATH, 'rb') as f:
            files = {'file': (os.path.basename(AUDIO_FILE_PATH), f, 'audio/m4a')}
            
            # These parameters are required by the endpoint
            data = {'user_id': 1, 'npc_id': 1} 
            
            print("\n📤 Sending request to backend... (This may take a moment)")
            response = requests.post(BACKEND_URL, files=files, data=data, timeout=120) # 2 minute timeout

            print(f"\n✅ Request complete. Status Code: {response.status_code}")
            
            if response.status_code == 200:
                print("\n🎉 SUCCESS! Full pipeline test successful.")
                print("📝 Backend Response:")
                try:
                    print(response.json())
                except requests.exceptions.JSONDecodeError:
                    print("Could not decode JSON, showing raw text:")
                    print(response.text)
            else:
                print("\n🔥 ERROR: Backend returned an error.")
                print("📝 Error Response:")
                try:
                    print(response.json())
                except requests.exceptions.JSONDecodeError:
                    print("Could not decode JSON, showing raw text:")
                    print(response.text)

    except requests.exceptions.RequestException as e:
        print(f"\n❌ NETWORK ERROR: Could not connect to the backend.")
        print(f"   Please ensure the backend server is running at {BACKEND_URL}.")
        print(f"   Error details: {e}")
    except Exception as e:
        print(f"\n❌ An unexpected error occurred: {e}")

if __name__ == "__main__":
    run_test()
