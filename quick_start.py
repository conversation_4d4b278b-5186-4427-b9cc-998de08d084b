#!/usr/bin/env python3
"""
快速启动语音对话系统
"""

import subprocess
import time
import sys
import os
import requests

def check_service(port, name):
    """检查服务是否运行"""
    try:
        response = requests.get(f'http://localhost:{port}/', timeout=2)
        print(f"✅ {name} (端口{port}): 运行正常")
        return True
    except:
        print(f"❌ {name} (端口{port}): 未运行")
        return False

def main():
    print("🚀 语音对话系统快速启动")
    print("=" * 40)
    
    # 检查服务状态
    backend_ok = check_service(8000, "后端ASR服务")
    tts_ok = check_service(8001, "TTS API服务") 
    web_ok = check_service(8002, "Web服务器")
    
    if not web_ok:
        print("\n🔧 重新启动Web服务器...")
        # 杀死可能存在的进程
        os.system("pkill -f 'python.*http.server.*8002'")
        time.sleep(1)
        
        # 启动新的Web服务器
        subprocess.Popen([sys.executable, '-m', 'http.server', '8002'])
        time.sleep(2)
        
        web_ok = check_service(8002, "Web服务器")
    
    if not backend_ok:
        print("\n⚠️ 后端ASR服务未运行，请在新终端执行:")
        print("cd backend && python main.py")
    
    if not tts_ok:
        print("\n⚠️ TTS API服务未运行，请在新终端执行:")
        print("python start_real_voice_test.py")
    
    if backend_ok and tts_ok and web_ok:
        print("\n🎉 所有服务运行正常！")
        print("🌐 请打开浏览器访问:")
        print("   http://localhost:8002/real_voice_pipeline_test.html")
        print("\n📋 测试步骤:")
        print("1. 点击'开始录音'按钮")
        print("2. 说话后点击'停止录音'")
        print("3. 查看ASR识别结果")
        print("4. 等待LLM回复和TTS语音播放")
    else:
        print("\n❌ 部分服务未运行，请按提示启动缺失的服务")

if __name__ == "__main__":
    main()