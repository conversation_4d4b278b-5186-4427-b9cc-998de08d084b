#!/usr/bin/env python3
"""
修改版的后端主函数
跳过VAD，使用模拟服务进行测试
"""
import asyncio
import websockets
import json
import base64
import requests
import numpy as np
import wave
import logging
from pathlib import Path
import time
from datetime import datetime
import sys
import os

# 添加backend目录到Python路径
sys.path.append('backend')

from services.multimodal_asr_service import MultimodalASRService
from services.llm_service import LLMService
from services.tts_service import TTSService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockTTSService:
    """模拟TTS服务"""
    async def synthesize_speech_stream(self, text, emotion="neutral", speed=1.0):
        """模拟语音合成流"""
        # 模拟处理时间
        await asyncio.sleep(0.2)
        
        # 生成模拟音频数据
        sample_rate = 16000
        duration = min(len(text) * 0.08, 3.0)  # 根据文本长度估算时长
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = (np.sin(2 * np.pi * 440 * t) * 0.5).astype(np.float32)
        
        # 转换为WAV格式的字节数据
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        # 创建WAV文件数据
        import io
        buffer = io.BytesIO()
        with wave.open(buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        audio_bytes = buffer.getvalue()
        
        logger.info(f"🔊 Mock TTS generated: {len(audio_bytes)} bytes for '{text[:30]}...'")
        
        # 分块返回音频数据
        chunk_size = 4096
        for i in range(0, len(audio_bytes), chunk_size):
            chunk = audio_bytes[i:i + chunk_size]
            yield chunk
            await asyncio.sleep(0.01)  # 模拟流式传输

class MockWebSocketManager:
    """模拟WebSocket管理器"""
    def __init__(self):
        self.active_connections = {}
        self.user_sessions = {}
    
    async def connect(self, websocket, user_id):
        """连接WebSocket"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        
        # 初始化用户会话
        self.user_sessions[user_id] = {
            "session_id": f"mock_session_{int(time.time())}",
            "npc_id": 1,
            "audio_buffer": [],
            "is_speaking": False,
            "conversation_history": []
        }
        
        logger.info(f"✅ User {user_id} connected")
    
    def disconnect(self, user_id):
        """断开WebSocket"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        logger.info(f"❌ User {user_id} disconnected")
    
    async def send_message(self, user_id, message):
        """发送消息给用户"""
        if user_id in self.active_connections:
            websocket = self.active_connections[user_id]
            try:
                await websocket.send(json.dumps(message))
                logger.info(f"📤 Sent to {user_id}: {message.get('type', 'unknown')}")
            except Exception as e:
                logger.error(f"❌ Failed to send message to {user_id}: {e}")

# 初始化服务
asr_service = MultimodalASRService(
    model="Qwen2-Audio-7B-Instruct",
    api_key="EMPTY",
    api_base="http://172.16.1.151:20257/v1"
)

# 使用模拟LLM服务
class MockLLMService:
    async def generate_response_stream(self, user_input, conversation_history, system_prompt):
        """模拟LLM流式响应"""
        await asyncio.sleep(0.1)  # 模拟处理时间
        
        # 生成响应
        responses = {
            "你好": "你好！很高兴见到你！",
            "测试": "测试成功！系统运行正常。",
            "请问你能听到我说话吗": "是的，我能听到你说话！语音识别功能工作正常。",
            "语音识别": "语音识别功能已经成功运行。",
        }
        
        response_text = None
        for key, value in responses.items():
            if key in user_input:
                response_text = value
                break
        
        if not response_text:
            response_text = f"我收到了你的消息：{user_input}。这是一个智能回复。"
        
        logger.info(f"🧠 Mock LLM response: '{response_text}'")
        
        # 返回speak格式的响应
        yield {
            "type": "speak",
            "text": response_text,
            "emotion": "friendly",
            "speed": 1.0
        }

llm_service = MockLLMService()
tts_service = MockTTSService()
manager = MockWebSocketManager()

def save_audio_to_wav(audio_array, filename, sample_rate=16000):
    """保存音频数组为WAV文件"""
    try:
        # 确保音频目录存在
        audio_dir = Path("backend/audio_recordings")
        audio_dir.mkdir(exist_ok=True)
        
        filepath = audio_dir / filename
        
        # 转换为int16格式
        if audio_array.dtype != np.int16:
            if audio_array.dtype == np.float32 or audio_array.dtype == np.float64:
                audio_int16 = (audio_array * 32767).astype(np.int16)
            else:
                audio_int16 = audio_array.astype(np.int16)
        else:
            audio_int16 = audio_array
        
        # 保存为WAV文件
        with wave.open(str(filepath), 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        logger.info(f"💾 Audio saved: {filepath} ({len(audio_int16)} samples)")
        
    except Exception as e:
        logger.error(f"❌ Failed to save audio: {e}")

async def process_audio_chunk(user_id: str, audio_data: bytes):
    """处理音频块 - 修改版，跳过VAD"""
    try:
        logger.info(f"🎤 Processing audio chunk for user {user_id}, size: {len(audio_data)} bytes")
        
        session = manager.user_sessions.get(user_id)
        if not session:
            logger.warning(f"⚠️ No session found for user {user_id}")
            return
        
        # 转换音频数据为numpy数组
        try:
            if len(audio_data) % 2 == 0:
                audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            else:
                logger.warning(f"⚠️ Audio data has odd length ({len(audio_data)} bytes)")
                audio_uint8 = np.frombuffer(audio_data, dtype=np.uint8)
                audio_array = (audio_uint8.astype(np.float32) - 128.0) / 128.0
        except Exception as e:
            logger.error(f"❌ Audio parsing failed: {e}")
            return
        
        # 保存音频块用于调试
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        chunk_filename = f"chunk_{user_id}_{timestamp}.wav"
        save_audio_to_wav(audio_array, chunk_filename)
        
        # 添加到缓冲区
        session["audio_buffer"].extend(audio_array)
        logger.info(f"📊 Audio buffer size: {len(session['audio_buffer'])} samples")
        
        # 跳过VAD，直接处理音频（当缓冲区足够大时）
        if len(session["audio_buffer"]) >= 16000:  # 1秒音频
            buffer_array = np.array(session["audio_buffer"])
            
            logger.info(f"🚀 Processing audio directly (VAD skipped): buffer_size={len(buffer_array)}")
            
            # 保存完整音频段
            segment_filename = f"segment_{user_id}_{timestamp}.wav"
            save_audio_to_wav(buffer_array, segment_filename)
            
            # 直接处理音频段
            await process_speech_segment(user_id, buffer_array)
            session["audio_buffer"] = []
    
    except Exception as e:
        logger.error(f"❌ Error processing audio chunk: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

async def process_speech_segment(user_id: str, audio_data: np.ndarray):
    """处理语音段"""
    try:
        session = manager.user_sessions.get(user_id)
        if not session or session["is_speaking"]:
            return
        
        session["is_speaking"] = True
        logger.info(f"🎯 Processing speech segment for user {user_id}")
        
        # ASR处理
        logger.info("🎯 Starting ASR processing...")
        transcription = asr_service.transcribe(audio_data)
        
        if not transcription["text"] or transcription["confidence"] < 0.5:
            logger.warning("⚠️ ASR result not confident enough")
            session["is_speaking"] = False
            return
        
        # 发送转录结果
        await manager.send_message(user_id, {
            "type": "transcription",
            "text": transcription["text"],
            "confidence": transcription["confidence"]
        })
        
        logger.info(f"✅ ASR result: '{transcription['text']}'")
        
        # LLM处理
        logger.info("🧠 Starting LLM processing...")
        async for chunk in llm_service.generate_response_stream(
            transcription["text"],
            session["conversation_history"],
            "你是一个友好的AI助手"
        ):
            if chunk["type"] == "speak":
                # 发送LLM响应
                await manager.send_message(user_id, {
                    "type": "response",
                    "text": chunk["text"]
                })
                
                logger.info(f"✅ LLM response: '{chunk['text']}'")
                
                # TTS处理
                logger.info("🔊 Starting TTS processing...")
                async for audio_chunk in tts_service.synthesize_speech_stream(
                    chunk["text"],
                    chunk["emotion"],
                    chunk["speed"]
                ):
                    if audio_chunk and session["is_speaking"]:
                        # 发送音频块
                        audio_b64 = base64.b64encode(audio_chunk).decode()
                        await manager.send_message(user_id, {
                            "type": "audio_chunk",
                            "data": audio_b64
                        })
                
                logger.info("✅ TTS processing completed")
                
                # 发送完成信号
                await manager.send_message(user_id, {
                    "type": "response_complete"
                })
                
                break
        
        session["is_speaking"] = False
        logger.info("✅ Speech segment processing completed")
        
    except Exception as e:
        logger.error(f"❌ Error processing speech segment: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        session = manager.user_sessions.get(user_id)
        if session:
            session["is_speaking"] = False

async def websocket_endpoint(websocket, user_id):
    """WebSocket端点"""
    await manager.connect(websocket, user_id)
    
    try:
        # 发送连接确认
        await manager.send_message(user_id, {
            "type": "connection_established",
            "connection_id": f"mock_conn_{user_id}_{int(time.time())}"
        })
        
        async for message in websocket:
            try:
                data = json.loads(message)
                message_type = data.get("type")
                
                logger.info(f"📥 Received from {user_id}: {message_type}")
                
                if message_type == "start_session":
                    npc_id = data.get("npc_id", 1)
                    session = manager.user_sessions[user_id]
                    session["npc_id"] = npc_id
                    
                    await manager.send_message(user_id, {
                        "type": "session_started",
                        "session_id": session["session_id"],
                        "npc_id": npc_id
                    })
                
                elif message_type == "audio_chunk":
                    audio_data = base64.b64decode(data["data"])
                    await process_audio_chunk(user_id, audio_data)
                
                elif message_type == "end_session":
                    await manager.send_message(user_id, {
                        "type": "session_ended"
                    })
                
                else:
                    logger.warning(f"⚠️ Unknown message type: {message_type}")
                    
            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON decode error: {e}")
            except Exception as e:
                logger.error(f"❌ Message processing error: {e}")
    
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"🔌 WebSocket connection closed for user {user_id}")
    except Exception as e:
        logger.error(f"❌ WebSocket error for user {user_id}: {e}")
    finally:
        manager.disconnect(user_id)

async def test_mock_websocket():
    """测试模拟WebSocket服务"""
    logger.info("🧪 启动模拟WebSocket服务测试...")
    
    # 模拟WebSocket服务器
    class MockWebSocket:
        def __init__(self):
            self.messages = []
            self.closed = False
        
        async def accept(self):
            logger.info("✅ Mock WebSocket accepted")
        
        async def send(self, message):
            self.messages.append(message)
            data = json.loads(message)
            logger.info(f"📤 Mock WebSocket sent: {data.get('type', 'unknown')}")
        
        async def recv(self):
            # 模拟接收消息
            await asyncio.sleep(0.1)
            return json.dumps({"type": "test", "data": "mock_data"})
        
        def close(self):
            self.closed = True
    
    # 创建模拟WebSocket
    mock_ws = MockWebSocket()
    user_id = "test_user_1"
    
    try:
        # 测试连接
        await manager.connect(mock_ws, user_id)
        
        # 测试会话启动
        await manager.send_message(user_id, {
            "type": "session_started",
            "session_id": "test_session_123",
            "npc_id": 1
        })
        
        # 生成测试音频
        sample_rate = 16000
        duration = 2.0
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = (np.sin(2 * np.pi * 440 * t) * 32767).astype(np.int16)
        audio_bytes = audio_data.tobytes()
        
        # 测试音频处理
        await process_audio_chunk(user_id, audio_bytes)
        
        logger.info("✅ Mock WebSocket test completed successfully")
        logger.info(f"📊 Messages sent: {len(mock_ws.messages)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Mock WebSocket test failed: {e}")
        return False
    finally:
        manager.disconnect(user_id)

async def main():
    """主函数"""
    logger.info("🚀 启动修改版后端服务（跳过VAD，使用模拟服务）")
    logger.info("=" * 60)
    
    # 运行模拟WebSocket测试
    success = await test_mock_websocket()
    
    if success:
        logger.info("🎉 模拟后端服务测试成功！")
        logger.info("💡 完整流程验证通过：录音→ASR→LLM→TTS→播放")
        logger.info("🔧 建议：现在可以集成到真实的WebSocket服务中")
    else:
        logger.error("❌ 模拟后端服务测试失败！")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())