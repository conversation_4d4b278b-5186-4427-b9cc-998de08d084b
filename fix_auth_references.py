#!/usr/bin/env python3
"""
批量修复认证服务引用
"""
import os
import re

def replace_in_file(file_path, old_text, new_text):
    """替换文件中的文本"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if old_text in content:
            content = content.replace(old_text, new_text)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复了 {file_path}")
        else:
            print(f"⚠️  {file_path} 中没有找到 {old_text}")
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")

# 需要修复的文件
files_to_fix = [
    'frontend/lib/screens/login_screen.dart',
    'frontend/lib/screens/npc_selection_screen.dart', 
    'frontend/lib/screens/voice_chat_screen.dart'
]

# 替换所有的 simpleAuthServiceProvider 为 memoryAuthServiceProvider
for file_path in files_to_fix:
    if os.path.exists(file_path):
        replace_in_file(file_path, 'simpleAuthServiceProvider', 'memoryAuthServiceProvider')
    else:
        print(f"❌ 文件不存在: {file_path}")

print("🎉 批量修复完成！")