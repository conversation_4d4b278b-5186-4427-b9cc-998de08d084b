#!/usr/bin/env python3
"""
启动前端应用的说明脚本
"""
import os
import sys
import subprocess
import webbrowser

def main():
    """显示启动前端应用的说明"""
    print("📱 语音聊天前端应用启动说明")
    print("=" * 50)
    
    print("\n📋 前端应用信息:")
    print("   应用名称: Voice Chat App")
    print("   开发框架: Flutter")
    print("   主入口文件: frontend/lib/main.dart")
    
    print("\n⚙️  启动步骤:")
    print("   1. 确保已安装 Flutter SDK")
    print("      - 下载地址: https://flutter.dev/docs/get-started/install")
    print("      - 安装后请将 Flutter 添加到系统 PATH")
    
    print("\n   2. 安装项目依赖")
    print("      cd frontend")
    print("      flutter pub get")
    
    print("\n   3. 启动开发服务器")
    print("      flutter run")
    print("      或指定设备:")
    print("      flutter run -d chrome  # Web")
    print("      flutter run -d android # Android")
    print("      flutter run -d ios     # iOS")
    
    print("\n   4. 构建应用 (可选)")
    print("      flutter build apk      # Android APK")
    print("      flutter build ios      # iOS")
    print("      flutter build web      # Web")
    
    print("\n🔗 相关链接:")
    print("   - Flutter 文档: https://flutter.dev/docs")
    print("   - 项目文档: frontend/README.md")
    print("   - 测试文档: frontend/test/README.md")
    
    print("\n💡 提示:")
    print("   - 确保后端服务已在运行 (http://localhost:8000)")
    print("   - 前端配置文件: frontend/lib/config/app_config.dart")
    print("   - 默认后端地址: http://localhost:8000")
    
    print("\n📂 项目结构:")
    print("   frontend/")
    print("   ├── lib/              # 应用源代码")
    print("   │   ├── main.dart     # 应用入口")
    print("   │   ├── screens/      # 页面组件")
    print("   │   ├── services/     # 业务逻辑")
    print("   │   └── models/       # 数据模型")
    print("   ├── test/             # 测试文件")
    print("   ├── assets/           # 静态资源")
    print("   └── pubspec.yaml      # 项目依赖")
    
    print("\n" + "=" * 50)
    print("✅ 前端应用启动说明已完成")
    
    # Check if Flutter is installed
    try:
        result = subprocess.run(["flutter", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("\n🎉 检测到 Flutter 已安装")
            print("   版本信息:", result.stdout.strip())
            
            # Ask user if they want to start the app
            choice = input("\n🚀 是否现在启动前端应用? (y/N): ").strip().lower()
            if choice in ['y', 'yes']:
                print("\n🏃‍♂️ 正在启动前端应用...")
                os.chdir("frontend")
                subprocess.run(["flutter", "run"])
        else:
            print("\n⚠️  Flutter 未正确安装或未添加到 PATH")
    except FileNotFoundError:
        print("\n⚠️  未检测到 Flutter，请先安装 Flutter SDK")

if __name__ == "__main__":
    main()
