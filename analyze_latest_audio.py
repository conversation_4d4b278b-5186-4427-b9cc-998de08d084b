#!/usr/bin/env python3
"""
分析最新的音频文件
"""

import os
import wave
import numpy as np
from pathlib import Path

def analyze_wav_file(file_path):
    """分析WAV文件"""
    try:
        with wave.open(file_path, 'rb') as wav_file:
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            sample_width = wav_file.getsampwidth()
            duration = frames / sample_rate
            
            print(f"📁 文件: {os.path.basename(file_path)}")
            print(f"📊 时长: {duration:.2f} 秒")
            print(f"🎵 采样率: {sample_rate} Hz")
            print(f"🔊 声道数: {channels}")
            print(f"📏 样本宽度: {sample_width} 字节")
            print(f"📈 总帧数: {frames}")
            print(f"💾 文件大小: {os.path.getsize(file_path)} 字节")
            
            # 读取音频数据
            audio_data = wav_file.readframes(frames)
            if sample_width == 2:  # 16-bit
                audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            elif sample_width == 1:  # 8-bit
                audio_array = np.frombuffer(audio_data, dtype=np.uint8).astype(np.float32) / 128.0 - 1.0
            else:
                print(f"⚠️ 不支持的样本宽度: {sample_width}")
                return
            
            # 分析音频特征
            rms = np.sqrt(np.mean(audio_array**2))
            max_amplitude = np.max(np.abs(audio_array))
            
            print(f"🔊 RMS 音量: {rms:.4f}")
            print(f"📊 最大幅度: {max_amplitude:.4f}")
            
            # 检查是否为静音
            if max_amplitude < 0.01:
                print("⚠️ 检测到静音或极低音量")
            elif max_amplitude > 0.8:
                print("⚠️ 检测到可能的音频削波")
            else:
                print("✅ 音频幅度正常")
            
            # 简单的语音活动检测
            frame_size = int(sample_rate * 0.025)  # 25ms frames
            hop_size = int(sample_rate * 0.010)   # 10ms hop
            
            speech_frames = 0
            total_frames = 0
            
            for i in range(0, len(audio_array) - frame_size, hop_size):
                frame = audio_array[i:i + frame_size]
                frame_energy = np.sum(frame ** 2)
                total_frames += 1
                
                if frame_energy > 0.001:  # 简单的能量阈值
                    speech_frames += 1
            
            speech_ratio = speech_frames / total_frames if total_frames > 0 else 0
            print(f"🗣️ 语音活动比例: {speech_ratio:.2%}")
            
            if speech_ratio < 0.1:
                print("⚠️ 检测到很少的语音活动")
            elif speech_ratio > 0.5:
                print("✅ 检测到较多的语音活动")
            else:
                print("🔍 检测到中等语音活动")
                
            print("-" * 50)
            
    except Exception as e:
        print(f"❌ 分析文件失败 {file_path}: {e}")

def main():
    """主函数"""
    audio_dir = Path("backend/audio_recordings")
    
    if not audio_dir.exists():
        print("❌ 音频目录不存在")
        return
    
    # 查找最新的完整音频文件
    complete_files = list(audio_dir.glob("complete_*.wav"))
    if not complete_files:
        print("❌ 没有找到完整音频文件")
        return
    
    # 按修改时间排序，获取最新的几个文件
    complete_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    print("🔍 分析最新的完整音频文件:")
    print("=" * 60)
    
    # 分析最新的3个文件
    for file_path in complete_files[:3]:
        analyze_wav_file(str(file_path))

if __name__ == "__main__":
    main()