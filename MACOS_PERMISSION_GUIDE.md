# 🎤 macOS麦克风权限设置指南

## 方法1: 系统偏好设置
1. 打开 **系统偏好设置** (System Preferences)
2. 点击 **安全性与隐私** (Security & Privacy)
3. 选择 **隐私** (Privacy) 标签
4. 在左侧列表中找到 **麦克风** (Microphone)
5. 在右侧列表中找到你的Flutter应用 (可能显示为 "Runner" 或 "voice_chat_app")
6. 勾选应用名称旁边的复选框以授予权限

## 方法2: 系统设置 (macOS 13+)
1. 打开 **系统设置** (System Settings)
2. 点击 **隐私与安全性** (Privacy & Security)
3. 点击 **麦克风** (Microphone)
4. 找到你的Flutter应用并开启权限

## 方法3: 命令行授权 (开发者选项)
```bash
# 重置应用权限 (会提示重新授权)
tccutil reset Microphone com.example.voiceChatApp

# 或者直接授权 (需要管理员权限)
sudo tccutil reset All com.example.voiceChatApp
```

## 方法4: 应用首次运行时授权
1. 重新启动应用
2. 当应用尝试访问麦克风时，系统会弹出权限请求对话框
3. 点击 **允许** (Allow) 或 **好** (OK)

## 验证权限
权限设置成功后，应用日志应该显示：
```
🎤 Checking microphone permission...
✅ Recorder initialized successfully
🎤 Starting Flutter Sound recording...
✅ Flutter Sound recording started successfully
```

## 故障排除
如果仍然无法获取权限：
1. 完全退出应用
2. 在系统设置中移除应用的麦克风权限
3. 重新启动应用
4. 系统会重新提示权限请求

## 注意事项
- macOS对麦克风权限管理很严格
- 每个应用都需要单独授权
- 权限设置后可能需要重启应用才能生效