<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 完整语音流水线测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .pipeline-status {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin: 30px 0;
        }
        .stage {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .stage.active {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .stage.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .stage.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .stage h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #666;
        }
        .stage .icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .stage .status {
            font-size: 12px;
            color: #666;
        }
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .record-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            font-size: 18px;
            padding: 20px 40px;
        }
        .record-btn.recording {
            background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .results-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .result-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        .result-section h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .result-content {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            min-height: 100px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .audio-section {
            margin: 20px 0;
        }
        audio {
            width: 100%;
            margin: 10px 0;
        }
        .log-panel {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 30px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
        }
        .log-entry.info { color: #63b3ed; }
        .log-entry.success { color: #68d391; }
        .log-entry.warning { color: #fbd38d; }
        .log-entry.error { color: #fc8181; }
        .metrics {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 完整语音流水线测试</h1>
        
        <!-- 流水线状态显示 -->
        <div class="pipeline-status">
            <div class="stage" id="stage-recording">
                <div class="icon">🎤</div>
                <h3>录音</h3>
                <div class="status">待开始</div>
            </div>
            <div class="stage" id="stage-asr">
                <div class="icon">🎯</div>
                <h3>语音识别</h3>
                <div class="status">等待中</div>
            </div>
            <div class="stage" id="stage-llm">
                <div class="icon">🧠</div>
                <h3>对话生成</h3>
                <div class="status">等待中</div>
            </div>
            <div class="stage" id="stage-tts">
                <div class="icon">🔊</div>
                <h3>语音合成</h3>
                <div class="status">等待中</div>
            </div>
            <div class="stage" id="stage-playback">
                <div class="icon">▶️</div>
                <h3>音频播放</h3>
                <div class="status">等待中</div>
            </div>
        </div>
        
        <!-- 状态面板 -->
        <div class="status-panel">
            <div class="status-item">
                <span>当前阶段:</span>
                <span id="currentStage">准备中</span>
            </div>
            <div class="status-item">
                <span>处理时间:</span>
                <span id="processingTime">0秒</span>
            </div>
        </div>
        
        <!-- 控制按钮 -->
        <div class="controls">
            <button id="recordBtn" class="record-btn" onclick="toggleRecording()">开始录音</button>
            <button id="startBtn" onclick="startPipeline()">开始完整测试</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <!-- 性能指标 -->
        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="recordingDuration">0</div>
                <div class="metric-label">录音时长(秒)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="asrTime">0</div>
                <div class="metric-label">ASR耗时(秒)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="llmTime">0</div>
                <div class="metric-label">LLM耗时(秒)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="ttsTime">0</div>
                <div class="metric-label">TTS耗时(秒)</div>
            </div>
        </div>
        
        <!-- 结果显示 -->
        <div class="results-panel">
            <div class="result-section">
                <h3>🎯 ASR识别结果</h3>
                <div class="result-content" id="asrResult">等待语音输入...</div>
            </div>
            <div class="result-section">
                <h3>🧠 LLM对话响应</h3>
                <div class="result-content" id="llmResult">等待对话生成...</div>
            </div>
        </div>
        
        <!-- 音频播放区域 -->
        <div class="audio-section">
            <h3>🔊 音频播放</h3>
            <div>
                <label>录制的音频:</label>
                <audio id="recordedAudio" controls style="display: none;"></audio>
            </div>
            <div>
                <label>AI响应音频:</label>
                <audio id="responseAudio" controls style="display: none;"></audio>
            </div>
        </div>
        
        <!-- 日志面板 -->
        <div class="log-panel" id="logPanel">
            <div class="log-entry info">🚀 语音流水线测试工具已加载</div>
            <div class="log-entry info">💡 点击"开始录音"或"开始完整测试"开始</div>
        </div>
    </div>

    <script>
        // 全局变量
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;
        let startTime = null;
        let stageTimers = {};
        
        // DOM元素
        const elements = {
            currentStage: document.getElementById('currentStage'),
            processingTime: document.getElementById('processingTime'),
            recordBtn: document.getElementById('recordBtn'),
            startBtn: document.getElementById('startBtn'),
            asrResult: document.getElementById('asrResult'),
            llmResult: document.getElementById('llmResult'),
            recordedAudio: document.getElementById('recordedAudio'),
            responseAudio: document.getElementById('responseAudio'),
            logPanel: document.getElementById('logPanel'),
            recordingDuration: document.getElementById('recordingDuration'),
            asrTime: document.getElementById('asrTime'),
            llmTime: document.getElementById('llmTime'),
            ttsTime: document.getElementById('ttsTime')
        };

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            elements.logPanel.appendChild(logEntry);
            elements.logPanel.scrollTop = elements.logPanel.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 更新阶段状态
        function updateStage(stageName, status, statusText) {
            const stageEl = document.getElementById(`stage-${stageName}`);
            if (stageEl) {
                stageEl.className = `stage ${status}`;
                stageEl.querySelector('.status').textContent = statusText;
            }
            
            elements.currentStage.textContent = `${stageName} - ${statusText}`;
            
            if (status === 'active') {
                stageTimers[stageName] = Date.now();
                log(`🔄 开始 ${stageName} 阶段`, 'info');
            } else if (status === 'success') {
                const duration = stageTimers[stageName] ? 
                    ((Date.now() - stageTimers[stageName]) / 1000).toFixed(2) : '0';
                log(`✅ ${stageName} 阶段完成 (${duration}秒)`, 'success');
                
                // 更新性能指标
                if (stageName === 'asr') elements.asrTime.textContent = duration;
                else if (stageName === 'llm') elements.llmTime.textContent = duration;
                else if (stageName === 'tts') elements.ttsTime.textContent = duration;
            } else if (status === 'error') {
                log(`❌ ${stageName} 阶段失败: ${statusText}`, 'error');
            }
        }

        // 更新处理时间
        function updateProcessingTime() {
            if (startTime) {
                const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
                elements.processingTime.textContent = `${elapsed}秒`;
            }
        }

        // 重置所有阶段
        function resetStages() {
            const stages = ['recording', 'asr', 'llm', 'tts', 'playback'];
            stages.forEach(stage => {
                const stageEl = document.getElementById(`stage-${stage}`);
                if (stageEl) {
                    stageEl.className = 'stage';
                    stageEl.querySelector('.status').textContent = '等待中';
                }
            });
            document.getElementById('stage-recording').querySelector('.status').textContent = '待开始';
        }

        // 切换录音状态
        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                stopRecording();
            }
        }

        // 开始录音
        async function startRecording() {
            try {
                log('🎤 请求麦克风权限...', 'info');
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                const options = {};
                if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
                    options.mimeType = 'audio/webm;codecs=opus';
                    log('✅ 使用WebM/Opus格式录音', 'info');
                } else {
                    log('⚠️ 使用默认录音格式', 'warning');
                }

                mediaRecorder = new MediaRecorder(stream, options);
                audioChunks = [];
                
                // 重置阶段和计时器
                resetStages();
                startTime = Date.now();
                const recordingStartTime = Date.now();
                
                updateStage('recording', 'active', '录音中');

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                        log(`📦 收到音频数据块: ${event.data.size} bytes`, 'info');
                    }
                };

                mediaRecorder.onstop = function() {
                    const recordingDuration = ((Date.now() - recordingStartTime) / 1000).toFixed(2);
                    elements.recordingDuration.textContent = recordingDuration;
                    
                    updateStage('recording', 'success', '录音完成');
                    
                    const audioBlob = new Blob(audioChunks, { type: options.mimeType || 'audio/webm' });
                    log(`🎵 录音完成: ${audioBlob.size} bytes, 时长: ${recordingDuration}秒`, 'success');
                    
                    // 保存录音用于播放
                    const recordedUrl = URL.createObjectURL(audioBlob);
                    elements.recordedAudio.src = recordedUrl;
                    elements.recordedAudio.style.display = 'block';
                    
                    // 自动开始处理流水线
                    processAudioPipeline(audioBlob);
                    
                    // 停止音频流
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.onerror = function(event) {
                    log(`❌ 录音错误: ${event.error}`, 'error');
                    updateStage('recording', 'error', '录音失败');
                };

                mediaRecorder.start(1000);
                isRecording = true;
                
                elements.recordBtn.textContent = '停止录音';
                elements.recordBtn.className = 'record-btn recording';
                
                log('✅ 录音已开始', 'success');

            } catch (error) {
                log(`❌ 录音启动失败: ${error.message}`, 'error');
                updateStage('recording', 'error', '权限被拒绝');
            }
        }

        // 停止录音
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                log('🛑 停止录音...', 'info');
                mediaRecorder.stop();
                isRecording = false;
                
                elements.recordBtn.textContent = '开始录音';
                elements.recordBtn.className = 'record-btn';
            }
        }

        // 处理音频流水线
        async function processAudioPipeline(audioBlob) {
            try {
                log('🔄 开始处理音频流水线...', 'info');
                
                // 模拟ASR阶段
                updateStage('asr', 'active', '识别中');
                await simulateDelay(2000); // 模拟2秒处理时间
                
                const mockAsrResults = [
                    "你好，我想测试一下语音识别功能",
                    "请问你能听到我说话吗？",
                    "这是一个完整的语音流水线测试",
                    "我正在测试录音到播放的完整流程"
                ];
                const asrResult = mockAsrResults[Math.floor(Math.random() * mockAsrResults.length)];
                
                updateStage('asr', 'success', '识别完成');
                elements.asrResult.textContent = `识别文本: "${asrResult}"\n置信度: 95.2%`;
                log(`🎯 ASR识别结果: "${asrResult}"`, 'success');
                
                // 模拟LLM阶段
                updateStage('llm', 'active', '生成中');
                await simulateDelay(3000); // 模拟3秒处理时间
                
                const mockLlmResponses = [
                    "你好！我听到了你的声音，语音识别功能工作正常。很高兴为你服务！",
                    "是的，我能清楚地听到你说话。你的声音很清晰，系统运行良好。",
                    "完整的语音流水线测试正在顺利进行，所有组件都在正常工作。",
                    "录音到播放的完整流程测试成功！系统的各个环节都运行正常。"
                ];
                const llmResult = mockLlmResponses[Math.floor(Math.random() * mockLlmResponses.length)];
                
                updateStage('llm', 'success', '生成完成');
                elements.llmResult.textContent = `对话响应: "${llmResult}"`;
                log(`🧠 LLM响应: "${llmResult}"`, 'success');
                
                // 模拟TTS阶段
                updateStage('tts', 'active', '合成中');
                await simulateDelay(2500); // 模拟2.5秒处理时间
                
                // 创建模拟音频（实际应用中这里会是真实的TTS音频）
                const mockAudioData = createMockAudio();
                const audioUrl = URL.createObjectURL(mockAudioData);
                
                updateStage('tts', 'success', '合成完成');
                log(`🔊 TTS合成完成: ${mockAudioData.size} bytes`, 'success');
                
                // 播放阶段
                updateStage('playback', 'active', '播放中');
                elements.responseAudio.src = audioUrl;
                elements.responseAudio.style.display = 'block';
                
                // 自动播放
                try {
                    await elements.responseAudio.play();
                    updateStage('playback', 'success', '播放完成');
                    log('🎵 音频播放完成', 'success');
                } catch (e) {
                    updateStage('playback', 'success', '准备播放');
                    log('🎵 音频已准备，请手动播放', 'info');
                }
                
                // 完成
                const totalTime = startTime ? ((Date.now() - startTime) / 1000).toFixed(2) : '0';
                log(`✅ 完整流水线处理完成！总耗时: ${totalTime}秒`, 'success');
                startTime = null;
                
            } catch (error) {
                log(`❌ 流水线处理失败: ${error.message}`, 'error');
            }
        }

        // 模拟延迟
        function simulateDelay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 创建模拟音频
        function createMockAudio() {
            // 创建一个简单的音频文件（实际应用中会是真实的TTS音频）
            const sampleRate = 16000;
            const duration = 3; // 3秒
            const samples = sampleRate * duration;
            const buffer = new ArrayBuffer(44 + samples * 2);
            const view = new DataView(buffer);
            
            // WAV文件头
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + samples * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, samples * 2, true);
            
            // 生成简单的正弦波音频数据
            for (let i = 0; i < samples; i++) {
                const sample = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.3;
                view.setInt16(44 + i * 2, sample * 32767, true);
            }
            
            return new Blob([buffer], { type: 'audio/wav' });
        }

        // 开始完整测试
        async function startPipeline() {
            log('🚀 开始完整流水线测试...', 'info');
            
            // 自动录音3秒
            log('🎤 自动录音测试 (3秒)...', 'info');
            await startRecording();
            
            setTimeout(() => {
                stopRecording();
                log('✅ 自动录音测试完成', 'success');
            }, 3000);
        }

        // 清空结果
        function clearResults() {
            elements.asrResult.textContent = '等待语音输入...';
            elements.llmResult.textContent = '等待对话生成...';
            elements.recordedAudio.style.display = 'none';
            elements.responseAudio.style.display = 'none';
            elements.recordingDuration.textContent = '0';
            elements.asrTime.textContent = '0';
            elements.llmTime.textContent = '0';
            elements.ttsTime.textContent = '0';
            elements.processingTime.textContent = '0秒';
            resetStages();
            log('🗑️ 结果已清空', 'info');
        }

        // 定时更新处理时间
        setInterval(updateProcessingTime, 100);

        // 页面加载完成
        window.onload = function() {
            log('🌐 页面加载完成，准备开始测试', 'info');
        };
    </script>
</body>
</html>