#!/usr/bin/env python3
"""
Setup script for Real-time Voice Chat App
This script helps download and setup required models and dependencies.
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
from pathlib import Path

def create_directories():
    """Create necessary directories"""
    directories = [
        "backend/models",
        "backend/logs",
        "frontend/assets/images",
        "frontend/assets/animations",
        "frontend/assets/icons",
        "frontend/assets/fonts"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def download_silero_vad():
    """Download Silero VAD ONNX model"""
    model_url = "https://github.com/snakers4/silero-vad/raw/master/files/silero_vad.onnx"
    model_path = "backend/models/silero_vad.onnx"
    
    if os.path.exists(model_path):
        print(f"✓ Silero VAD model already exists at {model_path}")
        return
    
    print("Downloading Silero VAD model...")
    try:
        urllib.request.urlretrieve(model_url, model_path)
        print(f"✓ Downloaded Silero VAD model to {model_path}")
    except Exception as e:
        print(f"✗ Failed to download Silero VAD model: {e}")
        print("Please download manually from: https://github.com/snakers4/silero-vad")

def setup_firered_asr():
    """Setup FireRedASR model directory"""
    model_dir = "backend/models/firered_asr"
    
    print("Setting up FireRedASR model...")
    try:
        # Try to clone the model from Hugging Face
        subprocess.run([
            "git", "clone", 
            "https://huggingface.co/FireRedTeam/FireRedASR-AED-L",
            model_dir
        ], check=True)
        print(f"✓ Successfully cloned FireRedASR model to {model_dir}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"✗ Failed to clone FireRedASR model: {e}")
        
        # Create directory and instructions as fallback
        Path(model_dir).mkdir(parents=True, exist_ok=True)
        
        readme_content = """# FireRedASR Model Setup

Automatic download failed. Please download manually using:

```bash
git clone https://huggingface.co/FireRedTeam/FireRedASR-AED-L backend/models/firered_asr
```

Alternative download from ModelScope:
https://www.modelscope.cn/models/manyeyes/fireredasr-aed-large-zh-en-onnx-offline-20250124

The directory should contain:
- model files (.onnx, .bin, etc.)
- tokenizer.json
- config.json
- other model artifacts
"""
        
        readme_path = os.path.join(model_dir, "README.md")
        with open(readme_path, "w") as f:
            f.write(readme_content)
        
        print(f"✓ Created FireRedASR model directory: {model_dir}")
        print("  Please download the model manually using the git clone command above")
        return False

def install_python_dependencies():
    """Install Python backend dependencies"""
    print("Installing Python dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"], 
                      check=True, cwd=".")
        print("✓ Python dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install Python dependencies: {e}")
        return False
    return True

def setup_flutter_dependencies():
    """Setup Flutter dependencies"""
    print("Setting up Flutter dependencies...")
    try:
        # Check if Flutter is installed
        subprocess.run(["flutter", "--version"], check=True, capture_output=True)
        
        # Get Flutter dependencies
        subprocess.run(["flutter", "pub", "get"], check=True, cwd="frontend")
        print("✓ Flutter dependencies installed successfully")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ Flutter not found. Please install Flutter first:")
        print("  https://docs.flutter.dev/get-started/install")
        return False

def create_env_file():
    """Create .env file from example"""
    env_example_path = "backend/.env.example"
    env_path = "backend/.env"
    
    if os.path.exists(env_path):
        print(f"✓ .env file already exists at {env_path}")
        return
    
    if os.path.exists(env_example_path):
        shutil.copy(env_example_path, env_path)
        print(f"✓ Created .env file from example")
        print("  Please update the .env file with your actual API keys and configuration")
    else:
        print("✗ .env.example file not found")

def setup_supabase():
    """Setup Supabase instructions"""
    print("\n" + "="*50)
    print("SUPABASE SETUP INSTRUCTIONS")
    print("="*50)
    print("1. Create a new Supabase project at https://supabase.com")
    print("2. Go to Settings > API to get your URL and anon key")
    print("3. Update backend/.env with your Supabase credentials")
    print("4. Run the migration file in Supabase SQL Editor:")
    print("   - Copy content from supabase/migrations/001_initial_schema.sql")
    print("   - Paste and run in Supabase SQL Editor")
    print("5. Enable Row Level Security if needed")

def setup_api_keys():
    """Setup API keys instructions"""
    print("\n" + "="*50)
    print("API KEYS SETUP INSTRUCTIONS")
    print("="*50)
    print("Update backend/.env with the following API keys:")
    print("1. Volcano Engine API:")
    print("   - Get API key from https://www.volcengine.com/")
    print("   - Update VOLCANO_API_KEY and VOLCANO_ENDPOINT")
    print("2. MiniMax TTS API:")
    print("   - Get API key from https://platform.minimaxi.com/")
    print("   - Update MINIMAX_API_KEY and MINIMAX_GROUP_ID")
    print("3. WeChat Login (optional):")
    print("   - Get App ID from WeChat Open Platform")
    print("   - Update WECHAT_APP_ID and WECHAT_APP_SECRET")

def main():
    """Main setup function"""
    print("🚀 Setting up Real-time Voice Chat App")
    print("="*50)
    
    # Create directories
    create_directories()
    
    # Download models
    download_silero_vad()
    setup_firered_asr()
    
    # Create .env file
    create_env_file()
    
    # Install dependencies
    python_success = install_python_dependencies()
    flutter_success = setup_flutter_dependencies()
    
    # Setup instructions
    setup_supabase()
    setup_api_keys()
    
    print("\n" + "="*50)
    print("SETUP SUMMARY")
    print("="*50)
    print(f"✓ Directories created")
    print(f"{'✓' if python_success else '✗'} Python dependencies")
    print(f"{'✓' if flutter_success else '✗'} Flutter dependencies")
    print(f"✓ Model directories prepared")
    
    print("\n📋 NEXT STEPS:")
    print("1. Download FireRedASR model: git clone https://huggingface.co/FireRedTeam/FireRedASR-AED-L backend/models/firered_asr")
    print("2. Setup Supabase project and run migrations")
    print("3. Update .env file with your API keys")
    print("4. Test the backend: cd backend && python main.py")
    print("5. Test the frontend: cd frontend && flutter run")
    
    print("\n🎉 Setup completed! Check the instructions above.")

if __name__ == "__main__":
    main()