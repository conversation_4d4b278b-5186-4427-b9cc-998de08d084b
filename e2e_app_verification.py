#!/usr/bin/env python3
"""
端到端应用验证脚本
验证Flutter macOS应用的登录体系、上下文管理和TTS功能
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class E2EAppVerification:
    def __init__(self, backend_url="http://localhost:8000"):
        self.backend_url = backend_url
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def log_test_result(self, test_name, success, details="", error=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": str(error) if error else None,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {details}")
        if error:
            logger.error(f"Error: {error}")
    
    async def test_backend_health(self):
        """测试后端健康状态"""
        try:
            async with self.session.get(f"{self.backend_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    self.log_test_result(
                        "Backend Health Check", 
                        True, 
                        f"Backend is healthy. Services: {data.get('services', {})}"
                    )
                    return True
                else:
                    self.log_test_result(
                        "Backend Health Check", 
                        False, 
                        f"HTTP {response.status}"
                    )
                    return False
        except Exception as e:
            self.log_test_result("Backend Health Check", False, error=e)
            return False
    
    async def test_login_system(self):
        """测试登录体系"""
        test_users = [
            {"username": "test_user", "password": "test_password"},
            {"username": "admin", "password": "admin123"}
        ]
        
        login_success_count = 0
        
        for user in test_users:
            try:
                async with self.session.post(
                    f"{self.backend_url}/auth/login",
                    json=user
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success") and data.get("user"):
                            login_success_count += 1
                            self.log_test_result(
                                f"Login - {user['username']}", 
                                True, 
                                f"User: {data['user']['nickname']}, Token: {data.get('token', 'N/A')[:20]}..."
                            )
                        else:
                            self.log_test_result(
                                f"Login - {user['username']}", 
                                False, 
                                "Invalid response format"
                            )
                    else:
                        self.log_test_result(
                            f"Login - {user['username']}", 
                            False, 
                            f"HTTP {response.status}"
                        )
            except Exception as e:
                self.log_test_result(f"Login - {user['username']}", False, error=e)
        
        # Test invalid login
        try:
            async with self.session.post(
                f"{self.backend_url}/auth/login",
                json={"username": "invalid", "password": "wrong"}
            ) as response:
                if response.status == 401:
                    self.log_test_result(
                        "Login - Invalid Credentials", 
                        True, 
                        "Correctly rejected invalid credentials"
                    )
                else:
                    self.log_test_result(
                        "Login - Invalid Credentials", 
                        False, 
                        f"Expected 401, got {response.status}"
                    )
        except Exception as e:
            self.log_test_result("Login - Invalid Credentials", False, error=e)
        
        return login_success_count >= 2
    
    async def test_npc_context_management(self):
        """测试NPC和上下文管理"""
        try:
            async with self.session.get(f"{self.backend_url}/npcs") as response:
                if response.status == 200:
                    data = await response.json()
                    npcs = data.get("npcs", [])
                    
                    if len(npcs) >= 2:
                        self.log_test_result(
                            "NPC Context Management", 
                            True, 
                            f"Found {len(npcs)} NPCs: {[npc['name'] for npc in npcs]}"
                        )
                        
                        # Test each NPC's context
                        for npc in npcs[:2]:  # Test first 2 NPCs
                            npc_details = f"NPC '{npc['name']}' - Voice: {npc.get('voice_id', 'default')}, Prompt: {npc.get('system_prompt', '')[:50]}..."
                            self.log_test_result(
                                f"NPC Details - {npc['name']}", 
                                True, 
                                npc_details
                            )
                        
                        return True
                    else:
                        self.log_test_result(
                            "NPC Context Management", 
                            False, 
                            f"Expected at least 2 NPCs, got {len(npcs)}"
                        )
                        return False
                else:
                    self.log_test_result(
                        "NPC Context Management", 
                        False, 
                        f"HTTP {response.status}"
                    )
                    return False
        except Exception as e:
            self.log_test_result("NPC Context Management", False, error=e)
            return False
    
    async def test_tts_functionality(self):
        """测试TTS功能"""
        # Test TTS voices
        try:
            async with self.session.get(f"{self.backend_url}/api/tts/voices") as response:
                if response.status == 200:
                    data = await response.json()
                    voices = data.get("voices", {})
                    self.log_test_result(
                        "TTS Voices", 
                        True, 
                        f"Available voices: {list(voices.keys())}"
                    )
                else:
                    self.log_test_result("TTS Voices", False, f"HTTP {response.status}")
        except Exception as e:
            self.log_test_result("TTS Voices", False, error=e)
        
        # Test TTS synthesis
        test_texts = [
            "你好，这是TTS测试。",
            "欢迎使用语音聊天系统！",
            "测试不同的语音合成效果。"
        ]
        
        tts_success_count = 0
        
        for i, text in enumerate(test_texts):
            try:
                tts_request = {
                    "text": text,
                    "emotion": "neutral",
                    "speed": 1.0,
                    "voice_id": "default"
                }
                
                async with self.session.post(
                    f"{self.backend_url}/api/tts/synthesize",
                    json=tts_request
                ) as response:
                    if response.status == 200:
                        audio_data = await response.read()
                        if len(audio_data) > 0:
                            tts_success_count += 1
                            self.log_test_result(
                                f"TTS Synthesis {i+1}", 
                                True, 
                                f"Generated {len(audio_data)} bytes of audio for: '{text[:30]}...'"
                            )
                        else:
                            self.log_test_result(
                                f"TTS Synthesis {i+1}", 
                                False, 
                                "Empty audio data"
                            )
                    else:
                        self.log_test_result(
                            f"TTS Synthesis {i+1}", 
                            False, 
                            f"HTTP {response.status}"
                        )
            except Exception as e:
                self.log_test_result(f"TTS Synthesis {i+1}", False, error=e)
        
        return tts_success_count >= 2
    
    async def test_conversation_flow(self):
        """测试对话流程"""
        try:
            async with self.session.get(f"{self.backend_url}/test") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("status") == "success":
                        self.log_test_result(
                            "Conversation Flow", 
                            True, 
                            f"Q: {data.get('question', '')} | A: {data.get('response', '')[:50]}..."
                        )
                        return True
                    else:
                        self.log_test_result(
                            "Conversation Flow", 
                            False, 
                            f"Status: {data.get('status', 'unknown')}"
                        )
                        return False
                else:
                    self.log_test_result(
                        "Conversation Flow", 
                        False, 
                        f"HTTP {response.status}"
                    )
                    return False
        except Exception as e:
            self.log_test_result("Conversation Flow", False, error=e)
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始端到端应用验证...")
        logger.info(f"📍 后端地址: {self.backend_url}")
        
        # 等待后端服务启动
        logger.info("⏳ 等待后端服务启动...")
        await asyncio.sleep(3)
        
        test_functions = [
            self.test_backend_health,
            self.test_login_system,
            self.test_npc_context_management,
            self.test_tts_functionality,
            self.test_conversation_flow
        ]
        
        passed_tests = 0
        total_tests = len(test_functions)
        
        for test_func in test_functions:
            try:
                result = await test_func()
                if result:
                    passed_tests += 1
            except Exception as e:
                logger.error(f"Test function {test_func.__name__} failed: {e}")
        
        # 生成测试报告
        self.generate_test_report(passed_tests, total_tests)
        
        return passed_tests, total_tests
    
    def generate_test_report(self, passed_tests, total_tests):
        """生成测试报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 端到端应用验证报告")
        logger.info("="*60)
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        logger.info(f"📈 总体成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        # 按类别统计
        categories = {}
        for result in self.test_results:
            category = result["test_name"].split(" - ")[0] if " - " in result["test_name"] else result["test_name"]
            if category not in categories:
                categories[category] = {"passed": 0, "total": 0}
            categories[category]["total"] += 1
            if result["success"]:
                categories[category]["passed"] += 1
        
        logger.info("\n📋 详细结果:")
        for category, stats in categories.items():
            rate = (stats["passed"] / stats["total"]) * 100 if stats["total"] > 0 else 0
            status = "✅" if rate == 100 else "⚠️" if rate >= 50 else "❌"
            logger.info(f"{status} {category}: {rate:.1f}% ({stats['passed']}/{stats['total']})")
        
        # 失败的测试
        failed_tests = [r for r in self.test_results if not r["success"]]
        if failed_tests:
            logger.info(f"\n❌ 失败的测试 ({len(failed_tests)}):")
            for test in failed_tests:
                logger.info(f"  • {test['test_name']}: {test.get('error', 'Unknown error')}")
        
        logger.info("\n🎯 验证重点:")
        logger.info("  • 登录体系: 用户认证和权限管理")
        logger.info("  • 上下文管理: NPC角色和对话上下文")
        logger.info("  • TTS功能: 语音合成和音频输出")
        logger.info("  • 对话流程: 端到端对话体验")
        
        if success_rate >= 80:
            logger.info("\n🎉 应用验证通过！系统功能正常。")
        elif success_rate >= 60:
            logger.info("\n⚠️ 应用基本可用，但有部分功能需要优化。")
        else:
            logger.info("\n❌ 应用验证失败，需要修复关键问题。")
        
        logger.info("="*60)

async def main():
    """主函数"""
    async with E2EAppVerification() as verifier:
        passed, total = await verifier.run_all_tests()
        return passed, total

if __name__ == "__main__":
    asyncio.run(main())
