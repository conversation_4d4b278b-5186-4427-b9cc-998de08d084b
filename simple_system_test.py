#!/usr/bin/env python3
"""
简化的系统功能测试脚本
测试基本的后端功能，不依赖复杂的音频库
"""

import requests
import json
import time
from datetime import datetime

class SimpleSystemTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.tts_url = "http://localhost:8001"
        self.test_results = []
        
    def log_test(self, test_name, success, details="", error=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": str(error) if error else None,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {details}")
        if error:
            print(f"   Error: {error}")
    
    def test_backend_basic(self):
        """测试后端基本功能"""
        try:
            # 测试根路径
            response = requests.get(f"{self.backend_url}/", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_test("Backend Root", True, f"Message: {data.get('message', 'N/A')}")
            else:
                self.log_test("Backend Root", False, f"HTTP {response.status_code}")
                return False
            
            # 测试健康检查
            response = requests.get(f"{self.backend_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                services = data.get("services", {})
                service_count = len(services)
                self.log_test("Backend Health", True, f"Checked {service_count} services")
            else:
                self.log_test("Backend Health", False, f"HTTP {response.status_code}")
            
            return True
            
        except Exception as e:
            self.log_test("Backend Basic", False, error=e)
            return False
    
    def test_authentication(self):
        """测试认证功能"""
        try:
            # 测试登录
            login_params = {
                "username": "test_user",
                "password": "test_password"
            }
            
            response = requests.post(
                f"{self.backend_url}/auth/login",
                params=login_params,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                user = data.get("user", {})
                username = user.get("username", "unknown")
                self.log_test("Authentication", True, f"Login successful for: {username}")
                return True
            else:
                self.log_test("Authentication", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Authentication", False, error=e)
            return False
    
    def test_npc_endpoints(self):
        """测试NPC端点"""
        try:
            response = requests.get(f"{self.backend_url}/npcs", timeout=10)
            if response.status_code == 200:
                data = response.json()
                npcs = data.get("npcs", [])
                source = data.get("source", "unknown")
                self.log_test("NPC Endpoints", True, f"Found {len(npcs)} NPCs from {source}")
                return True
            else:
                self.log_test("NPC Endpoints", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("NPC Endpoints", False, error=e)
            return False
    
    def test_mcp_endpoints(self):
        """测试MCP端点"""
        try:
            # 测试MCP服务器列表
            response = requests.get(f"{self.backend_url}/mcp/servers", timeout=10)
            if response.status_code == 200:
                data = response.json()
                servers = data.get("servers", [])
                self.log_test("MCP Servers", True, f"Found {len(servers)} MCP servers")
            else:
                self.log_test("MCP Servers", False, f"HTTP {response.status_code}")
                return False
            
            # 测试MCP工具列表
            response = requests.get(f"{self.backend_url}/mcp/tools", timeout=10)
            if response.status_code == 200:
                data = response.json()
                tools = data.get("tools", [])
                
                # 统计工具类别
                categories = {}
                for tool in tools:
                    category = tool.get("category", "unknown")
                    if category not in categories:
                        categories[category] = 0
                    categories[category] += 1
                
                category_summary = ", ".join([f"{cat}:{count}" for cat, count in categories.items()])
                self.log_test("MCP Tools", True, f"Found {len(tools)} tools. Categories: {category_summary}")
            else:
                self.log_test("MCP Tools", False, f"HTTP {response.status_code}")
                return False
            
            # 测试MCP工具执行
            test_params = {
                "tool_name": "search_and_summarize",
                "parameters": json.dumps({"query": "AI发展趋势测试"})
            }
            
            response = requests.post(
                f"{self.backend_url}/mcp/tools/execute",
                params=test_params,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                success = result.get("success", False)
                tool_name = result.get("tool_name", "unknown")
                self.log_test("MCP Tool Execution", success, f"Executed: {tool_name}")
            else:
                self.log_test("MCP Tool Execution", False, f"HTTP {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("MCP Endpoints", False, error=e)
            return False
    
    def test_llm_endpoint(self):
        """测试LLM端点"""
        try:
            response = requests.get(f"{self.backend_url}/test", timeout=20)
            if response.status_code == 200:
                data = response.json()
                if "error" not in data:
                    question = data.get("question", "")
                    response_text = data.get("response", "")
                    database_status = data.get("database_status", "unknown")
                    
                    self.log_test(
                        "LLM Endpoint", 
                        True, 
                        f"Q: '{question}' -> A: '{response_text[:50]}...' (DB: {database_status})"
                    )
                    return True
                else:
                    self.log_test("LLM Endpoint", False, data.get("error"))
                    return False
            else:
                self.log_test("LLM Endpoint", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("LLM Endpoint", False, error=e)
            return False
    
    def test_debug_endpoints(self):
        """测试调试端点"""
        try:
            # 测试用户调试端点
            response = requests.get(f"{self.backend_url}/debug/users", timeout=5)
            if response.status_code == 200:
                data = response.json()
                user_count = data.get("count", 0)
                self.log_test("Debug Users", True, f"Found {user_count} users")
            else:
                self.log_test("Debug Users", False, f"HTTP {response.status_code}")
            
            # 测试会话调试端点
            response = requests.get(f"{self.backend_url}/debug/sessions", timeout=5)
            if response.status_code == 200:
                data = response.json()
                memory_sessions = len(data.get("memory_sessions", {}))
                websocket_sessions = len(data.get("active_websocket_sessions", []))
                self.log_test(
                    "Debug Sessions", 
                    True, 
                    f"Memory sessions: {memory_sessions}, WebSocket sessions: {websocket_sessions}"
                )
            else:
                self.log_test("Debug Sessions", False, f"HTTP {response.status_code}")
            
            return True
            
        except Exception as e:
            self.log_test("Debug Endpoints", False, error=e)
            return False
    
    def test_tts_service(self):
        """测试TTS服务（如果可用）"""
        try:
            # 测试TTS健康检查
            response = requests.get(f"{self.tts_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                services = data.get("services", {})
                self.log_test("TTS Health", True, f"TTS services: {services}")
                
                # 测试TTS根路径
                response = requests.get(f"{self.tts_url}/", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    self.log_test("TTS Root", True, f"Message: {data.get('message', 'N/A')}")
                
                return True
            else:
                self.log_test("TTS Health", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("TTS Service", False, error=e)
            return False
    
    def generate_simple_report(self):
        """生成简单的测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        report = {
            "simple_test_summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
            },
            "test_results": self.test_results,
            "generated_at": datetime.now().isoformat()
        }
        
        # 保存报告
        report_file = f"simple_system_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n{'='*60}")
        print("📊 SIMPLE SYSTEM TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {report['simple_test_summary']['success_rate']}")
        print(f"Report saved to: {report_file}")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result.get('error', 'Unknown error')}")
        
        return report

def main():
    """主测试函数"""
    print("🚀 Starting Simple System Test")
    print("="*60)
    
    tester = SimpleSystemTester()
    
    # 1. 后端基本功能测试
    print("\n1. Backend Basic Tests")
    tester.test_backend_basic()
    
    # 2. 认证测试
    print("\n2. Authentication Test")
    tester.test_authentication()
    
    # 3. NPC端点测试
    print("\n3. NPC Endpoints Test")
    tester.test_npc_endpoints()
    
    # 4. MCP端点测试
    print("\n4. MCP Endpoints Test")
    tester.test_mcp_endpoints()
    
    # 5. LLM端点测试
    print("\n5. LLM Endpoint Test")
    tester.test_llm_endpoint()
    
    # 6. 调试端点测试
    print("\n6. Debug Endpoints Test")
    tester.test_debug_endpoints()
    
    # 7. TTS服务测试（可选）
    print("\n7. TTS Service Test")
    tester.test_tts_service()
    
    # 生成测试报告
    print("\n8. Generating Test Report")
    report = tester.generate_simple_report()
    
    return report

if __name__ == "__main__":
    main()
