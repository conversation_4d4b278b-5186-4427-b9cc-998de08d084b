# Real-time Voice Chat App - Project Structure

## 📁 Complete Project Organization

```
voice-chat-app/
├── README.md                           # Project overview and setup
├── PROJECT_STRUCTURE.md               # This file - project organization
├── QUICKSTART.md                      # Quick setup guide
├── DEPLOYMENT.md                      # Production deployment guide
├── setup.py                          # Automated setup script
├── .gitignore                         # Git ignore rules
│
├── backend/                           # Python FastAPI backend
│   ├── main.py                       # Main API server with WebSocket
│   ├── requirements.txt              # Python dependencies
│   ├── .env                         # Environment variables (your config)
│   ├── .env.example                 # Environment template
│   │
│   ├── services/                    # Core processing services
│   │   ├── __init__.py
│   │   ├── vad_service.py          # Silero VAD implementation
│   │   ├── asr_service.py          # FireRedASR integration
│   │   ├── llm_service.py          # Volcano Engine API
│   │   ├── tts_service.py          # MiniMax WebSocket TTS
│   │   └── conversation_service.py  # 4-role conversation handling
│   │
│   ├── models/                     # ONNX and AI models
│   │   ├── silero_vad.onnx        # VAD model (auto-downloaded)
│   │   └── firered_asr/           # ASR model directory
│   │       ├── model.onnx         # Main ASR model
│   │       ├── tokenizer.json     # Tokenizer config
│   │       └── config.json        # Model config
│   │
│   └── logs/                      # Application logs
│       └── app.log
│
├── frontend/                      # Flutter mobile application
│   ├── pubspec.yaml              # Flutter dependencies
│   ├── android/                  # Android-specific files
│   ├── ios/                      # iOS-specific files
│   │
│   └── lib/                      # Flutter source code
│       ├── main.dart            # App entry point
│       │
│       ├── config/              # App configuration
│       │   └── app_config.dart  # API URLs and settings
│       │
│       ├── models/              # Data models
│       │   └── user_model.dart  # User, NPC, Message models
│       │
│       ├── services/            # Business logic services
│       │   ├── auth_service.dart      # Username+password auth
│       │   └── voice_chat_service.dart # WebSocket & audio handling
│       │
│       ├── screens/             # UI screens
│       │   ├── splash_screen.dart     # App loading screen
│       │   ├── login_screen.dart      # Login/register UI
│       │   ├── npc_selection_screen.dart # Choose conversation partner
│       │   └── voice_chat_screen.dart    # Main voice chat interface
│       │
│       └── widgets/             # Reusable UI components
│           ├── audio_visualizer.dart
│           ├── voice_button.dart
│           └── chat_bubble.dart
│
├── supabase/                    # Database and backend services
│   └── migrations/              # Database schema
│       └── 001_initial_schema.sql # Tables, indexes, RLS policies
│
└── .codebuddy/                 # CodeBuddy specific files
    └── sft_training_data_2025-07-28_11-28-32.jsonl # Your SFT training data
```

## 🔧 Key Components

### Backend Services Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WebSocket     │    │   Audio         │    │   Database      │
│   Handler       │◄──►│   Pipeline      │◄──►│   Service       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Conversation    │    │ VAD → ASR →     │    │ User & Session  │
│ Management      │    │ LLM → TTS       │    │ Management      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Frontend Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI Screens    │    │   Services      │    │   State Mgmt    │
│   (Flutter)     │◄──►│   (Business)    │◄──►│   (Riverpod)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Voice Recording │    │ WebSocket Comm  │    │ User Session    │
│ & Playback      │    │ & Audio Stream  │    │ & Preferences   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🗄️ Database Schema

### Core Tables

1. **users** - User authentication and profiles
   - `id`, `username`, `password_hash`, `email`, `nickname`
   - Supports username+password authentication

2. **npcs** - AI conversation partners
   - `id`, `name`, `description`, `system_prompt`, `avatar_url`
   - Different personalities and conversation styles

3. **conversation_sessions** - Chat sessions
   - `id`, `user_id`, `npc_id`, `started_at`, `ended_at`
   - Tracks individual conversation instances

4. **conversation_messages** - Message history
   - `id`, `session_id`, `role`, `content`, `audio_url`
   - **4-role system**: `user`, `assistant`, `developer`, `tool`
   - Supports tool calls and audio metadata

## 🎯 Data Flow

### Voice Chat Process

```
1. User speaks → Frontend records audio
2. Audio sent via WebSocket → Backend VAD processing
3. VAD detects speech → ASR transcribes to text
4. Text sent to LLM → Volcano Engine generates response
5. Response parsed → Extract SPEAK content (emotion, speed, text)
6. Text sent to TTS → MiniMax generates audio
7. Audio streamed back → Frontend plays response
8. All messages saved → Database with 4-role system
```

### Authentication Flow

```
1. User enters username/password → Frontend validation
2. Credentials sent to backend → bcrypt password verification
3. JWT token generated → Stored securely on device
4. Token used for API calls → Session management
5. Guest mode available → For testing without registration
```

## 🔄 4-Role Conversation System

Based on your SFT training data format:

- **user**: Human input messages
- **assistant**: AI responses with SPEAK formatting
- **developer**: System/debug messages for development
- **tool**: Tool call results and function outputs

### Message Format Example

```json
{
  "role": "assistant",
  "content": "<turn>\n<THINK>\n## Analysis...\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>Response text</text></SPEAK>\n<JUDGE>\nPerformance: 8/10\n</JUDGE>\n</turn>",
  "tool_calls": [...],
  "emotion": "neutral",
  "speed": 1.0
}
```

## 🚀 Development Workflow

### Backend Development
```bash
cd backend
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
python main.py
```

### Frontend Development
```bash
cd frontend
flutter pub get
flutter run
```

### Database Setup
1. Run Supabase migration: `supabase/migrations/001_initial_schema.sql`
2. Configure environment: Update `backend/.env`
3. Test connection: `curl http://localhost:8000/health`

## 📦 Deployment Ready

- **Docker support**: Containerized backend deployment
- **Flutter builds**: Android APK/AAB, iOS IPA
- **Database migrations**: Supabase schema management
- **Environment configs**: Production-ready settings
- **Monitoring**: Health checks and logging

This structure supports your complete real-time voice chat application with username+password authentication, 4-role conversation system, and all the AI processing pipeline components you specified.