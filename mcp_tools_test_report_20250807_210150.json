{"mcp_test_summary": {"total_tests": 10, "passed": 8, "failed": 2, "success_rate": "80.0%"}, "test_results": [{"test_name": "Direct MCP Service - Server List", "success": true, "details": "Found 1 servers", "error": null, "timestamp": "2025-08-07T21:01:50.798018"}, {"test_name": "Direct MCP Service - Tools List", "success": true, "details": "Found 5 tools in 4 categories", "error": null, "timestamp": "2025-08-07T21:01:50.798048"}, {"test_name": "Builtin Tool - search_and_summarize", "success": true, "details": "Tool executed successfully. summary: 关于'人工智能最新发展'的搜索结果摘要：这是一个模拟的搜索摘要结果。...", "error": null, "timestamp": "2025-08-07T21:01:50.798077"}, {"test_name": "Builtin Tool - fetch_news", "success": true, "details": "Tool executed successfully. news: 3 items", "error": null, "timestamp": "2025-08-07T21:01:50.798094"}, {"test_name": "Builtin Tool - recall_current_activity", "success": true, "details": "Tool executed successfully. activity: 正在与用户进行语音对话", "error": null, "timestamp": "2025-08-07T21:01:50.798101"}, {"test_name": "Builtin Tool - recall_relevant_experiences", "success": true, "details": "Tool executed successfully. experiences: 2 items", "error": null, "timestamp": "2025-08-07T21:01:50.798109"}, {"test_name": "MCP Server Tools - search_server", "success": true, "details": "Found 1 tools", "error": null, "timestamp": "2025-08-07T21:01:50.798118"}, {"test_name": "Server Tool - search_server_generic_tool", "success": true, "details": "Executed with params: {'query': '测试查询', 'limit': 3}. Result keys: ['message', 'parameters_received', 'server']", "error": null, "timestamp": "2025-08-07T21:01:50.798129"}, {"test_name": "API Endpoints Test", "success": false, "details": "", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /mcp/servers (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1059b27f0>: Failed to establish a new connection: [Errno 61] Connection refused'))", "timestamp": "2025-08-07T21:01:50.814229"}, {"test_name": "Tool Categories Coverage", "success": false, "details": "Coverage: 44.4% (4/9). Missing: {'research', 'cooking', 'entertainment', 'location', 'travel'}", "error": null, "timestamp": "2025-08-07T21:01:50.814310"}], "generated_at": "2025-08-07T21:01:50.814336"}