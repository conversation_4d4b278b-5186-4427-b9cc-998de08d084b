#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MCP服务初始化和测试脚本
该脚本将初始化所有MCP配置并进行基本服务测试
"""

import sys
import os
import json
import glob
import logging

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_mcp_config(config_path):
    """
    加载MCP配置文件
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"无法加载配置文件 {config_path}: {e}")
        return None

def register_mcp_servers_from_config(config_data, config_name, master_service):
    """
    从配置数据注册MCP服务器
    """
    registered_servers = []
    mcp_servers = config_data.get("mcpServers", {})
    
    for server_name, server_config in mcp_servers.items():
        command = server_config.get("command")
        args = server_config.get("args", [])
        env = server_config.get("env", {})
        
        # 创建服务器URL表示
        server_url = f"local://{command} {' '.join(args)}"
        
        # 注册服务器
        result = master_service.call(
            "register_server", 
            server_name=f"{config_name}-{server_name}", 
            server_url=server_url,
            server_info={
                "command": command,
                "args": args,
                "env": env,
                "type": "local_command",
                "source_config": config_name
            }
        )
        
        if result:
            registered_servers.append(f"{config_name}-{server_name}")
            logger.info(f"注册服务器: {config_name}-{server_name}")
        else:
            logger.error(f"注册服务器失败: {config_name}-{server_name}")
    
    return registered_servers

def initialize_all_mcp_configs():
    """
    初始化所有MCP配置
    """
    logger.info("开始初始化所有MCP配置...")
    
    try:
        # 导入服务
        from functions.master_service import master_service
        from services.mcp_service import mcp_service
        
        # 1. 查找所有MCP配置文件
        logger.info("查找MCP配置文件...")
        mcp_config_dir = os.path.join(os.path.dirname(__file__), 'backend', 'mcp_config')
        config_files = glob.glob(os.path.join(mcp_config_dir, "*.json"))
        
        logger.info(f"找到 {len(config_files)} 个配置文件")
        for config_file in config_files:
            logger.info(f"  - {os.path.basename(config_file)}")
        
        # 2. 加载并注册所有配置文件中的服务器
        logger.info("加载并注册MCP服务器...")
        all_registered_servers = []
        
        for config_file in config_files:
            config_name = os.path.splitext(os.path.basename(config_file))[0]
            # 跳过多服务器配置文件，因为它不是单个MCP服务器配置
            if config_name == "multi_server_config":
                continue
                
            logger.info(f"处理配置文件: {config_name}")
            
            # 加载配置
            config_data = load_mcp_config(config_file)
            if config_data:
                # 注册服务器
                registered_servers = register_mcp_servers_from_config(config_data, config_name, master_service)
                all_registered_servers.extend(registered_servers)
            else:
                logger.error(f"无法加载配置文件: {config_name}")
        
        # 3. 显示所有已注册的服务器
        logger.info("所有已注册的服务器:")
        servers = master_service.call("list_servers")
        for server in servers:
            logger.info(f"  - {server['name']}: {server['url']}")
            if 'source_config' in server:
                logger.info(f"    来源配置: {server['source_config']}")
        
        logger.info(f"总共注册了 {len(servers)} 个服务器")
        return True
        
    except Exception as e:
        logger.error(f"初始化MCP配置时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mcp_services():
    """
    测试MCP服务
    """
    logger.info("开始测试MCP服务...")
    
    try:
        # 导入服务
        from functions.master_service import master_service
        
        # 1. 测试获取所有工具用于reranker
        logger.info("测试获取所有工具用于reranker...")
        all_tools = master_service.get_all_tools_for_reranker()
        logger.info(f"成功获取到 {len(all_tools)} 个工具用于reranker")
        
        if all_tools:
            logger.info("可用工具:")
            for i, tool in enumerate(all_tools[:10], 1):  # 只显示前10个工具
                logger.info(f"  {i}. {tool['name']}: {tool['description'][:60]}...")
            if len(all_tools) > 10:
                logger.info(f"  ... 还有 {len(all_tools) - 10} 个工具")
        else:
            logger.info("暂无工具信息")
        
        # 2. 测试服务器管理功能
        logger.info("测试服务器管理功能...")
        
        # 注册测试服务器
        test_servers = [
            {"name": "test_web_search", "url": "http://test-web-search:8000"},
            {"name": "test_news_service", "url": "http://test-news:8000"}
        ]
        
        for server_info in test_servers:
            result = master_service.call(
                "register_server", 
                server_name=server_info["name"], 
                server_url=server_info["url"]
            )
            if result:
                logger.info(f"注册测试服务器 {server_info['name']}: 成功")
            else:
                logger.error(f"注册测试服务器 {server_info['name']}: 失败")
        
        # 列出所有服务器
        servers = master_service.call("list_servers")
        logger.info(f"当前服务器数量: {len(servers)}")
        
        # 获取服务器信息
        for server in servers[:3]:  # 只测试前3个服务器
            server_info = master_service.call("get_server_info", server_name=server["name"])
            if server_info:
                logger.info(f"服务器 {server['name']} 信息获取成功")
            else:
                logger.error(f"服务器 {server['name']} 信息获取失败")
        
        # 获取服务器工具
        for server in servers[:3]:  # 只测试前3个服务器
            tools = master_service.call("get_server_tools", server_name=server["name"])
            logger.info(f"服务器 {server['name']} 提供 {len(tools)} 个工具")
        
        # 3. 测试工具执行
        logger.info("测试工具执行...")
        test_tools = [
            {"name": "search_and_summarize", "params": {"query": "人工智能发展趋势"}},
            {"name": "fetch_news", "params": {"topic": "科技", "limit": 5}}
        ]
        
        for tool in test_tools:
            result = master_service.execute_mcp_tool(tool["name"], **tool["params"])
            if result["status"] == "success":
                logger.info(f"工具 {tool['name']} 执行成功")
            else:
                logger.error(f"工具 {tool['name']} 执行失败: {result.get('error', '未知错误')}")
        
        # 4. 测试配置文件操作
        logger.info("测试配置文件操作...")
        
        # 创建测试配置
        sample_config = {
            "servers": [
                {
                    "name": "config_test_server_1",
                    "url": "http://config-test-1:8000",
                    "description": "配置测试服务器1"
                },
                {
                    "name": "config_test_server_2",
                    "url": "http://config-test-2:8000",
                    "description": "配置测试服务器2"
                }
            ],
            "default_server": "config_test_server_1"
        }
        
        # 初始化配置
        init_result = master_service.initialize_mcp_config(sample_config)
        logger.info(f"配置初始化: {'成功' if init_result else '失败'}")
        
        # 验证配置
        servers_after_init = master_service.call("list_servers")
        logger.info(f"初始化后服务器数量: {len(servers_after_init)}")
        
        # 删除配置
        delete_result = master_service.delete_mcp_config()
        logger.info(f"配置删除: {'成功' if delete_result else '失败'}")
        
        # 5. 清理测试服务器
        logger.info("清理测试服务器...")
        for server_info in test_servers:
            result = master_service.call("unregister_server", server_name=server_info["name"])
            if result:
                logger.info(f"注销测试服务器 {server_info['name']}: 成功")
            else:
                logger.error(f"注销测试服务器 {server_info['name']}: 失败")
        
        logger.info("MCP服务测试完成")
        return True
        
    except Exception as e:
        logger.error(f"测试MCP服务时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数
    """
    logger.info("开始MCP服务初始化和测试")
    
    # 初始化所有MCP配置
    init_success = initialize_all_mcp_configs()
    if not init_success:
        logger.error("MCP配置初始化失败")
        return False
    
    # 测试MCP服务
    test_success = test_mcp_services()
    if not test_success:
        logger.error("MCP服务测试失败")
        return False
    
    logger.info("MCP服务初始化和测试全部完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
