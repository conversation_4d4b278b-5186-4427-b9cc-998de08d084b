{"test_time": "2025-08-07T16:34:28.703539", "services": {"asr": {"status": "success", "duration": 0.001192, "result": {"text": "请问你能听到我说话吗？", "confidence": 0.8, "tokens": [], "duration": 0.0, "error": "Testing mode - server bypassed", "fallback": true}, "audio_samples": 32000, "audio_file": "test_direct_audio.wav"}, "llm": {"status": "success", "duration": 0.000635, "result": {"success": true, "full_response": "<turn>\n<THINK>\n## 1. 意图分析: 用户询问：请问你能听到我说话吗？\n## 2. 行动规划: 提供友好回答\n## 3. 工具选择与参数构建: 无需工具\n## 4. 回应构建: <SPEAK><emotion>friendly</emotion><speed>1.0</speed><text>这是一个很好的问题：请问你能听到我说话吗？。让我来为你解答。</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>friendly</emotion><speed>1.0</speed><text>这是一个很好的问题：请问你能听到我说话吗？。让我来为你解答。</text></SPEAK>\n<JUDGE>\n本轮表现：9/10分\n优点：回答友好准确\n缺点：使用模拟响应\n重大失误：无\n</JUDGE>\n</turn>", "speak_content": {"emotion": "friendly", "speed": 1.0, "text": "这是一个很好的问题：请问你能听到我说话吗？。让我来为你解答。"}, "mock_mode": true}, "input_text": "请问你能听到我说话吗？"}, "tts": {"status": "failed", "duration": 2.158325, "result": {"success": false, "error": "No audio data received", "audio_data": null, "file_path": null}, "input_text": "这是一个很好的问题：请问你能听到我说话吗？。让我来为你解答。"}}, "pipeline_test": {}, "issues": []}