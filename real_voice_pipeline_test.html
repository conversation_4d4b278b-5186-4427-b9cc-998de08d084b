<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 真实语音流水线测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .pipeline-status {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin: 30px 0;
        }

        .stage {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stage.active {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .stage.success {
            border-color: #28a745;
            background: #d4edda;
        }

        .stage.error {
            border-color: #dc3545;
            background: #f8d7da;
        }

        .stage h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #666;
        }

        .stage .icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .stage .status {
            font-size: 12px;
            color: #666;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .record-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            font-size: 18px;
            padding: 20px 40px;
        }

        .record-btn.recording {
            background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);
            animation: pulse 1.5s infinite;
        }

        #asrTestBtn {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        #asrTestBtn:hover {
            background: linear-gradient(135deg, #e084fc 0%, #e6486d 100%);
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }

            100% {
                opacity: 1;
            }
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .results-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .result-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .result-section h3 {
            margin: 0 0 15px 0;
            color: #333;
        }

        .result-content {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            min-height: 100px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }

        .audio-section {
            margin: 20px 0;
        }

        audio {
            width: 100%;
            margin: 10px 0;
        }

        .log-panel {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 30px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
        }

        .log-entry.info {
            color: #63b3ed;
        }

        .log-entry.success {
            color: #68d391;
        }

        .log-entry.warning {
            color: #fbd38d;
        }

        .log-entry.error {
            color: #fc8181;
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .metric {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🎤 真实语音流水线测试</h1>

        <!-- 流水线状态显示 -->
        <div class="pipeline-status">
            <div class="stage" id="stage-recording">
                <div class="icon">🎤</div>
                <h3>录音</h3>
                <div class="status">待开始</div>
            </div>
            <div class="stage" id="stage-asr">
                <div class="icon">🎯</div>
                <h3>语音识别</h3>
                <div class="status">等待中</div>
            </div>
            <div class="stage" id="stage-llm">
                <div class="icon">🧠</div>
                <h3>对话生成</h3>
                <div class="status">等待中</div>
            </div>
            <div class="stage" id="stage-tts">
                <div class="icon">🔊</div>
                <h3>语音合成</h3>
                <div class="status">等待中</div>
            </div>
            <div class="stage" id="stage-playback">
                <div class="icon">▶️</div>
                <h3>音频播放</h3>
                <div class="status">等待中</div>
            </div>
        </div>

        <!-- 状态面板 -->
        <div class="status-panel">
            <div class="status-item">
                <span>后端连接:</span>
                <span id="backendStatus">未连接</span>
            </div>
            <div class="status-item">
                <span>当前阶段:</span>
                <span id="currentStage">准备中</span>
            </div>
            <div class="status-item">
                <span>处理时间:</span>
                <span id="processingTime">0秒</span>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <button id="connectBtn" onclick="testBackendConnection()">测试TTS连接</button>
            <button id="asrTestBtn" onclick="testASRConnection()">测试ASR连接</button>
            <button id="recordBtn" class="record-btn" onclick="toggleRecording()">开始录音</button>
            <button id="testBtn" onclick="testRealServices()">测试真实服务</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <!-- 性能指标 -->
        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="recordingDuration">0</div>
                <div class="metric-label">录音时长(秒)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="asrTime">0</div>
                <div class="metric-label">ASR耗时(秒)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="llmTime">0</div>
                <div class="metric-label">LLM耗时(秒)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="ttsTime">0</div>
                <div class="metric-label">TTS耗时(秒)</div>
            </div>
        </div>

        <!-- 结果显示 -->
        <div class="results-panel">
            <div class="result-section">
                <h3>🎯 ASR识别结果</h3>
                <div class="result-content" id="asrResult">等待语音输入...</div>
            </div>
            <div class="result-section">
                <h3>🧠 LLM对话响应</h3>
                <div class="result-content" id="llmResult">等待对话生成...</div>
            </div>
        </div>

        <!-- 音频播放区域 -->
        <div class="audio-section">
            <h3>🔊 音频播放</h3>
            <div>
                <label>录制的音频:</label>
                <audio id="recordedAudio" controls style="display: none;"></audio>
            </div>
            <div>
                <label>AI响应音频 (真实TTS):</label>
                <audio id="responseAudio" controls style="display: none;"></audio>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="log-panel" id="logPanel">
            <div class="log-entry info">🚀 真实语音流水线测试工具已加载</div>
            <div class="log-entry info">💡 点击"测试后端连接"开始</div>
        </div>
    </div>

    <script>
        // 全局变量
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;
        let startTime = null;
        let stageTimers = {};
        let backendConnected = false;

        // 后端API地址
        const BACKEND_URL = 'http://localhost:8000';

        // DOM元素
        const elements = {
            backendStatus: document.getElementById('backendStatus'),
            currentStage: document.getElementById('currentStage'),
            processingTime: document.getElementById('processingTime'),
            connectBtn: document.getElementById('connectBtn'),
            asrTestBtn: document.getElementById('asrTestBtn'),
            recordBtn: document.getElementById('recordBtn'),
            testBtn: document.getElementById('testBtn'),
            asrResult: document.getElementById('asrResult'),
            llmResult: document.getElementById('llmResult'),
            recordedAudio: document.getElementById('recordedAudio'),
            responseAudio: document.getElementById('responseAudio'),
            logPanel: document.getElementById('logPanel'),
            recordingDuration: document.getElementById('recordingDuration'),
            asrTime: document.getElementById('asrTime'),
            llmTime: document.getElementById('llmTime'),
            ttsTime: document.getElementById('ttsTime')
        };

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            elements.logPanel.appendChild(logEntry);
            elements.logPanel.scrollTop = elements.logPanel.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 更新阶段状态
        function updateStage(stageName, status, statusText) {
            const stageEl = document.getElementById(`stage-${stageName}`);
            if (stageEl) {
                stageEl.className = `stage ${status}`;
                stageEl.querySelector('.status').textContent = statusText;
            }

            elements.currentStage.textContent = `${stageName} - ${statusText}`;

            if (status === 'active') {
                stageTimers[stageName] = Date.now();
                log(`🔄 开始 ${stageName} 阶段`, 'info');
            } else if (status === 'success') {
                const duration = stageTimers[stageName] ?
                    ((Date.now() - stageTimers[stageName]) / 1000).toFixed(2) : '0';
                log(`✅ ${stageName} 阶段完成 (${duration}秒)`, 'success');

                // 更新性能指标
                if (stageName === 'asr') elements.asrTime.textContent = duration;
                else if (stageName === 'llm') elements.llmTime.textContent = duration;
                else if (stageName === 'tts') elements.ttsTime.textContent = duration;
            } else if (status === 'error') {
                log(`❌ ${stageName} 阶段失败: ${statusText}`, 'error');
            }
        }

        // 测试后端连接
        async function testBackendConnection() {
            log('🔌 测试TTS API服务器连接...', 'info');
            elements.connectBtn.disabled = true;

            try {
                const response = await fetch('http://localhost:8001/health');
                if (response.ok) {
                    const data = await response.json();
                    log('✅ TTS API服务器连接成功', 'success');
                    elements.backendStatus.textContent = 'TTS已连接';
                    elements.asrTestBtn.disabled = false;
                    elements.testBtn.disabled = false;
                    elements.recordBtn.disabled = false;  // 直接启用录音按钮
                    backendConnected = true;

                    log(`📊 TTS服务状态: ${JSON.stringify(data.services)}`, 'info');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ TTS API服务器连接失败: ${error.message}`, 'error');
                elements.backendStatus.textContent = 'TTS连接失败';
                log('💡 请确保TTS API服务器在 http://localhost:8001 运行', 'warning');
                log('💡 运行命令: python start_real_voice_test.py', 'info');
            }

            elements.connectBtn.disabled = false;
        }

        // 测试ASR连接
        async function testASRConnection() {
            log('🔌 测试增强ASR服务连接 (Qwen + Gemini)...', 'info');
            elements.asrTestBtn.disabled = true;

            try {
                // 测试专门的ASR连接端点
                const response = await fetch('http://localhost:8000/api/asr/test');
                if (response.ok) {
                    const data = await response.json();
                    log('✅ 增强ASR服务连接成功', 'success');

                    const availableServices = data.asr_services.available_services || [];
                    const qwenAvailable = data.asr_services.qwen_available;
                    const geminiAvailable = data.asr_services.gemini_available;

                    let statusText = 'ASR已连接';
                    if (qwenAvailable && geminiAvailable) {
                        statusText = 'ASR已连接 (Qwen+Gemini)';
                    } else if (qwenAvailable) {
                        statusText = 'ASR已连接 (仅Qwen)';
                    } else if (geminiAvailable) {
                        statusText = 'ASR已连接 (仅Gemini)';
                    }

                    elements.backendStatus.textContent = statusText;
                    elements.recordBtn.disabled = false;

                    log(`📊 可用ASR服务: ${availableServices.join(', ')}`, 'info');
                    log(`🔧 Qwen2-Audio-7B: ${qwenAvailable ? '✅' : '❌'}`, qwenAvailable ? 'success' : 'warning');
                    log(`🔧 Gemini-2.0-Flash: ${geminiAvailable ? '✅' : '❌'}`, geminiAvailable ? 'success' : 'warning');

                    if (availableServices.length === 0) {
                        log('⚠️ 警告: 没有可用的ASR服务，将使用备用文本', 'warning');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ ASR服务器连接失败: ${error.message}`, 'error');
                elements.backendStatus.textContent = 'ASR连接失败';
                log('💡 请确保ASR服务器在 http://localhost:8000 运行', 'warning');
                log('💡 运行命令: python backend/main.py', 'info');
                log('💡 检查GEMINI_API_KEY环境变量配置', 'info');
                log('💡 确保Qwen2-Audio-7B服务在 http://************:20257 运行', 'info');
            }

            elements.asrTestBtn.disabled = false;
        }

        // 测试真实服务
        async function testRealServices() {
            log('🧪 开始测试真实LLM和TTS服务...', 'info');
            startTime = Date.now();

            try {
                // 测试LLM服务
                updateStage('llm', 'active', '生成中');

                const llmResponse = await fetch('http://localhost:8001/test');

                if (!llmResponse.ok) {
                    throw new Error(`LLM测试失败: ${llmResponse.status}`);
                }

                const llmData = await llmResponse.json();
                updateStage('llm', 'success', '生成完成');

                const aiResponse = llmData.response || '测试响应';
                elements.llmResult.textContent = `AI回复: "${aiResponse}"`;
                log(`🧠 LLM响应: "${aiResponse}"`, 'success');

                // 测试TTS服务
                updateStage('tts', 'active', '合成中');

                const ttsResponse = await fetch('http://localhost:8001/api/tts/synthesize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: aiResponse,
                        emotion: 'neutral',
                        speed: 1.0
                    })
                });

                if (!ttsResponse.ok) {
                    throw new Error(`TTS合成失败: ${ttsResponse.status}`);
                }

                const audioBlob = await ttsResponse.blob();
                updateStage('tts', 'success', '合成完成');
                log(`🔊 TTS合成成功: ${audioBlob.size} 字节`, 'success');

                // 播放真实TTS音频
                updateStage('playback', 'active', '播放中');
                const audioUrl = URL.createObjectURL(audioBlob);
                elements.responseAudio.src = audioUrl;
                elements.responseAudio.style.display = 'block';

                try {
                    await elements.responseAudio.play();
                    updateStage('playback', 'success', '播放完成');
                    log('🎵 真实TTS音频播放成功', 'success');
                } catch (e) {
                    updateStage('playback', 'success', '准备播放');
                    log('🎵 音频已准备，请手动播放', 'info');
                }

                const totalTime = startTime ? ((Date.now() - startTime) / 1000).toFixed(2) : '0';
                log(`✅ 真实服务测试完成！总耗时: ${totalTime}秒`, 'success');

            } catch (error) {
                log(`❌ 真实服务测试失败: ${error.message}`, 'error');
                updateStage('llm', 'error', '测试失败');
                updateStage('tts', 'error', '测试失败');
            }
        }

        // 录音相关函数
        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                stopRecording();
            }
        }

        async function startRecording() {
            try {
                log('🎤 请求麦克风权限...', 'info');

                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                const options = {};
                if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
                    options.mimeType = 'audio/webm;codecs=opus';
                } else if (MediaRecorder.isTypeSupported('audio/wav')) {
                    options.mimeType = 'audio/wav';
                }

                mediaRecorder = new MediaRecorder(stream, options);
                audioChunks = [];

                resetStages();
                startTime = Date.now();
                const recordingStartTime = Date.now();

                updateStage('recording', 'active', '录音中');

                mediaRecorder.ondataavailable = function (event) {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = function () {
                    const recordingDuration = ((Date.now() - recordingStartTime) / 1000).toFixed(2);
                    elements.recordingDuration.textContent = recordingDuration;

                    updateStage('recording', 'success', '录音完成');

                    const audioBlob = new Blob(audioChunks, { type: options.mimeType || 'audio/webm' });
                    log(`🎵 录音完成: ${audioBlob.size} bytes, 时长: ${recordingDuration}秒`, 'success');

                    // 保存录音用于播放
                    const recordedUrl = URL.createObjectURL(audioBlob);
                    elements.recordedAudio.src = recordedUrl;
                    elements.recordedAudio.style.display = 'block';

                    // 发送到后端处理
                    processRecordedAudio(audioBlob);

                    // 停止音频流
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.start(1000);
                isRecording = true;

                elements.recordBtn.textContent = '停止录音';
                elements.recordBtn.className = 'record-btn recording';

                log('✅ 录音已开始', 'success');

            } catch (error) {
                log(`❌ 录音启动失败: ${error.message}`, 'error');
                updateStage('recording', 'error', '权限被拒绝');
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                log('🛑 停止录音...', 'info');
                mediaRecorder.stop();
                isRecording = false;

                elements.recordBtn.textContent = '开始录音';
                elements.recordBtn.className = 'record-btn';
            }
        }

        // 执行LLM处理
        async function performLLM(asrText, originalStartTime) {
            updateStage('llm', 'active', '生成中');

            const llmStartTime = Date.now();
            let aiResponse = "";

            try {
                // 使用ASR识别的文本作为LLM输入
                log(`📝 LLM输入文本: "${asrText}"`, 'info');

                // 尝试调用测试端点，生成基于ASR结果的回复
                const llmResponse = await fetch('http://localhost:8001/test');

                if (llmResponse.ok) {
                    const llmData = await llmResponse.json();
                    // 生成基于ASR结果的智能回复
                    aiResponse = `针对"${asrText}"的回复：${llmData.response || '你好！我收到了你的语音消息。'}`;
                } else {
                    throw new Error(`LLM调用失败: ${llmResponse.status}`);
                }

                updateStage('llm', 'success', '生成完成');
                elements.llmResult.textContent = `用户输入: "${asrText}"\nAI回复: "${aiResponse}"\n服务: 基于ASR结果的智能LLM`;
                log(`🧠 LLM回复 (基于"${asrText}"): "${aiResponse}"`, 'success');

            } catch (error) {
                log(`❌ LLM生成失败: ${error.message}`, 'error');
                // 生成一个基于ASR结果的智能备用回复
                aiResponse = `针对"${asrText}"的回复：你好！我听到你说"${asrText}"，这是一个基于你语音内容的智能回复。`;
                updateStage('llm', 'warning', '使用智能备用回复');
                elements.llmResult.textContent = `用户输入: "${asrText}"\nAI回复: "${aiResponse}"\n(LLM服务连接失败，使用基于ASR结果的智能备用回复)`;
                log(`🧠 使用智能备用回复 (基于"${asrText}"): "${aiResponse}"`, 'warning');
            }

            const llmEndTime = Date.now();
            const llmDuration = (llmEndTime - llmStartTime) / 1000;
            elements.llmTime.textContent = llmDuration.toFixed(2);
            log(`⏱️ LLM处理耗时: ${llmDuration.toFixed(2)}秒`, 'info');

            // 继续TTS处理
            await performTTS(aiResponse, originalStartTime);
        }

        // 执行TTS处理
        async function performTTS(aiResponse, originalStartTime) {
            updateStage('tts', 'active', '合成中');

            const ttsStartTime = Date.now();
            let ttsAudioBlob = null;

            try {
                log(`🔊 TTS输入文本: "${aiResponse}"`, 'info');

                const ttsResponse = await fetch('http://localhost:8001/api/tts/synthesize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: aiResponse,
                        emotion: 'neutral',
                        speed: 1.0
                    })
                });

                if (!ttsResponse.ok) {
                    const errorText = await ttsResponse.text();
                    throw new Error(`TTS合成失败: ${ttsResponse.status} - ${errorText}`);
                }

                ttsAudioBlob = await ttsResponse.blob();
                updateStage('tts', 'success', '合成完成');
                log(`🔊 TTS合成成功: ${ttsAudioBlob.size} 字节`, 'success');

            } catch (error) {
                log(`❌ TTS合成失败: ${error.message}`, 'error');
                updateStage('tts', 'error', '合成失败');
                log('💡 请确保TTS服务在 http://localhost:8001 运行', 'warning');
                return; // 如果TTS失败，停止后续处理
            }

            const ttsEndTime = Date.now();
            const ttsDuration = (ttsEndTime - ttsStartTime) / 1000;
            elements.ttsTime.textContent = ttsDuration.toFixed(2);
            log(`⏱️ TTS处理耗时: ${ttsDuration.toFixed(2)}秒`, 'info');

            // 播放真实TTS音频
            updateStage('playback', 'active', '播放中');
            const audioUrl = URL.createObjectURL(ttsAudioBlob);
            elements.responseAudio.src = audioUrl;
            elements.responseAudio.style.display = 'block';

            try {
                await elements.responseAudio.play();
                updateStage('playback', 'success', '播放完成');
                log('🎵 真实TTS音频播放成功', 'success');
            } catch (e) {
                updateStage('playback', 'success', '准备播放');
                log('🎵 音频已准备，请手动播放', 'info');
            }

            const totalTime = originalStartTime ? ((Date.now() - originalStartTime) / 1000).toFixed(2) : '0';
            log(`✅ 完整流水线处理完成！总耗时: ${totalTime}秒`, 'success');
            log(`📊 性能统计: ASR→LLM→TTS→播放 完成`, 'info');
        }

        // 处理录制的音频
        async function processRecordedAudio(audioBlob) {
            try {
                log('🎤 开始处理录制的音频...', 'info');

                // 真实ASR阶段 - 调用后端ASR服务
                updateStage('asr', 'active', '识别中');

                const asrStartTime = Date.now();
                let asrText = "";

                try {
                    // 简化ASR调用 - 直接使用完整流水线API
                    const formData = new FormData();
                    formData.append('file', audioBlob, 'recording.wav');
                    formData.append('user_id', '1');
                    formData.append('npc_id', '1');

                    log('📤 发送音频到ASR服务 (完整流水线)...', 'info');
                    const asrResponse = await fetch('http://localhost:8000/process-audio', {
                        method: 'POST',
                        body: formData
                    });

                    if (asrResponse.ok) {
                        const asrResult = await asrResponse.json();

                        if (asrResult.transcription) {
                            asrText = asrResult.transcription;
                            const provider = asrResult.asr_provider || "Enhanced ASR";
                            const confidence = asrResult.asr_confidence || 0.0;

                            updateStage('asr', 'success', '识别完成');
                            elements.asrResult.textContent = `识别文本: "${asrText}"\n服务: ${provider}\n置信度: ${confidence.toFixed(2)}\n状态: 真实识别结果`;
                            log(`🎯 ASR识别成功 (${provider}): "${asrText}"`, 'success');

                            // 如果完整流水线成功，直接使用返回的LLM结果
                            if (asrResult.response_text) {
                                log('🎉 完整流水线成功，跳过单独的LLM调用', 'info');

                                // 直接使用流水线返回的LLM结果
                                const aiResponse = asrResult.response_text;

                                updateStage('llm', 'success', '生成完成');
                                elements.llmResult.textContent = `用户输入: "${asrText}"\nAI回复: "${aiResponse}"\n服务: 完整流水线 (ASR→LLM)`;
                                log(`🧠 LLM回复 (基于"${asrText}"): "${aiResponse}"`, 'success');

                                const asrEndTime = Date.now();
                                const asrDuration = (asrEndTime - asrStartTime) / 1000;
                                elements.asrTime.textContent = asrDuration.toFixed(2);
                                elements.llmTime.textContent = '0.00'; // 包含在ASR时间中
                                log(`⏱️ 完整流水线耗时: ${asrDuration.toFixed(2)}秒`, 'info');

                                // 直接进行TTS
                                await performTTS(aiResponse, asrStartTime);
                                return; // 跳过后续的单独LLM调用
                            }
                        } else if (asrResult.error) {
                            throw new Error(`ASR识别失败: ${asrResult.error}`);
                        } else {
                            throw new Error('ASR返回格式错误');
                        }
                    } else {
                        const errorText = await asrResponse.text();
                        throw new Error(`ASR服务返回错误: ${asrResponse.status} - ${errorText}`);
                    }
                } catch (error) {
                    log(`❌ ASR识别失败: ${error.message}`, 'error');
                    log('💡 请确保后端服务在 http://localhost:8000 运行', 'warning');
                    log('💡 运行命令: python backend/main.py', 'warning');

                    // 使用智能备用文本
                    asrText = "你好，我想测试一下真实的语音合成功能";
                    updateStage('asr', 'warning', '使用备用文本');
                    elements.asrResult.textContent = `识别文本: "${asrText}"\n(ASR服务连接失败，使用备用文本)\n错误: ${error.message}`;
                    log(`🎯 使用备用文本: "${asrText}"`, 'warning');
                }

                const asrEndTime = Date.now();
                const asrDuration = (asrEndTime - asrStartTime) / 1000;
                elements.asrTime.textContent = asrDuration.toFixed(2);
                log(`⏱️ ASR处理耗时: ${asrDuration.toFixed(2)}秒`, 'info');

                // 单独的LLM调用 (如果完整流水线没有返回LLM结果)
                await performLLM(asrText, startTime);

            } catch (error) {
                log(`❌ 音频处理失败: ${error.message}`, 'error');
                updateStage('asr', 'error', '处理失败');
            }
        }

        // 模拟延迟
        function simulateDelay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 重置所有阶段
        function resetStages() {
            const stages = ['recording', 'asr', 'llm', 'tts', 'playback'];
            stages.forEach(stage => {
                const stageEl = document.getElementById(`stage-${stage}`);
                if (stageEl) {
                    stageEl.className = 'stage';
                    stageEl.querySelector('.status').textContent = '等待中';
                }
            });
            document.getElementById('stage-recording').querySelector('.status').textContent = '待开始';
        }

        // 清空结果
        function clearResults() {
            elements.asrResult.textContent = '等待语音输入...';
            elements.llmResult.textContent = '等待对话生成...';
            elements.recordedAudio.style.display = 'none';
            elements.responseAudio.style.display = 'none';
            elements.recordingDuration.textContent = '0';
            elements.asrTime.textContent = '0';
            elements.llmTime.textContent = '0';
            elements.ttsTime.textContent = '0';
            elements.processingTime.textContent = '0秒';
            resetStages();
            log('🗑️ 结果已清空', 'info');
        }

        // 更新处理时间
        function updateProcessingTime() {
            if (startTime) {
                const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
                elements.processingTime.textContent = `${elapsed}秒`;
            }
        }

        // 定时更新处理时间
        setInterval(updateProcessingTime, 100);

        // 页面加载完成
        window.onload = function () {
            log('🌐 页面加载完成，准备连接后端', 'info');
            // 自动测试连接
            setTimeout(() => {
                testBackendConnection();
            }, 1000);
        };
    </script>
</body>

</html>