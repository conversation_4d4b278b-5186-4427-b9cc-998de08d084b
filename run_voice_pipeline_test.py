#!/usr/bin/env python3
"""
启动完整语音流水线测试环境
提供Web界面和后端API支持
"""

import subprocess
import sys
import time
import webbrowser
import threading
from pathlib import Path
import http.server
import socketserver
import os

def start_web_server():
    """启动Web服务器"""
    try:
        PORT = 8002
        Handler = http.server.SimpleHTTPRequestHandler
        
        # 切换到当前目录
        os.chdir(Path(__file__).parent)
        
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"✅ Web服务器已启动: http://localhost:{PORT}")
            print(f"📄 测试页面: http://localhost:{PORT}/complete_voice_pipeline_test.html")
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")

def main():
    """启动完整语音流水线测试环境"""
    print("🎤 启动完整语音流水线测试环境")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "complete_voice_pipeline_test.html",
        "test_complete_voice_pipeline.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return
    
    print("✅ 所有必要文件存在")
    
    try:
        # 在后台启动Web服务器
        print("🚀 启动Web服务器...")
        server_thread = threading.Thread(target=start_web_server, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        
        # 自动打开浏览器
        print("🌍 正在打开浏览器...")
        webbrowser.open("http://localhost:8002/complete_voice_pipeline_test.html")
        
        print("\n" + "="*60)
        print("🎤 完整语音流水线测试说明:")
        print("1. 浏览器会自动打开测试页面")
        print("2. 点击'开始录音'进行手动录音测试")
        print("3. 点击'开始完整测试'进行自动化测试")
        print("4. 页面会显示完整的流水线处理过程")
        print("5. 也可以运行 Python 脚本进行后端测试:")
        print("   python test_complete_voice_pipeline.py")
        print("6. 按 Ctrl+C 停止测试环境")
        print("="*60)
        
        try:
            # 保持程序运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止测试环境...")
            print("✅ 测试环境已停止")
    
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()